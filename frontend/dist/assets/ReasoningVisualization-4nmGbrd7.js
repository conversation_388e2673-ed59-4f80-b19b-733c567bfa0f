import{r as v,j as e}from"./index-YkVyuS57.js";const p=({reasoningData:t})=>{const[o,h]=v.useState(new Set),a=i=>{const d=new Set(o);d.has(i)?d.delete(i):d.add(i),h(d)},r=(i,d=0)=>{const n=i.children&&i.children.length>0,s=o.has(i.id);return e.jsxs("div",{className:`reasoning-node level-${d}`,children:[e.jsxs("div",{className:"node-content",onClick:()=>n&&a(i.id),children:[e.jsxs("div",{className:"node-header",children:[n&&e.jsx("span",{className:`expand-icon ${s?"expanded":""}`,children:"▶"}),e.jsx("span",{className:"node-title",children:i.step}),e.jsxs("span",{className:"confidence-badge",children:[(i.confidence*100).toFixed(0),"%"]})]}),i.evidence&&i.evidence.length>0&&e.jsx("div",{className:"evidence-list",children:i.evidence.map((l,c)=>e.jsx("span",{className:"evidence-item",children:l},c))})]}),n&&s&&e.jsx("div",{className:"children-container",children:i.children.map(l=>r(l,d+1))})]},i.id)};return!t||!t.reasoning?e.jsx("div",{className:"reasoning-tree",children:"No reasoning data available"}):e.jsxs("div",{className:"reasoning-tree",children:[e.jsx("h3",{children:"Reasoning Process"}),e.jsx("div",{className:"tree-container",children:t.reasoning.map(i=>r(i))})]})},x=({steps:t,currentStep:o=0,onStepClick:h})=>{const[a,r]=v.useState(o),i=n=>{r(n),h&&h(n,t[n])},d=n=>n<a?"completed":n===a?"active":"pending";return!t||t.length===0?e.jsx("div",{className:"step-by-step-empty",children:"No reasoning steps available"}):e.jsxs("div",{className:"step-by-step",children:[e.jsxs("div",{className:"steps-header",children:[e.jsx("h4",{children:"Step-by-Step Reasoning"}),e.jsxs("span",{className:"step-counter",children:[a+1," of ",t.length]})]}),e.jsx("div",{className:"steps-timeline",children:t.map((n,s)=>e.jsxs("div",{className:`step-item ${d(s)}`,onClick:()=>i(s),children:[e.jsx("div",{className:"step-marker",children:e.jsx("span",{className:"step-number",children:s+1})}),e.jsxs("div",{className:"step-content",children:[e.jsx("div",{className:"step-title",children:n.title}),e.jsx("div",{className:"step-description",children:n.description}),n.reasoning&&e.jsxs("div",{className:"step-reasoning",children:[e.jsx("strong",{children:"Reasoning:"})," ",n.reasoning]}),n.evidence&&n.evidence.length>0&&e.jsxs("div",{className:"step-evidence",children:[e.jsx("strong",{children:"Evidence:"}),e.jsx("ul",{children:n.evidence.map((l,c)=>e.jsx("li",{children:l},c))})]}),n.confidence&&e.jsxs("div",{className:"step-confidence",children:[e.jsx("span",{className:"confidence-label",children:"Confidence:"}),e.jsx("div",{className:"confidence-bar",children:e.jsx("div",{className:"confidence-fill",style:{width:`${n.confidence*100}%`}})}),e.jsxs("span",{className:"confidence-value",children:[Math.round(n.confidence*100),"%"]})]})]})]},s))}),e.jsxs("div",{className:"steps-navigation",children:[e.jsx("button",{onClick:()=>i(Math.max(0,a-1)),disabled:a===0,className:"nav-button prev",children:"Previous"}),e.jsx("button",{onClick:()=>i(Math.min(t.length-1,a+1)),disabled:a===t.length-1,className:"nav-button next",children:"Next"})]})]})},j=({sources:t,onSourceClick:o,highlightedText:h})=>{const[a,r]=v.useState(new Set),i=s=>{const l=new Set(a);l.has(s)?l.delete(s):l.add(s),r(l)},d=s=>s>=.8?"high":s>=.6?"medium":"low",n=s=>{switch(s){case"policy":return"📋";case"document":return"📄";case"database":return"🗃️";case"external":return"🔗";default:return"📝"}};return!t||t.length===0?e.jsx("div",{className:"source-attribution-empty",children:"No sources available"}):e.jsxs("div",{className:"source-attribution",children:[e.jsxs("div",{className:"sources-header",children:[e.jsx("h4",{children:"Sources & Citations"}),e.jsxs("span",{className:"source-count",children:[t.length," sources"]})]}),e.jsx("div",{className:"sources-list",children:t.map((s,l)=>e.jsxs("div",{className:`source-item ${a.has(s.id)?"expanded":""}`,children:[e.jsxs("div",{className:"source-header",onClick:()=>i(s.id),children:[e.jsxs("div",{className:"source-info",children:[e.jsx("span",{className:"source-icon",children:n(s.type)}),e.jsxs("div",{className:"source-details",children:[e.jsx("span",{className:"source-title",children:s.title}),e.jsx("span",{className:"source-type",children:s.type})]})]}),e.jsxs("div",{className:"source-meta",children:[e.jsx("div",{className:`credibility-indicator ${d(s.credibility)}`,children:e.jsxs("span",{className:"credibility-score",children:[Math.round(s.credibility*100),"%"]})}),e.jsx("span",{className:"expand-toggle",children:a.has(s.id)?"▼":"▶"})]})]}),a.has(s.id)&&e.jsxs("div",{className:"source-content",children:[s.excerpt&&e.jsxs("div",{className:"source-excerpt",children:[e.jsx("strong",{children:"Excerpt:"}),e.jsx("p",{children:s.excerpt})]}),s.relevance&&e.jsxs("div",{className:"source-relevance",children:[e.jsx("span",{className:"relevance-label",children:"Relevance:"}),e.jsx("div",{className:"relevance-bar",children:e.jsx("div",{className:"relevance-fill",style:{width:`${s.relevance*100}%`}})}),e.jsxs("span",{className:"relevance-value",children:[Math.round(s.relevance*100),"%"]})]}),s.url&&e.jsx("div",{className:"source-link",children:e.jsx("a",{href:s.url,target:"_blank",rel:"noopener noreferrer",onClick:c=>{c.stopPropagation(),o&&o(s)},children:"View Source →"})}),s.lastUpdated&&e.jsxs("div",{className:"source-updated",children:["Last updated: ",new Date(s.lastUpdated).toLocaleDateString()]})]})]},s.id||l))}),h&&e.jsxs("div",{className:"highlighted-evidence",children:[e.jsx("h5",{children:"Evidence Highlights"}),e.jsx("div",{className:"evidence-text",children:e.jsx("span",{dangerouslySetInnerHTML:{__html:h}})})]})]})},N=({confidence:t,uncertainty:o,alternatives:h=[],factors:a=[]})=>{const[r,i]=v.useState("overview"),d=c=>c>=.8?"high":c>=.6?"medium":"low",n=()=>e.jsx("div",{className:"confidence-overview",children:e.jsxs("div",{className:"confidence-main",children:[e.jsxs("div",{className:"confidence-circle",children:[e.jsxs("svg",{width:"120",height:"120",viewBox:"0 0 120 120",children:[e.jsx("circle",{cx:"60",cy:"60",r:"50",fill:"none",stroke:"#e8eaed",strokeWidth:"8"}),e.jsx("circle",{cx:"60",cy:"60",r:"50",fill:"none",stroke:"#1a73e8",strokeWidth:"8",strokeDasharray:`${t*314} 314`,strokeDashoffset:"78.5",transform:"rotate(-90 60 60)",className:"confidence-arc"})]}),e.jsxs("div",{className:"confidence-text",children:[e.jsxs("span",{className:"confidence-value",children:[Math.round(t*100),"%"]}),e.jsx("span",{className:"confidence-label",children:"Confidence"})]})]}),e.jsxs("div",{className:"confidence-details",children:[e.jsx("div",{className:`confidence-badge ${d(t)}`,children:d(t).toUpperCase()}),o&&e.jsxs("div",{className:"uncertainty-info",children:[e.jsx("span",{className:"uncertainty-label",children:"Uncertainty:"}),e.jsxs("span",{className:"uncertainty-value",children:["±",Math.round(o*100),"%"]})]})]})]})}),s=()=>e.jsxs("div",{className:"confidence-factors",children:[e.jsx("h5",{children:"Confidence Factors"}),a.length>0?e.jsx("div",{className:"factors-list",children:a.map((c,m)=>e.jsxs("div",{className:"factor-item",children:[e.jsxs("div",{className:"factor-header",children:[e.jsx("span",{className:"factor-name",children:c.name}),e.jsxs("span",{className:`factor-impact ${c.impact>0?"positive":"negative"}`,children:[c.impact>0?"+":"",Math.round(c.impact*100),"%"]})]}),e.jsx("div",{className:"factor-description",children:c.description}),e.jsx("div",{className:"factor-bar",children:e.jsx("div",{className:`factor-fill ${c.impact>0?"positive":"negative"}`,style:{width:`${Math.abs(c.impact)*100}%`}})})]},m))}):e.jsx("div",{className:"no-factors",children:"No confidence factors available"})]}),l=()=>e.jsxs("div",{className:"alternatives-view",children:[e.jsx("h5",{children:"Alternative Hypotheses"}),h.length>0?e.jsx("div",{className:"alternatives-list",children:h.map((c,m)=>e.jsxs("div",{className:"alternative-item",children:[e.jsxs("div",{className:"alternative-header",children:[e.jsx("span",{className:"alternative-title",children:c.title}),e.jsxs("span",{className:`alternative-confidence ${d(c.confidence)}`,children:[Math.round(c.confidence*100),"%"]})]}),e.jsx("div",{className:"alternative-description",children:c.description}),e.jsxs("div",{className:"alternative-likelihood",children:[e.jsx("span",{className:"likelihood-label",children:"Likelihood:"}),e.jsx("div",{className:"likelihood-bar",children:e.jsx("div",{className:"likelihood-fill",style:{width:`${c.likelihood*100}%`}})}),e.jsxs("span",{className:"likelihood-value",children:[Math.round(c.likelihood*100),"%"]})]})]},m))}):e.jsx("div",{className:"no-alternatives",children:"No alternative hypotheses available"})]});return e.jsxs("div",{className:"confidence-visualization",children:[e.jsxs("div",{className:"confidence-header",children:[e.jsx("h4",{children:"Confidence Analysis"}),e.jsxs("div",{className:"confidence-tabs",children:[e.jsx("button",{className:`tab ${r==="overview"?"active":""}`,onClick:()=>i("overview"),children:"Overview"}),e.jsx("button",{className:`tab ${r==="factors"?"active":""}`,onClick:()=>i("factors"),children:"Factors"}),e.jsx("button",{className:`tab ${r==="alternatives"?"active":""}`,onClick:()=>i("alternatives"),children:"Alternatives"})]})]}),e.jsxs("div",{className:"confidence-content",children:[r==="overview"&&n(),r==="factors"&&s(),r==="alternatives"&&l()]})]})},f=({reasoningData:t,isVisible:o,onClose:h})=>{const[a,r]=v.useState("tree");if(!o||!t)return null;const i={root:{id:"root",title:"HR Policy Analysis",description:"Analyzing privilege leave policy for NUVO AI",confidence:.87,reasoning:"Starting comprehensive policy analysis",children:[{id:"search",title:"Policy Search",description:"Searching for relevant HR policies",confidence:.92,reasoning:"Found 3 relevant policy documents",children:[{id:"policy1",title:"Leave Policy Document",description:"Main leave policy document found",confidence:.95,reasoning:"High confidence in policy accuracy"}]},{id:"analysis",title:"Policy Analysis",description:"Analyzing policy requirements",confidence:.83,reasoning:"Cross-referencing multiple sources",children:[]}]},totalNodes:4,totalPaths:2,paths:[{nodes:["root","search","policy1"],confidence:.91,description:"Direct policy lookup path"},{nodes:["root","analysis"],confidence:.83,description:"Analytical reasoning path"}]},d=[{title:"Query Understanding",description:"Analyzing the user query about privilege leave policy",reasoning:"Identified key terms: privilege leave, policy, NUVO AI",evidence:["Query contains specific policy request","Organization context provided"],confidence:.95},{title:"Knowledge Search",description:"Searching organizational knowledge base",reasoning:"Using semantic search across HR documents",evidence:["Found 3 relevant documents","High semantic similarity scores"],confidence:.88},{title:"Policy Extraction",description:"Extracting relevant policy information",reasoning:"Parsing structured policy data",evidence:["Policy section identified","Eligibility criteria found"],confidence:.92},{title:"Response Generation",description:"Generating comprehensive response",reasoning:"Combining policy data with context",evidence:["All requirements addressed","Clear explanation provided"],confidence:.87}],n=[{id:"src1",title:"NUVO AI Employee Handbook",type:"policy",credibility:.95,relevance:.92,excerpt:"Privilege leave is granted to employees with more than 2 years of service...",url:"#",lastUpdated:"2024-01-15"},{id:"src2",title:"HR Policy Database",type:"database",credibility:.88,relevance:.85,excerpt:"Leave policies are updated annually and require manager approval...",url:"#",lastUpdated:"2024-02-01"}],s={confidence:.87,uncertainty:.13,factors:[{name:"Source Reliability",impact:.15,description:"Official HR documents provide high reliability"},{name:"Policy Recency",impact:.08,description:"Recent policy updates increase confidence"},{name:"Context Completeness",impact:-.05,description:"Some context details missing"}],alternatives:[{title:"Alternative Interpretation",description:"Policy might have different eligibility criteria",confidence:.23,likelihood:.15}]};return e.jsx("div",{className:"reasoning-visualization-overlay",children:e.jsxs("div",{className:"reasoning-visualization-modal",children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:"Reasoning Analysis"}),e.jsxs("div",{className:"view-tabs",children:[e.jsx("button",{className:`tab ${a==="tree"?"active":""}`,onClick:()=>r("tree"),children:"Tree View"}),e.jsx("button",{className:`tab ${a==="steps"?"active":""}`,onClick:()=>r("steps"),children:"Step-by-Step"}),e.jsx("button",{className:`tab ${a==="sources"?"active":""}`,onClick:()=>r("sources"),children:"Sources"}),e.jsx("button",{className:`tab ${a==="confidence"?"active":""}`,onClick:()=>r("confidence"),children:"Confidence"})]}),e.jsx("button",{className:"close-button",onClick:h,children:"×"})]}),e.jsxs("div",{className:"modal-content",children:[a==="tree"&&e.jsx(p,{treeData:i,onNodeClick:l=>console.log("Node clicked:",l)}),a==="steps"&&e.jsx(x,{steps:d,onStepClick:(l,c)=>console.log("Step clicked:",l,c)}),a==="sources"&&e.jsx(j,{sources:n,onSourceClick:l=>console.log("Source clicked:",l),highlightedText:"<mark>Privilege leave</mark> is granted to employees with <mark>more than 2 years</mark> of service"}),a==="confidence"&&e.jsx(N,{confidence:s.confidence,uncertainty:s.uncertainty,factors:s.factors,alternatives:s.alternatives})]})]})})};export{f as default};
