import{r as m,j as e}from"./index-YkVyuS57.js";const p=({agents:n,activeAgent:l,onAgentClick:d})=>{const[a,t]=m.useState(null),o=s=>({thinking:"🤔",searching:"🔍",analyzing:"📊",coordinating:"🤝",responding:"💬",idle:"⏸️"})[s]||"🤖",r=s=>({active:"#34a853",busy:"#fbbc04",waiting:"#9aa0a6",error:"#ea4335"})[s]||"#9aa0a6",i=s=>{t(a===s?null:s)};return e.jsxs("div",{className:"agent-activity",children:[e.jsxs("div",{className:"activity-header",children:[e.jsx("h4",{children:"Agent Activity"}),e.jsxs("span",{className:"active-count",children:[n.filter(s=>s.status==="active").length," active"]})]}),e.jsx("div",{className:"agents-grid",children:n.map(s=>e.jsxs("div",{className:`agent-card ${s.id===l?"active":""} ${a===s.id?"expanded":""}`,onClick:()=>{i(s.id),d&&d(s)},children:[e.jsxs("div",{className:"agent-header",children:[e.jsxs("div",{className:"agent-info",children:[e.jsx("span",{className:"agent-icon",children:o(s.activity)}),e.jsxs("div",{className:"agent-details",children:[e.jsx("span",{className:"agent-name",children:s.name}),e.jsx("span",{className:"agent-role",children:s.role})]})]}),e.jsxs("div",{className:"agent-status",children:[e.jsx("div",{className:"status-dot",style:{backgroundColor:r(s.status)}}),e.jsx("span",{className:"status-text",children:s.status})]})]}),a===s.id&&e.jsxs("div",{className:"agent-expanded",children:[e.jsxs("div",{className:"agent-activity-detail",children:[e.jsx("strong",{children:"Current Activity:"})," ",s.currentTask||"Idle"]}),s.contributions&&s.contributions.length>0&&e.jsxs("div",{className:"agent-contributions",children:[e.jsx("strong",{children:"Recent Contributions:"}),e.jsx("ul",{children:s.contributions.slice(0,3).map((c,u)=>e.jsx("li",{children:c},u))})]}),s.performance&&e.jsxs("div",{className:"agent-performance",children:[e.jsxs("div",{className:"performance-metric",children:[e.jsx("span",{children:"Accuracy:"}),e.jsx("div",{className:"metric-bar",children:e.jsx("div",{className:"metric-fill",style:{width:`${s.performance.accuracy*100}%`}})}),e.jsxs("span",{children:[Math.round(s.performance.accuracy*100),"%"]})]}),e.jsxs("div",{className:"performance-metric",children:[e.jsx("span",{children:"Speed:"}),e.jsx("div",{className:"metric-bar",children:e.jsx("div",{className:"metric-fill",style:{width:`${s.performance.speed*100}%`}})}),e.jsxs("span",{children:[Math.round(s.performance.speed*100),"%"]})]})]})]})]},s.id))})]})},h=({toolChain:n,activeTools:l,onToolClick:d})=>{const[a,t]=m.useState(null),o=s=>({search:"🔍",database:"🗃️",analysis:"📊",validation:"✅",generation:"✨",communication:"📡"})[s]||"🔧",r=s=>{const c={running:{color:"#1a73e8",text:"Running"},completed:{color:"#34a853",text:"Completed"},failed:{color:"#ea4335",text:"Failed"},pending:{color:"#9aa0a6",text:"Pending"}};return c[s]||c.pending},i=s=>{t(a===s?null:s)};return e.jsxs("div",{className:"tool-execution",children:[e.jsxs("div",{className:"execution-header",children:[e.jsx("h4",{children:"Tool Execution"}),e.jsxs("span",{className:"active-tools",children:[(l==null?void 0:l.length)||0," active"]})]}),n&&n.length>0?e.jsx("div",{className:"tool-chain",children:n.map((s,c)=>e.jsxs("div",{className:"tool-chain-item",children:[e.jsxs("div",{className:`tool-card ${a===s.id?"expanded":""}`,onClick:()=>{i(s.id),d&&d(s)},children:[e.jsxs("div",{className:"tool-header",children:[e.jsxs("div",{className:"tool-info",children:[e.jsx("span",{className:"tool-icon",children:o(s.type)}),e.jsxs("div",{className:"tool-details",children:[e.jsx("span",{className:"tool-name",children:s.name}),e.jsx("span",{className:"tool-type",children:s.type})]})]}),e.jsxs("div",{className:"tool-status",children:[e.jsx("div",{className:"status-indicator",style:{backgroundColor:r(s.status).color}}),e.jsx("span",{className:"status-text",children:r(s.status).text})]})]}),s.progress&&e.jsxs("div",{className:"tool-progress",children:[e.jsx("div",{className:"progress-bar",children:e.jsx("div",{className:"progress-fill",style:{width:`${s.progress}%`}})}),e.jsxs("span",{className:"progress-text",children:[s.progress,"%"]})]}),a===s.id&&e.jsxs("div",{className:"tool-expanded",children:[s.description&&e.jsxs("div",{className:"tool-description",children:[e.jsx("strong",{children:"Description:"})," ",s.description]}),s.input&&e.jsxs("div",{className:"tool-input",children:[e.jsx("strong",{children:"Input:"}),e.jsx("pre",{children:JSON.stringify(s.input,null,2)})]}),s.output&&e.jsxs("div",{className:"tool-output",children:[e.jsx("strong",{children:"Output:"}),e.jsx("pre",{children:JSON.stringify(s.output,null,2)})]}),s.executionTime&&e.jsxs("div",{className:"tool-timing",children:[e.jsx("strong",{children:"Execution Time:"})," ",s.executionTime,"ms"]})]})]}),c<n.length-1&&e.jsxs("div",{className:"chain-connector",children:[e.jsx("div",{className:"connector-line"}),e.jsx("div",{className:"connector-arrow",children:"↓"})]})]},s.id))}):e.jsx("div",{className:"no-tools",children:"No tool execution data available"})]})},x=({collaborationData:n,onInteractionClick:l})=>{const[d,a]=m.useState(null),t=()=>n!=null&&n.communications?e.jsxs("div",{className:"communication-flow",children:[e.jsx("h5",{children:"Communication Flow"}),e.jsx("div",{className:"flow-timeline",children:n.communications.map((i,s)=>e.jsxs("div",{className:"communication-item",children:[e.jsxs("div",{className:"comm-header",children:[e.jsx("span",{className:"comm-from",children:i.from}),e.jsx("span",{className:"comm-arrow",children:"→"}),e.jsx("span",{className:"comm-to",children:i.to}),e.jsx("span",{className:"comm-time",children:i.timestamp})]}),e.jsx("div",{className:"comm-content",children:i.message}),i.type&&e.jsx("div",{className:"comm-type",children:e.jsx("span",{className:`type-badge ${i.type}`,children:i.type})})]},s))})]}):null,o=()=>n!=null&&n.delegations?e.jsxs("div",{className:"task-delegation",children:[e.jsx("h5",{children:"Task Delegation"}),e.jsx("div",{className:"delegation-grid",children:n.delegations.map((i,s)=>e.jsxs("div",{className:"delegation-item",children:[e.jsxs("div",{className:"delegation-header",children:[e.jsx("span",{className:"delegator",children:i.delegator}),e.jsx("span",{className:"delegation-arrow",children:"📋→"}),e.jsx("span",{className:"assignee",children:i.assignee})]}),e.jsxs("div",{className:"task-details",children:[e.jsx("div",{className:"task-name",children:i.taskName}),e.jsxs("div",{className:"task-status",children:[e.jsx("span",{className:`status-badge ${i.status}`,children:i.status}),i.progress&&e.jsxs("div",{className:"task-progress",children:[e.jsx("div",{className:"progress-bar",children:e.jsx("div",{className:"progress-fill",style:{width:`${i.progress}%`}})}),e.jsxs("span",{children:[i.progress,"%"]})]})]})]})]},s))})]}):null,r=()=>n!=null&&n.consensus?e.jsxs("div",{className:"consensus-formation",children:[e.jsx("h5",{children:"Consensus Formation"}),e.jsx("div",{className:"consensus-process",children:n.consensus.map((i,s)=>e.jsxs("div",{className:"consensus-item",children:[e.jsx("div",{className:"consensus-topic",children:i.topic}),e.jsxs("div",{className:"consensus-participants",children:[e.jsx("strong",{children:"Participants:"}),e.jsx("div",{className:"participant-list",children:i.participants.map((c,u)=>e.jsxs("div",{className:"participant",children:[e.jsx("span",{className:"participant-name",children:c.name}),e.jsx("span",{className:`vote ${c.vote}`,children:c.vote==="agree"?"✅":c.vote==="disagree"?"❌":"🤔"})]},u))})]}),e.jsxs("div",{className:"consensus-result",children:[e.jsx("span",{className:"result-label",children:"Result:"}),e.jsx("span",{className:`result-status ${i.result}`,children:i.result}),e.jsxs("span",{className:"consensus-score",children:[Math.round(i.agreementScore*100),"% agreement"]})]})]},s))})]}):null;return e.jsxs("div",{className:"agent-collaboration",children:[e.jsxs("div",{className:"collaboration-header",children:[e.jsx("h4",{children:"Agent Collaboration"}),e.jsxs("div",{className:"collaboration-stats",children:[e.jsxs("span",{children:["Active: ",(n==null?void 0:n.activeInteractions)||0]}),e.jsxs("span",{children:["Total: ",(n==null?void 0:n.totalInteractions)||0]})]})]}),e.jsxs("div",{className:"collaboration-content",children:[t(),o(),r()]}),!n&&e.jsx("div",{className:"no-collaboration",children:"No collaboration data available"})]})},j=({interactionData:n,isVisible:l,onClose:d})=>{const[a,t]=m.useState("activity");if(!l)return null;const o=[{id:"org_agent",name:"Organization Agent",role:"Policy Expert",status:"active",activity:"analyzing",currentTask:"Analyzing NUVO AI leave policies",contributions:["Retrieved employee handbook","Identified relevant policy sections","Cross-referenced with current regulations"],performance:{accuracy:.92,speed:.85}},{id:"hr_agent",name:"HR Agent",role:"HR Specialist",status:"busy",activity:"searching",currentTask:"Searching HR database for leave records",contributions:["Accessed employee leave history","Validated policy compliance"],performance:{accuracy:.88,speed:.91}},{id:"reasoning_agent",name:"Reasoning Agent",role:"Logic Processor",status:"waiting",activity:"idle",currentTask:null,contributions:["Processed logical constraints","Validated reasoning chains"],performance:{accuracy:.95,speed:.78}}],r=[{id:"search_tool",name:"Policy Search",type:"search",status:"completed",progress:100,description:"Semantic search across HR documents",input:{query:"privilege leave policy",organization:"NUVO AI"},output:{documents:3,relevance_score:.92},executionTime:245},{id:"db_tool",name:"Database Query",type:"database",status:"running",progress:65,description:"Querying employee database for leave records",input:{table:"employee_leaves",filters:{type:"privilege"}},output:null,executionTime:null},{id:"analysis_tool",name:"Policy Analysis",type:"analysis",status:"pending",progress:0,description:"Analyzing policy requirements and eligibility",input:null,output:null,executionTime:null}],i={activeInteractions:2,totalInteractions:8,communications:[{from:"Organization Agent",to:"HR Agent",message:"Found 3 relevant policy documents, sharing search results",timestamp:"10:32 AM",type:"request"},{from:"HR Agent",to:"Organization Agent",message:"Received documents, cross-referencing with database",timestamp:"10:33 AM",type:"response"},{from:"Reasoning Agent",to:"All Agents",message:"Ready to process logical constraints when data is available",timestamp:"10:34 AM",type:"notification"}],delegations:[{delegator:"Organization Agent",assignee:"HR Agent",taskName:"Validate employee eligibility",status:"in-progress",progress:60},{delegator:"HR Agent",assignee:"Reasoning Agent",taskName:"Process policy logic",status:"pending",progress:0}],consensus:[{topic:"Policy interpretation accuracy",participants:[{name:"Organization Agent",vote:"agree"},{name:"HR Agent",vote:"agree"},{name:"Reasoning Agent",vote:"pending"}],result:"pending",agreementScore:.67}]};return e.jsx("div",{className:"agent-interaction-overlay",children:e.jsxs("div",{className:"agent-interaction-modal",children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:"Agent Interactions"}),e.jsxs("div",{className:"interaction-tabs",children:[e.jsx("button",{className:`tab ${a==="activity"?"active":""}`,onClick:()=>t("activity"),children:"Activity"}),e.jsx("button",{className:`tab ${a==="tools"?"active":""}`,onClick:()=>t("tools"),children:"Tools"}),e.jsx("button",{className:`tab ${a==="collaboration"?"active":""}`,onClick:()=>t("collaboration"),children:"Collaboration"})]}),e.jsx("button",{className:"close-button",onClick:d,children:"×"})]}),e.jsxs("div",{className:"modal-content",children:[a==="activity"&&e.jsx(p,{agents:o,activeAgent:"org_agent",onAgentClick:s=>console.log("Agent clicked:",s)}),a==="tools"&&e.jsx(h,{toolChain:r,activeTools:r.filter(s=>s.status==="running"),onToolClick:s=>console.log("Tool clicked:",s)}),a==="collaboration"&&e.jsx(x,{collaborationData:i,onInteractionClick:s=>console.log("Interaction clicked:",s)})]})]})})};export{j as default};
