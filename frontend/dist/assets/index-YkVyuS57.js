const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ReasoningVisualization-4nmGbrd7.js","assets/ReasoningVisualization-B_x8q2m2.css","assets/AgentInteractionDisplay-DZ6fEyFp.js","assets/AgentInteractionDisplay-LAFI-pYP.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var wo={exports:{}},hs={},So={exports:{}},_={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rr=Symbol.for("react.element"),Vu=Symbol.for("react.portal"),Gu=Symbol.for("react.fragment"),Wu=Symbol.for("react.strict_mode"),Yu=Symbol.for("react.profiler"),qu=Symbol.for("react.provider"),Ku=Symbol.for("react.context"),$u=Symbol.for("react.forward_ref"),Xu=Symbol.for("react.suspense"),Ju=Symbol.for("react.memo"),Zu=Symbol.for("react.lazy"),Ji=Symbol.iterator;function bu(e){return e===null||typeof e!="object"?null:(e=Ji&&e[Ji]||e["@@iterator"],typeof e=="function"?e:null)}var Co={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Eo=Object.assign,ko={};function fn(e,t,n){this.props=e,this.context=t,this.refs=ko,this.updater=n||Co}fn.prototype.isReactComponent={};fn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};fn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ro(){}Ro.prototype=fn.prototype;function ei(e,t,n){this.props=e,this.context=t,this.refs=ko,this.updater=n||Co}var ti=ei.prototype=new Ro;ti.constructor=ei;Eo(ti,fn.prototype);ti.isPureReactComponent=!0;var Zi=Array.isArray,To=Object.prototype.hasOwnProperty,ni={current:null},Io={key:!0,ref:!0,__self:!0,__source:!0};function _o(e,t,n){var r,s={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)To.call(t,r)&&!Io.hasOwnProperty(r)&&(s[r]=t[r]);var o=arguments.length-2;if(o===1)s.children=n;else if(1<o){for(var c=Array(o),f=0;f<o;f++)c[f]=arguments[f+2];s.children=c}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)s[r]===void 0&&(s[r]=o[r]);return{$$typeof:rr,type:e,key:i,ref:a,props:s,_owner:ni.current}}function ed(e,t){return{$$typeof:rr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ri(e){return typeof e=="object"&&e!==null&&e.$$typeof===rr}function td(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var bi=/\/+/g;function Os(e,t){return typeof e=="object"&&e!==null&&e.key!=null?td(""+e.key):t.toString(36)}function Rr(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case rr:case Vu:a=!0}}if(a)return a=e,s=s(a),e=r===""?"."+Os(a,0):r,Zi(s)?(n="",e!=null&&(n=e.replace(bi,"$&/")+"/"),Rr(s,t,n,"",function(f){return f})):s!=null&&(ri(s)&&(s=ed(s,n+(!s.key||a&&a.key===s.key?"":(""+s.key).replace(bi,"$&/")+"/")+e)),t.push(s)),1;if(a=0,r=r===""?".":r+":",Zi(e))for(var o=0;o<e.length;o++){i=e[o];var c=r+Os(i,o);a+=Rr(i,t,n,c,s)}else if(c=bu(e),typeof c=="function")for(e=c.call(e),o=0;!(i=e.next()).done;)i=i.value,c=r+Os(i,o++),a+=Rr(i,t,n,c,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function dr(e,t,n){if(e==null)return e;var r=[],s=0;return Rr(e,r,"","",function(i){return t.call(n,i,s++)}),r}function nd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var oe={current:null},Tr={transition:null},rd={ReactCurrentDispatcher:oe,ReactCurrentBatchConfig:Tr,ReactCurrentOwner:ni};function Po(){throw Error("act(...) is not supported in production builds of React.")}_.Children={map:dr,forEach:function(e,t,n){dr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return dr(e,function(){t++}),t},toArray:function(e){return dr(e,function(t){return t})||[]},only:function(e){if(!ri(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};_.Component=fn;_.Fragment=Gu;_.Profiler=Yu;_.PureComponent=ei;_.StrictMode=Wu;_.Suspense=Xu;_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rd;_.act=Po;_.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Eo({},e.props),s=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=ni.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)To.call(t,c)&&!Io.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&o!==void 0?o[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){o=Array(c);for(var f=0;f<c;f++)o[f]=arguments[f+2];r.children=o}return{$$typeof:rr,type:e.type,key:s,ref:i,props:r,_owner:a}};_.createContext=function(e){return e={$$typeof:Ku,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:qu,_context:e},e.Consumer=e};_.createElement=_o;_.createFactory=function(e){var t=_o.bind(null,e);return t.type=e,t};_.createRef=function(){return{current:null}};_.forwardRef=function(e){return{$$typeof:$u,render:e}};_.isValidElement=ri;_.lazy=function(e){return{$$typeof:Zu,_payload:{_status:-1,_result:e},_init:nd}};_.memo=function(e,t){return{$$typeof:Ju,type:e,compare:t===void 0?null:t}};_.startTransition=function(e){var t=Tr.transition;Tr.transition={};try{e()}finally{Tr.transition=t}};_.unstable_act=Po;_.useCallback=function(e,t){return oe.current.useCallback(e,t)};_.useContext=function(e){return oe.current.useContext(e)};_.useDebugValue=function(){};_.useDeferredValue=function(e){return oe.current.useDeferredValue(e)};_.useEffect=function(e,t){return oe.current.useEffect(e,t)};_.useId=function(){return oe.current.useId()};_.useImperativeHandle=function(e,t,n){return oe.current.useImperativeHandle(e,t,n)};_.useInsertionEffect=function(e,t){return oe.current.useInsertionEffect(e,t)};_.useLayoutEffect=function(e,t){return oe.current.useLayoutEffect(e,t)};_.useMemo=function(e,t){return oe.current.useMemo(e,t)};_.useReducer=function(e,t,n){return oe.current.useReducer(e,t,n)};_.useRef=function(e){return oe.current.useRef(e)};_.useState=function(e){return oe.current.useState(e)};_.useSyncExternalStore=function(e,t,n){return oe.current.useSyncExternalStore(e,t,n)};_.useTransition=function(){return oe.current.useTransition()};_.version="18.3.1";So.exports=_;var A=So.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sd=A,ld=Symbol.for("react.element"),id=Symbol.for("react.fragment"),ad=Object.prototype.hasOwnProperty,od=sd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,cd={key:!0,ref:!0,__self:!0,__source:!0};function Oo(e,t,n){var r,s={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)ad.call(t,r)&&!cd.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:ld,type:e,key:i,ref:a,props:s,_owner:od.current}}hs.Fragment=id;hs.jsx=Oo;hs.jsxs=Oo;wo.exports=hs;var l=wo.exports,al={},Mo={exports:{}},je={},Do={exports:{}},Lo={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,T){var I=E.length;E.push(T);e:for(;0<I;){var W=I-1>>>1,X=E[W];if(0<s(X,T))E[W]=T,E[I]=X,I=W;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var T=E[0],I=E.pop();if(I!==T){E[0]=I;e:for(var W=0,X=E.length,cr=X>>>1;W<cr;){var xt=2*(W+1)-1,Ps=E[xt],Nt=xt+1,ur=E[Nt];if(0>s(Ps,I))Nt<X&&0>s(ur,Ps)?(E[W]=ur,E[Nt]=I,W=Nt):(E[W]=Ps,E[xt]=I,W=xt);else if(Nt<X&&0>s(ur,I))E[W]=ur,E[Nt]=I,W=Nt;else break e}}return T}function s(E,T){var I=E.sortIndex-T.sortIndex;return I!==0?I:E.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,o=a.now();e.unstable_now=function(){return a.now()-o}}var c=[],f=[],m=1,u=null,d=3,g=!1,y=!1,j=!1,M=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(E){for(var T=n(f);T!==null;){if(T.callback===null)r(f);else if(T.startTime<=E)r(f),T.sortIndex=T.expirationTime,t(c,T);else break;T=n(f)}}function x(E){if(j=!1,v(E),!y)if(n(c)!==null)y=!0,Is(C);else{var T=n(f);T!==null&&_s(x,T.startTime-E)}}function C(E,T){y=!1,j&&(j=!1,p(R),R=-1),g=!0;var I=d;try{for(v(T),u=n(c);u!==null&&(!(u.expirationTime>T)||E&&!Te());){var W=u.callback;if(typeof W=="function"){u.callback=null,d=u.priorityLevel;var X=W(u.expirationTime<=T);T=e.unstable_now(),typeof X=="function"?u.callback=X:u===n(c)&&r(c),v(T)}else r(c);u=n(c)}if(u!==null)var cr=!0;else{var xt=n(f);xt!==null&&_s(x,xt.startTime-T),cr=!1}return cr}finally{u=null,d=I,g=!1}}var k=!1,w=null,R=-1,G=5,P=-1;function Te(){return!(e.unstable_now()-P<G)}function pn(){if(w!==null){var E=e.unstable_now();P=E;var T=!0;try{T=w(!0,E)}finally{T?vn():(k=!1,w=null)}}else k=!1}var vn;if(typeof h=="function")vn=function(){h(pn)};else if(typeof MessageChannel<"u"){var Xi=new MessageChannel,Hu=Xi.port2;Xi.port1.onmessage=pn,vn=function(){Hu.postMessage(null)}}else vn=function(){M(pn,0)};function Is(E){w=E,k||(k=!0,vn())}function _s(E,T){R=M(function(){E(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,Is(C))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(E){switch(d){case 1:case 2:case 3:var T=3;break;default:T=d}var I=d;d=T;try{return E()}finally{d=I}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,T){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var I=d;d=E;try{return T()}finally{d=I}},e.unstable_scheduleCallback=function(E,T,I){var W=e.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?W+I:W):I=W,E){case 1:var X=-1;break;case 2:X=250;break;case 5:X=**********;break;case 4:X=1e4;break;default:X=5e3}return X=I+X,E={id:m++,callback:T,priorityLevel:E,startTime:I,expirationTime:X,sortIndex:-1},I>W?(E.sortIndex=I,t(f,E),n(c)===null&&E===n(f)&&(j?(p(R),R=-1):j=!0,_s(x,I-W))):(E.sortIndex=X,t(c,E),y||g||(y=!0,Is(C))),E},e.unstable_shouldYield=Te,e.unstable_wrapCallback=function(E){var T=d;return function(){var I=d;d=T;try{return E.apply(this,arguments)}finally{d=I}}}})(Lo);Do.exports=Lo;var ud=Do.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dd=A,Ne=ud;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var zo=new Set,Fn={};function Mt(e,t){sn(e,t),sn(e+"Capture",t)}function sn(e,t){for(Fn[e]=t,e=0;e<t.length;e++)zo.add(t[e])}var qe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ol=Object.prototype.hasOwnProperty,fd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ea={},ta={};function md(e){return ol.call(ta,e)?!0:ol.call(ea,e)?!1:fd.test(e)?ta[e]=!0:(ea[e]=!0,!1)}function hd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function pd(e,t,n,r){if(t===null||typeof t>"u"||hd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ce(e,t,n,r,s,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var te={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){te[e]=new ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];te[t]=new ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){te[e]=new ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){te[e]=new ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){te[e]=new ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){te[e]=new ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){te[e]=new ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){te[e]=new ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){te[e]=new ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var si=/[\-:]([a-z])/g;function li(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(si,li);te[t]=new ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(si,li);te[t]=new ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(si,li);te[t]=new ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){te[e]=new ce(e,1,!1,e.toLowerCase(),null,!1,!1)});te.xlinkHref=new ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){te[e]=new ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function ii(e,t,n,r){var s=te.hasOwnProperty(t)?te[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(pd(t,n,s,r)&&(n=null),r||s===null?md(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Je=dd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,fr=Symbol.for("react.element"),Ut=Symbol.for("react.portal"),Ft=Symbol.for("react.fragment"),ai=Symbol.for("react.strict_mode"),cl=Symbol.for("react.profiler"),Uo=Symbol.for("react.provider"),Fo=Symbol.for("react.context"),oi=Symbol.for("react.forward_ref"),ul=Symbol.for("react.suspense"),dl=Symbol.for("react.suspense_list"),ci=Symbol.for("react.memo"),be=Symbol.for("react.lazy"),Bo=Symbol.for("react.offscreen"),na=Symbol.iterator;function gn(e){return e===null||typeof e!="object"?null:(e=na&&e[na]||e["@@iterator"],typeof e=="function"?e:null)}var H=Object.assign,Ms;function En(e){if(Ms===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ms=t&&t[1]||""}return`
`+Ms+e}var Ds=!1;function Ls(e,t){if(!e||Ds)return"";Ds=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(f){var r=f}Reflect.construct(e,[],t)}else{try{t.call()}catch(f){r=f}e.call(t.prototype)}else{try{throw Error()}catch(f){r=f}e()}}catch(f){if(f&&r&&typeof f.stack=="string"){for(var s=f.stack.split(`
`),i=r.stack.split(`
`),a=s.length-1,o=i.length-1;1<=a&&0<=o&&s[a]!==i[o];)o--;for(;1<=a&&0<=o;a--,o--)if(s[a]!==i[o]){if(a!==1||o!==1)do if(a--,o--,0>o||s[a]!==i[o]){var c=`
`+s[a].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=a&&0<=o);break}}}finally{Ds=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?En(e):""}function vd(e){switch(e.tag){case 5:return En(e.type);case 16:return En("Lazy");case 13:return En("Suspense");case 19:return En("SuspenseList");case 0:case 2:case 15:return e=Ls(e.type,!1),e;case 11:return e=Ls(e.type.render,!1),e;case 1:return e=Ls(e.type,!0),e;default:return""}}function fl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ft:return"Fragment";case Ut:return"Portal";case cl:return"Profiler";case ai:return"StrictMode";case ul:return"Suspense";case dl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Fo:return(e.displayName||"Context")+".Consumer";case Uo:return(e._context.displayName||"Context")+".Provider";case oi:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ci:return t=e.displayName||null,t!==null?t:fl(e.type)||"Memo";case be:t=e._payload,e=e._init;try{return fl(e(t))}catch{}}return null}function gd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fl(t);case 8:return t===ai?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ht(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Qo(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function yd(e){var t=Qo(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function mr(e){e._valueTracker||(e._valueTracker=yd(e))}function Ho(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Qo(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Hr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ml(e,t){var n=t.checked;return H({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ra(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=ht(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Vo(e,t){t=t.checked,t!=null&&ii(e,"checked",t,!1)}function hl(e,t){Vo(e,t);var n=ht(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?pl(e,t.type,n):t.hasOwnProperty("defaultValue")&&pl(e,t.type,ht(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function sa(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function pl(e,t,n){(t!=="number"||Hr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var kn=Array.isArray;function Zt(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ht(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function vl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return H({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function la(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(N(92));if(kn(n)){if(1<n.length)throw Error(N(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:ht(n)}}function Go(e,t){var n=ht(t.value),r=ht(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ia(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Wo(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function gl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Wo(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var hr,Yo=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(hr=hr||document.createElement("div"),hr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=hr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Bn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var In={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},xd=["Webkit","ms","Moz","O"];Object.keys(In).forEach(function(e){xd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),In[t]=In[e]})});function qo(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||In.hasOwnProperty(e)&&In[e]?(""+t).trim():t+"px"}function Ko(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=qo(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Nd=H({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yl(e,t){if(t){if(Nd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function xl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Nl=null;function ui(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var jl=null,bt=null,en=null;function aa(e){if(e=ir(e)){if(typeof jl!="function")throw Error(N(280));var t=e.stateNode;t&&(t=xs(t),jl(e.stateNode,e.type,t))}}function $o(e){bt?en?en.push(e):en=[e]:bt=e}function Xo(){if(bt){var e=bt,t=en;if(en=bt=null,aa(e),t)for(e=0;e<t.length;e++)aa(t[e])}}function Jo(e,t){return e(t)}function Zo(){}var zs=!1;function bo(e,t,n){if(zs)return e(t,n);zs=!0;try{return Jo(e,t,n)}finally{zs=!1,(bt!==null||en!==null)&&(Zo(),Xo())}}function Qn(e,t){var n=e.stateNode;if(n===null)return null;var r=xs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(N(231,t,typeof n));return n}var Al=!1;if(qe)try{var yn={};Object.defineProperty(yn,"passive",{get:function(){Al=!0}}),window.addEventListener("test",yn,yn),window.removeEventListener("test",yn,yn)}catch{Al=!1}function jd(e,t,n,r,s,i,a,o,c){var f=Array.prototype.slice.call(arguments,3);try{t.apply(n,f)}catch(m){this.onError(m)}}var _n=!1,Vr=null,Gr=!1,wl=null,Ad={onError:function(e){_n=!0,Vr=e}};function wd(e,t,n,r,s,i,a,o,c){_n=!1,Vr=null,jd.apply(Ad,arguments)}function Sd(e,t,n,r,s,i,a,o,c){if(wd.apply(this,arguments),_n){if(_n){var f=Vr;_n=!1,Vr=null}else throw Error(N(198));Gr||(Gr=!0,wl=f)}}function Dt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ec(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function oa(e){if(Dt(e)!==e)throw Error(N(188))}function Cd(e){var t=e.alternate;if(!t){if(t=Dt(e),t===null)throw Error(N(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return oa(s),e;if(i===r)return oa(s),t;i=i.sibling}throw Error(N(188))}if(n.return!==r.return)n=s,r=i;else{for(var a=!1,o=s.child;o;){if(o===n){a=!0,n=s,r=i;break}if(o===r){a=!0,r=s,n=i;break}o=o.sibling}if(!a){for(o=i.child;o;){if(o===n){a=!0,n=i,r=s;break}if(o===r){a=!0,r=i,n=s;break}o=o.sibling}if(!a)throw Error(N(189))}}if(n.alternate!==r)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?e:t}function tc(e){return e=Cd(e),e!==null?nc(e):null}function nc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=nc(e);if(t!==null)return t;e=e.sibling}return null}var rc=Ne.unstable_scheduleCallback,ca=Ne.unstable_cancelCallback,Ed=Ne.unstable_shouldYield,kd=Ne.unstable_requestPaint,Y=Ne.unstable_now,Rd=Ne.unstable_getCurrentPriorityLevel,di=Ne.unstable_ImmediatePriority,sc=Ne.unstable_UserBlockingPriority,Wr=Ne.unstable_NormalPriority,Td=Ne.unstable_LowPriority,lc=Ne.unstable_IdlePriority,ps=null,Be=null;function Id(e){if(Be&&typeof Be.onCommitFiberRoot=="function")try{Be.onCommitFiberRoot(ps,e,void 0,(e.current.flags&128)===128)}catch{}}var Me=Math.clz32?Math.clz32:Od,_d=Math.log,Pd=Math.LN2;function Od(e){return e>>>=0,e===0?32:31-(_d(e)/Pd|0)|0}var pr=64,vr=4194304;function Rn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Yr(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var o=a&~s;o!==0?r=Rn(o):(i&=a,i!==0&&(r=Rn(i)))}else a=n&~s,a!==0?r=Rn(a):i!==0&&(r=Rn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Me(t),s=1<<n,r|=e[n],t&=~s;return r}function Md(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Dd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-Me(i),o=1<<a,c=s[a];c===-1?(!(o&n)||o&r)&&(s[a]=Md(o,t)):c<=t&&(e.expiredLanes|=o),i&=~o}}function Sl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ic(){var e=pr;return pr<<=1,!(pr&4194240)&&(pr=64),e}function Us(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function sr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Me(t),e[t]=n}function Ld(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Me(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function fi(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Me(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var D=0;function ac(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var oc,mi,cc,uc,dc,Cl=!1,gr=[],it=null,at=null,ot=null,Hn=new Map,Vn=new Map,nt=[],zd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ua(e,t){switch(e){case"focusin":case"focusout":it=null;break;case"dragenter":case"dragleave":at=null;break;case"mouseover":case"mouseout":ot=null;break;case"pointerover":case"pointerout":Hn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vn.delete(t.pointerId)}}function xn(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=ir(t),t!==null&&mi(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Ud(e,t,n,r,s){switch(t){case"focusin":return it=xn(it,e,t,n,r,s),!0;case"dragenter":return at=xn(at,e,t,n,r,s),!0;case"mouseover":return ot=xn(ot,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Hn.set(i,xn(Hn.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Vn.set(i,xn(Vn.get(i)||null,e,t,n,r,s)),!0}return!1}function fc(e){var t=wt(e.target);if(t!==null){var n=Dt(t);if(n!==null){if(t=n.tag,t===13){if(t=ec(n),t!==null){e.blockedOn=t,dc(e.priority,function(){cc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ir(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=El(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Nl=r,n.target.dispatchEvent(r),Nl=null}else return t=ir(n),t!==null&&mi(t),e.blockedOn=n,!1;t.shift()}return!0}function da(e,t,n){Ir(e)&&n.delete(t)}function Fd(){Cl=!1,it!==null&&Ir(it)&&(it=null),at!==null&&Ir(at)&&(at=null),ot!==null&&Ir(ot)&&(ot=null),Hn.forEach(da),Vn.forEach(da)}function Nn(e,t){e.blockedOn===t&&(e.blockedOn=null,Cl||(Cl=!0,Ne.unstable_scheduleCallback(Ne.unstable_NormalPriority,Fd)))}function Gn(e){function t(s){return Nn(s,e)}if(0<gr.length){Nn(gr[0],e);for(var n=1;n<gr.length;n++){var r=gr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(it!==null&&Nn(it,e),at!==null&&Nn(at,e),ot!==null&&Nn(ot,e),Hn.forEach(t),Vn.forEach(t),n=0;n<nt.length;n++)r=nt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<nt.length&&(n=nt[0],n.blockedOn===null);)fc(n),n.blockedOn===null&&nt.shift()}var tn=Je.ReactCurrentBatchConfig,qr=!0;function Bd(e,t,n,r){var s=D,i=tn.transition;tn.transition=null;try{D=1,hi(e,t,n,r)}finally{D=s,tn.transition=i}}function Qd(e,t,n,r){var s=D,i=tn.transition;tn.transition=null;try{D=4,hi(e,t,n,r)}finally{D=s,tn.transition=i}}function hi(e,t,n,r){if(qr){var s=El(e,t,n,r);if(s===null)Ks(e,t,r,Kr,n),ua(e,r);else if(Ud(s,e,t,n,r))r.stopPropagation();else if(ua(e,r),t&4&&-1<zd.indexOf(e)){for(;s!==null;){var i=ir(s);if(i!==null&&oc(i),i=El(e,t,n,r),i===null&&Ks(e,t,r,Kr,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else Ks(e,t,r,null,n)}}var Kr=null;function El(e,t,n,r){if(Kr=null,e=ui(r),e=wt(e),e!==null)if(t=Dt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ec(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kr=e,null}function mc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Rd()){case di:return 1;case sc:return 4;case Wr:case Td:return 16;case lc:return 536870912;default:return 16}default:return 16}}var st=null,pi=null,_r=null;function hc(){if(_r)return _r;var e,t=pi,n=t.length,r,s="value"in st?st.value:st.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===s[i-r];r++);return _r=s.slice(e,1<r?1-r:void 0)}function Pr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function yr(){return!0}function fa(){return!1}function Ae(e){function t(n,r,s,i,a){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(i):i[o]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?yr:fa,this.isPropagationStopped=fa,this}return H(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=yr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=yr)},persist:function(){},isPersistent:yr}),t}var mn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vi=Ae(mn),lr=H({},mn,{view:0,detail:0}),Hd=Ae(lr),Fs,Bs,jn,vs=H({},lr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==jn&&(jn&&e.type==="mousemove"?(Fs=e.screenX-jn.screenX,Bs=e.screenY-jn.screenY):Bs=Fs=0,jn=e),Fs)},movementY:function(e){return"movementY"in e?e.movementY:Bs}}),ma=Ae(vs),Vd=H({},vs,{dataTransfer:0}),Gd=Ae(Vd),Wd=H({},lr,{relatedTarget:0}),Qs=Ae(Wd),Yd=H({},mn,{animationName:0,elapsedTime:0,pseudoElement:0}),qd=Ae(Yd),Kd=H({},mn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$d=Ae(Kd),Xd=H({},mn,{data:0}),ha=Ae(Xd),Jd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zd={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},bd={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ef(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=bd[e])?!!t[e]:!1}function gi(){return ef}var tf=H({},lr,{key:function(e){if(e.key){var t=Jd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Pr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zd[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gi,charCode:function(e){return e.type==="keypress"?Pr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Pr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),nf=Ae(tf),rf=H({},vs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pa=Ae(rf),sf=H({},lr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gi}),lf=Ae(sf),af=H({},mn,{propertyName:0,elapsedTime:0,pseudoElement:0}),of=Ae(af),cf=H({},vs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),uf=Ae(cf),df=[9,13,27,32],yi=qe&&"CompositionEvent"in window,Pn=null;qe&&"documentMode"in document&&(Pn=document.documentMode);var ff=qe&&"TextEvent"in window&&!Pn,pc=qe&&(!yi||Pn&&8<Pn&&11>=Pn),va=" ",ga=!1;function vc(e,t){switch(e){case"keyup":return df.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function gc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Bt=!1;function mf(e,t){switch(e){case"compositionend":return gc(t);case"keypress":return t.which!==32?null:(ga=!0,va);case"textInput":return e=t.data,e===va&&ga?null:e;default:return null}}function hf(e,t){if(Bt)return e==="compositionend"||!yi&&vc(e,t)?(e=hc(),_r=pi=st=null,Bt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return pc&&t.locale!=="ko"?null:t.data;default:return null}}var pf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ya(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!pf[e.type]:t==="textarea"}function yc(e,t,n,r){$o(r),t=$r(t,"onChange"),0<t.length&&(n=new vi("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var On=null,Wn=null;function vf(e){Tc(e,0)}function gs(e){var t=Vt(e);if(Ho(t))return e}function gf(e,t){if(e==="change")return t}var xc=!1;if(qe){var Hs;if(qe){var Vs="oninput"in document;if(!Vs){var xa=document.createElement("div");xa.setAttribute("oninput","return;"),Vs=typeof xa.oninput=="function"}Hs=Vs}else Hs=!1;xc=Hs&&(!document.documentMode||9<document.documentMode)}function Na(){On&&(On.detachEvent("onpropertychange",Nc),Wn=On=null)}function Nc(e){if(e.propertyName==="value"&&gs(Wn)){var t=[];yc(t,Wn,e,ui(e)),bo(vf,t)}}function yf(e,t,n){e==="focusin"?(Na(),On=t,Wn=n,On.attachEvent("onpropertychange",Nc)):e==="focusout"&&Na()}function xf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return gs(Wn)}function Nf(e,t){if(e==="click")return gs(t)}function jf(e,t){if(e==="input"||e==="change")return gs(t)}function Af(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Le=typeof Object.is=="function"?Object.is:Af;function Yn(e,t){if(Le(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!ol.call(t,s)||!Le(e[s],t[s]))return!1}return!0}function ja(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Aa(e,t){var n=ja(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ja(n)}}function jc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?jc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ac(){for(var e=window,t=Hr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Hr(e.document)}return t}function xi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function wf(e){var t=Ac(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jc(n.ownerDocument.documentElement,n)){if(r!==null&&xi(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=Aa(n,i);var a=Aa(n,r);s&&a&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Sf=qe&&"documentMode"in document&&11>=document.documentMode,Qt=null,kl=null,Mn=null,Rl=!1;function wa(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Rl||Qt==null||Qt!==Hr(r)||(r=Qt,"selectionStart"in r&&xi(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Mn&&Yn(Mn,r)||(Mn=r,r=$r(kl,"onSelect"),0<r.length&&(t=new vi("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Qt)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ht={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},Gs={},wc={};qe&&(wc=document.createElement("div").style,"AnimationEvent"in window||(delete Ht.animationend.animation,delete Ht.animationiteration.animation,delete Ht.animationstart.animation),"TransitionEvent"in window||delete Ht.transitionend.transition);function ys(e){if(Gs[e])return Gs[e];if(!Ht[e])return e;var t=Ht[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wc)return Gs[e]=t[n];return e}var Sc=ys("animationend"),Cc=ys("animationiteration"),Ec=ys("animationstart"),kc=ys("transitionend"),Rc=new Map,Sa="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function vt(e,t){Rc.set(e,t),Mt(t,[e])}for(var Ws=0;Ws<Sa.length;Ws++){var Ys=Sa[Ws],Cf=Ys.toLowerCase(),Ef=Ys[0].toUpperCase()+Ys.slice(1);vt(Cf,"on"+Ef)}vt(Sc,"onAnimationEnd");vt(Cc,"onAnimationIteration");vt(Ec,"onAnimationStart");vt("dblclick","onDoubleClick");vt("focusin","onFocus");vt("focusout","onBlur");vt(kc,"onTransitionEnd");sn("onMouseEnter",["mouseout","mouseover"]);sn("onMouseLeave",["mouseout","mouseover"]);sn("onPointerEnter",["pointerout","pointerover"]);sn("onPointerLeave",["pointerout","pointerover"]);Mt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Mt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Mt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Mt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Mt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Mt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Tn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),kf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Tn));function Ca(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Sd(r,t,void 0,e),e.currentTarget=null}function Tc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var o=r[a],c=o.instance,f=o.currentTarget;if(o=o.listener,c!==i&&s.isPropagationStopped())break e;Ca(s,o,f),i=c}else for(a=0;a<r.length;a++){if(o=r[a],c=o.instance,f=o.currentTarget,o=o.listener,c!==i&&s.isPropagationStopped())break e;Ca(s,o,f),i=c}}}if(Gr)throw e=wl,Gr=!1,wl=null,e}function z(e,t){var n=t[Ol];n===void 0&&(n=t[Ol]=new Set);var r=e+"__bubble";n.has(r)||(Ic(t,e,2,!1),n.add(r))}function qs(e,t,n){var r=0;t&&(r|=4),Ic(n,e,r,t)}var Nr="_reactListening"+Math.random().toString(36).slice(2);function qn(e){if(!e[Nr]){e[Nr]=!0,zo.forEach(function(n){n!=="selectionchange"&&(kf.has(n)||qs(n,!1,e),qs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Nr]||(t[Nr]=!0,qs("selectionchange",!1,t))}}function Ic(e,t,n,r){switch(mc(t)){case 1:var s=Bd;break;case 4:s=Qd;break;default:s=hi}n=s.bind(null,t,n,e),s=void 0,!Al||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Ks(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var o=r.stateNode.containerInfo;if(o===s||o.nodeType===8&&o.parentNode===s)break;if(a===4)for(a=r.return;a!==null;){var c=a.tag;if((c===3||c===4)&&(c=a.stateNode.containerInfo,c===s||c.nodeType===8&&c.parentNode===s))return;a=a.return}for(;o!==null;){if(a=wt(o),a===null)return;if(c=a.tag,c===5||c===6){r=i=a;continue e}o=o.parentNode}}r=r.return}bo(function(){var f=i,m=ui(n),u=[];e:{var d=Rc.get(e);if(d!==void 0){var g=vi,y=e;switch(e){case"keypress":if(Pr(n)===0)break e;case"keydown":case"keyup":g=nf;break;case"focusin":y="focus",g=Qs;break;case"focusout":y="blur",g=Qs;break;case"beforeblur":case"afterblur":g=Qs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=ma;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Gd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=lf;break;case Sc:case Cc:case Ec:g=qd;break;case kc:g=of;break;case"scroll":g=Hd;break;case"wheel":g=uf;break;case"copy":case"cut":case"paste":g=$d;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=pa}var j=(t&4)!==0,M=!j&&e==="scroll",p=j?d!==null?d+"Capture":null:d;j=[];for(var h=f,v;h!==null;){v=h;var x=v.stateNode;if(v.tag===5&&x!==null&&(v=x,p!==null&&(x=Qn(h,p),x!=null&&j.push(Kn(h,x,v)))),M)break;h=h.return}0<j.length&&(d=new g(d,y,null,n,m),u.push({event:d,listeners:j}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",d&&n!==Nl&&(y=n.relatedTarget||n.fromElement)&&(wt(y)||y[Ke]))break e;if((g||d)&&(d=m.window===m?m:(d=m.ownerDocument)?d.defaultView||d.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=f,y=y?wt(y):null,y!==null&&(M=Dt(y),y!==M||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=f),g!==y)){if(j=ma,x="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(j=pa,x="onPointerLeave",p="onPointerEnter",h="pointer"),M=g==null?d:Vt(g),v=y==null?d:Vt(y),d=new j(x,h+"leave",g,n,m),d.target=M,d.relatedTarget=v,x=null,wt(m)===f&&(j=new j(p,h+"enter",y,n,m),j.target=v,j.relatedTarget=M,x=j),M=x,g&&y)t:{for(j=g,p=y,h=0,v=j;v;v=Lt(v))h++;for(v=0,x=p;x;x=Lt(x))v++;for(;0<h-v;)j=Lt(j),h--;for(;0<v-h;)p=Lt(p),v--;for(;h--;){if(j===p||p!==null&&j===p.alternate)break t;j=Lt(j),p=Lt(p)}j=null}else j=null;g!==null&&Ea(u,d,g,j,!1),y!==null&&M!==null&&Ea(u,M,y,j,!0)}}e:{if(d=f?Vt(f):window,g=d.nodeName&&d.nodeName.toLowerCase(),g==="select"||g==="input"&&d.type==="file")var C=gf;else if(ya(d))if(xc)C=jf;else{C=xf;var k=yf}else(g=d.nodeName)&&g.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=Nf);if(C&&(C=C(e,f))){yc(u,C,n,m);break e}k&&k(e,d,f),e==="focusout"&&(k=d._wrapperState)&&k.controlled&&d.type==="number"&&pl(d,"number",d.value)}switch(k=f?Vt(f):window,e){case"focusin":(ya(k)||k.contentEditable==="true")&&(Qt=k,kl=f,Mn=null);break;case"focusout":Mn=kl=Qt=null;break;case"mousedown":Rl=!0;break;case"contextmenu":case"mouseup":case"dragend":Rl=!1,wa(u,n,m);break;case"selectionchange":if(Sf)break;case"keydown":case"keyup":wa(u,n,m)}var w;if(yi)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else Bt?vc(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(pc&&n.locale!=="ko"&&(Bt||R!=="onCompositionStart"?R==="onCompositionEnd"&&Bt&&(w=hc()):(st=m,pi="value"in st?st.value:st.textContent,Bt=!0)),k=$r(f,R),0<k.length&&(R=new ha(R,e,null,n,m),u.push({event:R,listeners:k}),w?R.data=w:(w=gc(n),w!==null&&(R.data=w)))),(w=ff?mf(e,n):hf(e,n))&&(f=$r(f,"onBeforeInput"),0<f.length&&(m=new ha("onBeforeInput","beforeinput",null,n,m),u.push({event:m,listeners:f}),m.data=w))}Tc(u,t)})}function Kn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $r(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Qn(e,n),i!=null&&r.unshift(Kn(e,i,s)),i=Qn(e,t),i!=null&&r.push(Kn(e,i,s))),e=e.return}return r}function Lt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ea(e,t,n,r,s){for(var i=t._reactName,a=[];n!==null&&n!==r;){var o=n,c=o.alternate,f=o.stateNode;if(c!==null&&c===r)break;o.tag===5&&f!==null&&(o=f,s?(c=Qn(n,i),c!=null&&a.unshift(Kn(n,c,o))):s||(c=Qn(n,i),c!=null&&a.push(Kn(n,c,o)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Rf=/\r\n?/g,Tf=/\u0000|\uFFFD/g;function ka(e){return(typeof e=="string"?e:""+e).replace(Rf,`
`).replace(Tf,"")}function jr(e,t,n){if(t=ka(t),ka(e)!==t&&n)throw Error(N(425))}function Xr(){}var Tl=null,Il=null;function _l(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Pl=typeof setTimeout=="function"?setTimeout:void 0,If=typeof clearTimeout=="function"?clearTimeout:void 0,Ra=typeof Promise=="function"?Promise:void 0,_f=typeof queueMicrotask=="function"?queueMicrotask:typeof Ra<"u"?function(e){return Ra.resolve(null).then(e).catch(Pf)}:Pl;function Pf(e){setTimeout(function(){throw e})}function $s(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Gn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Gn(t)}function ct(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ta(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var hn=Math.random().toString(36).slice(2),Fe="__reactFiber$"+hn,$n="__reactProps$"+hn,Ke="__reactContainer$"+hn,Ol="__reactEvents$"+hn,Of="__reactListeners$"+hn,Mf="__reactHandles$"+hn;function wt(e){var t=e[Fe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ke]||n[Fe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ta(e);e!==null;){if(n=e[Fe])return n;e=Ta(e)}return t}e=n,n=e.parentNode}return null}function ir(e){return e=e[Fe]||e[Ke],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Vt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function xs(e){return e[$n]||null}var Ml=[],Gt=-1;function gt(e){return{current:e}}function U(e){0>Gt||(e.current=Ml[Gt],Ml[Gt]=null,Gt--)}function L(e,t){Gt++,Ml[Gt]=e.current,e.current=t}var pt={},le=gt(pt),he=gt(!1),Rt=pt;function ln(e,t){var n=e.type.contextTypes;if(!n)return pt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function pe(e){return e=e.childContextTypes,e!=null}function Jr(){U(he),U(le)}function Ia(e,t,n){if(le.current!==pt)throw Error(N(168));L(le,t),L(he,n)}function _c(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(N(108,gd(e)||"Unknown",s));return H({},n,r)}function Zr(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||pt,Rt=le.current,L(le,e),L(he,he.current),!0}function _a(e,t,n){var r=e.stateNode;if(!r)throw Error(N(169));n?(e=_c(e,t,Rt),r.__reactInternalMemoizedMergedChildContext=e,U(he),U(le),L(le,e)):U(he),L(he,n)}var Ve=null,Ns=!1,Xs=!1;function Pc(e){Ve===null?Ve=[e]:Ve.push(e)}function Df(e){Ns=!0,Pc(e)}function yt(){if(!Xs&&Ve!==null){Xs=!0;var e=0,t=D;try{var n=Ve;for(D=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ve=null,Ns=!1}catch(s){throw Ve!==null&&(Ve=Ve.slice(e+1)),rc(di,yt),s}finally{D=t,Xs=!1}}return null}var Wt=[],Yt=0,br=null,es=0,we=[],Se=0,Tt=null,Ge=1,We="";function jt(e,t){Wt[Yt++]=es,Wt[Yt++]=br,br=e,es=t}function Oc(e,t,n){we[Se++]=Ge,we[Se++]=We,we[Se++]=Tt,Tt=e;var r=Ge;e=We;var s=32-Me(r)-1;r&=~(1<<s),n+=1;var i=32-Me(t)+s;if(30<i){var a=s-s%5;i=(r&(1<<a)-1).toString(32),r>>=a,s-=a,Ge=1<<32-Me(t)+s|n<<s|r,We=i+e}else Ge=1<<i|n<<s|r,We=e}function Ni(e){e.return!==null&&(jt(e,1),Oc(e,1,0))}function ji(e){for(;e===br;)br=Wt[--Yt],Wt[Yt]=null,es=Wt[--Yt],Wt[Yt]=null;for(;e===Tt;)Tt=we[--Se],we[Se]=null,We=we[--Se],we[Se]=null,Ge=we[--Se],we[Se]=null}var xe=null,ye=null,F=!1,Oe=null;function Mc(e,t){var n=Ce(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Pa(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xe=e,ye=ct(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xe=e,ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Tt!==null?{id:Ge,overflow:We}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ce(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,xe=e,ye=null,!0):!1;default:return!1}}function Dl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ll(e){if(F){var t=ye;if(t){var n=t;if(!Pa(e,t)){if(Dl(e))throw Error(N(418));t=ct(n.nextSibling);var r=xe;t&&Pa(e,t)?Mc(r,n):(e.flags=e.flags&-4097|2,F=!1,xe=e)}}else{if(Dl(e))throw Error(N(418));e.flags=e.flags&-4097|2,F=!1,xe=e}}}function Oa(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xe=e}function Ar(e){if(e!==xe)return!1;if(!F)return Oa(e),F=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!_l(e.type,e.memoizedProps)),t&&(t=ye)){if(Dl(e))throw Dc(),Error(N(418));for(;t;)Mc(e,t),t=ct(t.nextSibling)}if(Oa(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ye=ct(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ye=null}}else ye=xe?ct(e.stateNode.nextSibling):null;return!0}function Dc(){for(var e=ye;e;)e=ct(e.nextSibling)}function an(){ye=xe=null,F=!1}function Ai(e){Oe===null?Oe=[e]:Oe.push(e)}var Lf=Je.ReactCurrentBatchConfig;function An(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var r=n.stateNode}if(!r)throw Error(N(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var o=s.refs;a===null?delete o[i]:o[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,e))}return e}function wr(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ma(e){var t=e._init;return t(e._payload)}function Lc(e){function t(p,h){if(e){var v=p.deletions;v===null?(p.deletions=[h],p.flags|=16):v.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function s(p,h){return p=mt(p,h),p.index=0,p.sibling=null,p}function i(p,h,v){return p.index=v,e?(v=p.alternate,v!==null?(v=v.index,v<h?(p.flags|=2,h):v):(p.flags|=2,h)):(p.flags|=1048576,h)}function a(p){return e&&p.alternate===null&&(p.flags|=2),p}function o(p,h,v,x){return h===null||h.tag!==6?(h=rl(v,p.mode,x),h.return=p,h):(h=s(h,v),h.return=p,h)}function c(p,h,v,x){var C=v.type;return C===Ft?m(p,h,v.props.children,x,v.key):h!==null&&(h.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===be&&Ma(C)===h.type)?(x=s(h,v.props),x.ref=An(p,h,v),x.return=p,x):(x=Fr(v.type,v.key,v.props,null,p.mode,x),x.ref=An(p,h,v),x.return=p,x)}function f(p,h,v,x){return h===null||h.tag!==4||h.stateNode.containerInfo!==v.containerInfo||h.stateNode.implementation!==v.implementation?(h=sl(v,p.mode,x),h.return=p,h):(h=s(h,v.children||[]),h.return=p,h)}function m(p,h,v,x,C){return h===null||h.tag!==7?(h=kt(v,p.mode,x,C),h.return=p,h):(h=s(h,v),h.return=p,h)}function u(p,h,v){if(typeof h=="string"&&h!==""||typeof h=="number")return h=rl(""+h,p.mode,v),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case fr:return v=Fr(h.type,h.key,h.props,null,p.mode,v),v.ref=An(p,null,h),v.return=p,v;case Ut:return h=sl(h,p.mode,v),h.return=p,h;case be:var x=h._init;return u(p,x(h._payload),v)}if(kn(h)||gn(h))return h=kt(h,p.mode,v,null),h.return=p,h;wr(p,h)}return null}function d(p,h,v,x){var C=h!==null?h.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return C!==null?null:o(p,h,""+v,x);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case fr:return v.key===C?c(p,h,v,x):null;case Ut:return v.key===C?f(p,h,v,x):null;case be:return C=v._init,d(p,h,C(v._payload),x)}if(kn(v)||gn(v))return C!==null?null:m(p,h,v,x,null);wr(p,v)}return null}function g(p,h,v,x,C){if(typeof x=="string"&&x!==""||typeof x=="number")return p=p.get(v)||null,o(h,p,""+x,C);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case fr:return p=p.get(x.key===null?v:x.key)||null,c(h,p,x,C);case Ut:return p=p.get(x.key===null?v:x.key)||null,f(h,p,x,C);case be:var k=x._init;return g(p,h,v,k(x._payload),C)}if(kn(x)||gn(x))return p=p.get(v)||null,m(h,p,x,C,null);wr(h,x)}return null}function y(p,h,v,x){for(var C=null,k=null,w=h,R=h=0,G=null;w!==null&&R<v.length;R++){w.index>R?(G=w,w=null):G=w.sibling;var P=d(p,w,v[R],x);if(P===null){w===null&&(w=G);break}e&&w&&P.alternate===null&&t(p,w),h=i(P,h,R),k===null?C=P:k.sibling=P,k=P,w=G}if(R===v.length)return n(p,w),F&&jt(p,R),C;if(w===null){for(;R<v.length;R++)w=u(p,v[R],x),w!==null&&(h=i(w,h,R),k===null?C=w:k.sibling=w,k=w);return F&&jt(p,R),C}for(w=r(p,w);R<v.length;R++)G=g(w,p,R,v[R],x),G!==null&&(e&&G.alternate!==null&&w.delete(G.key===null?R:G.key),h=i(G,h,R),k===null?C=G:k.sibling=G,k=G);return e&&w.forEach(function(Te){return t(p,Te)}),F&&jt(p,R),C}function j(p,h,v,x){var C=gn(v);if(typeof C!="function")throw Error(N(150));if(v=C.call(v),v==null)throw Error(N(151));for(var k=C=null,w=h,R=h=0,G=null,P=v.next();w!==null&&!P.done;R++,P=v.next()){w.index>R?(G=w,w=null):G=w.sibling;var Te=d(p,w,P.value,x);if(Te===null){w===null&&(w=G);break}e&&w&&Te.alternate===null&&t(p,w),h=i(Te,h,R),k===null?C=Te:k.sibling=Te,k=Te,w=G}if(P.done)return n(p,w),F&&jt(p,R),C;if(w===null){for(;!P.done;R++,P=v.next())P=u(p,P.value,x),P!==null&&(h=i(P,h,R),k===null?C=P:k.sibling=P,k=P);return F&&jt(p,R),C}for(w=r(p,w);!P.done;R++,P=v.next())P=g(w,p,R,P.value,x),P!==null&&(e&&P.alternate!==null&&w.delete(P.key===null?R:P.key),h=i(P,h,R),k===null?C=P:k.sibling=P,k=P);return e&&w.forEach(function(pn){return t(p,pn)}),F&&jt(p,R),C}function M(p,h,v,x){if(typeof v=="object"&&v!==null&&v.type===Ft&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case fr:e:{for(var C=v.key,k=h;k!==null;){if(k.key===C){if(C=v.type,C===Ft){if(k.tag===7){n(p,k.sibling),h=s(k,v.props.children),h.return=p,p=h;break e}}else if(k.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===be&&Ma(C)===k.type){n(p,k.sibling),h=s(k,v.props),h.ref=An(p,k,v),h.return=p,p=h;break e}n(p,k);break}else t(p,k);k=k.sibling}v.type===Ft?(h=kt(v.props.children,p.mode,x,v.key),h.return=p,p=h):(x=Fr(v.type,v.key,v.props,null,p.mode,x),x.ref=An(p,h,v),x.return=p,p=x)}return a(p);case Ut:e:{for(k=v.key;h!==null;){if(h.key===k)if(h.tag===4&&h.stateNode.containerInfo===v.containerInfo&&h.stateNode.implementation===v.implementation){n(p,h.sibling),h=s(h,v.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=sl(v,p.mode,x),h.return=p,p=h}return a(p);case be:return k=v._init,M(p,h,k(v._payload),x)}if(kn(v))return y(p,h,v,x);if(gn(v))return j(p,h,v,x);wr(p,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,h!==null&&h.tag===6?(n(p,h.sibling),h=s(h,v),h.return=p,p=h):(n(p,h),h=rl(v,p.mode,x),h.return=p,p=h),a(p)):n(p,h)}return M}var on=Lc(!0),zc=Lc(!1),ts=gt(null),ns=null,qt=null,wi=null;function Si(){wi=qt=ns=null}function Ci(e){var t=ts.current;U(ts),e._currentValue=t}function zl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function nn(e,t){ns=e,wi=qt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(me=!0),e.firstContext=null)}function ke(e){var t=e._currentValue;if(wi!==e)if(e={context:e,memoizedValue:t,next:null},qt===null){if(ns===null)throw Error(N(308));qt=e,ns.dependencies={lanes:0,firstContext:e}}else qt=qt.next=e;return t}var St=null;function Ei(e){St===null?St=[e]:St.push(e)}function Uc(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Ei(t)):(n.next=s.next,s.next=n),t.interleaved=n,$e(e,r)}function $e(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var et=!1;function ki(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Fc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ye(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ut(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,O&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,$e(e,n)}return s=r.interleaved,s===null?(t.next=t,Ei(r)):(t.next=s.next,s.next=t),r.interleaved=t,$e(e,n)}function Or(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fi(e,n)}}function Da(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function rs(e,t,n,r){var s=e.updateQueue;et=!1;var i=s.firstBaseUpdate,a=s.lastBaseUpdate,o=s.shared.pending;if(o!==null){s.shared.pending=null;var c=o,f=c.next;c.next=null,a===null?i=f:a.next=f,a=c;var m=e.alternate;m!==null&&(m=m.updateQueue,o=m.lastBaseUpdate,o!==a&&(o===null?m.firstBaseUpdate=f:o.next=f,m.lastBaseUpdate=c))}if(i!==null){var u=s.baseState;a=0,m=f=c=null,o=i;do{var d=o.lane,g=o.eventTime;if((r&d)===d){m!==null&&(m=m.next={eventTime:g,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var y=e,j=o;switch(d=t,g=n,j.tag){case 1:if(y=j.payload,typeof y=="function"){u=y.call(g,u,d);break e}u=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=j.payload,d=typeof y=="function"?y.call(g,u,d):y,d==null)break e;u=H({},u,d);break e;case 2:et=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,d=s.effects,d===null?s.effects=[o]:d.push(o))}else g={eventTime:g,lane:d,tag:o.tag,payload:o.payload,callback:o.callback,next:null},m===null?(f=m=g,c=u):m=m.next=g,a|=d;if(o=o.next,o===null){if(o=s.shared.pending,o===null)break;d=o,o=d.next,d.next=null,s.lastBaseUpdate=d,s.shared.pending=null}}while(!0);if(m===null&&(c=u),s.baseState=c,s.firstBaseUpdate=f,s.lastBaseUpdate=m,t=s.shared.interleaved,t!==null){s=t;do a|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);_t|=a,e.lanes=a,e.memoizedState=u}}function La(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(N(191,s));s.call(r)}}}var ar={},Qe=gt(ar),Xn=gt(ar),Jn=gt(ar);function Ct(e){if(e===ar)throw Error(N(174));return e}function Ri(e,t){switch(L(Jn,t),L(Xn,e),L(Qe,ar),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:gl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=gl(t,e)}U(Qe),L(Qe,t)}function cn(){U(Qe),U(Xn),U(Jn)}function Bc(e){Ct(Jn.current);var t=Ct(Qe.current),n=gl(t,e.type);t!==n&&(L(Xn,e),L(Qe,n))}function Ti(e){Xn.current===e&&(U(Qe),U(Xn))}var B=gt(0);function ss(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Js=[];function Ii(){for(var e=0;e<Js.length;e++)Js[e]._workInProgressVersionPrimary=null;Js.length=0}var Mr=Je.ReactCurrentDispatcher,Zs=Je.ReactCurrentBatchConfig,It=0,Q=null,K=null,J=null,ls=!1,Dn=!1,Zn=0,zf=0;function ne(){throw Error(N(321))}function _i(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Le(e[n],t[n]))return!1;return!0}function Pi(e,t,n,r,s,i){if(It=i,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Mr.current=e===null||e.memoizedState===null?Qf:Hf,e=n(r,s),Dn){i=0;do{if(Dn=!1,Zn=0,25<=i)throw Error(N(301));i+=1,J=K=null,t.updateQueue=null,Mr.current=Vf,e=n(r,s)}while(Dn)}if(Mr.current=is,t=K!==null&&K.next!==null,It=0,J=K=Q=null,ls=!1,t)throw Error(N(300));return e}function Oi(){var e=Zn!==0;return Zn=0,e}function Ue(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return J===null?Q.memoizedState=J=e:J=J.next=e,J}function Re(){if(K===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=K.next;var t=J===null?Q.memoizedState:J.next;if(t!==null)J=t,K=e;else{if(e===null)throw Error(N(310));K=e,e={memoizedState:K.memoizedState,baseState:K.baseState,baseQueue:K.baseQueue,queue:K.queue,next:null},J===null?Q.memoizedState=J=e:J=J.next=e}return J}function bn(e,t){return typeof t=="function"?t(e):t}function bs(e){var t=Re(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=K,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var a=s.next;s.next=i.next,i.next=a}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var o=a=null,c=null,f=i;do{var m=f.lane;if((It&m)===m)c!==null&&(c=c.next={lane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),r=f.hasEagerState?f.eagerState:e(r,f.action);else{var u={lane:m,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null};c===null?(o=c=u,a=r):c=c.next=u,Q.lanes|=m,_t|=m}f=f.next}while(f!==null&&f!==i);c===null?a=r:c.next=o,Le(r,t.memoizedState)||(me=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,Q.lanes|=i,_t|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function el(e){var t=Re(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var a=s=s.next;do i=e(i,a.action),a=a.next;while(a!==s);Le(i,t.memoizedState)||(me=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Qc(){}function Hc(e,t){var n=Q,r=Re(),s=t(),i=!Le(r.memoizedState,s);if(i&&(r.memoizedState=s,me=!0),r=r.queue,Mi(Wc.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||J!==null&&J.memoizedState.tag&1){if(n.flags|=2048,er(9,Gc.bind(null,n,r,s,t),void 0,null),Z===null)throw Error(N(349));It&30||Vc(n,t,s)}return s}function Vc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Gc(e,t,n,r){t.value=n,t.getSnapshot=r,Yc(t)&&qc(e)}function Wc(e,t,n){return n(function(){Yc(t)&&qc(e)})}function Yc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Le(e,n)}catch{return!0}}function qc(e){var t=$e(e,1);t!==null&&De(t,e,1,-1)}function za(e){var t=Ue();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:bn,lastRenderedState:e},t.queue=e,e=e.dispatch=Bf.bind(null,Q,e),[t.memoizedState,e]}function er(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Kc(){return Re().memoizedState}function Dr(e,t,n,r){var s=Ue();Q.flags|=e,s.memoizedState=er(1|t,n,void 0,r===void 0?null:r)}function js(e,t,n,r){var s=Re();r=r===void 0?null:r;var i=void 0;if(K!==null){var a=K.memoizedState;if(i=a.destroy,r!==null&&_i(r,a.deps)){s.memoizedState=er(t,n,i,r);return}}Q.flags|=e,s.memoizedState=er(1|t,n,i,r)}function Ua(e,t){return Dr(8390656,8,e,t)}function Mi(e,t){return js(2048,8,e,t)}function $c(e,t){return js(4,2,e,t)}function Xc(e,t){return js(4,4,e,t)}function Jc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Zc(e,t,n){return n=n!=null?n.concat([e]):null,js(4,4,Jc.bind(null,t,e),n)}function Di(){}function bc(e,t){var n=Re();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&_i(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function eu(e,t){var n=Re();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&_i(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function tu(e,t,n){return It&21?(Le(n,t)||(n=ic(),Q.lanes|=n,_t|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,me=!0),e.memoizedState=n)}function Uf(e,t){var n=D;D=n!==0&&4>n?n:4,e(!0);var r=Zs.transition;Zs.transition={};try{e(!1),t()}finally{D=n,Zs.transition=r}}function nu(){return Re().memoizedState}function Ff(e,t,n){var r=ft(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},ru(e))su(t,n);else if(n=Uc(e,t,n,r),n!==null){var s=ae();De(n,e,r,s),lu(n,t,r)}}function Bf(e,t,n){var r=ft(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(ru(e))su(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,o=i(a,n);if(s.hasEagerState=!0,s.eagerState=o,Le(o,a)){var c=t.interleaved;c===null?(s.next=s,Ei(t)):(s.next=c.next,c.next=s),t.interleaved=s;return}}catch{}finally{}n=Uc(e,t,s,r),n!==null&&(s=ae(),De(n,e,r,s),lu(n,t,r))}}function ru(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function su(e,t){Dn=ls=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function lu(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fi(e,n)}}var is={readContext:ke,useCallback:ne,useContext:ne,useEffect:ne,useImperativeHandle:ne,useInsertionEffect:ne,useLayoutEffect:ne,useMemo:ne,useReducer:ne,useRef:ne,useState:ne,useDebugValue:ne,useDeferredValue:ne,useTransition:ne,useMutableSource:ne,useSyncExternalStore:ne,useId:ne,unstable_isNewReconciler:!1},Qf={readContext:ke,useCallback:function(e,t){return Ue().memoizedState=[e,t===void 0?null:t],e},useContext:ke,useEffect:Ua,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Dr(4194308,4,Jc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Dr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Dr(4,2,e,t)},useMemo:function(e,t){var n=Ue();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ue();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ff.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=Ue();return e={current:e},t.memoizedState=e},useState:za,useDebugValue:Di,useDeferredValue:function(e){return Ue().memoizedState=e},useTransition:function(){var e=za(!1),t=e[0];return e=Uf.bind(null,e[1]),Ue().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,s=Ue();if(F){if(n===void 0)throw Error(N(407));n=n()}else{if(n=t(),Z===null)throw Error(N(349));It&30||Vc(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Ua(Wc.bind(null,r,i,e),[e]),r.flags|=2048,er(9,Gc.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ue(),t=Z.identifierPrefix;if(F){var n=We,r=Ge;n=(r&~(1<<32-Me(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Zn++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=zf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Hf={readContext:ke,useCallback:bc,useContext:ke,useEffect:Mi,useImperativeHandle:Zc,useInsertionEffect:$c,useLayoutEffect:Xc,useMemo:eu,useReducer:bs,useRef:Kc,useState:function(){return bs(bn)},useDebugValue:Di,useDeferredValue:function(e){var t=Re();return tu(t,K.memoizedState,e)},useTransition:function(){var e=bs(bn)[0],t=Re().memoizedState;return[e,t]},useMutableSource:Qc,useSyncExternalStore:Hc,useId:nu,unstable_isNewReconciler:!1},Vf={readContext:ke,useCallback:bc,useContext:ke,useEffect:Mi,useImperativeHandle:Zc,useInsertionEffect:$c,useLayoutEffect:Xc,useMemo:eu,useReducer:el,useRef:Kc,useState:function(){return el(bn)},useDebugValue:Di,useDeferredValue:function(e){var t=Re();return K===null?t.memoizedState=e:tu(t,K.memoizedState,e)},useTransition:function(){var e=el(bn)[0],t=Re().memoizedState;return[e,t]},useMutableSource:Qc,useSyncExternalStore:Hc,useId:nu,unstable_isNewReconciler:!1};function _e(e,t){if(e&&e.defaultProps){t=H({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ul(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:H({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var As={isMounted:function(e){return(e=e._reactInternals)?Dt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ae(),s=ft(e),i=Ye(r,s);i.payload=t,n!=null&&(i.callback=n),t=ut(e,i,s),t!==null&&(De(t,e,s,r),Or(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ae(),s=ft(e),i=Ye(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=ut(e,i,s),t!==null&&(De(t,e,s,r),Or(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ae(),r=ft(e),s=Ye(n,r);s.tag=2,t!=null&&(s.callback=t),t=ut(e,s,r),t!==null&&(De(t,e,r,n),Or(t,e,r))}};function Fa(e,t,n,r,s,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!Yn(n,r)||!Yn(s,i):!0}function iu(e,t,n){var r=!1,s=pt,i=t.contextType;return typeof i=="object"&&i!==null?i=ke(i):(s=pe(t)?Rt:le.current,r=t.contextTypes,i=(r=r!=null)?ln(e,s):pt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=As,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Ba(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&As.enqueueReplaceState(t,t.state,null)}function Fl(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},ki(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=ke(i):(i=pe(t)?Rt:le.current,s.context=ln(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ul(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&As.enqueueReplaceState(s,s.state,null),rs(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function un(e,t){try{var n="",r=t;do n+=vd(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function tl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Bl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Gf=typeof WeakMap=="function"?WeakMap:Map;function au(e,t,n){n=Ye(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){os||(os=!0,Xl=r),Bl(e,t)},n}function ou(e,t,n){n=Ye(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){Bl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Bl(e,t),typeof r!="function"&&(dt===null?dt=new Set([this]):dt.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Qa(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Gf;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=sm.bind(null,e,t,n),t.then(e,e))}function Ha(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Va(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ye(-1,1),t.tag=2,ut(n,t,1))),n.lanes|=1),e)}var Wf=Je.ReactCurrentOwner,me=!1;function ie(e,t,n,r){t.child=e===null?zc(t,null,n,r):on(t,e.child,n,r)}function Ga(e,t,n,r,s){n=n.render;var i=t.ref;return nn(t,s),r=Pi(e,t,n,r,i,s),n=Oi(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Xe(e,t,s)):(F&&n&&Ni(t),t.flags|=1,ie(e,t,r,s),t.child)}function Wa(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Vi(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,cu(e,t,i,r,s)):(e=Fr(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:Yn,n(a,r)&&e.ref===t.ref)return Xe(e,t,s)}return t.flags|=1,e=mt(i,r),e.ref=t.ref,e.return=t,t.child=e}function cu(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Yn(i,r)&&e.ref===t.ref)if(me=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(me=!0);else return t.lanes=e.lanes,Xe(e,t,s)}return Ql(e,t,n,r,s)}function uu(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},L($t,ge),ge|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,L($t,ge),ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,L($t,ge),ge|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,L($t,ge),ge|=r;return ie(e,t,s,n),t.child}function du(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ql(e,t,n,r,s){var i=pe(n)?Rt:le.current;return i=ln(t,i),nn(t,s),n=Pi(e,t,n,r,i,s),r=Oi(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Xe(e,t,s)):(F&&r&&Ni(t),t.flags|=1,ie(e,t,n,s),t.child)}function Ya(e,t,n,r,s){if(pe(n)){var i=!0;Zr(t)}else i=!1;if(nn(t,s),t.stateNode===null)Lr(e,t),iu(t,n,r),Fl(t,n,r,s),r=!0;else if(e===null){var a=t.stateNode,o=t.memoizedProps;a.props=o;var c=a.context,f=n.contextType;typeof f=="object"&&f!==null?f=ke(f):(f=pe(n)?Rt:le.current,f=ln(t,f));var m=n.getDerivedStateFromProps,u=typeof m=="function"||typeof a.getSnapshotBeforeUpdate=="function";u||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==r||c!==f)&&Ba(t,a,r,f),et=!1;var d=t.memoizedState;a.state=d,rs(t,r,a,s),c=t.memoizedState,o!==r||d!==c||he.current||et?(typeof m=="function"&&(Ul(t,n,m,r),c=t.memoizedState),(o=et||Fa(t,n,o,r,d,c,f))?(u||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),a.props=r,a.state=c,a.context=f,r=o):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Fc(e,t),o=t.memoizedProps,f=t.type===t.elementType?o:_e(t.type,o),a.props=f,u=t.pendingProps,d=a.context,c=n.contextType,typeof c=="object"&&c!==null?c=ke(c):(c=pe(n)?Rt:le.current,c=ln(t,c));var g=n.getDerivedStateFromProps;(m=typeof g=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==u||d!==c)&&Ba(t,a,r,c),et=!1,d=t.memoizedState,a.state=d,rs(t,r,a,s);var y=t.memoizedState;o!==u||d!==y||he.current||et?(typeof g=="function"&&(Ul(t,n,g,r),y=t.memoizedState),(f=et||Fa(t,n,f,r,d,y,c)||!1)?(m||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,y,c),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,y,c)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),a.props=r,a.state=y,a.context=c,r=f):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Hl(e,t,n,r,i,s)}function Hl(e,t,n,r,s,i){du(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return s&&_a(t,n,!1),Xe(e,t,i);r=t.stateNode,Wf.current=t;var o=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=on(t,e.child,null,i),t.child=on(t,null,o,i)):ie(e,t,o,i),t.memoizedState=r.state,s&&_a(t,n,!0),t.child}function fu(e){var t=e.stateNode;t.pendingContext?Ia(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ia(e,t.context,!1),Ri(e,t.containerInfo)}function qa(e,t,n,r,s){return an(),Ai(s),t.flags|=256,ie(e,t,n,r),t.child}var Vl={dehydrated:null,treeContext:null,retryLane:0};function Gl(e){return{baseLanes:e,cachePool:null,transitions:null}}function mu(e,t,n){var r=t.pendingProps,s=B.current,i=!1,a=(t.flags&128)!==0,o;if((o=a)||(o=e!==null&&e.memoizedState===null?!1:(s&2)!==0),o?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),L(B,s&1),e===null)return Ll(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=Cs(a,r,0,null),e=kt(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Gl(n),t.memoizedState=Vl,e):Li(t,a));if(s=e.memoizedState,s!==null&&(o=s.dehydrated,o!==null))return Yf(e,t,a,r,o,s,n);if(i){i=r.fallback,a=t.mode,s=e.child,o=s.sibling;var c={mode:"hidden",children:r.children};return!(a&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=mt(s,c),r.subtreeFlags=s.subtreeFlags&14680064),o!==null?i=mt(o,i):(i=kt(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?Gl(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=Vl,r}return i=e.child,e=i.sibling,r=mt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Li(e,t){return t=Cs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Sr(e,t,n,r){return r!==null&&Ai(r),on(t,e.child,null,n),e=Li(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Yf(e,t,n,r,s,i,a){if(n)return t.flags&256?(t.flags&=-257,r=tl(Error(N(422))),Sr(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=Cs({mode:"visible",children:r.children},s,0,null),i=kt(i,s,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&on(t,e.child,null,a),t.child.memoizedState=Gl(a),t.memoizedState=Vl,i);if(!(t.mode&1))return Sr(e,t,a,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var o=r.dgst;return r=o,i=Error(N(419)),r=tl(i,r,void 0),Sr(e,t,a,r)}if(o=(a&e.childLanes)!==0,me||o){if(r=Z,r!==null){switch(a&-a){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|a)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,$e(e,s),De(r,e,s,-1))}return Hi(),r=tl(Error(N(421))),Sr(e,t,a,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=lm.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,ye=ct(s.nextSibling),xe=t,F=!0,Oe=null,e!==null&&(we[Se++]=Ge,we[Se++]=We,we[Se++]=Tt,Ge=e.id,We=e.overflow,Tt=t),t=Li(t,r.children),t.flags|=4096,t)}function Ka(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),zl(e.return,t,n)}function nl(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function hu(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(ie(e,t,r.children,n),r=B.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ka(e,n,t);else if(e.tag===19)Ka(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(L(B,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&ss(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),nl(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&ss(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}nl(t,!0,n,null,i);break;case"together":nl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Lr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Xe(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),_t|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,n=mt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=mt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qf(e,t,n){switch(t.tag){case 3:fu(t),an();break;case 5:Bc(t);break;case 1:pe(t.type)&&Zr(t);break;case 4:Ri(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;L(ts,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(L(B,B.current&1),t.flags|=128,null):n&t.child.childLanes?mu(e,t,n):(L(B,B.current&1),e=Xe(e,t,n),e!==null?e.sibling:null);L(B,B.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return hu(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),L(B,B.current),r)break;return null;case 22:case 23:return t.lanes=0,uu(e,t,n)}return Xe(e,t,n)}var pu,Wl,vu,gu;pu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Wl=function(){};vu=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Ct(Qe.current);var i=null;switch(n){case"input":s=ml(e,s),r=ml(e,r),i=[];break;case"select":s=H({},s,{value:void 0}),r=H({},r,{value:void 0}),i=[];break;case"textarea":s=vl(e,s),r=vl(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Xr)}yl(n,r);var a;n=null;for(f in s)if(!r.hasOwnProperty(f)&&s.hasOwnProperty(f)&&s[f]!=null)if(f==="style"){var o=s[f];for(a in o)o.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else f!=="dangerouslySetInnerHTML"&&f!=="children"&&f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&f!=="autoFocus"&&(Fn.hasOwnProperty(f)?i||(i=[]):(i=i||[]).push(f,null));for(f in r){var c=r[f];if(o=s!=null?s[f]:void 0,r.hasOwnProperty(f)&&c!==o&&(c!=null||o!=null))if(f==="style")if(o){for(a in o)!o.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&o[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(i||(i=[]),i.push(f,n)),n=c;else f==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,o=o?o.__html:void 0,c!=null&&o!==c&&(i=i||[]).push(f,c)):f==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(f,""+c):f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&(Fn.hasOwnProperty(f)?(c!=null&&f==="onScroll"&&z("scroll",e),i||o===c||(i=[])):(i=i||[]).push(f,c))}n&&(i=i||[]).push("style",n);var f=i;(t.updateQueue=f)&&(t.flags|=4)}};gu=function(e,t,n,r){n!==r&&(t.flags|=4)};function wn(e,t){if(!F)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function re(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Kf(e,t,n){var r=t.pendingProps;switch(ji(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return re(t),null;case 1:return pe(t.type)&&Jr(),re(t),null;case 3:return r=t.stateNode,cn(),U(he),U(le),Ii(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ar(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Oe!==null&&(bl(Oe),Oe=null))),Wl(e,t),re(t),null;case 5:Ti(t);var s=Ct(Jn.current);if(n=t.type,e!==null&&t.stateNode!=null)vu(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(N(166));return re(t),null}if(e=Ct(Qe.current),Ar(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Fe]=t,r[$n]=i,e=(t.mode&1)!==0,n){case"dialog":z("cancel",r),z("close",r);break;case"iframe":case"object":case"embed":z("load",r);break;case"video":case"audio":for(s=0;s<Tn.length;s++)z(Tn[s],r);break;case"source":z("error",r);break;case"img":case"image":case"link":z("error",r),z("load",r);break;case"details":z("toggle",r);break;case"input":ra(r,i),z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},z("invalid",r);break;case"textarea":la(r,i),z("invalid",r)}yl(n,i),s=null;for(var a in i)if(i.hasOwnProperty(a)){var o=i[a];a==="children"?typeof o=="string"?r.textContent!==o&&(i.suppressHydrationWarning!==!0&&jr(r.textContent,o,e),s=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(i.suppressHydrationWarning!==!0&&jr(r.textContent,o,e),s=["children",""+o]):Fn.hasOwnProperty(a)&&o!=null&&a==="onScroll"&&z("scroll",r)}switch(n){case"input":mr(r),sa(r,i,!0);break;case"textarea":mr(r),ia(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Xr)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Wo(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[Fe]=t,e[$n]=r,pu(e,t,!1,!1),t.stateNode=e;e:{switch(a=xl(n,r),n){case"dialog":z("cancel",e),z("close",e),s=r;break;case"iframe":case"object":case"embed":z("load",e),s=r;break;case"video":case"audio":for(s=0;s<Tn.length;s++)z(Tn[s],e);s=r;break;case"source":z("error",e),s=r;break;case"img":case"image":case"link":z("error",e),z("load",e),s=r;break;case"details":z("toggle",e),s=r;break;case"input":ra(e,r),s=ml(e,r),z("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=H({},r,{value:void 0}),z("invalid",e);break;case"textarea":la(e,r),s=vl(e,r),z("invalid",e);break;default:s=r}yl(n,s),o=s;for(i in o)if(o.hasOwnProperty(i)){var c=o[i];i==="style"?Ko(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Yo(e,c)):i==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&Bn(e,c):typeof c=="number"&&Bn(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Fn.hasOwnProperty(i)?c!=null&&i==="onScroll"&&z("scroll",e):c!=null&&ii(e,i,c,a))}switch(n){case"input":mr(e),sa(e,r,!1);break;case"textarea":mr(e),ia(e);break;case"option":r.value!=null&&e.setAttribute("value",""+ht(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Zt(e,!!r.multiple,i,!1):r.defaultValue!=null&&Zt(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Xr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return re(t),null;case 6:if(e&&t.stateNode!=null)gu(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(N(166));if(n=Ct(Jn.current),Ct(Qe.current),Ar(t)){if(r=t.stateNode,n=t.memoizedProps,r[Fe]=t,(i=r.nodeValue!==n)&&(e=xe,e!==null))switch(e.tag){case 3:jr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&jr(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Fe]=t,t.stateNode=r}return re(t),null;case 13:if(U(B),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(F&&ye!==null&&t.mode&1&&!(t.flags&128))Dc(),an(),t.flags|=98560,i=!1;else if(i=Ar(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(N(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(N(317));i[Fe]=t}else an(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;re(t),i=!1}else Oe!==null&&(bl(Oe),Oe=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||B.current&1?$===0&&($=3):Hi())),t.updateQueue!==null&&(t.flags|=4),re(t),null);case 4:return cn(),Wl(e,t),e===null&&qn(t.stateNode.containerInfo),re(t),null;case 10:return Ci(t.type._context),re(t),null;case 17:return pe(t.type)&&Jr(),re(t),null;case 19:if(U(B),i=t.memoizedState,i===null)return re(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)wn(i,!1);else{if($!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=ss(e),a!==null){for(t.flags|=128,wn(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return L(B,B.current&1|2),t.child}e=e.sibling}i.tail!==null&&Y()>dn&&(t.flags|=128,r=!0,wn(i,!1),t.lanes=4194304)}else{if(!r)if(e=ss(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),wn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!F)return re(t),null}else 2*Y()-i.renderingStartTime>dn&&n!==1073741824&&(t.flags|=128,r=!0,wn(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Y(),t.sibling=null,n=B.current,L(B,r?n&1|2:n&1),t):(re(t),null);case 22:case 23:return Qi(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ge&1073741824&&(re(t),t.subtreeFlags&6&&(t.flags|=8192)):re(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function $f(e,t){switch(ji(t),t.tag){case 1:return pe(t.type)&&Jr(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return cn(),U(he),U(le),Ii(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ti(t),null;case 13:if(U(B),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));an()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(B),null;case 4:return cn(),null;case 10:return Ci(t.type._context),null;case 22:case 23:return Qi(),null;case 24:return null;default:return null}}var Cr=!1,se=!1,Xf=typeof WeakSet=="function"?WeakSet:Set,S=null;function Kt(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){V(e,t,r)}else n.current=null}function Yl(e,t,n){try{n()}catch(r){V(e,t,r)}}var $a=!1;function Jf(e,t){if(Tl=qr,e=Ac(),xi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,o=-1,c=-1,f=0,m=0,u=e,d=null;t:for(;;){for(var g;u!==n||s!==0&&u.nodeType!==3||(o=a+s),u!==i||r!==0&&u.nodeType!==3||(c=a+r),u.nodeType===3&&(a+=u.nodeValue.length),(g=u.firstChild)!==null;)d=u,u=g;for(;;){if(u===e)break t;if(d===n&&++f===s&&(o=a),d===i&&++m===r&&(c=a),(g=u.nextSibling)!==null)break;u=d,d=u.parentNode}u=g}n=o===-1||c===-1?null:{start:o,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Il={focusedElem:e,selectionRange:n},qr=!1,S=t;S!==null;)if(t=S,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,S=e;else for(;S!==null;){t=S;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var j=y.memoizedProps,M=y.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?j:_e(t.type,j),M);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(x){V(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,S=e;break}S=t.return}return y=$a,$a=!1,y}function Ln(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Yl(t,n,i)}s=s.next}while(s!==r)}}function ws(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ql(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function yu(e){var t=e.alternate;t!==null&&(e.alternate=null,yu(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Fe],delete t[$n],delete t[Ol],delete t[Of],delete t[Mf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function xu(e){return e.tag===5||e.tag===3||e.tag===4}function Xa(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||xu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Kl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Xr));else if(r!==4&&(e=e.child,e!==null))for(Kl(e,t,n),e=e.sibling;e!==null;)Kl(e,t,n),e=e.sibling}function $l(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for($l(e,t,n),e=e.sibling;e!==null;)$l(e,t,n),e=e.sibling}var b=null,Pe=!1;function Ze(e,t,n){for(n=n.child;n!==null;)Nu(e,t,n),n=n.sibling}function Nu(e,t,n){if(Be&&typeof Be.onCommitFiberUnmount=="function")try{Be.onCommitFiberUnmount(ps,n)}catch{}switch(n.tag){case 5:se||Kt(n,t);case 6:var r=b,s=Pe;b=null,Ze(e,t,n),b=r,Pe=s,b!==null&&(Pe?(e=b,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):b.removeChild(n.stateNode));break;case 18:b!==null&&(Pe?(e=b,n=n.stateNode,e.nodeType===8?$s(e.parentNode,n):e.nodeType===1&&$s(e,n),Gn(e)):$s(b,n.stateNode));break;case 4:r=b,s=Pe,b=n.stateNode.containerInfo,Pe=!0,Ze(e,t,n),b=r,Pe=s;break;case 0:case 11:case 14:case 15:if(!se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&Yl(n,t,a),s=s.next}while(s!==r)}Ze(e,t,n);break;case 1:if(!se&&(Kt(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){V(n,t,o)}Ze(e,t,n);break;case 21:Ze(e,t,n);break;case 22:n.mode&1?(se=(r=se)||n.memoizedState!==null,Ze(e,t,n),se=r):Ze(e,t,n);break;default:Ze(e,t,n)}}function Ja(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Xf),t.forEach(function(r){var s=im.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Ie(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,a=t,o=a;e:for(;o!==null;){switch(o.tag){case 5:b=o.stateNode,Pe=!1;break e;case 3:b=o.stateNode.containerInfo,Pe=!0;break e;case 4:b=o.stateNode.containerInfo,Pe=!0;break e}o=o.return}if(b===null)throw Error(N(160));Nu(i,a,s),b=null,Pe=!1;var c=s.alternate;c!==null&&(c.return=null),s.return=null}catch(f){V(s,t,f)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ju(t,e),t=t.sibling}function ju(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ie(t,e),ze(e),r&4){try{Ln(3,e,e.return),ws(3,e)}catch(j){V(e,e.return,j)}try{Ln(5,e,e.return)}catch(j){V(e,e.return,j)}}break;case 1:Ie(t,e),ze(e),r&512&&n!==null&&Kt(n,n.return);break;case 5:if(Ie(t,e),ze(e),r&512&&n!==null&&Kt(n,n.return),e.flags&32){var s=e.stateNode;try{Bn(s,"")}catch(j){V(e,e.return,j)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,o=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{o==="input"&&i.type==="radio"&&i.name!=null&&Vo(s,i),xl(o,a);var f=xl(o,i);for(a=0;a<c.length;a+=2){var m=c[a],u=c[a+1];m==="style"?Ko(s,u):m==="dangerouslySetInnerHTML"?Yo(s,u):m==="children"?Bn(s,u):ii(s,m,u,f)}switch(o){case"input":hl(s,i);break;case"textarea":Go(s,i);break;case"select":var d=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Zt(s,!!i.multiple,g,!1):d!==!!i.multiple&&(i.defaultValue!=null?Zt(s,!!i.multiple,i.defaultValue,!0):Zt(s,!!i.multiple,i.multiple?[]:"",!1))}s[$n]=i}catch(j){V(e,e.return,j)}}break;case 6:if(Ie(t,e),ze(e),r&4){if(e.stateNode===null)throw Error(N(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(j){V(e,e.return,j)}}break;case 3:if(Ie(t,e),ze(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Gn(t.containerInfo)}catch(j){V(e,e.return,j)}break;case 4:Ie(t,e),ze(e);break;case 13:Ie(t,e),ze(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Fi=Y())),r&4&&Ja(e);break;case 22:if(m=n!==null&&n.memoizedState!==null,e.mode&1?(se=(f=se)||m,Ie(t,e),se=f):Ie(t,e),ze(e),r&8192){if(f=e.memoizedState!==null,(e.stateNode.isHidden=f)&&!m&&e.mode&1)for(S=e,m=e.child;m!==null;){for(u=S=m;S!==null;){switch(d=S,g=d.child,d.tag){case 0:case 11:case 14:case 15:Ln(4,d,d.return);break;case 1:Kt(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(j){V(r,n,j)}}break;case 5:Kt(d,d.return);break;case 22:if(d.memoizedState!==null){ba(u);continue}}g!==null?(g.return=d,S=g):ba(u)}m=m.sibling}e:for(m=null,u=e;;){if(u.tag===5){if(m===null){m=u;try{s=u.stateNode,f?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(o=u.stateNode,c=u.memoizedProps.style,a=c!=null&&c.hasOwnProperty("display")?c.display:null,o.style.display=qo("display",a))}catch(j){V(e,e.return,j)}}}else if(u.tag===6){if(m===null)try{u.stateNode.nodeValue=f?"":u.memoizedProps}catch(j){V(e,e.return,j)}}else if((u.tag!==22&&u.tag!==23||u.memoizedState===null||u===e)&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===e)break e;for(;u.sibling===null;){if(u.return===null||u.return===e)break e;m===u&&(m=null),u=u.return}m===u&&(m=null),u.sibling.return=u.return,u=u.sibling}}break;case 19:Ie(t,e),ze(e),r&4&&Ja(e);break;case 21:break;default:Ie(t,e),ze(e)}}function ze(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(xu(n)){var r=n;break e}n=n.return}throw Error(N(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Bn(s,""),r.flags&=-33);var i=Xa(e);$l(e,i,s);break;case 3:case 4:var a=r.stateNode.containerInfo,o=Xa(e);Kl(e,o,a);break;default:throw Error(N(161))}}catch(c){V(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zf(e,t,n){S=e,Au(e)}function Au(e,t,n){for(var r=(e.mode&1)!==0;S!==null;){var s=S,i=s.child;if(s.tag===22&&r){var a=s.memoizedState!==null||Cr;if(!a){var o=s.alternate,c=o!==null&&o.memoizedState!==null||se;o=Cr;var f=se;if(Cr=a,(se=c)&&!f)for(S=s;S!==null;)a=S,c=a.child,a.tag===22&&a.memoizedState!==null?eo(s):c!==null?(c.return=a,S=c):eo(s);for(;i!==null;)S=i,Au(i),i=i.sibling;S=s,Cr=o,se=f}Za(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,S=i):Za(e)}}function Za(e){for(;S!==null;){var t=S;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:se||ws(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!se)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:_e(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&La(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}La(t,a,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var f=t.alternate;if(f!==null){var m=f.memoizedState;if(m!==null){var u=m.dehydrated;u!==null&&Gn(u)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}se||t.flags&512&&ql(t)}catch(d){V(t,t.return,d)}}if(t===e){S=null;break}if(n=t.sibling,n!==null){n.return=t.return,S=n;break}S=t.return}}function ba(e){for(;S!==null;){var t=S;if(t===e){S=null;break}var n=t.sibling;if(n!==null){n.return=t.return,S=n;break}S=t.return}}function eo(e){for(;S!==null;){var t=S;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ws(4,t)}catch(c){V(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(c){V(t,s,c)}}var i=t.return;try{ql(t)}catch(c){V(t,i,c)}break;case 5:var a=t.return;try{ql(t)}catch(c){V(t,a,c)}}}catch(c){V(t,t.return,c)}if(t===e){S=null;break}var o=t.sibling;if(o!==null){o.return=t.return,S=o;break}S=t.return}}var bf=Math.ceil,as=Je.ReactCurrentDispatcher,zi=Je.ReactCurrentOwner,Ee=Je.ReactCurrentBatchConfig,O=0,Z=null,q=null,ee=0,ge=0,$t=gt(0),$=0,tr=null,_t=0,Ss=0,Ui=0,zn=null,de=null,Fi=0,dn=1/0,He=null,os=!1,Xl=null,dt=null,Er=!1,lt=null,cs=0,Un=0,Jl=null,zr=-1,Ur=0;function ae(){return O&6?Y():zr!==-1?zr:zr=Y()}function ft(e){return e.mode&1?O&2&&ee!==0?ee&-ee:Lf.transition!==null?(Ur===0&&(Ur=ic()),Ur):(e=D,e!==0||(e=window.event,e=e===void 0?16:mc(e.type)),e):1}function De(e,t,n,r){if(50<Un)throw Un=0,Jl=null,Error(N(185));sr(e,n,r),(!(O&2)||e!==Z)&&(e===Z&&(!(O&2)&&(Ss|=n),$===4&&rt(e,ee)),ve(e,r),n===1&&O===0&&!(t.mode&1)&&(dn=Y()+500,Ns&&yt()))}function ve(e,t){var n=e.callbackNode;Dd(e,t);var r=Yr(e,e===Z?ee:0);if(r===0)n!==null&&ca(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ca(n),t===1)e.tag===0?Df(to.bind(null,e)):Pc(to.bind(null,e)),_f(function(){!(O&6)&&yt()}),n=null;else{switch(ac(r)){case 1:n=di;break;case 4:n=sc;break;case 16:n=Wr;break;case 536870912:n=lc;break;default:n=Wr}n=Iu(n,wu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function wu(e,t){if(zr=-1,Ur=0,O&6)throw Error(N(327));var n=e.callbackNode;if(rn()&&e.callbackNode!==n)return null;var r=Yr(e,e===Z?ee:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=us(e,r);else{t=r;var s=O;O|=2;var i=Cu();(Z!==e||ee!==t)&&(He=null,dn=Y()+500,Et(e,t));do try{nm();break}catch(o){Su(e,o)}while(!0);Si(),as.current=i,O=s,q!==null?t=0:(Z=null,ee=0,t=$)}if(t!==0){if(t===2&&(s=Sl(e),s!==0&&(r=s,t=Zl(e,s))),t===1)throw n=tr,Et(e,0),rt(e,r),ve(e,Y()),n;if(t===6)rt(e,r);else{if(s=e.current.alternate,!(r&30)&&!em(s)&&(t=us(e,r),t===2&&(i=Sl(e),i!==0&&(r=i,t=Zl(e,i))),t===1))throw n=tr,Et(e,0),rt(e,r),ve(e,Y()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(N(345));case 2:At(e,de,He);break;case 3:if(rt(e,r),(r&130023424)===r&&(t=Fi+500-Y(),10<t)){if(Yr(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){ae(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Pl(At.bind(null,e,de,He),t);break}At(e,de,He);break;case 4:if(rt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var a=31-Me(r);i=1<<a,a=t[a],a>s&&(s=a),r&=~i}if(r=s,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*bf(r/1960))-r,10<r){e.timeoutHandle=Pl(At.bind(null,e,de,He),r);break}At(e,de,He);break;case 5:At(e,de,He);break;default:throw Error(N(329))}}}return ve(e,Y()),e.callbackNode===n?wu.bind(null,e):null}function Zl(e,t){var n=zn;return e.current.memoizedState.isDehydrated&&(Et(e,t).flags|=256),e=us(e,t),e!==2&&(t=de,de=n,t!==null&&bl(t)),e}function bl(e){de===null?de=e:de.push.apply(de,e)}function em(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Le(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function rt(e,t){for(t&=~Ui,t&=~Ss,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Me(t),r=1<<n;e[n]=-1,t&=~r}}function to(e){if(O&6)throw Error(N(327));rn();var t=Yr(e,0);if(!(t&1))return ve(e,Y()),null;var n=us(e,t);if(e.tag!==0&&n===2){var r=Sl(e);r!==0&&(t=r,n=Zl(e,r))}if(n===1)throw n=tr,Et(e,0),rt(e,t),ve(e,Y()),n;if(n===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,At(e,de,He),ve(e,Y()),null}function Bi(e,t){var n=O;O|=1;try{return e(t)}finally{O=n,O===0&&(dn=Y()+500,Ns&&yt())}}function Pt(e){lt!==null&&lt.tag===0&&!(O&6)&&rn();var t=O;O|=1;var n=Ee.transition,r=D;try{if(Ee.transition=null,D=1,e)return e()}finally{D=r,Ee.transition=n,O=t,!(O&6)&&yt()}}function Qi(){ge=$t.current,U($t)}function Et(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,If(n)),q!==null)for(n=q.return;n!==null;){var r=n;switch(ji(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Jr();break;case 3:cn(),U(he),U(le),Ii();break;case 5:Ti(r);break;case 4:cn();break;case 13:U(B);break;case 19:U(B);break;case 10:Ci(r.type._context);break;case 22:case 23:Qi()}n=n.return}if(Z=e,q=e=mt(e.current,null),ee=ge=t,$=0,tr=null,Ui=Ss=_t=0,de=zn=null,St!==null){for(t=0;t<St.length;t++)if(n=St[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=s,r.next=a}n.pending=r}St=null}return e}function Su(e,t){do{var n=q;try{if(Si(),Mr.current=is,ls){for(var r=Q.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}ls=!1}if(It=0,J=K=Q=null,Dn=!1,Zn=0,zi.current=null,n===null||n.return===null){$=1,tr=t,q=null;break}e:{var i=e,a=n.return,o=n,c=t;if(t=ee,o.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var f=c,m=o,u=m.tag;if(!(m.mode&1)&&(u===0||u===11||u===15)){var d=m.alternate;d?(m.updateQueue=d.updateQueue,m.memoizedState=d.memoizedState,m.lanes=d.lanes):(m.updateQueue=null,m.memoizedState=null)}var g=Ha(a);if(g!==null){g.flags&=-257,Va(g,a,o,i,t),g.mode&1&&Qa(i,f,t),t=g,c=f;var y=t.updateQueue;if(y===null){var j=new Set;j.add(c),t.updateQueue=j}else y.add(c);break e}else{if(!(t&1)){Qa(i,f,t),Hi();break e}c=Error(N(426))}}else if(F&&o.mode&1){var M=Ha(a);if(M!==null){!(M.flags&65536)&&(M.flags|=256),Va(M,a,o,i,t),Ai(un(c,o));break e}}i=c=un(c,o),$!==4&&($=2),zn===null?zn=[i]:zn.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=au(i,c,t);Da(i,p);break e;case 1:o=c;var h=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(dt===null||!dt.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=ou(i,o,t);Da(i,x);break e}}i=i.return}while(i!==null)}ku(n)}catch(C){t=C,q===n&&n!==null&&(q=n=n.return);continue}break}while(!0)}function Cu(){var e=as.current;return as.current=is,e===null?is:e}function Hi(){($===0||$===3||$===2)&&($=4),Z===null||!(_t&268435455)&&!(Ss&268435455)||rt(Z,ee)}function us(e,t){var n=O;O|=2;var r=Cu();(Z!==e||ee!==t)&&(He=null,Et(e,t));do try{tm();break}catch(s){Su(e,s)}while(!0);if(Si(),O=n,as.current=r,q!==null)throw Error(N(261));return Z=null,ee=0,$}function tm(){for(;q!==null;)Eu(q)}function nm(){for(;q!==null&&!Ed();)Eu(q)}function Eu(e){var t=Tu(e.alternate,e,ge);e.memoizedProps=e.pendingProps,t===null?ku(e):q=t,zi.current=null}function ku(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=$f(n,t),n!==null){n.flags&=32767,q=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{$=6,q=null;return}}else if(n=Kf(n,t,ge),n!==null){q=n;return}if(t=t.sibling,t!==null){q=t;return}q=t=e}while(t!==null);$===0&&($=5)}function At(e,t,n){var r=D,s=Ee.transition;try{Ee.transition=null,D=1,rm(e,t,n,r)}finally{Ee.transition=s,D=r}return null}function rm(e,t,n,r){do rn();while(lt!==null);if(O&6)throw Error(N(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Ld(e,i),e===Z&&(q=Z=null,ee=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Er||(Er=!0,Iu(Wr,function(){return rn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ee.transition,Ee.transition=null;var a=D;D=1;var o=O;O|=4,zi.current=null,Jf(e,n),ju(n,e),wf(Il),qr=!!Tl,Il=Tl=null,e.current=n,Zf(n),kd(),O=o,D=a,Ee.transition=i}else e.current=n;if(Er&&(Er=!1,lt=e,cs=s),i=e.pendingLanes,i===0&&(dt=null),Id(n.stateNode),ve(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(os)throw os=!1,e=Xl,Xl=null,e;return cs&1&&e.tag!==0&&rn(),i=e.pendingLanes,i&1?e===Jl?Un++:(Un=0,Jl=e):Un=0,yt(),null}function rn(){if(lt!==null){var e=ac(cs),t=Ee.transition,n=D;try{if(Ee.transition=null,D=16>e?16:e,lt===null)var r=!1;else{if(e=lt,lt=null,cs=0,O&6)throw Error(N(331));var s=O;for(O|=4,S=e.current;S!==null;){var i=S,a=i.child;if(S.flags&16){var o=i.deletions;if(o!==null){for(var c=0;c<o.length;c++){var f=o[c];for(S=f;S!==null;){var m=S;switch(m.tag){case 0:case 11:case 15:Ln(8,m,i)}var u=m.child;if(u!==null)u.return=m,S=u;else for(;S!==null;){m=S;var d=m.sibling,g=m.return;if(yu(m),m===f){S=null;break}if(d!==null){d.return=g,S=d;break}S=g}}}var y=i.alternate;if(y!==null){var j=y.child;if(j!==null){y.child=null;do{var M=j.sibling;j.sibling=null,j=M}while(j!==null)}}S=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,S=a;else e:for(;S!==null;){if(i=S,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Ln(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,S=p;break e}S=i.return}}var h=e.current;for(S=h;S!==null;){a=S;var v=a.child;if(a.subtreeFlags&2064&&v!==null)v.return=a,S=v;else e:for(a=h;S!==null;){if(o=S,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:ws(9,o)}}catch(C){V(o,o.return,C)}if(o===a){S=null;break e}var x=o.sibling;if(x!==null){x.return=o.return,S=x;break e}S=o.return}}if(O=s,yt(),Be&&typeof Be.onPostCommitFiberRoot=="function")try{Be.onPostCommitFiberRoot(ps,e)}catch{}r=!0}return r}finally{D=n,Ee.transition=t}}return!1}function no(e,t,n){t=un(n,t),t=au(e,t,1),e=ut(e,t,1),t=ae(),e!==null&&(sr(e,1,t),ve(e,t))}function V(e,t,n){if(e.tag===3)no(e,e,n);else for(;t!==null;){if(t.tag===3){no(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(dt===null||!dt.has(r))){e=un(n,e),e=ou(t,e,1),t=ut(t,e,1),e=ae(),t!==null&&(sr(t,1,e),ve(t,e));break}}t=t.return}}function sm(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ae(),e.pingedLanes|=e.suspendedLanes&n,Z===e&&(ee&n)===n&&($===4||$===3&&(ee&130023424)===ee&&500>Y()-Fi?Et(e,0):Ui|=n),ve(e,t)}function Ru(e,t){t===0&&(e.mode&1?(t=vr,vr<<=1,!(vr&130023424)&&(vr=4194304)):t=1);var n=ae();e=$e(e,t),e!==null&&(sr(e,t,n),ve(e,n))}function lm(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ru(e,n)}function im(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(N(314))}r!==null&&r.delete(t),Ru(e,n)}var Tu;Tu=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||he.current)me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return me=!1,qf(e,t,n);me=!!(e.flags&131072)}else me=!1,F&&t.flags&1048576&&Oc(t,es,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Lr(e,t),e=t.pendingProps;var s=ln(t,le.current);nn(t,n),s=Pi(null,t,r,e,s,n);var i=Oi();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,pe(r)?(i=!0,Zr(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,ki(t),s.updater=As,t.stateNode=s,s._reactInternals=t,Fl(t,r,e,n),t=Hl(null,t,r,!0,i,n)):(t.tag=0,F&&i&&Ni(t),ie(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Lr(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=om(r),e=_e(r,e),s){case 0:t=Ql(null,t,r,e,n);break e;case 1:t=Ya(null,t,r,e,n);break e;case 11:t=Ga(null,t,r,e,n);break e;case 14:t=Wa(null,t,r,_e(r.type,e),n);break e}throw Error(N(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),Ql(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),Ya(e,t,r,s,n);case 3:e:{if(fu(t),e===null)throw Error(N(387));r=t.pendingProps,i=t.memoizedState,s=i.element,Fc(e,t),rs(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=un(Error(N(423)),t),t=qa(e,t,r,n,s);break e}else if(r!==s){s=un(Error(N(424)),t),t=qa(e,t,r,n,s);break e}else for(ye=ct(t.stateNode.containerInfo.firstChild),xe=t,F=!0,Oe=null,n=zc(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(an(),r===s){t=Xe(e,t,n);break e}ie(e,t,r,n)}t=t.child}return t;case 5:return Bc(t),e===null&&Ll(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,a=s.children,_l(r,s)?a=null:i!==null&&_l(r,i)&&(t.flags|=32),du(e,t),ie(e,t,a,n),t.child;case 6:return e===null&&Ll(t),null;case 13:return mu(e,t,n);case 4:return Ri(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=on(t,null,r,n):ie(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),Ga(e,t,r,s,n);case 7:return ie(e,t,t.pendingProps,n),t.child;case 8:return ie(e,t,t.pendingProps.children,n),t.child;case 12:return ie(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,a=s.value,L(ts,r._currentValue),r._currentValue=a,i!==null)if(Le(i.value,a)){if(i.children===s.children&&!he.current){t=Xe(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var o=i.dependencies;if(o!==null){a=i.child;for(var c=o.firstContext;c!==null;){if(c.context===r){if(i.tag===1){c=Ye(-1,n&-n),c.tag=2;var f=i.updateQueue;if(f!==null){f=f.shared;var m=f.pending;m===null?c.next=c:(c.next=m.next,m.next=c),f.pending=c}}i.lanes|=n,c=i.alternate,c!==null&&(c.lanes|=n),zl(i.return,n,t),o.lanes|=n;break}c=c.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(N(341));a.lanes|=n,o=a.alternate,o!==null&&(o.lanes|=n),zl(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}ie(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,nn(t,n),s=ke(s),r=r(s),t.flags|=1,ie(e,t,r,n),t.child;case 14:return r=t.type,s=_e(r,t.pendingProps),s=_e(r.type,s),Wa(e,t,r,s,n);case 15:return cu(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),Lr(e,t),t.tag=1,pe(r)?(e=!0,Zr(t)):e=!1,nn(t,n),iu(t,r,s),Fl(t,r,s,n),Hl(null,t,r,!0,e,n);case 19:return hu(e,t,n);case 22:return uu(e,t,n)}throw Error(N(156,t.tag))};function Iu(e,t){return rc(e,t)}function am(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ce(e,t,n,r){return new am(e,t,n,r)}function Vi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function om(e){if(typeof e=="function")return Vi(e)?1:0;if(e!=null){if(e=e.$$typeof,e===oi)return 11;if(e===ci)return 14}return 2}function mt(e,t){var n=e.alternate;return n===null?(n=Ce(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fr(e,t,n,r,s,i){var a=2;if(r=e,typeof e=="function")Vi(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Ft:return kt(n.children,s,i,t);case ai:a=8,s|=8;break;case cl:return e=Ce(12,n,t,s|2),e.elementType=cl,e.lanes=i,e;case ul:return e=Ce(13,n,t,s),e.elementType=ul,e.lanes=i,e;case dl:return e=Ce(19,n,t,s),e.elementType=dl,e.lanes=i,e;case Bo:return Cs(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Uo:a=10;break e;case Fo:a=9;break e;case oi:a=11;break e;case ci:a=14;break e;case be:a=16,r=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=Ce(a,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function kt(e,t,n,r){return e=Ce(7,e,r,t),e.lanes=n,e}function Cs(e,t,n,r){return e=Ce(22,e,r,t),e.elementType=Bo,e.lanes=n,e.stateNode={isHidden:!1},e}function rl(e,t,n){return e=Ce(6,e,null,t),e.lanes=n,e}function sl(e,t,n){return t=Ce(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function cm(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Us(0),this.expirationTimes=Us(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Us(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Gi(e,t,n,r,s,i,a,o,c){return e=new cm(e,t,n,o,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ce(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ki(i),e}function um(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ut,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function _u(e){if(!e)return pt;e=e._reactInternals;e:{if(Dt(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var n=e.type;if(pe(n))return _c(e,n,t)}return t}function Pu(e,t,n,r,s,i,a,o,c){return e=Gi(n,r,!0,e,s,i,a,o,c),e.context=_u(null),n=e.current,r=ae(),s=ft(n),i=Ye(r,s),i.callback=t??null,ut(n,i,s),e.current.lanes=s,sr(e,s,r),ve(e,r),e}function Es(e,t,n,r){var s=t.current,i=ae(),a=ft(s);return n=_u(n),t.context===null?t.context=n:t.pendingContext=n,t=Ye(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ut(s,t,a),e!==null&&(De(e,s,a,i),Or(e,s,a)),a}function ds(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ro(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Wi(e,t){ro(e,t),(e=e.alternate)&&ro(e,t)}function dm(){return null}var Ou=typeof reportError=="function"?reportError:function(e){console.error(e)};function Yi(e){this._internalRoot=e}ks.prototype.render=Yi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));Es(e,t,null,null)};ks.prototype.unmount=Yi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Pt(function(){Es(null,e,null,null)}),t[Ke]=null}};function ks(e){this._internalRoot=e}ks.prototype.unstable_scheduleHydration=function(e){if(e){var t=uc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<nt.length&&t!==0&&t<nt[n].priority;n++);nt.splice(n,0,e),n===0&&fc(e)}};function qi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Rs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function so(){}function fm(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var f=ds(a);i.call(f)}}var a=Pu(t,r,e,0,null,!1,!1,"",so);return e._reactRootContainer=a,e[Ke]=a.current,qn(e.nodeType===8?e.parentNode:e),Pt(),a}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var o=r;r=function(){var f=ds(c);o.call(f)}}var c=Gi(e,0,!1,null,null,!1,!1,"",so);return e._reactRootContainer=c,e[Ke]=c.current,qn(e.nodeType===8?e.parentNode:e),Pt(function(){Es(t,c,n,r)}),c}function Ts(e,t,n,r,s){var i=n._reactRootContainer;if(i){var a=i;if(typeof s=="function"){var o=s;s=function(){var c=ds(a);o.call(c)}}Es(t,a,e,s)}else a=fm(n,t,e,s,r);return ds(a)}oc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Rn(t.pendingLanes);n!==0&&(fi(t,n|1),ve(t,Y()),!(O&6)&&(dn=Y()+500,yt()))}break;case 13:Pt(function(){var r=$e(e,1);if(r!==null){var s=ae();De(r,e,1,s)}}),Wi(e,1)}};mi=function(e){if(e.tag===13){var t=$e(e,134217728);if(t!==null){var n=ae();De(t,e,134217728,n)}Wi(e,134217728)}};cc=function(e){if(e.tag===13){var t=ft(e),n=$e(e,t);if(n!==null){var r=ae();De(n,e,t,r)}Wi(e,t)}};uc=function(){return D};dc=function(e,t){var n=D;try{return D=e,t()}finally{D=n}};jl=function(e,t,n){switch(t){case"input":if(hl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=xs(r);if(!s)throw Error(N(90));Ho(r),hl(r,s)}}}break;case"textarea":Go(e,n);break;case"select":t=n.value,t!=null&&Zt(e,!!n.multiple,t,!1)}};Jo=Bi;Zo=Pt;var mm={usingClientEntryPoint:!1,Events:[ir,Vt,xs,$o,Xo,Bi]},Sn={findFiberByHostInstance:wt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},hm={bundleType:Sn.bundleType,version:Sn.version,rendererPackageName:Sn.rendererPackageName,rendererConfig:Sn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Je.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=tc(e),e===null?null:e.stateNode},findFiberByHostInstance:Sn.findFiberByHostInstance||dm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var kr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!kr.isDisabled&&kr.supportsFiber)try{ps=kr.inject(hm),Be=kr}catch{}}je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=mm;je.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!qi(t))throw Error(N(200));return um(e,t,null,n)};je.createRoot=function(e,t){if(!qi(e))throw Error(N(299));var n=!1,r="",s=Ou;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Gi(e,1,!1,null,null,n,!1,r,s),e[Ke]=t.current,qn(e.nodeType===8?e.parentNode:e),new Yi(t)};je.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=tc(t),e=e===null?null:e.stateNode,e};je.flushSync=function(e){return Pt(e)};je.hydrate=function(e,t,n){if(!Rs(t))throw Error(N(200));return Ts(null,e,t,!0,n)};je.hydrateRoot=function(e,t,n){if(!qi(e))throw Error(N(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",a=Ou;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Pu(t,null,e,1,n??null,s,!1,i,a),e[Ke]=t.current,qn(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new ks(t)};je.render=function(e,t,n){if(!Rs(t))throw Error(N(200));return Ts(null,e,t,!1,n)};je.unmountComponentAtNode=function(e){if(!Rs(e))throw Error(N(40));return e._reactRootContainer?(Pt(function(){Ts(null,null,e,!1,function(){e._reactRootContainer=null,e[Ke]=null})}),!0):!1};je.unstable_batchedUpdates=Bi;je.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Rs(n))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return Ts(e,t,n,!1,r)};je.version="18.3.1-next-f1338f8080-20240426";function Mu(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Mu)}catch(e){console.error(e)}}Mu(),Mo.exports=je;var pm=Mo.exports,lo=pm;al.createRoot=lo.createRoot,al.hydrateRoot=lo.hydrateRoot;const vm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAQAAACQ9RH5AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QA/4ePzL8AAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfoBRQUFTglwCnRAAADk0lEQVRYw+2Yz08TURDHlwRaIxflZyJQiFnozne7NbF/ACdvEqJU4wW4GiB40IMFE7ko9WACUi8mcsBIIAQvEIJ4lQQ0xgOEhBDTGEBNavGG/Gw9kL7dbffH2xYaD33vuPPe5828N7MzIwiFURj/x/BeQic9xwJtIIY9OqAd2sACRdAp150R0l+FEH1FAkmzSWvUT/XZaNNg9gUi3tC+OVIzjzHh8zsEI2qEvnIBL+mAC5qaCRoVK52Ak5lo6RptOoKmzB5H0AlYjy6igYw7TeALDdJtyddUIboDJcpFkuU2eoolHGfAI4ESfjBDw0XjaVtt43FjjdlqpRYhbKWtmPOX8oOTiHobmovxLs1090W33Q6iW+qiuG7dIgdasyBK07rlM75q3gtrqsCkXmtbg5s5CD0Qihz6Rzcdao49kgWY9uU2I1kaYBIDRt/lFuxq9gk6BR9Ld4xl7cCCIF9Xtaa4pV8b6PvLLJrZgwUBPZq9Xju946gxmgcsCDSl+r9Xcfi46LtyOVuwVI7fTG7c6as21JoPrDP3ETxOwQZa84JFtyaahcxuJGwx72YHFgTqY+DVU0gL+MGNNThKySq1eQQLAi2nZKWO/ILDzNjDeQUjyGTn8wr2Kkx2I6/gpgpm6thpgn/KZJceMPCe8WZqAuviB9ujbcFqXEWZ7YMRdfnVFsQcTE3fmA4yR5bBjbZ9XPiQEpBv8lUeiPIYHLds3IkibJsnvEUPDxrPbAIIOpnAEndal2ZwqdxAoc/saO3GWVKdmmvxF51arSlscDSPWmOY/iRozfbfaaG1cSihfrbnirl3PmJn37SvG/RaG2krCKIb2wz80Bxcr/47pS5HKbyJ51OvmvpYXh8mOHNhzi4C7bD93lqK+vyawnQy54g+rSmE7IISjWrcozsn7D2Nj7+yFRcr1UKTDuWWrLGt6ntBzMi/LTIGJLGbHZpa8VetIqQbvMsiGiMdoicLIx9pDj/EvTBQgjldQj/lr+Jd66vWF/WYbS524giltKhD76DHPqTUn6Ne+qNb9zFw3qkPluq1RhLb1GceBOChfvxIWzHrGHticBoxaEws0yCCXgVlcMGFMp8fQQrTp4x2UwJDjoyc/sL1XRzuGeN+yeZRGMOZzTPrliLGcg+3J+4hY0zbx7FqotKMdPVU28bwUB9WLaErCJ1Z11qplTroBb3HOsVpn/YpjnWaxzC1mzcbC6Mw8jz+AUKYDKAQMvO+AAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI0LTA1LTIwVDIwOjIxOjU2KzAwOjAwx3vFgwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNC0wNS0yMFQyMDoyMTo1NiswMDowMLYmfT8AAAAASUVORK5CYII=",gm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAWklEQVR4nO3XsQ3AIAwEQE/LtsxEejpASvLiTvoBLLvwVwEAABzq4Vk2wmNgG55cd9IAAK9p4Vn29WuoLZV6WPpwHbSlFh4AgP8a4TGwDU+uO+keHgAAgNr2APldsP+m7K9hAAAAAElFTkSuQmCC",ym="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAA8CAYAAAAQTCjdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAACxEAAAsRAX9kX5EAAADJSURBVGhD7dcxCsIwFIfxJF7BUdBDuXsJx7RDs3oI3T2UgqNXsDHPvgMI/QtCvx+EvKbLBwUxAQCwDNF3ia7rTrXWtc0xxmcp5fh5IZB8l2hx+5TSwZbNfiwhDf0lQtUIVSNUjVA1QtUIVSNUjVC1aNcH4b/xTVuraQyvth7TOE+73lxjzvlsVwc/+0vjOF749I320/ss0ff9rW3b6Snch2HY+TwbP09qhKoRqkaoGqFqhKoRqkao2jJD7cpgFzFbNvsxAADfCuENcUUzai4mKxMAAAAASUVORK5CYII=",xm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEpElEQVR4nO2bXYhVVRTHfzOjBX7OTKYY0sPMKDp9qFM0On7M9JBP44NE2EMm9KHvPRS3FJQynRDFt0QR8YsK6UE0LAettLF5Vl/qJb/KLCiECpsJY8W6cNmsc+7Z++5z7zXuHxZczpz9X2udvc86a629BxpooIFAtAGLgWeBQRX5vUj/dl+jGVgGbAaGgdvAvTIi95wBNgF9ylH36AK2A9czOFhOrgHvK2fd4XHgGDAewVFXhPMo0E0dYDKwAxjLwVFXRMceYFqtnB0AbmYw8ht9KC8BzwAdwMMq8rtX/yb3jGR4eDeAldV0tEmDUdryvQC8CkwP4G8FXtcHlfYgC2pLrpgAHEgx5HONsLGwXCN3kr59QAs5OvtpSkRdk5di4Hldypbu43k43QTsT1B4AniI/CE6TibYcCj28t6coGioGu9RCUTXzgRb5J2OFo3HDQVvUTu8nRDIKo7e0xI+PTKztYY105LhTa2EdE/CO1vJMp4IzFGR36EQG04Z9u2qJF0cN6JxewDXA8BG4CuHU35/CWwIdH6GsQJlaS8I4OKY8fRCPj1PA99nSB2/A3oC+F8wuI74knQZsytJhS9WAX9mzJVF/gCeC9AzbMxypw/BDsMY3wxqHvC7h7NF+Q2Y66lrhcGzLevgZqOeldzYF1aS8AuwRWdeZCvwa0Jg9MWIw3E1axNhmWHAawEBz+W4DMw27n0EuGLc/5inzg0GR2+WgZucQX8HVD0uxz/6EJLwpN5TOkaSCx+0GaVlISQAyFLxxccOh5R6vkvyowC93zocX2QZ9LMzSHpUvngD2Fsir2QYc8TRey5A75DD8VO5Ae3GeyDdiGrg6wiB62XD/ta0AT3GAGnL5I1u47svHUtfLDHsX1SuMnIHSN8pT3RoluXqXRrA1WXw9KcNGDQGzCQfSLJwELhr6DwfyDnL4BqstcPSKjpcJr18oloODxgDvHLSDNia4uxfwOoKuL2XdE9otuIxu3cSnL0UIUAu9Q1abcaAdcTDPIP/My07Y3Qe1xv8030TD6mcYsFaQSE1cBI+8E08YqWWtXJ4NKSGf8coHlKzFQ886qSce/VaDLSHFg99xizIXk+9Y2NoltiszTrfaicLJgFPOSLXYuCiY/MPPt3V7cbTko2ten2H+w3ed30IOo1kXnbx6tXhsw7nWEgNcNQwTnbx6s3hFxM217zRbUS9G9r8rheH5RTBj8ZXZX4o4W7DwFMVbLU8qEutVORaCCS4njbskz2nijbTrscmjQRrMuTrMqVS4pUJB018u4ox4XZFi4EqxpfkPxQMBcWZruaGeHPCzIq8GVNRkx4gsRQNa+GdN2ZoZWXZIClqdLToARJL4U3dxcsLa41oXJRP8jzJ05Iy08XZll5VLPQbSUWpfJins6XLu1DmxNyI7vWEHAtu00LAzY3dABX1nc0avcudmh3T7Y8hbZL36bd3pkqHXluvxftohgOq12JG45DDpVsSWq3/u8OlpVige0N5nKod09w4OF3ME5268341gqNSz75XhZ2PaAlCrwY36SvdyuDgLb23oJ2KaiY0uaBVe8QDJf/kIZ+dhYHHjBtooAH4F8+LPcYEceJrAAAAAElFTkSuQmCC",Nm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAELklEQVR4nO1aTUgVURT+XvGCUF8UVNoPpbkoWhRtWmilUrtSMvvBskWaoC1cRbQxyTIoXLQsaGOPFi2iP9sE0SIQQttVWhBYoEYm5VMpSI0D58UwnTsz986855uaDy483px7fmbuPefccy4QIUKECBH+IAbgK4B5j2OC54QWJRrGpkcxQoxDBgbXIsS4ZGBwJ0KMJwYG9yLEGBUM2mZ5vl14PoYQYjmAdsGYHwDiFro4/2ena2ceC4oYgGYXL7oCwDUAk4rl2i/M6VfQTjIv4ukUBZozFcqOWZR5DaDDZvw+AJ9c9ucNge9Nlzm0xA9Y6NcCaAPwAsAc0xwN2tilAIYFZWYBPAdw2yLcabQIvFs8zCPeSZY1KzwfZh0DQ4eBt02PXwCeAjhs279pLAawF8BdAD99yLkQlLHrAEwZKJACcB3Aeg1Zq/nlfjOQNwNgQxAG3zEQ/hhAoQ+ZhcxDVy7p6gtlir35DMCg8D8tyVYEgxjzkpb5IOsg7fVyU4GLALwUmH4GsIxptvISfA9gHEAlgkcZe+qPvEXSBhUAGBH0e8W6a+OUYtk0Kr5GwoEXOas69rRv2Sek+HeSDxmSQ4PFOCnWNmro6IiEIi0kRVdCDzUA3nnYf0RTrcmbdJkWeI25fIC/cNJBsSEAm+AOWlZXNJ0O7cEuj0uylHVR8WqAJtoUQX6eqxi7XObrGmsdl11472Rfonpp5FeMcNwhGZhxWII1HjMvpy9tTSetqGbZ0jw6iNTDJyq41iQJ6Bbo4w579h6APQDyeBDv+w5bR3Jk3Qr6CeYdCLYoQkCTQFunUOisA/9zijnkve1oEuhGWMdA0ScIkoJ8UvFl3fBQmNcj0JULdKRb4PggCKLzqB1SBuZlqVUK8yhOe6mEkm6BY1oQRPvQjpRAl++Bf4GiCGBHnkBHugWOKY8GTxoanBDmfRfo8gU6esk5taQrPPCvyrUl3efDaVHoccOjXHJamwMISxR6VDiv0YXIeFja7dAQ0008HrA3zudRpfiyC5Z41Cvqxm6pZXUAqeX+bKeWbQ6Hh3EPlYUuHwZTTyqrh4cGB2WG+GjmhkVstM6XnmNjvRTWS13O2Sd0DE7wIVoK8LoFgGqXc6v1RaqWsQqrFAnRqG4BQLd8EuNMSYU4HwQozLzhRCHFv3vYGzuVeBKKr35aoSOVp7ShU8SjpfUly0U8VRlqwLSIB2ZuUqYNorlFPM4YlGnpBS1IIb7Ih8wiboxnvRDvp9UyFdZWC7hRNZ/jzTRqometXZr00S5t9TBvjluyWWuXgpvOfhvi1Py245bLnFFbfF4jNMSPIINXHkoCvvIwoKAlHldd7noUZ/LKQyYutSxR7NucuNRiAikxoKtKaez4V64tpdFrGMNDi04Dgy8ixKg1MPggQoxiA4M3IsSI/W8XxCNEiBABQeI3wYAfFtx9PqUAAAAASUVORK5CYII=",jm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAQAAACQ9RH5AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QA/4ePzL8AAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfoBRQUFTglwCnRAAADqElEQVRYw+2YW0hUURSGt2mJXaTsjkQXRitTZ876z5mGIIQgKIIu1GCQD4YYVAhhIkKgPhdFUeZDFAURdqELEcFEVNBDGCZ0MYrSCh+yCAtTQ3R2D7M8Ojmjs/fMsQud/TZz1v72Wmftf629hfjbHl+aO9Od6UsbNyC8qMVdfIbk8Rl3qca0HES6UmkPWmzgr+MFdrtSHcDSFvoQFTo43mNzQqE5U+nCrwgKoAENFBixnPP5UxKEdWfSs6GJqRlVnqzh/1vZqKLmYein7swEYI2F1GpD32JblNeS4B/2XquxMN5Nk4GXtidHXalCIM8oFEmRkw/HhlItb0Y83CTcZB/6USbEyrk4Rf2QWBY1BUvRx+jbkZcX254tG/TAKBLCNNAOiQ5j56jZv8MO+F5N7Mq5+MqTHBQCa9EFiVPe9DGXe4jB3zBfz986nuC+P9nKRieCVBGLXUEKHrDlcQ1s/hzqgYRE0DR8aWiBRG3MYgMEISHR7ZmtrlQVHOYGIVAJSbfEBAXry+zzPvVAN4VMLY97On1Bn+FSsiYGNyoHmoP1Tgh4db4Wy0kQs5TMzK0c6GNCCGGY6nWHjvMMamWDqjlUW3Q1wF76ATXwORYOUxtsMfiMWmqxVFrzdMHWPI7ZDTWP74TMciZpy+1EBgfUzK6FzNzT46hrIfAVNfDZkJm5VLuOL2fwaTVwFYO3a/doXKVi0/ch8FrOyTptcD2D1yiZedPxAxKSPvqTdbAFKeiAhKTeVdNUV3ydg71RK6c3c8SuqidHIZs+1gI/Zmu/RrDo7VDbo7joIsa+KUjRSY9SNu9UK4meLHSyZYnQSxBq5AnazAUxa/QCtLHVIy1/WQS6eZJ2UIwNQDtbdOmLjxACG+w++Ts2jbnQdfSN3+4z18d7HvZTL082MLoKoZz6WTR6za2JOKD6QmIACUnVUbGV9jtfaHWCzouGC6/tU1HEnYltNvaV2h4Ys8jRfZ76kzUzQgnkmNC9+A5rkXyaTE8YXRm1B2/CZAcuIywPt7wtIxYVuhkJWh6Hrl/oYQiQMzWsB5+CAUhIeujcVdMJDmlemFbl8q8nHAPTES6VRhjY4MQ6/B+ccHDk8U+CS0e9UCxxDFyQgloK0J0II0A12mX/j37MJVRMuyKMYnOJg1gj324KRqZWb7ieJVYy94+aXOW/x+MeBz0Wwrs42jd2LxL/8OPLoPrBHYyTCW92xj77xnuGVs/uS2Hgi+MnI7l4YWfzc2uFzhw/AVPOxLKtaxQVAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI0LTA1LTIwVDIwOjIxOjU2KzAwOjAwx3vFgwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNC0wNS0yMFQyMDoyMTo1NiswMDowMLYmfT8AAAAASUVORK5CYII=",Am="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAQAAACQ9RH5AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QA/4ePzL8AAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfoBRQUFTglwCnRAAAE7ElEQVRYw+2ZzW9UZRTGfwOlQSihpvYL5MOmECVEwJEoUAIrMZCQkGAiYaUhkGIrJdh0AcFSPnTcWGRC4scfIFKFRGJCMA0uaqDQVFQWDWnpgi6t04/p0GmHx8W8nZk7c2funTvjjvOueu575szc97zP85xTeG5OVoafg7RxgSBBLtDGQfws/v8SzmMnAfqIIZsV4z4BdjCvuElr6GDYkmiCJwgxwqTF/5gz1BQn6XKCRMzHjnKFRt6mDPAjhB9YwhaOcoV/za4pvmJZYUlLOMEEQkxzlV2UpDyrIESICsvud+kiihDjtFh252X19CHEU4K8bPO8kkob7wouM40QvdR5SbufMYS46SG8nlsIEWJfvqEtPEOE+dDzMR1iChGjKZ+gdoR4xPqCKmQDgwhx2m3ARwjxV6GVCdTQjxDH3Z3tM0Q/5UW5jOU8QMScz7qeMcQjqosGPrUMIUK5S7SEPkS4wLMFaODzBIJtJILozXWvTyBUQCXHbRGdxBAXEp4jCHEsOzhOIG4WmHYrA4Y2GhI+H92IMWrtQ4KIp97QxthCAswaxO60PFlLFHHRvvSnEMEC0r5uLo8QgxkM/Q0iYvebOxDTtpjsxkrpYCaRdpatGTtWEUW0p7t9DCF+8Jh2vaGTuRWw3XUN8Rif1bkTId7xRJ0nDRvNrYcstN25G6GUkgPgC8SoBwZdR2+aCJphc5a9CwghzludfYjv81ZhhwlnqK+OHBFdiLupjsXEEI15pV1Dj43o+4PSHDFNiFkWJR1x/fSW66Q+DhtJZF1R/DnjtiHExqTjIEKUuX7F120lrjjlELkUIQ4kHW2Icde/91iWtH0scIydRLQm/zyPeILfohmzn20Y8Ttn0tJGWOcQ+RJ+RhBnk65LJjhElUPwfHoQYdYmBNLcanWIrCJkdl6yS1zpEN5qIbhk6h7mO0RW2iV2+6rXEUHcTumQ4i88/gacrMK86nPW4ppwAY69iEnqE546fkOIj10XZhjxSfp1WuIQdAqlwMw8jhncuu66RyxPv05xANnioJKnEb8adllNt2nQ2vJoTbenA0gZMcTRnHzbjwixEvDRaHCrhzV5wWxzOmTCfQeSOIsQHwC1/GzubZtjJafbj+kkAQFEKCvybCKKuAG8x6ghgw15U6gtLe5AiF1ZJNxDxD+8yQ3TK5/01PvusRMCPh4jumwDAgjxSwG/NSl9BtOlTxwKoqyw0cmzKeqiIyff5rJVzCA+zXxQzRTickZXMJCipTYXIH6/yyZv44g9nYJLAJ0JwRrIIuHc2WtEEV/aP1zGOOKWRTHEp1oDNjo5H/NxGxHKPog6jhCHLOceo9N65T1YI0I056KBe4ipRN1W0862glvWN4gg7uSGmzpCiKFsfZ2nAd0wYpRXnDbuI4Z4UKRRxIv8iYix1/2J/M3yIowh+vPj69MIMZRKYJ7OdtiV6E0jsBgiwpFMiHN5gRqJIGI5yTbLWcfFWbcrPWW1V7ltpr17vXzrOu6ZxuRrVmIHstW2mPytmeDeYbX3sXGLGaVG+YndFr6uYoJxixwuZQ/XzFwgRHPeMiGjMi8yZRB7jKs00cBSUgfl5TTQTJf5iiJMZ7Gm9NW0M2TpG8KMmH8NWDvkQdqLOBU0dbqdz+hNYefUNctdztPg8Q64nNtt4gCtnCPIJc7Ryvts5AWem4P9B1/OfzyOpqAMAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI0LTA1LTIwVDIwOjIxOjU2KzAwOjAwx3vFgwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNC0wNS0yMFQyMDoyMTo1NiswMDowMLYmfT8AAAAASUVORK5CYII=",wm="/assets/gallery_icon-guxnbtak.png",Sm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAACzElEQVR4nO2Zu4tTQRTGT3w2ijYWWqhoYysBUZs0wTv7fWOiwm1tfCJa+YRdiNWirvhYbdZyrUV81ArqP7AKKharjYqglYqrsCpnTaHXmd1kN8nmhvODA4GcOzNf5sy5k3NEDMMwjP9JkmQ9ySEAz0h+qdtTkhf0O+khCiRPA/hO8lfEJkiekl4AwMVphGZtSPIMyd1NiJ0yAFXJcSi/bFYwyef6rOQNksWIoKtJkqxWIzkc8nHObZa8AeBQIFwfBaLgSUD0AckbAAYCggcDfoMBvwHJGyTPBnbu7Gz9uh6aYNoOKxbStDOcD2hJi5a0FEtatKSVD2hJi5a0cpG0vPfbtCj3t3nvS+3+e6hzZOcFsL2dWqfw3h8O7MiIZCB5MCDk8WwLAABuBPyOy3wU5gDcbaLEM1ypVNaoAbjWaImH5P2A7962C3bObQ0IfhsovBUAvJhlEe8farXaApLvsr56vDoheGm9a5Bd6Jasr/d+V7OCvfc7G/yRP6dpukQ6Acl7oVCN+A41KhbA+cgY1wO+d6RTADgSWPBErEcE4GS9nRIT+w3AidCzJDeE2jSaPKVTVCqV5SQ/BRZ+K1ZEB7BOdxDAmIZj3cZInvPer41MpXngdmB3P6Zpuqy9KjMAqEXC8oy0CAD9kTn6pdNUq9WVJD8EFjMJYN9cx+/r69uvYwUEvy+XyytkPgDgSP6MnMtLxWJxcbNj6jPajons7KT3fofMJ5w+C7+pXw4aao7pKwnAq2azeEcplUqLSN6c4ZXzGsBlkon3fpMmHDX9rFEC4Ir6zDDGaJqmC6VLKMSuiS2yEb1tSZdRAHAMwNcWCtUb3dGu7hc75zYCeNgCsQ90LMkL/PNvaZTkj0ZF1l9Dem0tS15xzq0CsEcTE4DxgMhxTWjqo77SS7DbyzmtxgTTdri3oJ1hWtLqKUiWtcKRsfxeMgzDMAxD5sRvDkSrmgGIMQAAAAAASUVORK5CYII=",Cm="/assets/user_icon-BHC8joRr.png",Em="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAABYklEQVR4nO3ZMUoDQRgF4Kkka6feYttt/mLfm2IrCwtPo5VGj6Kd1xA9hEkXCB5CUAYMTGJiVGacZfd9sM1C8u9j3hLI75yIiIiIiGTVNM0hyVsAcwBvJN//+wpzP+dPzazKFtbMKgDPJUJ+cz1lCw3gpgcBt13XuQLPoyEXdV0fuALCXACXUcVnWQYhemdLhV0J8+N3OssQRjVyPZD9eajAZemEU6MqXZYqnRpV6bJU6dSoSpelSqdGVbosVTo1qtJlqdKpUZUuS5VOjWOrNMb2vzSizUP4579Hm4eXLIMATH+z89n2HWZ2TPI+8W7pKktgM6vCtu6vgUmekVymDAvg0Xs/cbl47ydhWxcWWPv2w6vPtG17BOAu8X54Fk42a9if2AxM8hTAYuOBX0meuyHgerAvpxruhffYDQV313A4pxrbEfah67oTN0Qcw6nGRnGqsVGcaiz85q7dEBEREbfHB6zlz1MolYoJAAAAAElFTkSuQmCC",km="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAACi0lEQVR4nO3ZW2sTQRQH8CNewBtV6+XBG+iDiPgUxSrimpzZmMzZNsnOHKoigi9+AgUR/Qwt6KfwC/jki/gkiH2RgqigoA+i4q1UUFsdsjFriZjLZtNuz+95k90zM2fmnw2AEEIIIYQQQggxeCrgk0qbK0rxEGQd0fnNisysIjuP2tyCrPOJa67YqOAJyDrUZrJRsCsesg61nYoKnvOCc1shy4pF3oLa/KwvZzsFy6x/J2EpKYye3YFkbmLAx5Pu3/qxxdfcioDFIF8a36/IPq8/vPnoed6qTvrXLWusVodbXTPCvBa1nYmW/XSxMr4bBkmV+TCSfdOYKUXmCQCs+N/nXIHt9C8zr2wOpp1X2rzMn+EDMAgY1I4qbd81+9BOe6XarqT7N6/Dvajt0z/Xk3nva3MM0uSXuaC0/dwcefuoVOJt/Tp/oz3icWymvyCFCtJQCMJqIw5Gxd7vNAd3c/56lcomJPugOdP2m9Im7LqQdiDZi4rs99jyuucxb4CUzt8gCNYpMndj7fBDBfYS9K1nyc7FRvgOM69J+/x193T3/qtoPX4QklYkziVRcK/5uVXB+VFzqNPv6XpJnxgb25hWfk51STegNpUFm9bDfwWHJPt3IJtWA5I5jWQ+xQNHgXgn9Kl/Fx5LSOZrocw+pEmVzREk8zZWwAsXM5Pu3xbB40MnmT1RbndEbV7FZvqZi4O95Oe4XO7yajeQsUF93bcNql0q4D0uVkZ9OeMCfy/5Oc73L6xv/HhwmRop3AeLAVarw4rMdfdTLun+9XV4CrW94fu17bCUoLy/yrCivL/KONRmYnm9fyZ7OzqnZ93fK5B1SvEQar6qSrWRQT+LEEIIIYQQQgjxewR+AUW4xI2gz4jWAAAAAElFTkSuQmCC",Rm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAACeklEQVR4nO2aPYgTQRiGx/MfUQtREAKRMDDzvpNdkGDlQUDEzvJasbK1EltLW0sr4UpbsbrWwsJG4QQLBRFEEBRB/OHUlQ82GiTHTeLs7uRmHviasDB5dmfne2cSpTKZTCaTiRvn3CWl1B6VCiQrki8AXO/3+4dUIsKVFIB3AG5Za0+oFIT5t76SXB8Oh1YlIlzV9RPAAwAXVSLC1VQ9sdZeGY/H+1QiwlVdr0jeHAwGx1UiwlVdn0jeKYqip5YJzpABcAPAGx9xAN8B3DPGFGoZ4AwJ+Xw0Gu0nuUbyse9TB/DIOXc56iDDbYT/uWaV5H2SPzzl4w0y9BCeulbLe0vyi+cTjy/IcA7hCWVZnpKVmuRbzyceT5DhAsITtNYHpTcDeO4p3n2Q4X8IT7EiEiITfZBhGOE/ADgr0xfAVpRBhoGFJ1hrT8uCBeBjVEGGDQlPMMYclRYF4LVvkJEW6Jw7p5ZReIq9Eko6DzKcMVDTCwnJCyQfkvzlKf+M5FWSB0IMXs24s1sAXpLcAHC37rlr1tqR1vpYEOuuggz928j0wB+krci7BuA2yWvSloqiGCwy/bTWJ1sLMlxAeIf6tt3s6PV6hz2DzGZjQYYtCu+0mVg0uTnnzi/VlJZsLu8myfee43+Wm2mMMfOOpfKixQTbkmqAaE5QGGe0XG/sjIwNCRtjzsiCtus3D1hgeygzoCzLI6oNGEZ4pd4YbOzqAwDdRlCIQbhsMyh0KcxUjmlJrtYHdF5BAcBTiZtLdRA/iiUohIazv3haP6YxpqAQGs4v2m5QCA1jDwqhYexBITSMPSiEhrEHhdAw9qAQGpfan0szmUwmk1Gd8Bv41GFkySRCBgAAAABJRU5ErkJggg==",ue={history_icon:vm,menu_icon:gm,plus_icon:ym,question_icon:xm,setting_icon:Nm,bulb_icon:jm,compass_icon:Am,gallery_icon:wm,mic_icon:Sm,user_icon:Cm,message_icon:Em,code_icon:km,send_icon:Rm};/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const io=["user","model","function","system"];var Xt;(function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT"})(Xt||(Xt={}));var Jt;(function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"})(Jt||(Jt={}));var ao;(function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"})(ao||(ao={}));var oo;(function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER"})(oo||(oo={}));var fs;(function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.OTHER="OTHER"})(fs||(fs={}));var co;(function(e){e.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",e.RETRIEVAL_QUERY="RETRIEVAL_QUERY",e.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",e.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",e.CLASSIFICATION="CLASSIFICATION",e.CLUSTERING="CLUSTERING"})(co||(co={}));var uo;(function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.AUTO="AUTO",e.ANY="ANY",e.NONE="NONE"})(uo||(uo={}));/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var fo;(function(e){e.STRING="STRING",e.NUMBER="NUMBER",e.INTEGER="INTEGER",e.BOOLEAN="BOOLEAN",e.ARRAY="ARRAY",e.OBJECT="OBJECT"})(fo||(fo={}));/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class fe extends Error{constructor(t){super(`[GoogleGenerativeAI Error]: ${t}`)}}class zt extends fe{constructor(t,n){super(t),this.response=n}}class mo extends fe{constructor(t,n,r,s){super(t),this.status=n,this.statusText=r,this.errorDetails=s}}class Br extends fe{}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Tm="https://generativelanguage.googleapis.com",Im="v1beta",_m="0.11.5",Pm="genai-js";var Ot;(function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.EMBED_CONTENT="embedContent",e.BATCH_EMBED_CONTENTS="batchEmbedContents"})(Ot||(Ot={}));class Du{constructor(t,n,r,s,i){this.model=t,this.task=n,this.apiKey=r,this.stream=s,this.requestOptions=i}toString(){var t,n;const r=((t=this.requestOptions)===null||t===void 0?void 0:t.apiVersion)||Im;let i=`${((n=this.requestOptions)===null||n===void 0?void 0:n.baseUrl)||Tm}/${r}/${this.model}:${this.task}`;return this.stream&&(i+="?alt=sse"),i}}function Om(e){const t=[];return e!=null&&e.apiClient&&t.push(e.apiClient),t.push(`${Pm}/${_m}`),t.join(" ")}async function Mm(e){const t=new Headers;t.append("Content-Type","application/json"),t.append("x-goog-api-client",Om(e.requestOptions)),t.append("x-goog-api-key",e.apiKey);let n=e.requestOptions.customHeaders;if(n){if(!(n instanceof Headers))try{n=new Headers(n)}catch(r){throw new Br(`unable to convert customHeaders value ${JSON.stringify(n)} to Headers: ${r.message}`)}for(const[r,s]of n.entries()){if(r==="x-goog-api-key")throw new Br(`Cannot set reserved header name ${r}`);if(r==="x-goog-api-client")throw new Br(`Header name ${r} can only be set using the apiClient field`);t.append(r,s)}}return t}async function Dm(e,t,n,r,s,i){const a=new Du(e,t,n,r,i);return{url:a.toString(),fetchOptions:Object.assign(Object.assign({},zm(i)),{method:"POST",headers:await Mm(a),body:s})}}async function or(e,t,n,r,s,i){return Lm(e,t,n,r,s,i,fetch)}async function Lm(e,t,n,r,s,i,a=fetch){const o=new Du(e,t,n,r,i);let c;try{const f=await Dm(e,t,n,r,s,i);if(c=await a(f.url,f.fetchOptions),!c.ok){let m="",u;try{const d=await c.json();m=d.error.message,d.error.details&&(m+=` ${JSON.stringify(d.error.details)}`,u=d.error.details)}catch{}throw new mo(`Error fetching from ${o.toString()}: [${c.status} ${c.statusText}] ${m}`,c.status,c.statusText,u)}}catch(f){let m=f;throw f instanceof mo||f instanceof Br||(m=new fe(`Error fetching from ${o.toString()}: ${f.message}`),m.stack=f.stack),m}return c}function zm(e){const t={};if((e==null?void 0:e.timeout)>=0){const n=new AbortController,r=n.signal;setTimeout(()=>n.abort(),e.timeout),t.signal=r}return t}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ki(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),Qr(e.candidates[0]))throw new zt(`${tt(e)}`,e);return Um(e)}else if(e.promptFeedback)throw new zt(`Text not available. ${tt(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),Qr(e.candidates[0]))throw new zt(`${tt(e)}`,e);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),ho(e)[0]}else if(e.promptFeedback)throw new zt(`Function call not available. ${tt(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),Qr(e.candidates[0]))throw new zt(`${tt(e)}`,e);return ho(e)}else if(e.promptFeedback)throw new zt(`Function call not available. ${tt(e)}`,e)},e}function Um(e){var t,n,r,s;const i=[];if(!((n=(t=e.candidates)===null||t===void 0?void 0:t[0].content)===null||n===void 0)&&n.parts)for(const a of(s=(r=e.candidates)===null||r===void 0?void 0:r[0].content)===null||s===void 0?void 0:s.parts)a.text&&i.push(a.text);return i.length>0?i.join(""):""}function ho(e){var t,n,r,s;const i=[];if(!((n=(t=e.candidates)===null||t===void 0?void 0:t[0].content)===null||n===void 0)&&n.parts)for(const a of(s=(r=e.candidates)===null||r===void 0?void 0:r[0].content)===null||s===void 0?void 0:s.parts)a.functionCall&&i.push(a.functionCall);if(i.length>0)return i}const Fm=[fs.RECITATION,fs.SAFETY];function Qr(e){return!!e.finishReason&&Fm.includes(e.finishReason)}function tt(e){var t,n,r;let s="";if((!e.candidates||e.candidates.length===0)&&e.promptFeedback)s+="Response was blocked",!((t=e.promptFeedback)===null||t===void 0)&&t.blockReason&&(s+=` due to ${e.promptFeedback.blockReason}`),!((n=e.promptFeedback)===null||n===void 0)&&n.blockReasonMessage&&(s+=`: ${e.promptFeedback.blockReasonMessage}`);else if(!((r=e.candidates)===null||r===void 0)&&r[0]){const i=e.candidates[0];Qr(i)&&(s+=`Candidate was blocked due to ${i.finishReason}`,i.finishMessage&&(s+=`: ${i.finishMessage}`))}return s}function nr(e){return this instanceof nr?(this.v=e,this):new nr(e)}function Bm(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),s,i=[];return s={},a("next"),a("throw"),a("return"),s[Symbol.asyncIterator]=function(){return this},s;function a(d){r[d]&&(s[d]=function(g){return new Promise(function(y,j){i.push([d,g,y,j])>1||o(d,g)})})}function o(d,g){try{c(r[d](g))}catch(y){u(i[0][3],y)}}function c(d){d.value instanceof nr?Promise.resolve(d.value.v).then(f,m):u(i[0][2],d)}function f(d){o("next",d)}function m(d){o("throw",d)}function u(d,g){d(g),i.shift(),i.length&&o(i[0][0],i[0][1])}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const po=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function Qm(e){const t=e.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0})),n=Gm(t),[r,s]=n.tee();return{stream:Vm(r),response:Hm(s)}}async function Hm(e){const t=[],n=e.getReader();for(;;){const{done:r,value:s}=await n.read();if(r)return Ki(Wm(t));t.push(s)}}function Vm(e){return Bm(this,arguments,function*(){const n=e.getReader();for(;;){const{value:r,done:s}=yield nr(n.read());if(s)break;yield yield nr(Ki(r))}})}function Gm(e){const t=e.getReader();return new ReadableStream({start(r){let s="";return i();function i(){return t.read().then(({value:a,done:o})=>{if(o){if(s.trim()){r.error(new fe("Failed to parse stream"));return}r.close();return}s+=a;let c=s.match(po),f;for(;c;){try{f=JSON.parse(c[1])}catch{r.error(new fe(`Error parsing JSON response: "${c[1]}"`));return}r.enqueue(f),s=s.substring(c[0].length),c=s.match(po)}return i()})}}})}function Wm(e){const t=e[e.length-1],n={promptFeedback:t==null?void 0:t.promptFeedback};for(const r of e)if(r.candidates)for(const s of r.candidates){const i=s.index;if(n.candidates||(n.candidates=[]),n.candidates[i]||(n.candidates[i]={index:s.index}),n.candidates[i].citationMetadata=s.citationMetadata,n.candidates[i].finishReason=s.finishReason,n.candidates[i].finishMessage=s.finishMessage,n.candidates[i].safetyRatings=s.safetyRatings,s.content&&s.content.parts){n.candidates[i].content||(n.candidates[i].content={role:s.content.role||"user",parts:[]});const a={};for(const o of s.content.parts)o.text&&(a.text=o.text),o.functionCall&&(a.functionCall=o.functionCall),Object.keys(a).length===0&&(a.text=""),n.candidates[i].content.parts.push(a)}}return n}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Lu(e,t,n,r){const s=await or(t,Ot.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(n),r);return Qm(s)}async function zu(e,t,n,r){const i=await(await or(t,Ot.GENERATE_CONTENT,e,!1,JSON.stringify(n),r)).json();return{response:Ki(i)}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Uu(e){if(e!=null){if(typeof e=="string")return{role:"system",parts:[{text:e}]};if(e.text)return{role:"system",parts:[e]};if(e.parts)return e.role?e:{role:"system",parts:e.parts}}}function ms(e){let t=[];if(typeof e=="string")t=[{text:e}];else for(const n of e)typeof n=="string"?t.push({text:n}):t.push(n);return Ym(t)}function Ym(e){const t={role:"user",parts:[]},n={role:"function",parts:[]};let r=!1,s=!1;for(const i of e)"functionResponse"in i?(n.parts.push(i),s=!0):(t.parts.push(i),r=!0);if(r&&s)throw new fe("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!r&&!s)throw new fe("No content is provided for sending chat message.");return r?t:n}function ll(e){let t;return e.contents?t=e:t={contents:[ms(e)]},e.systemInstruction&&(t.systemInstruction=Uu(e.systemInstruction)),t}function qm(e){return typeof e=="string"||Array.isArray(e)?{content:ms(e)}:e}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const vo=["text","inlineData","functionCall","functionResponse"],Km={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall"],system:["text"]};function $m(e){let t=!1;for(const n of e){const{role:r,parts:s}=n;if(!t&&r!=="user")throw new fe(`First content should be with role 'user', got ${r}`);if(!io.includes(r))throw new fe(`Each item should include role field. Got ${r} but valid roles are: ${JSON.stringify(io)}`);if(!Array.isArray(s))throw new fe("Content should have 'parts' property with an array of Parts");if(s.length===0)throw new fe("Each Content should have at least one part");const i={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0};for(const o of s)for(const c of vo)c in o&&(i[c]+=1);const a=Km[r];for(const o of vo)if(!a.includes(o)&&i[o]>0)throw new fe(`Content with role '${r}' can't contain '${o}' part`);t=!0}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const go="SILENT_ERROR";class Xm{constructor(t,n,r,s){this.model=n,this.params=r,this.requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=t,r!=null&&r.history&&($m(r.history),this._history=r.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(t){var n,r,s,i,a;await this._sendPromise;const o=ms(t),c={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(r=this.params)===null||r===void 0?void 0:r.generationConfig,tools:(s=this.params)===null||s===void 0?void 0:s.tools,toolConfig:(i=this.params)===null||i===void 0?void 0:i.toolConfig,systemInstruction:(a=this.params)===null||a===void 0?void 0:a.systemInstruction,contents:[...this._history,o]};let f;return this._sendPromise=this._sendPromise.then(()=>zu(this._apiKey,this.model,c,this.requestOptions)).then(m=>{var u;if(m.response.candidates&&m.response.candidates.length>0){this._history.push(o);const d=Object.assign({parts:[],role:"model"},(u=m.response.candidates)===null||u===void 0?void 0:u[0].content);this._history.push(d)}else{const d=tt(m.response);d&&console.warn(`sendMessage() was unsuccessful. ${d}. Inspect response object for details.`)}f=m}),await this._sendPromise,f}async sendMessageStream(t){var n,r,s,i,a;await this._sendPromise;const o=ms(t),c={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(r=this.params)===null||r===void 0?void 0:r.generationConfig,tools:(s=this.params)===null||s===void 0?void 0:s.tools,toolConfig:(i=this.params)===null||i===void 0?void 0:i.toolConfig,systemInstruction:(a=this.params)===null||a===void 0?void 0:a.systemInstruction,contents:[...this._history,o]},f=Lu(this._apiKey,this.model,c,this.requestOptions);return this._sendPromise=this._sendPromise.then(()=>f).catch(m=>{throw new Error(go)}).then(m=>m.response).then(m=>{if(m.candidates&&m.candidates.length>0){this._history.push(o);const u=Object.assign({},m.candidates[0].content);u.role||(u.role="model"),this._history.push(u)}else{const u=tt(m);u&&console.warn(`sendMessageStream() was unsuccessful. ${u}. Inspect response object for details.`)}}).catch(m=>{m.message!==go&&console.error(m)}),f}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Jm(e,t,n,r){return(await or(t,Ot.COUNT_TOKENS,e,!1,JSON.stringify(Object.assign(Object.assign({},n),{model:t})),r)).json()}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Zm(e,t,n,r){return(await or(t,Ot.EMBED_CONTENT,e,!1,JSON.stringify(n),r)).json()}async function bm(e,t,n,r){const s=n.requests.map(a=>Object.assign(Object.assign({},a),{model:t}));return(await or(t,Ot.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:s}),r)).json()}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class eh{constructor(t,n,r){this.apiKey=t,n.model.includes("/")?this.model=n.model:this.model=`models/${n.model}`,this.generationConfig=n.generationConfig||{},this.safetySettings=n.safetySettings||[],this.tools=n.tools,this.toolConfig=n.toolConfig,this.systemInstruction=Uu(n.systemInstruction),this.requestOptions=r||{}}async generateContent(t){const n=ll(t);return zu(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction},n),this.requestOptions)}async generateContentStream(t){const n=ll(t);return Lu(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction},n),this.requestOptions)}startChat(t){return new Xm(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction},t),this.requestOptions)}async countTokens(t){const n=ll(t);return Jm(this.apiKey,this.model,n,this.requestOptions)}async embedContent(t){const n=qm(t);return Zm(this.apiKey,this.model,n,this.requestOptions)}async batchEmbedContents(t){return bm(this.apiKey,this.model,t,this.requestOptions)}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class th{constructor(t){this.apiKey=t}getGenerativeModel(t,n){if(!t.model)throw new fe("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new eh(this.apiKey,t,n)}}const nh=void 0,rh=new th(nh);rh.getGenerativeModel({model:"gemini-1.5-pro-latest"});Xt.HARM_CATEGORY_HARASSMENT,Jt.BLOCK_MEDIUM_AND_ABOVE,Xt.HARM_CATEGORY_HATE_SPEECH,Jt.BLOCK_MEDIUM_AND_ABOVE,Xt.HARM_CATEGORY_SEXUALLY_EXPLICIT,Jt.BLOCK_MEDIUM_AND_ABOVE,Xt.HARM_CATEGORY_DANGEROUS_CONTENT,Jt.BLOCK_MEDIUM_AND_ABOVE;async function yo(e){return"This is a demo response. Please configure your Gemini API key to get real responses from the HR chatbot."}const $i=A.createContext(),sh=e=>{const[t,n]=A.useState(""),[r,s]=A.useState(""),[i,a]=A.useState([]),[o,c]=A.useState(!1),[f,m]=A.useState(!1),[u,d]=A.useState(""),g=(p,h)=>{setTimeout(function(){d(v=>v+h)},75*p)},M={prevPrompts:i,setPrevPrompts:a,onSent:async p=>{d(""),m(!0),c(!0);let h;p!==void 0?(h=await yo(),s(p)):(a(w=>[...w,t]),s(t),h=await yo());let v=h.split("**"),x="";for(let w=0;w<v.length;w++)w===0||w%2!==1?x+=v[w]:x+="<b>"+v[w]+"</b>";let k=x.split("*").join("</br>").split(" ");for(let w=0;w<k.length;w++){const R=k[w];g(w,R+" ")}m(!1),n("")},recentPrompt:r,setRecentPrompt:s,showResult:o,loading:f,resultData:u,input:t,setInput:n,newChat:()=>{m(!1),c(!1)}};return l.jsx($i.Provider,{value:M,children:e.children})},lh=({onAccessibilityClick:e,onAdminClick:t})=>{const[n,r]=A.useState(!1),{onSent:s,prevPrompts:i,setRecentPrompt:a,newChat:o}=A.useContext($i),c=async f=>{a(f),await s(f)};return l.jsxs("aside",{className:`sidebar ${n?"extended":"collapsed"}`,children:[l.jsxs("div",{className:`top  ${n?"":"centered"}`,children:[l.jsx("div",{className:"menu",onClick:()=>r(f=>!f),children:l.jsx("img",{src:ue.menu_icon,alt:"Menu Icon"})}),l.jsxs("div",{onClick:()=>o(),className:"new-chat",children:[l.jsx("img",{src:ue.plus_icon,alt:"Plus Icon"}),l.jsx("p",{className:`${n?"block":"none"}`,children:"New Chat"})]}),n?l.jsxs("div",{className:"recent",children:[l.jsx("p",{className:"recent-title",children:"Recent"}),i.map((f,m)=>l.jsxs("div",{onClick:()=>c(f),className:"recent-entry",children:[l.jsx("img",{src:ue.message_icon,alt:""}),l.jsxs("p",{className:"recent-entry-p",children:[f.slice(0,18)," ..."]})]},m))]}):null]}),l.jsxs("div",{className:`bottom  ${n?"":"centered"}`,children:[l.jsxs("div",{className:"bottom-item recent-entry",children:[l.jsx("img",{src:ue.question_icon,alt:"Question Icon"}),l.jsx("p",{className:`fade ${n?"block":"none"}`,children:"Help"})]}),l.jsxs("div",{className:"bottom-item recent-entry",children:[l.jsx("img",{src:ue.history_icon,alt:"History Icon"}),l.jsx("p",{className:`fade ${n?"block":"none"}`,children:"Activity"})]}),l.jsxs("div",{className:"bottom-item recent-entry",children:[l.jsx("img",{src:ue.setting_icon,alt:"Settings Icon"}),l.jsx("p",{className:`fade ${n?"block":"none"}`,children:"Settings"})]}),l.jsxs("div",{className:"bottom-item recent-entry",onClick:e,children:[l.jsx("span",{className:"accessibility-icon",children:"♿"}),l.jsx("p",{className:`fade ${n?"block":"none"}`,children:"Accessibility"})]}),l.jsxs("div",{className:"bottom-item recent-entry",onClick:t,children:[l.jsx("span",{className:"admin-icon",children:"⚙️"}),l.jsx("p",{className:`fade ${n?"block":"none"}`,children:"Admin"})]})]})]})},ih="http://localhost:8000";class ah{constructor(){this.baseURL=ih,this.token=localStorage.getItem("auth_token")}setToken(t){this.token=t,localStorage.setItem("auth_token",t)}clearToken(){this.token=null,localStorage.removeItem("auth_token")}async request(t,n={}){const r=`${this.baseURL}${t}`,s={headers:{"Content-Type":"application/json",...n.headers},...n};this.token&&(s.headers.Authorization=`Bearer ${this.token}`);try{const i=await fetch(r,s);if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);return await i.json()}catch(i){throw console.error("API request failed:",i),i}}async login(t,n){const r=await this.request("/auth/login",{method:"POST",body:JSON.stringify({username:t,password:n})});return r.access_token&&this.setToken(r.access_token),r}async register(t,n,r,s="employee"){const i=await this.request("/auth/register",{method:"POST",body:JSON.stringify({username:t,email:n,password:r,role:s})});return i.access_token&&this.setToken(i.access_token),i}async logout(){this.clearToken()}async sendMessage(t,n=null){return await this.request("/api/chat/message",{method:"POST",body:JSON.stringify({message:t,context:n})})}async getTools(){return await this.request("/api/chat/tools")}async executeTool(t,n){return await this.request(`/api/chat/tools/${t}`,{method:"POST",body:JSON.stringify(n)})}async healthCheck(){return await this.request("/health")}}const Fu=new ah,Bu=()=>{const[e,t]=A.useState([]),[n,r]=A.useState(""),[s,i]=A.useState(!1),[a,o]=A.useState(!1),c=A.useRef(null);A.useEffect(()=>{const d=localStorage.getItem("auth_token");o(!!d),d&&t([{id:1,text:"Hello! I'm CHaBot, your AI assistant. How can I help you today?",sender:"bot",timestamp:new Date}])},[]),A.useEffect(()=>{f()},[e]);const f=()=>{var d;(d=c.current)==null||d.scrollIntoView({behavior:"smooth"})},m=async()=>{if(!n.trim()||s)return;const d={id:Date.now(),text:n,sender:"user",timestamp:new Date};t(g=>[...g,d]),r(""),i(!0);try{const g=await Fu.sendMessage(n),y={id:Date.now()+1,text:g.response,sender:"bot",timestamp:new Date,confidence:g.confidence,reasoning:g.reasoning_steps};t(j=>[...j,y])}catch{const y={id:Date.now()+1,text:"Sorry, I encountered an error. Please try again.",sender:"bot",timestamp:new Date,isError:!0};t(j=>[...j,y])}finally{i(!1)}},u=d=>{d.key==="Enter"&&!d.shiftKey&&(d.preventDefault(),m())};return a?l.jsxs("div",{className:"chat-container",children:[l.jsx("div",{className:"chat-header",children:l.jsx("h2",{children:"CHaBot - AI Assistant"})}),l.jsxs("div",{className:"chat-messages",children:[e.map(d=>l.jsxs("div",{className:`message ${d.sender}`,children:[l.jsxs("div",{className:"message-content",children:[l.jsx("p",{children:d.text}),d.confidence&&l.jsx("div",{className:"message-meta",children:l.jsxs("span",{children:["Confidence: ",(d.confidence*100).toFixed(1),"%"]})}),d.reasoning&&d.reasoning.length>0&&l.jsxs("details",{className:"reasoning-details",children:[l.jsx("summary",{children:"Reasoning Steps"}),l.jsx("ul",{children:d.reasoning.map((g,y)=>l.jsx("li",{children:g.step},y))})]})]}),l.jsx("div",{className:"message-time",children:d.timestamp.toLocaleTimeString()})]},d.id)),s&&l.jsx("div",{className:"message bot loading",children:l.jsx("div",{className:"message-content",children:l.jsx("p",{children:"Thinking..."})})}),l.jsx("div",{ref:c})]}),l.jsxs("div",{className:"chat-input",children:[l.jsx("textarea",{value:n,onChange:d=>r(d.target.value),onKeyPress:u,placeholder:"Type your message here...",disabled:s}),l.jsx("button",{onClick:m,disabled:s||!n.trim(),children:"Send"})]})]}):l.jsx("div",{className:"chat-container",children:l.jsxs("div",{className:"auth-required",children:[l.jsx("h3",{children:"Authentication Required"}),l.jsx("p",{children:"Please log in to use CHaBot"})]})})},oh=()=>{const{onSent:e,recentPrompt:t,showResult:n,loading:r,resultData:s,setInput:i,input:a}=A.useContext($i),o=A.useRef(null),[c,f]=A.useState(1);return A.useEffect(()=>{const m=()=>{window.innerWidth<=600?f(2):f(1)};return m(),window.addEventListener("resize",m),()=>window.removeEventListener("resize",m)},[]),A.useEffect(()=>{o.current&&(o.current.scrollTop=o.current.scrollHeight)},[s]),l.jsx("main",{className:"main",children:n?l.jsx(Bu,{}):l.jsxs(l.Fragment,{children:[l.jsxs("nav",{className:"nav",children:[l.jsx("p",{children:"CHaBot"}),l.jsx("img",{src:ue.user_icon,alt:""})]}),l.jsxs("div",{className:"main-container",children:[l.jsxs("div",{className:"greet",children:[l.jsx("p",{children:l.jsx("span",{children:"Hello, Welcome to CHaBot"})}),l.jsx("p",{children:"How can I help you with HR queries today?"})]}),l.jsxs("div",{className:"cards",children:[l.jsxs("div",{className:"card",onClick:()=>i("What is the privilege leave policy at NUVO AI?"),children:[l.jsx("p",{children:"What is the privilege leave policy at NUVO AI?"}),l.jsx("img",{src:ue.compass_icon,alt:""})]}),l.jsxs("div",{className:"card",onClick:()=>i("How do I apply for medical leave?"),children:[l.jsx("p",{children:"How do I apply for medical leave?"}),l.jsx("img",{src:ue.bulb_icon,alt:""})]}),l.jsxs("div",{className:"card",onClick:()=>i("What are the employee benefits available?"),children:[l.jsx("p",{children:"What are the employee benefits available?"}),l.jsx("img",{src:ue.message_icon,alt:""})]}),l.jsxs("div",{className:"card",onClick:()=>i("Tell me about the performance review process"),children:[l.jsx("p",{children:"Tell me about the performance review process"}),l.jsx("img",{src:ue.code_icon,alt:""})]})]}),l.jsxs("div",{className:"main-bottom",children:[l.jsxs("div",{className:"search-box",children:[l.jsx("textarea",{rows:c,onChange:m=>i(m.target.value),onKeyUp:m=>{m.key==="Enter"&&e()},value:a,type:"text",placeholder:"Ask about HR policies, leave, benefits..."}),l.jsxs("div",{className:"icon-container",children:[l.jsx("button",{children:l.jsx("img",{src:ue.gallery_icon,alt:""})}),l.jsx("button",{children:l.jsx("img",{src:ue.mic_icon,alt:""})}),l.jsx("button",{type:"submit",onClick:()=>e(),children:l.jsx("img",{src:ue.send_icon,alt:""})})]})]}),l.jsxs("p",{className:"bottom-info",children:["CHaBot provides HR information based on company policies. Please verify important details.",l.jsx("a",{href:"#",children:"Privacy Policy"})]})]})]})]})})},ch="modulepreload",uh=function(e){return"/"+e},xo={},Qu=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),o=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));s=Promise.allSettled(n.map(c=>{if(c=uh(c),c in xo)return;xo[c]=!0;const f=c.endsWith(".css"),m=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${m}`))return;const u=document.createElement("link");if(u.rel=f?"stylesheet":ch,f||(u.as="script"),u.crossOrigin="",u.href=c,o&&u.setAttribute("nonce",o),document.head.appendChild(u),f)return new Promise((d,g)=>{u.addEventListener("load",d),u.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${c}`)))})}))}function i(a){const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=a,window.dispatchEvent(o),!o.defaultPrevented)throw a}return s.then(a=>{for(const o of a||[])o.status==="rejected"&&i(o.reason);return t().catch(i)})},Cn={mobile:"(max-width: 767px)",tablet:"(min-width: 768px) and (max-width: 1023px)",desktop:"(min-width: 1024px)",touch:"(pointer: coarse)",hover:"(hover: hover)"},dh=()=>{const e=window.matchMedia(Cn.mobile).matches,t=window.matchMedia(Cn.tablet).matches,n=window.matchMedia(Cn.desktop).matches,r=window.matchMedia(Cn.touch).matches,s=window.matchMedia(Cn.hover).matches;return{isMobile:e,isTablet:t,isDesktop:n,isTouch:r,canHover:s,screenSize:e?"mobile":t?"tablet":"desktop"}};class fh{constructor(){this.dbName="CHaBotDB",this.version=1,this.storeName="messages",this.db=null}async init(){return new Promise((t,n)=>{const r=indexedDB.open(this.dbName,this.version);r.onerror=()=>n(r.error),r.onsuccess=()=>{this.db=r.result,t()},r.onupgradeneeded=s=>{const i=s.target.result;i.objectStoreNames.contains(this.storeName)||i.createObjectStore(this.storeName,{keyPath:"id"})}})}async addMessage(t){this.db||await this.init();const n={...t,id:Date.now()+Math.random(),timestamp:new Date().toISOString(),synced:!1};return new Promise((r,s)=>{const o=this.db.transaction([this.storeName],"readwrite").objectStore(this.storeName).add(n);o.onerror=()=>s(o.error),o.onsuccess=()=>r(n)})}async getUnsynced(){return this.db||await this.init(),new Promise((t,n)=>{const i=this.db.transaction([this.storeName],"readonly").objectStore(this.storeName).getAll();i.onerror=()=>n(i.error),i.onsuccess=()=>{const a=i.result.filter(o=>!o.synced);t(a)}})}async markSynced(t){return this.db||await this.init(),new Promise((n,r)=>{const i=this.db.transaction([this.storeName],"readwrite").objectStore(this.storeName),a=i.get(t);a.onsuccess=()=>{const o=a.result;if(o){o.synced=!0;const c=i.put(o);c.onsuccess=()=>n(),c.onerror=()=>r(c.error)}else n()},a.onerror=()=>r(a.error)})}async syncMessages(){const t=await this.getUnsynced(),n=[];for(const r of t)try{(await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok?(await this.markSynced(r.id),n.push({success:!0,message:r})):n.push({success:!1,message:r,error:"Server error"})}catch(s){n.push({success:!1,message:r,error:s.message})}return n}isOnline(){return navigator.onLine}async handleMessage(t){if(this.isOnline())try{return(await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok?{success:!0,online:!0}:(await this.addMessage(t),{success:!0,online:!1,queued:!0})}catch{return await this.addMessage(t),{success:!0,online:!1,queued:!0}}else return await this.addMessage(t),{success:!0,online:!1,queued:!0}}}const il=new fh,mh=A.createContext({isMobile:!1,isTablet:!1,isTouch:!1,screenSize:"desktop",onMessageSend:null}),hh=({children:e,value:t})=>l.jsx(mh.Provider,{value:t,children:e});A.lazy(()=>Qu(()=>import("./ReasoningVisualization-4nmGbrd7.js"),__vite__mapDeps([0,1])));A.lazy(()=>Qu(()=>import("./AgentInteractionDisplay-DZ6fEyFp.js"),__vite__mapDeps([2,3])));const ph=({children:e})=>{const{isMobile:t,isTablet:n,isTouch:r,screenSize:s}=dh(),[i,a]=A.useState(navigator.onLine),[o,c]=A.useState(0);A.useEffect(()=>{const d=()=>{a(!0),f()},g=()=>a(!1);return window.addEventListener("online",d),window.addEventListener("offline",g),m(),()=>{window.removeEventListener("online",d),window.removeEventListener("offline",g)}},[]);const f=async()=>{try{const g=(await il.syncMessages()).filter(y=>y.success).length;c(y=>Math.max(0,y-g))}catch(d){console.log("Sync failed:",d)}},m=async()=>{try{const d=await il.getUnsynced();c(d.length)}catch(d){console.log("Failed to check pending messages:",d)}},u=async d=>{const g=await il.handleMessage(d);return g.queued&&c(y=>y+1),g};return l.jsxs("div",{className:`mobile-optimized ${s}`,"data-touch":r,children:[l.jsxs("div",{className:`connection-status ${i?"online":"offline"}`,children:[l.jsx("span",{className:"status-indicator"}),l.jsx("span",{className:"status-text",children:i?"Online":"Offline"}),o>0&&l.jsxs("span",{className:"pending-sync",children:[o," pending"]})]}),l.jsx("div",{className:"mobile-content",children:l.jsx(A.Suspense,{fallback:l.jsx("div",{className:"loading-placeholder",children:"Loading..."}),children:l.jsx(hh,{value:{isMobile:t,isTablet:n,isTouch:r,screenSize:s,onMessageSend:u},children:e})})})]})},vh=()=>{const[e,t]=A.useState(!1),[n,r]=A.useState("medium"),[s,i]=A.useState(!1),[a,o]=A.useState(!1);A.useEffect(()=>{const d=localStorage.getItem("accessibility-high-contrast")==="true",g=localStorage.getItem("accessibility-font-size")||"medium",y=localStorage.getItem("accessibility-reduced-motion")==="true";t(d),r(g),i(y);const j=window.matchMedia("(prefers-reduced-motion: reduce)").matches,M=window.matchMedia("(prefers-contrast: high)").matches;j&&i(!0),M&&t(!0);const p=window.navigator.userAgent.includes("NVDA")||window.navigator.userAgent.includes("JAWS")||window.speechSynthesis;o(p),c()},[]);const c=()=>{const d=document.documentElement;d.classList.toggle("high-contrast",e),d.classList.toggle("reduced-motion",s),d.className=d.className.replace(/font-size-\w+/,""),d.classList.add(`font-size-${n}`)};return{highContrast:e,fontSize:n,reducedMotion:s,screenReader:a,toggleHighContrast:()=>{const d=!e;t(d),localStorage.setItem("accessibility-high-contrast",d),c()},changeFontSize:d=>{r(d),localStorage.setItem("accessibility-font-size",d),c()},toggleReducedMotion:()=>{const d=!s;i(d),localStorage.setItem("accessibility-reduced-motion",d),c()}}},gh=({isVisible:e,onClose:t})=>{const{highContrast:n,fontSize:r,reducedMotion:s,screenReader:i,toggleHighContrast:a,changeFontSize:o,toggleReducedMotion:c}=vh();return e?l.jsx("div",{className:"accessibility-panel-overlay",role:"dialog","aria-labelledby":"accessibility-title",children:l.jsxs("div",{className:"accessibility-panel",children:[l.jsxs("div",{className:"panel-header",children:[l.jsx("h3",{id:"accessibility-title",children:"Accessibility Settings"}),l.jsx("button",{className:"close-button",onClick:t,"aria-label":"Close accessibility panel",children:"×"})]}),l.jsxs("div",{className:"panel-content",children:[l.jsxs("div",{className:"setting-group",children:[l.jsx("h4",{children:"Visual Settings"}),l.jsxs("div",{className:"setting-item",children:[l.jsxs("label",{className:"setting-label",children:[l.jsx("input",{type:"checkbox",checked:n,onChange:a,"aria-describedby":"high-contrast-desc"}),"High Contrast Mode"]}),l.jsx("p",{id:"high-contrast-desc",className:"setting-description",children:"Increases contrast for better visibility"})]}),l.jsxs("div",{className:"setting-item",children:[l.jsx("label",{className:"setting-label",children:"Font Size"}),l.jsx("div",{className:"font-size-controls",role:"radiogroup","aria-labelledby":"font-size-label",children:["small","medium","large","extra-large"].map(f=>l.jsxs("label",{className:"radio-label",children:[l.jsx("input",{type:"radio",name:"fontSize",value:f,checked:r===f,onChange:()=>o(f)}),f.charAt(0).toUpperCase()+f.slice(1).replace("-"," ")]},f))})]})]}),l.jsxs("div",{className:"setting-group",children:[l.jsx("h4",{children:"Motion Settings"}),l.jsxs("div",{className:"setting-item",children:[l.jsxs("label",{className:"setting-label",children:[l.jsx("input",{type:"checkbox",checked:s,onChange:c,"aria-describedby":"reduced-motion-desc"}),"Reduce Motion"]}),l.jsx("p",{id:"reduced-motion-desc",className:"setting-description",children:"Minimizes animations and transitions"})]})]}),l.jsxs("div",{className:"setting-group",children:[l.jsx("h4",{children:"Screen Reader"}),l.jsxs("div",{className:"setting-item",children:[l.jsxs("p",{className:"screen-reader-status",children:["Status: ",i?"Detected":"Not detected"]}),l.jsx("p",{className:"setting-description",children:"Screen reader compatibility is automatically enabled"})]})]}),l.jsxs("div",{className:"setting-group",children:[l.jsx("h4",{children:"Keyboard Navigation"}),l.jsx("div",{className:"keyboard-help",children:l.jsxs("ul",{children:[l.jsxs("li",{children:[l.jsx("kbd",{children:"Tab"})," - Navigate forward"]}),l.jsxs("li",{children:[l.jsx("kbd",{children:"Shift + Tab"})," - Navigate backward"]}),l.jsxs("li",{children:[l.jsx("kbd",{children:"Enter"})," or ",l.jsx("kbd",{children:"Space"})," - Activate"]}),l.jsxs("li",{children:[l.jsx("kbd",{children:"Escape"})," - Close dialogs"]}),l.jsxs("li",{children:[l.jsx("kbd",{children:"Arrow keys"})," - Navigate lists"]})]})})]})]})]})}):null},No=()=>{const[e,t]=A.useState([{id:1,name:"NUVO AI",departments:5,employees:150,status:"active"},{id:2,name:"Tech Corp",departments:3,employees:80,status:"active"}]),[n,r]=A.useState(!1),[s,i]=A.useState({name:"",description:"",domain:""}),a=c=>{c.preventDefault();const f={id:Date.now(),name:s.name,departments:0,employees:0,status:"active"};t([...e,f]),i({name:"",description:"",domain:""}),r(!1)},o=c=>{t(f=>f.map(m=>m.id===c?{...m,status:m.status==="active"?"inactive":"active"}:m))};return l.jsxs("div",{className:"organization-management",children:[l.jsxs("div",{className:"management-header",children:[l.jsx("h2",{children:"Organization Management"}),l.jsx("button",{className:"add-button",onClick:()=>r(!0),children:"+ Add Organization"})]}),l.jsx("div",{className:"organizations-grid",children:e.map(c=>l.jsxs("div",{className:`org-card ${c.status}`,children:[l.jsxs("div",{className:"org-header",children:[l.jsx("h3",{children:c.name}),l.jsx("span",{className:`status-badge ${c.status}`,children:c.status})]}),l.jsxs("div",{className:"org-stats",children:[l.jsxs("div",{className:"stat",children:[l.jsx("span",{className:"stat-value",children:c.departments}),l.jsx("span",{className:"stat-label",children:"Departments"})]}),l.jsxs("div",{className:"stat",children:[l.jsx("span",{className:"stat-value",children:c.employees}),l.jsx("span",{className:"stat-label",children:"Employees"})]})]}),l.jsxs("div",{className:"org-actions",children:[l.jsx("button",{className:"edit-btn",children:"Edit"}),l.jsx("button",{className:"toggle-btn",onClick:()=>o(c.id),children:c.status==="active"?"Deactivate":"Activate"})]})]},c.id))}),n&&l.jsx("div",{className:"modal-overlay",children:l.jsxs("div",{className:"modal",children:[l.jsxs("div",{className:"modal-header",children:[l.jsx("h3",{children:"Add Organization"}),l.jsx("button",{onClick:()=>r(!1),children:"×"})]}),l.jsxs("form",{onSubmit:a,children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Organization Name"}),l.jsx("input",{type:"text",value:s.name,onChange:c=>i({...s,name:c.target.value}),required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Description"}),l.jsx("textarea",{value:s.description,onChange:c=>i({...s,description:c.target.value})})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Domain"}),l.jsx("input",{type:"text",value:s.domain,onChange:c=>i({...s,domain:c.target.value}),placeholder:"company.com"})]}),l.jsxs("div",{className:"form-actions",children:[l.jsx("button",{type:"button",onClick:()=>r(!1),children:"Cancel"}),l.jsx("button",{type:"submit",children:"Create Organization"})]})]})]})})]})},yh=()=>{const[e,t]=A.useState([{id:1,name:"Human Resources",employees:25,policies:12,status:"active"},{id:2,name:"Engineering",employees:80,policies:8,status:"active"},{id:3,name:"Marketing",employees:15,policies:6,status:"active"},{id:4,name:"Finance",employees:12,policies:15,status:"inactive"}]),[n,r]=A.useState(!1),[s,i]=A.useState({name:"",description:"",manager:""}),a=c=>{c.preventDefault();const f={id:Date.now(),name:s.name,employees:0,policies:0,status:"active"};t([...e,f]),i({name:"",description:"",manager:""}),r(!1)},o=c=>{t(f=>f.map(m=>m.id===c?{...m,status:m.status==="active"?"inactive":"active"}:m))};return l.jsxs("div",{className:"department-config",children:[l.jsxs("div",{className:"config-header",children:[l.jsx("h2",{children:"Department Configuration"}),l.jsx("button",{className:"add-button",onClick:()=>r(!0),children:"+ Add Department"})]}),l.jsxs("div",{className:"departments-table",children:[l.jsxs("div",{className:"table-header",children:[l.jsx("div",{className:"header-cell",children:"Department"}),l.jsx("div",{className:"header-cell",children:"Employees"}),l.jsx("div",{className:"header-cell",children:"Policies"}),l.jsx("div",{className:"header-cell",children:"Status"}),l.jsx("div",{className:"header-cell",children:"Actions"})]}),e.map(c=>l.jsxs("div",{className:`table-row ${c.status}`,children:[l.jsx("div",{className:"cell dept-name",children:l.jsx("div",{className:"dept-info",children:l.jsx("span",{className:"name",children:c.name})})}),l.jsx("div",{className:"cell",children:c.employees}),l.jsx("div",{className:"cell",children:c.policies}),l.jsx("div",{className:"cell",children:l.jsx("span",{className:`status-badge ${c.status}`,children:c.status})}),l.jsxs("div",{className:"cell actions",children:[l.jsx("button",{className:"action-btn edit",children:"Edit"}),l.jsx("button",{className:"action-btn toggle",onClick:()=>o(c.id),children:c.status==="active"?"Disable":"Enable"})]})]},c.id))]}),n&&l.jsx("div",{className:"modal-overlay",children:l.jsxs("div",{className:"modal",children:[l.jsxs("div",{className:"modal-header",children:[l.jsx("h3",{children:"Add Department"}),l.jsx("button",{onClick:()=>r(!1),children:"×"})]}),l.jsxs("form",{onSubmit:a,children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Department Name"}),l.jsx("input",{type:"text",value:s.name,onChange:c=>i({...s,name:c.target.value}),required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Description"}),l.jsx("textarea",{value:s.description,onChange:c=>i({...s,description:c.target.value})})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Department Manager"}),l.jsx("input",{type:"text",value:s.manager,onChange:c=>i({...s,manager:c.target.value})})]}),l.jsxs("div",{className:"form-actions",children:[l.jsx("button",{type:"button",onClick:()=>r(!1),children:"Cancel"}),l.jsx("button",{type:"submit",children:"Create Department"})]})]})]})})]})},xh=()=>{const[e,t]=A.useState([{id:1,name:"Admin",users:2,permissions:["read","write","delete","manage_users","manage_content"],description:"Full system access"},{id:2,name:"HR Manager",users:5,permissions:["read","write","manage_content"],description:"HR content management"},{id:3,name:"Employee",users:143,permissions:["read"],description:"Basic read access"}]),[n,r]=A.useState(!1),[s,i]=A.useState(null),[a,o]=A.useState({name:"",description:"",permissions:[]}),c=[{id:"read",name:"Read Content",description:"View content and conversations"},{id:"write",name:"Write Content",description:"Create and edit content"},{id:"delete",name:"Delete Content",description:"Remove content and data"},{id:"manage_users",name:"Manage Users",description:"Add, edit, and remove users"},{id:"manage_content",name:"Manage Content",description:"Approve and organize content"},{id:"manage_agents",name:"Manage Agents",description:"Configure and deploy agents"},{id:"view_analytics",name:"View Analytics",description:"Access reports and analytics"}],f=d=>{d.preventDefault();const g={id:Date.now(),name:a.name,users:0,permissions:a.permissions,description:a.description};t([...e,g]),o({name:"",description:"",permissions:[]}),r(!1)},m=d=>{o(g=>({...g,permissions:g.permissions.includes(d)?g.permissions.filter(y=>y!==d):[...g.permissions,d]}))},u=d=>{i(d),o({name:d.name,description:d.description,permissions:d.permissions}),r(!0)};return l.jsxs("div",{className:"role-permissions",children:[l.jsxs("div",{className:"permissions-header",children:[l.jsx("h2",{children:"Role & Permission Management"}),l.jsx("button",{className:"add-button",onClick:()=>r(!0),children:"+ Add Role"})]}),l.jsx("div",{className:"roles-grid",children:e.map(d=>l.jsxs("div",{className:"role-card",children:[l.jsxs("div",{className:"role-header",children:[l.jsx("h3",{children:d.name}),l.jsxs("span",{className:"user-count",children:[d.users," users"]})]}),l.jsx("p",{className:"role-description",children:d.description}),l.jsxs("div",{className:"permissions-list",children:[l.jsx("h4",{children:"Permissions:"}),l.jsx("div",{className:"permission-tags",children:d.permissions.map(g=>{var y;return l.jsx("span",{className:"permission-tag",children:((y=c.find(j=>j.id===g))==null?void 0:y.name)||g},g)})})]}),l.jsxs("div",{className:"role-actions",children:[l.jsx("button",{className:"edit-btn",onClick:()=>u(d),children:"Edit Role"}),l.jsx("button",{className:"users-btn",children:"Manage Users"})]})]},d.id))}),n&&l.jsx("div",{className:"modal-overlay",children:l.jsxs("div",{className:"modal large",children:[l.jsxs("div",{className:"modal-header",children:[l.jsx("h3",{children:s?"Edit Role":"Add Role"}),l.jsx("button",{onClick:()=>{r(!1),i(null),o({name:"",description:"",permissions:[]})},children:"×"})]}),l.jsxs("form",{onSubmit:f,children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Role Name"}),l.jsx("input",{type:"text",value:a.name,onChange:d=>o({...a,name:d.target.value}),required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Description"}),l.jsx("textarea",{value:a.description,onChange:d=>o({...a,description:d.target.value})})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Permissions"}),l.jsx("div",{className:"permissions-grid",children:c.map(d=>l.jsx("div",{className:"permission-item",children:l.jsxs("label",{className:"permission-checkbox",children:[l.jsx("input",{type:"checkbox",checked:a.permissions.includes(d.id),onChange:()=>m(d.id)}),l.jsxs("div",{className:"permission-info",children:[l.jsx("span",{className:"permission-name",children:d.name}),l.jsx("span",{className:"permission-desc",children:d.description})]})]})},d.id))})]}),l.jsxs("div",{className:"form-actions",children:[l.jsx("button",{type:"button",onClick:()=>{r(!1),i(null),o({name:"",description:"",permissions:[]})},children:"Cancel"}),l.jsx("button",{type:"submit",children:s?"Update Role":"Create Role"})]})]})]})})]})},Nh=()=>{const[e,t]=A.useState([{id:1,title:"Employee Handbook",type:"document",status:"published",tags:["HR","Policy"],lastModified:"2024-01-15",size:"2.3 MB"},{id:2,title:"Leave Policy Guidelines",type:"document",status:"draft",tags:["HR","Leave"],lastModified:"2024-01-10",size:"1.1 MB"},{id:3,title:"Benefits Overview",type:"document",status:"review",tags:["HR","Benefits"],lastModified:"2024-01-08",size:"850 KB"}]),[n,r]=A.useState(!1),[s,i]=A.useState("all"),[a,o]=A.useState(""),c=d=>{Array.from(d.target.files).forEach(y=>{const j={id:Date.now()+Math.random(),title:y.name,type:"document",status:"draft",tags:[],lastModified:new Date().toISOString().split("T")[0],size:`${(y.size/1024/1024).toFixed(1)} MB`};t(M=>[...M,j])}),r(!1)},f=(d,g)=>{t(y=>y.map(j=>j.id===d?{...j,status:g}:j))},m=e.filter(d=>{const g=s==="all"||d.status===s,y=d.title.toLowerCase().includes(a.toLowerCase())||d.tags.some(j=>j.toLowerCase().includes(a.toLowerCase()));return g&&y}),u=d=>{switch(d){case"published":return"green";case"draft":return"orange";case"review":return"blue";default:return"gray"}};return l.jsxs("div",{className:"content-management",children:[l.jsxs("div",{className:"content-header",children:[l.jsx("h2",{children:"Knowledge Base Management"}),l.jsx("button",{className:"upload-button",onClick:()=>r(!0),children:"📁 Upload Content"})]}),l.jsxs("div",{className:"content-controls",children:[l.jsx("div",{className:"search-bar",children:l.jsx("input",{type:"text",placeholder:"Search content...",value:a,onChange:d=>o(d.target.value)})}),l.jsx("div",{className:"filter-tabs",children:["all","published","draft","review"].map(d=>l.jsx("button",{className:`filter-tab ${s===d?"active":""}`,onClick:()=>i(d),children:d.charAt(0).toUpperCase()+d.slice(1)},d))})]}),l.jsxs("div",{className:"content-table",children:[l.jsxs("div",{className:"table-header",children:[l.jsx("div",{className:"header-cell",children:"Content"}),l.jsx("div",{className:"header-cell",children:"Type"}),l.jsx("div",{className:"header-cell",children:"Status"}),l.jsx("div",{className:"header-cell",children:"Tags"}),l.jsx("div",{className:"header-cell",children:"Modified"}),l.jsx("div",{className:"header-cell",children:"Actions"})]}),m.map(d=>l.jsxs("div",{className:"table-row",children:[l.jsx("div",{className:"cell content-info",children:l.jsxs("div",{className:"content-details",children:[l.jsx("span",{className:"content-title",children:d.title}),l.jsx("span",{className:"content-size",children:d.size})]})}),l.jsx("div",{className:"cell",children:d.type}),l.jsx("div",{className:"cell",children:l.jsx("span",{className:`status-badge ${u(d.status)}`,children:d.status})}),l.jsx("div",{className:"cell",children:l.jsx("div",{className:"tags",children:d.tags.map(g=>l.jsx("span",{className:"tag",children:g},g))})}),l.jsx("div",{className:"cell",children:d.lastModified}),l.jsxs("div",{className:"cell actions",children:[l.jsx("button",{className:"action-btn edit",children:"Edit"}),l.jsxs("select",{className:"status-select",value:d.status,onChange:g=>f(d.id,g.target.value),children:[l.jsx("option",{value:"draft",children:"Draft"}),l.jsx("option",{value:"review",children:"Review"}),l.jsx("option",{value:"published",children:"Published"})]})]})]},d.id))]}),n&&l.jsx("div",{className:"modal-overlay",children:l.jsxs("div",{className:"modal",children:[l.jsxs("div",{className:"modal-header",children:[l.jsx("h3",{children:"Upload Content"}),l.jsx("button",{onClick:()=>r(!1),children:"×"})]}),l.jsxs("div",{className:"upload-area",children:[l.jsx("input",{type:"file",multiple:!0,accept:".pdf,.doc,.docx,.txt,.md",onChange:c,id:"file-upload",style:{display:"none"}}),l.jsxs("label",{htmlFor:"file-upload",className:"upload-label",children:[l.jsx("div",{className:"upload-icon",children:"📁"}),l.jsxs("div",{className:"upload-text",children:[l.jsx("p",{children:"Click to upload files"}),l.jsx("p",{className:"upload-hint",children:"PDF, DOC, DOCX, TXT, MD files supported"})]})]})]})]})})]})},jh=()=>{const[e,t]=A.useState([{id:1,name:"HR Assistant",type:"specialized",status:"active",department:"HR",capabilities:["policy_lookup","leave_management","benefits_info"],performance:{accuracy:.92,speed:.85,uptime:.99}},{id:2,name:"General Assistant",type:"general",status:"active",department:"All",capabilities:["general_qa","routing","basic_info"],performance:{accuracy:.88,speed:.91,uptime:.97}},{id:3,name:"Policy Expert",type:"specialized",status:"inactive",department:"Legal",capabilities:["policy_analysis","compliance_check","legal_guidance"],performance:{accuracy:.95,speed:.78,uptime:.95}}]),[n,r]=A.useState(!1),[s,i]=A.useState({name:"",type:"specialized",department:"",capabilities:[]}),a=["policy_lookup","leave_management","benefits_info","general_qa","routing","basic_info","policy_analysis","compliance_check","legal_guidance","performance_review","training_info"],o=m=>{m.preventDefault();const u={id:Date.now(),...s,status:"inactive",performance:{accuracy:0,speed:0,uptime:0}};t([...e,u]),i({name:"",type:"specialized",department:"",capabilities:[]}),r(!1)},c=m=>{t(u=>u.map(d=>d.id===m?{...d,status:d.status==="active"?"inactive":"active"}:d))},f=m=>{i(u=>({...u,capabilities:u.capabilities.includes(m)?u.capabilities.filter(d=>d!==m):[...u.capabilities,m]}))};return l.jsxs("div",{className:"agent-deployment",children:[l.jsxs("div",{className:"deployment-header",children:[l.jsx("h2",{children:"Agent Deployment Management"}),l.jsx("button",{className:"deploy-button",onClick:()=>r(!0),children:"🤖 Deploy Agent"})]}),l.jsx("div",{className:"agents-grid",children:e.map(m=>l.jsxs("div",{className:`agent-card ${m.status}`,children:[l.jsxs("div",{className:"agent-header",children:[l.jsxs("div",{className:"agent-info",children:[l.jsx("h3",{children:m.name}),l.jsx("span",{className:"agent-type",children:m.type})]}),l.jsx("span",{className:`status-indicator ${m.status}`,children:m.status})]}),l.jsxs("div",{className:"agent-details",children:[l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"label",children:"Department:"}),l.jsx("span",{className:"value",children:m.department})]}),l.jsxs("div",{className:"capabilities",children:[l.jsx("span",{className:"label",children:"Capabilities:"}),l.jsx("div",{className:"capability-tags",children:m.capabilities.map(u=>l.jsx("span",{className:"capability-tag",children:u.replace("_"," ")},u))})]})]}),l.jsxs("div",{className:"performance-metrics",children:[l.jsxs("div",{className:"metric",children:[l.jsx("span",{className:"metric-label",children:"Accuracy"}),l.jsx("div",{className:"metric-bar",children:l.jsx("div",{className:"metric-fill",style:{width:`${m.performance.accuracy*100}%`}})}),l.jsxs("span",{className:"metric-value",children:[Math.round(m.performance.accuracy*100),"%"]})]}),l.jsxs("div",{className:"metric",children:[l.jsx("span",{className:"metric-label",children:"Speed"}),l.jsx("div",{className:"metric-bar",children:l.jsx("div",{className:"metric-fill",style:{width:`${m.performance.speed*100}%`}})}),l.jsxs("span",{className:"metric-value",children:[Math.round(m.performance.speed*100),"%"]})]}),l.jsxs("div",{className:"metric",children:[l.jsx("span",{className:"metric-label",children:"Uptime"}),l.jsx("div",{className:"metric-bar",children:l.jsx("div",{className:"metric-fill",style:{width:`${m.performance.uptime*100}%`}})}),l.jsxs("span",{className:"metric-value",children:[Math.round(m.performance.uptime*100),"%"]})]})]}),l.jsxs("div",{className:"agent-actions",children:[l.jsx("button",{className:"config-btn",children:"Configure"}),l.jsx("button",{className:`toggle-btn ${m.status}`,onClick:()=>c(m.id),children:m.status==="active"?"Deactivate":"Activate"})]})]},m.id))}),n&&l.jsx("div",{className:"modal-overlay",children:l.jsxs("div",{className:"modal large",children:[l.jsxs("div",{className:"modal-header",children:[l.jsx("h3",{children:"Deploy New Agent"}),l.jsx("button",{onClick:()=>r(!1),children:"×"})]}),l.jsxs("form",{onSubmit:o,children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Agent Name"}),l.jsx("input",{type:"text",value:s.name,onChange:m=>i({...s,name:m.target.value}),required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Agent Type"}),l.jsxs("select",{value:s.type,onChange:m=>i({...s,type:m.target.value}),children:[l.jsx("option",{value:"specialized",children:"Specialized"}),l.jsx("option",{value:"general",children:"General"})]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Department"}),l.jsx("input",{type:"text",value:s.department,onChange:m=>i({...s,department:m.target.value}),required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{children:"Capabilities"}),l.jsx("div",{className:"capabilities-grid",children:a.map(m=>l.jsxs("label",{className:"capability-checkbox",children:[l.jsx("input",{type:"checkbox",checked:s.capabilities.includes(m),onChange:()=>f(m)}),l.jsx("span",{children:m.replace("_"," ")})]},m))})]}),l.jsxs("div",{className:"form-actions",children:[l.jsx("button",{type:"button",onClick:()=>r(!1),children:"Cancel"}),l.jsx("button",{type:"submit",children:"Deploy Agent"})]})]})]})})]})},jo=()=>{const[e,t]=A.useState({throughput:{current:45,target:50,trend:"up"},latency:{current:120,target:100,trend:"down"},quality:{current:92,target:95,trend:"up"},uptime:{current:99.2,target:99.5,trend:"stable"}}),[n,r]=A.useState("24h"),[s,i]=A.useState("all"),a=[{id:"all",name:"All Agents"},{id:"hr_agent",name:"HR Agent"},{id:"general_agent",name:"General Agent"},{id:"policy_agent",name:"Policy Agent"}],o=(f,m,u)=>f>=m?"green":u==="up"?"orange":"red",c=f=>{switch(f){case"up":return"↗️";case"down":return"↘️";case"stable":return"➡️";default:return"➡️"}};return l.jsxs("div",{className:"performance-tracking",children:[l.jsxs("div",{className:"tracking-header",children:[l.jsx("h2",{children:"Agent Performance Tracking"}),l.jsxs("div",{className:"tracking-controls",children:[l.jsx("select",{value:s,onChange:f=>i(f.target.value),className:"agent-select",children:a.map(f=>l.jsx("option",{value:f.id,children:f.name},f.id))}),l.jsx("div",{className:"time-range",children:["1h","24h","7d","30d"].map(f=>l.jsx("button",{className:`range-btn ${n===f?"active":""}`,onClick:()=>r(f),children:f},f))})]})]}),l.jsxs("div",{className:"metrics-grid",children:[l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Throughput"}),l.jsx("span",{className:"metric-trend",children:c(e.throughput.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:e.throughput.current}),l.jsx("span",{className:"unit",children:"req/min"})]}),l.jsxs("div",{className:"metric-progress",children:[l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:`progress-fill ${o(e.throughput.current,e.throughput.target,e.throughput.trend)}`,style:{width:`${e.throughput.current/e.throughput.target*100}%`}})}),l.jsxs("span",{className:"target",children:["Target: ",e.throughput.target]})]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Latency"}),l.jsx("span",{className:"metric-trend",children:c(e.latency.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:e.latency.current}),l.jsx("span",{className:"unit",children:"ms"})]}),l.jsxs("div",{className:"metric-progress",children:[l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:`progress-fill ${e.latency.current<=e.latency.target?"green":"red"}`,style:{width:`${Math.min(e.latency.target/e.latency.current*100,100)}%`}})}),l.jsxs("span",{className:"target",children:["Target: ≤",e.latency.target,"ms"]})]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Quality Score"}),l.jsx("span",{className:"metric-trend",children:c(e.quality.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:e.quality.current}),l.jsx("span",{className:"unit",children:"%"})]}),l.jsxs("div",{className:"metric-progress",children:[l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:`progress-fill ${o(e.quality.current,e.quality.target,e.quality.trend)}`,style:{width:`${e.quality.current}%`}})}),l.jsxs("span",{className:"target",children:["Target: ",e.quality.target,"%"]})]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Uptime"}),l.jsx("span",{className:"metric-trend",children:c(e.uptime.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:e.uptime.current}),l.jsx("span",{className:"unit",children:"%"})]}),l.jsxs("div",{className:"metric-progress",children:[l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:`progress-fill ${o(e.uptime.current,e.uptime.target,e.uptime.trend)}`,style:{width:`${e.uptime.current}%`}})}),l.jsxs("span",{className:"target",children:["Target: ",e.uptime.target,"%"]})]})]})]}),l.jsxs("div",{className:"resource-utilization",children:[l.jsx("h3",{children:"Resource Utilization"}),l.jsxs("div",{className:"resource-grid",children:[l.jsxs("div",{className:"resource-item",children:[l.jsx("span",{className:"resource-label",children:"CPU"}),l.jsx("div",{className:"resource-bar",children:l.jsx("div",{className:"resource-fill",style:{width:"65%"}})}),l.jsx("span",{className:"resource-value",children:"65%"})]}),l.jsxs("div",{className:"resource-item",children:[l.jsx("span",{className:"resource-label",children:"Memory"}),l.jsx("div",{className:"resource-bar",children:l.jsx("div",{className:"resource-fill",style:{width:"42%"}})}),l.jsx("span",{className:"resource-value",children:"42%"})]}),l.jsxs("div",{className:"resource-item",children:[l.jsx("span",{className:"resource-label",children:"Network"}),l.jsx("div",{className:"resource-bar",children:l.jsx("div",{className:"resource-fill",style:{width:"28%"}})}),l.jsx("span",{className:"resource-value",children:"28%"})]})]})]})]})},Ah=()=>{const[e,t]=A.useState([{id:1,agent:"HR Agent",action:"Processed leave request",user:"john.doe",timestamp:"2024-01-15 10:32:15",status:"success"},{id:2,agent:"General Agent",action:"Answered policy question",user:"jane.smith",timestamp:"2024-01-15 10:31:45",status:"success"},{id:3,agent:"Policy Agent",action:"Failed to parse document",user:"system",timestamp:"2024-01-15 10:30:22",status:"error"},{id:4,agent:"HR Agent",action:"Updated employee record",user:"admin",timestamp:"2024-01-15 10:29:18",status:"success"},{id:5,agent:"General Agent",action:"Routed complex query",user:"bob.wilson",timestamp:"2024-01-15 10:28:33",status:"warning"}]),[n,r]=A.useState("all"),[s,i]=A.useState("all"),a=[{id:"all",name:"All Agents"},{id:"hr_agent",name:"HR Agent"},{id:"general_agent",name:"General Agent"},{id:"policy_agent",name:"Policy Agent"}],o=u=>{switch(u){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";default:return"🔵"}},c=u=>{switch(u){case"success":return"green";case"error":return"red";case"warning":return"orange";default:return"blue"}},f=e.filter(u=>{const d=n==="all"||u.status===n,g=s==="all"||u.agent.toLowerCase().includes(s.replace("_"," "));return d&&g}),m={total:e.length,success:e.filter(u=>u.status==="success").length,error:e.filter(u=>u.status==="error").length,warning:e.filter(u=>u.status==="warning").length};return l.jsxs("div",{className:"activity-monitoring",children:[l.jsxs("div",{className:"monitoring-header",children:[l.jsx("h2",{children:"Agent Activity Monitoring"}),l.jsx("div",{className:"monitoring-controls",children:l.jsx("select",{value:s,onChange:u=>i(u.target.value),className:"agent-select",children:a.map(u=>l.jsx("option",{value:u.id,children:u.name},u.id))})})]}),l.jsxs("div",{className:"activity-stats",children:[l.jsxs("div",{className:"stat-card",children:[l.jsx("span",{className:"stat-value",children:m.total}),l.jsx("span",{className:"stat-label",children:"Total Activities"})]}),l.jsxs("div",{className:"stat-card success",children:[l.jsx("span",{className:"stat-value",children:m.success}),l.jsx("span",{className:"stat-label",children:"Successful"})]}),l.jsxs("div",{className:"stat-card error",children:[l.jsx("span",{className:"stat-value",children:m.error}),l.jsx("span",{className:"stat-label",children:"Errors"})]}),l.jsxs("div",{className:"stat-card warning",children:[l.jsx("span",{className:"stat-value",children:m.warning}),l.jsx("span",{className:"stat-label",children:"Warnings"})]})]}),l.jsx("div",{className:"activity-filters",children:["all","success","error","warning"].map(u=>l.jsx("button",{className:`filter-btn ${n===u?"active":""} ${u}`,onClick:()=>r(u),children:u.charAt(0).toUpperCase()+u.slice(1)},u))}),l.jsxs("div",{className:"activity-feed",children:[l.jsxs("div",{className:"feed-header",children:[l.jsx("h3",{children:"Real-time Activity Feed"}),l.jsxs("span",{className:"activity-count",children:[f.length," activities"]})]}),l.jsx("div",{className:"activity-list",children:f.map(u=>l.jsxs("div",{className:`activity-item ${u.status}`,children:[l.jsx("div",{className:"activity-icon",children:o(u.status)}),l.jsxs("div",{className:"activity-content",children:[l.jsxs("div",{className:"activity-main",children:[l.jsx("span",{className:"activity-agent",children:u.agent}),l.jsx("span",{className:"activity-action",children:u.action})]}),l.jsxs("div",{className:"activity-meta",children:[l.jsxs("span",{className:"activity-user",children:["User: ",u.user]}),l.jsx("span",{className:"activity-time",children:u.timestamp})]})]}),l.jsx("div",{className:`activity-status ${c(u.status)}`,children:u.status})]},u.id))})]}),l.jsxs("div",{className:"interaction-graph",children:[l.jsx("h3",{children:"Agent Interaction Graph"}),l.jsxs("div",{className:"graph-placeholder",children:[l.jsx("div",{className:"graph-node hr",children:"HR Agent"}),l.jsx("div",{className:"graph-node general",children:"General Agent"}),l.jsx("div",{className:"graph-node policy",children:"Policy Agent"}),l.jsxs("div",{className:"graph-connections",children:[l.jsx("div",{className:"connection hr-general"}),l.jsx("div",{className:"connection general-policy"}),l.jsx("div",{className:"connection hr-policy"})]}),l.jsxs("div",{className:"graph-stats",children:[l.jsxs("div",{className:"connection-stat",children:[l.jsx("span",{className:"connection-label",children:"HR ↔ General"}),l.jsx("span",{className:"connection-count",children:"23 interactions"})]}),l.jsxs("div",{className:"connection-stat",children:[l.jsx("span",{className:"connection-label",children:"General ↔ Policy"}),l.jsx("span",{className:"connection-count",children:"15 interactions"})]}),l.jsxs("div",{className:"connection-stat",children:[l.jsx("span",{className:"connection-label",children:"HR ↔ Policy"}),l.jsx("span",{className:"connection-count",children:"8 interactions"})]})]})]})]})]})},wh=()=>{const[e,t]=A.useState("trace"),[n,r]=A.useState(null),s=[{id:1,step:"Query Analysis",status:"completed",time:"12ms",details:'Parsed user query: "What is the leave policy?"'},{id:2,step:"Knowledge Search",status:"completed",time:"45ms",details:"Found 3 relevant documents with 0.92 similarity"},{id:3,step:"Context Building",status:"completed",time:"23ms",details:"Built context from HR policies and user profile"},{id:4,step:"Response Generation",status:"completed",time:"67ms",details:"Generated response using GPT-4 with context"},{id:5,step:"Quality Check",status:"completed",time:"15ms",details:"Validated response quality: 94% confidence"}],i=[{id:1,user:"john.doe",message:"What is the leave policy?",agent:"HR Agent",timestamp:"10:32:15",status:"success"},{id:2,user:"jane.smith",message:"How do I apply for benefits?",agent:"General Agent",timestamp:"10:31:45",status:"success"},{id:3,user:"bob.wilson",message:"Complex policy question",agent:"Policy Agent",timestamp:"10:30:22",status:"error"}],a={currentTask:"Processing leave request",memory:{shortTerm:["User asked about leave policy","Found relevant documents","Generated response"],longTerm:["User john.doe works in Engineering","Previous queries about benefits","Prefers detailed explanations"]},context:{user:"john.doe",department:"Engineering",role:"Senior Developer",previousQueries:5},capabilities:["policy_lookup","leave_management","benefits_info"],performance:{accuracy:.92,speed:.85,confidence:.94}},o=u=>{switch(u){case"completed":return"green";case"running":return"blue";case"error":return"red";case"success":return"green";default:return"gray"}},c=()=>l.jsxs("div",{className:"reasoning-trace",children:[l.jsx("h3",{children:"Reasoning Trace Explorer"}),l.jsx("div",{className:"trace-timeline",children:s.map((u,d)=>l.jsxs("div",{className:`trace-step ${u.status}`,children:[l.jsx("div",{className:"step-marker",children:l.jsx("span",{className:"step-number",children:d+1})}),l.jsxs("div",{className:"step-content",children:[l.jsxs("div",{className:"step-header",children:[l.jsx("span",{className:"step-name",children:u.step}),l.jsx("span",{className:"step-time",children:u.time})]}),l.jsx("div",{className:"step-details",children:u.details}),l.jsx("div",{className:`step-status ${o(u.status)}`,children:u.status})]})]},u.id))})]}),f=()=>l.jsxs("div",{className:"message-inspector",children:[l.jsx("h3",{children:"Message Inspector"}),l.jsx("div",{className:"message-list",children:i.map(u=>l.jsxs("div",{className:`message-item ${n===u.id?"selected":""}`,onClick:()=>r(u.id),children:[l.jsxs("div",{className:"message-header",children:[l.jsx("span",{className:"message-user",children:u.user}),l.jsx("span",{className:"message-time",children:u.timestamp})]}),l.jsx("div",{className:"message-content",children:u.message}),l.jsxs("div",{className:"message-meta",children:[l.jsx("span",{className:"message-agent",children:u.agent}),l.jsx("span",{className:`message-status ${o(u.status)}`,children:u.status})]})]},u.id))}),n&&l.jsxs("div",{className:"message-details",children:[l.jsx("h4",{children:"Message Details"}),l.jsxs("div",{className:"detail-grid",children:[l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Request ID:"}),l.jsxs("span",{className:"detail-value",children:["req_",n,"_2024"]})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Processing Time:"}),l.jsx("span",{className:"detail-value",children:"147ms"})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Tokens Used:"}),l.jsx("span",{className:"detail-value",children:"234 tokens"})]}),l.jsxs("div",{className:"detail-item",children:[l.jsx("span",{className:"detail-label",children:"Confidence:"}),l.jsx("span",{className:"detail-value",children:"94%"})]})]})]})]}),m=()=>l.jsxs("div",{className:"state-analyzer",children:[l.jsx("h3",{children:"Agent State Analyzer"}),l.jsxs("div",{className:"state-section",children:[l.jsx("h4",{children:"Current Task"}),l.jsx("div",{className:"current-task",children:a.currentTask})]}),l.jsxs("div",{className:"state-section",children:[l.jsx("h4",{children:"Memory"}),l.jsxs("div",{className:"memory-section",children:[l.jsxs("div",{className:"memory-type",children:[l.jsx("h5",{children:"Short-term Memory"}),l.jsx("ul",{children:a.memory.shortTerm.map((u,d)=>l.jsx("li",{children:u},d))})]}),l.jsxs("div",{className:"memory-type",children:[l.jsx("h5",{children:"Long-term Memory"}),l.jsx("ul",{children:a.memory.longTerm.map((u,d)=>l.jsx("li",{children:u},d))})]})]})]}),l.jsxs("div",{className:"state-section",children:[l.jsx("h4",{children:"Context"}),l.jsx("div",{className:"context-grid",children:Object.entries(a.context).map(([u,d])=>l.jsxs("div",{className:"context-item",children:[l.jsxs("span",{className:"context-key",children:[u,":"]}),l.jsx("span",{className:"context-value",children:d})]},u))})]}),l.jsxs("div",{className:"state-section",children:[l.jsx("h4",{children:"Capabilities"}),l.jsx("div",{className:"capabilities",children:a.capabilities.map(u=>l.jsx("span",{className:"capability-tag",children:u.replace("_"," ")},u))})]}),l.jsxs("div",{className:"state-section",children:[l.jsx("h4",{children:"Performance Metrics"}),l.jsx("div",{className:"performance-grid",children:Object.entries(a.performance).map(([u,d])=>l.jsxs("div",{className:"performance-item",children:[l.jsxs("span",{className:"performance-label",children:[u,":"]}),l.jsx("div",{className:"performance-bar",children:l.jsx("div",{className:"performance-fill",style:{width:`${d*100}%`}})}),l.jsxs("span",{className:"performance-value",children:[Math.round(d*100),"%"]})]},u))})]})]});return l.jsxs("div",{className:"debugging-tools",children:[l.jsxs("div",{className:"debugging-header",children:[l.jsx("h2",{children:"Agent Debugging Tools"}),l.jsxs("div",{className:"debug-tabs",children:[l.jsx("button",{className:`debug-tab ${e==="trace"?"active":""}`,onClick:()=>t("trace"),children:"Reasoning Trace"}),l.jsx("button",{className:`debug-tab ${e==="inspector"?"active":""}`,onClick:()=>t("inspector"),children:"Message Inspector"}),l.jsx("button",{className:`debug-tab ${e==="state"?"active":""}`,onClick:()=>t("state"),children:"State Analyzer"})]})]}),l.jsxs("div",{className:"debug-content",children:[e==="trace"&&c(),e==="inspector"&&f(),e==="state"&&m()]})]})},Sh=({isVisible:e,onClose:t})=>{const[n,r]=A.useState("performance");if(!e)return null;const s=[{id:"performance",label:"Performance",icon:"📊"},{id:"activity",label:"Activity",icon:"🔄"},{id:"debugging",label:"Debugging",icon:"🐛"}],i=()=>{switch(n){case"performance":return l.jsx(jo,{});case"activity":return l.jsx(Ah,{});case"debugging":return l.jsx(wh,{});default:return l.jsx(jo,{})}};return l.jsx("div",{className:"monitoring-dashboard-overlay",children:l.jsxs("div",{className:"monitoring-dashboard",children:[l.jsxs("div",{className:"dashboard-header",children:[l.jsx("h2",{children:"Agent Monitoring Dashboard"}),l.jsx("button",{className:"close-button",onClick:t,children:"×"})]}),l.jsxs("div",{className:"dashboard-content",children:[l.jsxs("div",{className:"dashboard-sidebar",children:[l.jsx("nav",{className:"dashboard-nav",children:s.map(a=>l.jsxs("button",{className:`nav-item ${n===a.id?"active":""}`,onClick:()=>r(a.id),children:[l.jsx("span",{className:"nav-icon",children:a.icon}),l.jsx("span",{className:"nav-label",children:a.label})]},a.id))}),l.jsxs("div",{className:"dashboard-stats",children:[l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-value",children:"3"}),l.jsx("span",{className:"stat-label",children:"Active Agents"})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-value",children:"92%"}),l.jsx("span",{className:"stat-label",children:"Avg Quality"})]}),l.jsxs("div",{className:"stat-item",children:[l.jsx("span",{className:"stat-value",children:"45"}),l.jsx("span",{className:"stat-label",children:"Req/Min"})]})]})]}),l.jsx("div",{className:"dashboard-main",children:i()})]})]})})},Ao=()=>{const[e,t]=A.useState("7d"),[n,r]=A.useState("volume"),s={volume:{current:1247,change:14.5,trend:"up"},quality:{current:4.2,change:7.7,trend:"up"},satisfaction:{current:87,change:6.1,trend:"up"}},i=[{name:"HR",queries:456,quality:4.3,satisfaction:89},{name:"Engineering",queries:342,quality:4.1,satisfaction:85},{name:"Marketing",queries:289,quality:4,satisfaction:88},{name:"Finance",queries:160,quality:4.4,satisfaction:91}],a=[{query:"Leave policy information",count:89,satisfaction:4.5},{query:"Benefits enrollment",count:67,satisfaction:4.2},{query:"Performance review process",count:54,satisfaction:4.1},{query:"Remote work policy",count:43,satisfaction:4.3},{query:"Training opportunities",count:38,satisfaction:4}],o=f=>f>0?"positive":"negative",c=f=>f==="up"?"↗️":"↘️";return l.jsxs("div",{className:"usage-analytics",children:[l.jsxs("div",{className:"analytics-header",children:[l.jsx("h2",{children:"Usage Analytics Dashboard"}),l.jsx("div",{className:"time-controls",children:["24h","7d","30d","90d"].map(f=>l.jsx("button",{className:`time-btn ${e===f?"active":""}`,onClick:()=>t(f),children:f},f))})]}),l.jsxs("div",{className:"metrics-overview",children:[l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Query Volume"}),l.jsx("span",{className:"metric-trend",children:c(s.volume.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:s.volume.current.toLocaleString()}),l.jsx("span",{className:"unit",children:"queries"})]}),l.jsxs("div",{className:"metric-change",children:[l.jsxs("span",{className:`change ${o(s.volume.change)}`,children:["+",s.volume.change,"%"]}),l.jsxs("span",{className:"period",children:["vs last ",e]})]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Response Quality"}),l.jsx("span",{className:"metric-trend",children:c(s.quality.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:s.quality.current}),l.jsx("span",{className:"unit",children:"/5.0"})]}),l.jsxs("div",{className:"metric-change",children:[l.jsxs("span",{className:`change ${o(s.quality.change)}`,children:["+",s.quality.change,"%"]}),l.jsxs("span",{className:"period",children:["vs last ",e]})]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"User Satisfaction"}),l.jsx("span",{className:"metric-trend",children:c(s.satisfaction.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:s.satisfaction.current}),l.jsx("span",{className:"unit",children:"%"})]}),l.jsxs("div",{className:"metric-change",children:[l.jsxs("span",{className:`change ${o(s.satisfaction.change)}`,children:["+",s.satisfaction.change,"%"]}),l.jsxs("span",{className:"period",children:["vs last ",e]})]})]})]}),l.jsxs("div",{className:"analytics-grid",children:[l.jsxs("div",{className:"chart-section",children:[l.jsx("h3",{children:"Department Breakdown"}),l.jsx("div",{className:"department-chart",children:i.map(f=>l.jsxs("div",{className:"dept-row",children:[l.jsxs("div",{className:"dept-info",children:[l.jsx("span",{className:"dept-name",children:f.name}),l.jsxs("span",{className:"dept-queries",children:[f.queries," queries"]})]}),l.jsxs("div",{className:"dept-metrics",children:[l.jsxs("div",{className:"dept-metric",children:[l.jsx("span",{className:"metric-label",children:"Quality"}),l.jsx("span",{className:"metric-score",children:f.quality})]}),l.jsxs("div",{className:"dept-metric",children:[l.jsx("span",{className:"metric-label",children:"Satisfaction"}),l.jsxs("span",{className:"metric-score",children:[f.satisfaction,"%"]})]})]})]},f.name))})]}),l.jsxs("div",{className:"queries-section",children:[l.jsx("h3",{children:"Top Queries"}),l.jsx("div",{className:"queries-list",children:a.map((f,m)=>l.jsxs("div",{className:"query-item",children:[l.jsx("div",{className:"query-rank",children:m+1}),l.jsxs("div",{className:"query-content",children:[l.jsx("div",{className:"query-text",children:f.query}),l.jsxs("div",{className:"query-stats",children:[l.jsxs("span",{className:"query-count",children:[f.count," queries"]}),l.jsxs("span",{className:"query-rating",children:["★ ",f.satisfaction]})]})]})]},m))})]})]}),l.jsxs("div",{className:"satisfaction-tracking",children:[l.jsx("h3",{children:"User Satisfaction Tracking"}),l.jsxs("div",{className:"satisfaction-grid",children:[l.jsxs("div",{className:"satisfaction-breakdown",children:[l.jsxs("div",{className:"rating-bar",children:[l.jsx("span",{className:"rating-label",children:"5 Stars"}),l.jsx("div",{className:"rating-progress",children:l.jsx("div",{className:"rating-fill",style:{width:"45%"}})}),l.jsx("span",{className:"rating-percent",children:"45%"})]}),l.jsxs("div",{className:"rating-bar",children:[l.jsx("span",{className:"rating-label",children:"4 Stars"}),l.jsx("div",{className:"rating-progress",children:l.jsx("div",{className:"rating-fill",style:{width:"32%"}})}),l.jsx("span",{className:"rating-percent",children:"32%"})]}),l.jsxs("div",{className:"rating-bar",children:[l.jsx("span",{className:"rating-label",children:"3 Stars"}),l.jsx("div",{className:"rating-progress",children:l.jsx("div",{className:"rating-fill",style:{width:"15%"}})}),l.jsx("span",{className:"rating-percent",children:"15%"})]}),l.jsxs("div",{className:"rating-bar",children:[l.jsx("span",{className:"rating-label",children:"2 Stars"}),l.jsx("div",{className:"rating-progress",children:l.jsx("div",{className:"rating-fill",style:{width:"6%"}})}),l.jsx("span",{className:"rating-percent",children:"6%"})]}),l.jsxs("div",{className:"rating-bar",children:[l.jsx("span",{className:"rating-label",children:"1 Star"}),l.jsx("div",{className:"rating-progress",children:l.jsx("div",{className:"rating-fill",style:{width:"2%"}})}),l.jsx("span",{className:"rating-percent",children:"2%"})]})]}),l.jsxs("div",{className:"satisfaction-summary",children:[l.jsxs("div",{className:"summary-stat",children:[l.jsx("span",{className:"stat-value",children:"4.2"}),l.jsx("span",{className:"stat-label",children:"Average Rating"})]}),l.jsxs("div",{className:"summary-stat",children:[l.jsx("span",{className:"stat-value",children:"892"}),l.jsx("span",{className:"stat-label",children:"Total Ratings"})]}),l.jsxs("div",{className:"summary-stat",children:[l.jsx("span",{className:"stat-value",children:"77%"}),l.jsx("span",{className:"stat-label",children:"4+ Stars"})]})]})]})]})]})},Ch=()=>{const[e,t]=A.useState("all"),[n,r]=A.useState("7d"),s=[{id:"all",name:"All Agents"},{id:"hr_agent",name:"HR Agent"},{id:"general_agent",name:"General Agent"},{id:"policy_agent",name:"Policy Agent"}],i={taskSuccess:{rate:89,target:90,trend:"up"},reasoningQuality:{score:4.3,target:4.5,trend:"up"},collaborationEfficiency:{score:92,target:95,trend:"stable"},responseAccuracy:{rate:94,target:95,trend:"up"}},a=[{name:"HR Agent",taskSuccess:92,reasoningQuality:4.4,collaboration:95,accuracy:96,totalTasks:456,avgResponseTime:1.2},{name:"General Agent",taskSuccess:87,reasoningQuality:4.1,collaboration:89,accuracy:91,totalTasks:623,avgResponseTime:.8},{name:"Policy Agent",taskSuccess:91,reasoningQuality:4.5,collaboration:93,accuracy:97,totalTasks:234,avgResponseTime:2.1}],o=[{from:"HR Agent",to:"General Agent",interactions:45,efficiency:94},{from:"General Agent",to:"Policy Agent",interactions:32,efficiency:89},{from:"HR Agent",to:"Policy Agent",interactions:18,efficiency:96},{from:"Policy Agent",to:"HR Agent",interactions:12,efficiency:91}],c=[{category:"Query Understanding",score:4.5,improvement:8},{category:"Context Retrieval",score:4.2,improvement:12},{category:"Logic Application",score:4.4,improvement:6},{category:"Response Generation",score:4.1,improvement:15},{category:"Quality Validation",score:4.3,improvement:9}],f=(u,d)=>u>=d?"excellent":u>=d*.9?"good":"needs-improvement",m=u=>{switch(u){case"up":return"↗️";case"down":return"↘️";case"stable":return"➡️";default:return"➡️"}};return l.jsxs("div",{className:"effectiveness-analytics",children:[l.jsxs("div",{className:"effectiveness-header",children:[l.jsx("h2",{children:"Agent Effectiveness Analytics"}),l.jsxs("div",{className:"controls",children:[l.jsx("select",{value:e,onChange:u=>t(u.target.value),className:"agent-select",children:s.map(u=>l.jsx("option",{value:u.id,children:u.name},u.id))}),l.jsx("div",{className:"time-controls",children:["24h","7d","30d"].map(u=>l.jsx("button",{className:`time-btn ${n===u?"active":""}`,onClick:()=>r(u),children:u},u))})]})]}),l.jsxs("div",{className:"effectiveness-overview",children:[l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Task Success Rate"}),l.jsx("span",{className:"trend",children:m(i.taskSuccess.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:i.taskSuccess.rate}),l.jsx("span",{className:"unit",children:"%"})]}),l.jsxs("div",{className:"metric-target",children:["Target: ",i.taskSuccess.target,"%"]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Reasoning Quality"}),l.jsx("span",{className:"trend",children:m(i.reasoningQuality.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:i.reasoningQuality.score}),l.jsx("span",{className:"unit",children:"/5.0"})]}),l.jsxs("div",{className:"metric-target",children:["Target: ",i.reasoningQuality.target,"/5.0"]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Collaboration Efficiency"}),l.jsx("span",{className:"trend",children:m(i.collaborationEfficiency.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:i.collaborationEfficiency.score}),l.jsx("span",{className:"unit",children:"%"})]}),l.jsxs("div",{className:"metric-target",children:["Target: ",i.collaborationEfficiency.target,"%"]})]}),l.jsxs("div",{className:"metric-card",children:[l.jsxs("div",{className:"metric-header",children:[l.jsx("h3",{children:"Response Accuracy"}),l.jsx("span",{className:"trend",children:m(i.responseAccuracy.trend)})]}),l.jsxs("div",{className:"metric-value",children:[l.jsx("span",{className:"current",children:i.responseAccuracy.rate}),l.jsx("span",{className:"unit",children:"%"})]}),l.jsxs("div",{className:"metric-target",children:["Target: ",i.responseAccuracy.target,"%"]})]})]}),l.jsxs("div",{className:"analytics-sections",children:[l.jsxs("div",{className:"agent-performance",children:[l.jsx("h3",{children:"Agent Performance Comparison"}),l.jsxs("div",{className:"performance-table",children:[l.jsxs("div",{className:"table-header",children:[l.jsx("div",{className:"header-cell",children:"Agent"}),l.jsx("div",{className:"header-cell",children:"Success Rate"}),l.jsx("div",{className:"header-cell",children:"Quality"}),l.jsx("div",{className:"header-cell",children:"Collaboration"}),l.jsx("div",{className:"header-cell",children:"Accuracy"}),l.jsx("div",{className:"header-cell",children:"Tasks"})]}),a.map(u=>l.jsxs("div",{className:"table-row",children:[l.jsx("div",{className:"cell agent-name",children:u.name}),l.jsx("div",{className:"cell",children:l.jsxs("span",{className:`score ${f(u.taskSuccess,90)}`,children:[u.taskSuccess,"%"]})}),l.jsx("div",{className:"cell",children:l.jsx("span",{className:`score ${f(u.reasoningQuality,4.5)}`,children:u.reasoningQuality})}),l.jsx("div",{className:"cell",children:l.jsxs("span",{className:`score ${f(u.collaboration,95)}`,children:[u.collaboration,"%"]})}),l.jsx("div",{className:"cell",children:l.jsxs("span",{className:`score ${f(u.accuracy,95)}`,children:[u.accuracy,"%"]})}),l.jsx("div",{className:"cell",children:u.totalTasks})]},u.name))]})]}),l.jsxs("div",{className:"reasoning-analysis",children:[l.jsx("h3",{children:"Reasoning Quality Metrics"}),l.jsx("div",{className:"reasoning-metrics",children:c.map(u=>l.jsxs("div",{className:"reasoning-item",children:[l.jsxs("div",{className:"reasoning-header",children:[l.jsx("span",{className:"reasoning-category",children:u.category}),l.jsxs("span",{className:"reasoning-score",children:[u.score,"/5.0"]})]}),l.jsx("div",{className:"reasoning-progress",children:l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:"progress-fill",style:{width:`${u.score/5*100}%`}})})}),l.jsxs("div",{className:"reasoning-improvement",children:["+",u.improvement,"% improvement"]})]},u.category))})]})]}),l.jsxs("div",{className:"collaboration-analysis",children:[l.jsx("h3",{children:"Collaboration Efficiency Analysis"}),l.jsx("div",{className:"collaboration-grid",children:o.map((u,d)=>l.jsxs("div",{className:"collaboration-item",children:[l.jsxs("div",{className:"collaboration-flow",children:[l.jsx("span",{className:"from-agent",children:u.from}),l.jsx("span",{className:"flow-arrow",children:"→"}),l.jsx("span",{className:"to-agent",children:u.to})]}),l.jsxs("div",{className:"collaboration-stats",children:[l.jsxs("div",{className:"stat",children:[l.jsx("span",{className:"stat-value",children:u.interactions}),l.jsx("span",{className:"stat-label",children:"Interactions"})]}),l.jsxs("div",{className:"stat",children:[l.jsxs("span",{className:`stat-value ${f(u.efficiency,90)}`,children:[u.efficiency,"%"]}),l.jsx("span",{className:"stat-label",children:"Efficiency"})]})]})]},d))})]})]})},Eh=()=>{const[e,t]=A.useState("performance"),[n,r]=A.useState("30d"),s=[{week:"Week 1",accuracy:78,speed:65,quality:72},{week:"Week 2",accuracy:82,speed:71,quality:76},{week:"Week 3",accuracy:85,speed:76,quality:80},{week:"Week 4",accuracy:88,speed:82,quality:84},{week:"Week 5",accuracy:91,speed:87,quality:87},{week:"Week 6",accuracy:93,speed:89,quality:90},{week:"Week 7",accuracy:94,speed:91,quality:92},{week:"Week 8",accuracy:95,speed:93,quality:93}],i={accuracy:{current:95,change:12,trend:"up",target:96},speed:{current:93,change:18,trend:"up",target:95},quality:{current:93,change:15,trend:"up",target:95},satisfaction:{current:89,change:8,trend:"up",target:90}},a=[{category:"Response Time",current:1.2,industry:1.8,best:.9,unit:"seconds",performance:"above-average"},{category:"Accuracy Rate",current:95,industry:87,best:98,unit:"%",performance:"excellent"},{category:"User Satisfaction",current:4.2,industry:3.8,best:4.7,unit:"/5.0",performance:"above-average"},{category:"Task Completion",current:89,industry:82,best:94,unit:"%",performance:"above-average"}],o=[{area:"Complex Query Handling",currentScore:78,targetScore:85,priority:"high",estimatedImprovement:"2-3 weeks"},{area:"Multi-step Reasoning",currentScore:82,targetScore:88,priority:"medium",estimatedImprovement:"3-4 weeks"},{area:"Context Retention",currentScore:85,targetScore:90,priority:"medium",estimatedImprovement:"2-3 weeks"},{area:"Error Recovery",currentScore:73,targetScore:82,priority:"high",estimatedImprovement:"4-5 weeks"}],c=u=>{switch(u){case"excellent":return"excellent";case"above-average":return"good";case"average":return"average";case"below-average":return"poor";default:return"average"}},f=u=>{switch(u){case"high":return"high";case"medium":return"medium";case"low":return"low";default:return"medium"}},m=u=>u==="up"?"↗️":u==="down"?"↘️":"➡️";return l.jsxs("div",{className:"improvement-analytics",children:[l.jsxs("div",{className:"improvement-header",children:[l.jsx("h2",{children:"System Improvement Analytics"}),l.jsxs("div",{className:"controls",children:[l.jsxs("select",{value:e,onChange:u=>t(u.target.value),className:"metric-select",children:[l.jsx("option",{value:"performance",children:"Performance"}),l.jsx("option",{value:"accuracy",children:"Accuracy"}),l.jsx("option",{value:"speed",children:"Speed"}),l.jsx("option",{value:"quality",children:"Quality"})]}),l.jsx("div",{className:"time-controls",children:["7d","30d","90d"].map(u=>l.jsx("button",{className:`time-btn ${n===u?"active":""}`,onClick:()=>r(u),children:u},u))})]})]}),l.jsx("div",{className:"trends-overview",children:Object.entries(i).map(([u,d])=>l.jsxs("div",{className:"trend-card",children:[l.jsxs("div",{className:"trend-header",children:[l.jsx("h3",{children:u.charAt(0).toUpperCase()+u.slice(1)}),l.jsx("span",{className:"trend-icon",children:m(d.trend)})]}),l.jsxs("div",{className:"trend-value",children:[l.jsx("span",{className:"current",children:d.current}),l.jsx("span",{className:"unit",children:u==="quality"||u==="accuracy"||u==="speed"||u==="satisfaction"?"%":""})]}),l.jsxs("div",{className:"trend-change",children:[l.jsxs("span",{className:"change positive",children:["+",d.change,"%"]}),l.jsx("span",{className:"period",children:"improvement"})]}),l.jsxs("div",{className:"trend-target",children:["Target: ",d.target,u==="quality"||u==="accuracy"||u==="speed"||u==="satisfaction"?"%":""]})]},u))}),l.jsxs("div",{className:"analytics-grid",children:[l.jsxs("div",{className:"learning-curve",children:[l.jsx("h3",{children:"Learning Curve Visualization"}),l.jsxs("div",{className:"curve-chart",children:[l.jsx("div",{className:"chart-header",children:l.jsxs("div",{className:"legend",children:[l.jsxs("div",{className:"legend-item",children:[l.jsx("div",{className:"legend-color accuracy"}),l.jsx("span",{children:"Accuracy"})]}),l.jsxs("div",{className:"legend-item",children:[l.jsx("div",{className:"legend-color speed"}),l.jsx("span",{children:"Speed"})]}),l.jsxs("div",{className:"legend-item",children:[l.jsx("div",{className:"legend-color quality"}),l.jsx("span",{children:"Quality"})]})]})}),l.jsx("div",{className:"chart-data",children:s.map((u,d)=>l.jsxs("div",{className:"data-point",children:[l.jsx("div",{className:"week-label",children:u.week}),l.jsxs("div",{className:"metrics-bars",children:[l.jsx("div",{className:"metric-bar",children:l.jsx("div",{className:"bar-fill accuracy",style:{height:`${u.accuracy}%`}})}),l.jsx("div",{className:"metric-bar",children:l.jsx("div",{className:"bar-fill speed",style:{height:`${u.speed}%`}})}),l.jsx("div",{className:"metric-bar",children:l.jsx("div",{className:"bar-fill quality",style:{height:`${u.quality}%`}})})]})]},d))})]})]}),l.jsxs("div",{className:"improvement-areas",children:[l.jsx("h3",{children:"Priority Improvement Areas"}),l.jsx("div",{className:"areas-list",children:o.map((u,d)=>l.jsxs("div",{className:"area-item",children:[l.jsxs("div",{className:"area-header",children:[l.jsx("span",{className:"area-name",children:u.area}),l.jsx("span",{className:`priority-badge ${f(u.priority)}`,children:u.priority})]}),l.jsxs("div",{className:"area-progress",children:[l.jsxs("div",{className:"progress-info",children:[l.jsxs("span",{children:["Current: ",u.currentScore,"%"]}),l.jsxs("span",{children:["Target: ",u.targetScore,"%"]})]}),l.jsx("div",{className:"progress-bar",children:l.jsx("div",{className:"progress-fill",style:{width:`${u.currentScore/u.targetScore*100}%`}})})]}),l.jsxs("div",{className:"area-timeline",children:["Estimated: ",u.estimatedImprovement]})]},d))})]})]}),l.jsxs("div",{className:"benchmarking",children:[l.jsx("h3",{children:"Comparative Benchmarking"}),l.jsxs("div",{className:"benchmark-table",children:[l.jsxs("div",{className:"table-header",children:[l.jsx("div",{className:"header-cell",children:"Category"}),l.jsx("div",{className:"header-cell",children:"Current"}),l.jsx("div",{className:"header-cell",children:"Industry Avg"}),l.jsx("div",{className:"header-cell",children:"Best in Class"}),l.jsx("div",{className:"header-cell",children:"Performance"})]}),a.map((u,d)=>l.jsxs("div",{className:"table-row",children:[l.jsx("div",{className:"cell category",children:u.category}),l.jsxs("div",{className:"cell current",children:[u.current,u.unit]}),l.jsxs("div",{className:"cell industry",children:[u.industry,u.unit]}),l.jsxs("div",{className:"cell best",children:[u.best,u.unit]}),l.jsx("div",{className:"cell",children:l.jsx("span",{className:`performance-badge ${c(u.performance)}`,children:u.performance.replace("-"," ")})})]},d))]})]})]})},kh=({isVisible:e,onClose:t})=>{const[n,r]=A.useState("usage");if(!e)return null;const s=[{id:"usage",label:"Usage Analytics",icon:"📈"},{id:"effectiveness",label:"Effectiveness",icon:"🎯"},{id:"improvement",label:"Improvement",icon:"📊"}],i=()=>{switch(n){case"usage":return l.jsx(Ao,{});case"effectiveness":return l.jsx(Ch,{});case"improvement":return l.jsx(Eh,{});default:return l.jsx(Ao,{})}};return l.jsx("div",{className:"analytics-dashboard-overlay",children:l.jsxs("div",{className:"analytics-dashboard",children:[l.jsxs("div",{className:"dashboard-header",children:[l.jsx("h2",{children:"Advanced Analytics Dashboard"}),l.jsx("button",{className:"close-button",onClick:t,children:"×"})]}),l.jsxs("div",{className:"dashboard-content",children:[l.jsxs("div",{className:"dashboard-sidebar",children:[l.jsx("nav",{className:"dashboard-nav",children:s.map(a=>l.jsxs("button",{className:`nav-item ${n===a.id?"active":""}`,onClick:()=>r(a.id),children:[l.jsx("span",{className:"nav-icon",children:a.icon}),l.jsx("span",{className:"nav-label",children:a.label})]},a.id))}),l.jsxs("div",{className:"dashboard-summary",children:[l.jsxs("div",{className:"summary-item",children:[l.jsx("span",{className:"summary-value",children:"1,247"}),l.jsx("span",{className:"summary-label",children:"Total Queries"})]}),l.jsxs("div",{className:"summary-item",children:[l.jsx("span",{className:"summary-value",children:"4.2"}),l.jsx("span",{className:"summary-label",children:"Avg Quality"})]}),l.jsxs("div",{className:"summary-item",children:[l.jsx("span",{className:"summary-value",children:"89%"}),l.jsx("span",{className:"summary-label",children:"Success Rate"})]}),l.jsxs("div",{className:"summary-item",children:[l.jsx("span",{className:"summary-value",children:"87%"}),l.jsx("span",{className:"summary-label",children:"Satisfaction"})]})]})]}),l.jsx("div",{className:"dashboard-main",children:i()})]})]})})},Rh=({isVisible:e,onClose:t})=>{const[n,r]=A.useState("organization"),[s,i]=A.useState(!1),[a,o]=A.useState(!1);if(!e)return null;const c=[{id:"organization",label:"Organization",icon:"🏢"},{id:"departments",label:"Departments",icon:"🏛️"},{id:"roles",label:"Roles & Permissions",icon:"👥"},{id:"content",label:"Knowledge Base",icon:"📚"},{id:"agents",label:"Agent Config",icon:"🤖"},{id:"monitoring",label:"Monitoring",icon:"📊"},{id:"analytics",label:"Analytics",icon:"📈"}],f=u=>{if(u==="monitoring"){i(!0);return}if(u==="analytics"){o(!0);return}r(u)},m=()=>{switch(n){case"organization":return l.jsx(No,{});case"departments":return l.jsx(yh,{});case"roles":return l.jsx(xh,{});case"content":return l.jsx(Nh,{});case"agents":return l.jsx(jh,{});default:return l.jsx(No,{})}};return l.jsx("div",{className:"admin-interface-overlay",children:l.jsxs("div",{className:"admin-interface",children:[l.jsxs("div",{className:"admin-header",children:[l.jsx("h2",{children:"Admin Dashboard"}),l.jsx("button",{className:"close-button",onClick:t,children:"×"})]}),l.jsxs("div",{className:"admin-content",children:[l.jsx("div",{className:"admin-sidebar",children:l.jsx("nav",{className:"admin-nav",children:c.map(u=>l.jsxs("button",{className:`nav-item ${n===u.id&&u.id!=="monitoring"&&u.id!=="analytics"?"active":""}`,onClick:()=>f(u.id),children:[l.jsx("span",{className:"nav-icon",children:u.icon}),l.jsx("span",{className:"nav-label",children:u.label})]},u.id))})}),l.jsx("div",{className:"admin-main",children:m()})]}),l.jsx(Sh,{isVisible:s,onClose:()=>{i(!1),r("organization")}}),l.jsx(kh,{isVisible:a,onClose:()=>{o(!1),r("organization")}})]})})},Th=({onLoginSuccess:e})=>{const[t,n]=A.useState({username:"",password:""}),[r,s]=A.useState(!1),[i,a]=A.useState(""),o=f=>{n({...t,[f.target.name]:f.target.value}),a("")},c=async f=>{f.preventDefault(),s(!0),a("");try{const m=await Fu.login(t.username,t.password);e(m)}catch{a("Invalid credentials. Try admin/admin123 or user/user123")}finally{s(!1)}};return l.jsxs("div",{className:"login-form",children:[l.jsx("h2",{children:"Login to CHaBot"}),l.jsxs("form",{onSubmit:c,children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"username",children:"Username:"}),l.jsx("input",{type:"text",id:"username",name:"username",value:t.username,onChange:o,required:!0,disabled:r})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"password",children:"Password:"}),l.jsx("input",{type:"password",id:"password",name:"password",value:t.password,onChange:o,required:!0,disabled:r})]}),i&&l.jsx("div",{className:"error-message",children:i}),l.jsx("button",{type:"submit",disabled:r,children:r?"Logging in...":"Login"})]}),l.jsxs("div",{className:"demo-credentials",children:[l.jsx("h4",{children:"Demo Credentials:"}),l.jsx("p",{children:"Admin: admin / admin123"}),l.jsx("p",{children:"User: user / user123"})]})]})},Ih=()=>{const[e,t]=A.useState(!1),[n,r]=A.useState(!1),[s,i]=A.useState(!1),[a,o]=A.useState(!1);A.useEffect(()=>{const m=localStorage.getItem("auth_token");i(!!m)},[]);const c=()=>{i(!0)},f=()=>{localStorage.removeItem("auth_token"),i(!1),o(!1)};return s?l.jsx(ph,{children:l.jsxs("div",{className:"app-container",children:[l.jsx(lh,{onAccessibilityClick:()=>t(!0),onAdminClick:()=>r(!0),onChatClick:()=>o(!0),onLogout:f}),a?l.jsx(Bu,{}):l.jsx(oh,{}),l.jsx(gh,{isVisible:e,onClose:()=>t(!1)}),l.jsx(Rh,{isVisible:n,onClose:()=>r(!1)})]})}):l.jsx("div",{className:"login-container",children:l.jsx(Th,{onLoginSuccess:c})})};al.createRoot(document.getElementById("root")).render(l.jsx(sh,{children:l.jsx(Ih,{})}));export{l as j,A as r};
