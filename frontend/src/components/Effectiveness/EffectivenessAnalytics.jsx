import React, { useState } from 'react';
import './EffectivenessAnalytics.css';

const EffectivenessAnalytics = () => {
    const [selectedAgent, setSelectedAgent] = useState('all');
    const [timeRange, setTimeRange] = useState('7d');

    const agents = [
        { id: 'all', name: 'All Agents' },
        { id: 'hr_agent', name: 'HR Agent' },
        { id: 'general_agent', name: 'General Agent' },
        { id: 'policy_agent', name: 'Policy Agent' }
    ];

    const effectivenessMetrics = {
        taskSuccess: { rate: 89, target: 90, trend: 'up' },
        reasoningQuality: { score: 4.3, target: 4.5, trend: 'up' },
        collaborationEfficiency: { score: 92, target: 95, trend: 'stable' },
        responseAccuracy: { rate: 94, target: 95, trend: 'up' }
    };

    const agentPerformance = [
        { 
            name: 'HR Agent', 
            taskSuccess: 92, 
            reasoningQuality: 4.4, 
            collaboration: 95, 
            accuracy: 96,
            totalTasks: 456,
            avgResponseTime: 1.2
        },
        { 
            name: 'General Agent', 
            taskSuccess: 87, 
            reasoningQuality: 4.1, 
            collaboration: 89, 
            accuracy: 91,
            totalTasks: 623,
            avgResponseTime: 0.8
        },
        { 
            name: 'Policy Agent', 
            taskSuccess: 91, 
            reasoningQuality: 4.5, 
            collaboration: 93, 
            accuracy: 97,
            totalTasks: 234,
            avgResponseTime: 2.1
        }
    ];

    const collaborationData = [
        { from: 'HR Agent', to: 'General Agent', interactions: 45, efficiency: 94 },
        { from: 'General Agent', to: 'Policy Agent', interactions: 32, efficiency: 89 },
        { from: 'HR Agent', to: 'Policy Agent', interactions: 18, efficiency: 96 },
        { from: 'Policy Agent', to: 'HR Agent', interactions: 12, efficiency: 91 }
    ];

    const reasoningMetrics = [
        { category: 'Query Understanding', score: 4.5, improvement: 8 },
        { category: 'Context Retrieval', score: 4.2, improvement: 12 },
        { category: 'Logic Application', score: 4.4, improvement: 6 },
        { category: 'Response Generation', score: 4.1, improvement: 15 },
        { category: 'Quality Validation', score: 4.3, improvement: 9 }
    ];

    const getScoreColor = (score, target) => {
        if (score >= target) return 'excellent';
        if (score >= target * 0.9) return 'good';
        return 'needs-improvement';
    };

    const getTrendIcon = (trend) => {
        switch (trend) {
            case 'up': return '↗️';
            case 'down': return '↘️';
            case 'stable': return '➡️';
            default: return '➡️';
        }
    };

    return (
        <div className="effectiveness-analytics">
            <div className="effectiveness-header">
                <h2>Agent Effectiveness Analytics</h2>
                <div className="controls">
                    <select 
                        value={selectedAgent} 
                        onChange={(e) => setSelectedAgent(e.target.value)}
                        className="agent-select"
                    >
                        {agents.map(agent => (
                            <option key={agent.id} value={agent.id}>{agent.name}</option>
                        ))}
                    </select>
                    <div className="time-controls">
                        {['24h', '7d', '30d'].map(range => (
                            <button
                                key={range}
                                className={`time-btn ${timeRange === range ? 'active' : ''}`}
                                onClick={() => setTimeRange(range)}
                            >
                                {range}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            <div className="effectiveness-overview">
                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Task Success Rate</h3>
                        <span className="trend">{getTrendIcon(effectivenessMetrics.taskSuccess.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{effectivenessMetrics.taskSuccess.rate}</span>
                        <span className="unit">%</span>
                    </div>
                    <div className="metric-target">
                        Target: {effectivenessMetrics.taskSuccess.target}%
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Reasoning Quality</h3>
                        <span className="trend">{getTrendIcon(effectivenessMetrics.reasoningQuality.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{effectivenessMetrics.reasoningQuality.score}</span>
                        <span className="unit">/5.0</span>
                    </div>
                    <div className="metric-target">
                        Target: {effectivenessMetrics.reasoningQuality.target}/5.0
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Collaboration Efficiency</h3>
                        <span className="trend">{getTrendIcon(effectivenessMetrics.collaborationEfficiency.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{effectivenessMetrics.collaborationEfficiency.score}</span>
                        <span className="unit">%</span>
                    </div>
                    <div className="metric-target">
                        Target: {effectivenessMetrics.collaborationEfficiency.target}%
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Response Accuracy</h3>
                        <span className="trend">{getTrendIcon(effectivenessMetrics.responseAccuracy.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{effectivenessMetrics.responseAccuracy.rate}</span>
                        <span className="unit">%</span>
                    </div>
                    <div className="metric-target">
                        Target: {effectivenessMetrics.responseAccuracy.target}%
                    </div>
                </div>
            </div>

            <div className="analytics-sections">
                <div className="agent-performance">
                    <h3>Agent Performance Comparison</h3>
                    <div className="performance-table">
                        <div className="table-header">
                            <div className="header-cell">Agent</div>
                            <div className="header-cell">Success Rate</div>
                            <div className="header-cell">Quality</div>
                            <div className="header-cell">Collaboration</div>
                            <div className="header-cell">Accuracy</div>
                            <div className="header-cell">Tasks</div>
                        </div>
                        {agentPerformance.map(agent => (
                            <div key={agent.name} className="table-row">
                                <div className="cell agent-name">{agent.name}</div>
                                <div className="cell">
                                    <span className={`score ${getScoreColor(agent.taskSuccess, 90)}`}>
                                        {agent.taskSuccess}%
                                    </span>
                                </div>
                                <div className="cell">
                                    <span className={`score ${getScoreColor(agent.reasoningQuality, 4.5)}`}>
                                        {agent.reasoningQuality}
                                    </span>
                                </div>
                                <div className="cell">
                                    <span className={`score ${getScoreColor(agent.collaboration, 95)}`}>
                                        {agent.collaboration}%
                                    </span>
                                </div>
                                <div className="cell">
                                    <span className={`score ${getScoreColor(agent.accuracy, 95)}`}>
                                        {agent.accuracy}%
                                    </span>
                                </div>
                                <div className="cell">{agent.totalTasks}</div>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="reasoning-analysis">
                    <h3>Reasoning Quality Metrics</h3>
                    <div className="reasoning-metrics">
                        {reasoningMetrics.map(metric => (
                            <div key={metric.category} className="reasoning-item">
                                <div className="reasoning-header">
                                    <span className="reasoning-category">{metric.category}</span>
                                    <span className="reasoning-score">{metric.score}/5.0</span>
                                </div>
                                <div className="reasoning-progress">
                                    <div className="progress-bar">
                                        <div 
                                            className="progress-fill"
                                            style={{ width: `${(metric.score / 5) * 100}%` }}
                                        ></div>
                                    </div>
                                </div>
                                <div className="reasoning-improvement">
                                    +{metric.improvement}% improvement
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="collaboration-analysis">
                <h3>Collaboration Efficiency Analysis</h3>
                <div className="collaboration-grid">
                    {collaborationData.map((collab, index) => (
                        <div key={index} className="collaboration-item">
                            <div className="collaboration-flow">
                                <span className="from-agent">{collab.from}</span>
                                <span className="flow-arrow">→</span>
                                <span className="to-agent">{collab.to}</span>
                            </div>
                            <div className="collaboration-stats">
                                <div className="stat">
                                    <span className="stat-value">{collab.interactions}</span>
                                    <span className="stat-label">Interactions</span>
                                </div>
                                <div className="stat">
                                    <span className={`stat-value ${getScoreColor(collab.efficiency, 90)}`}>
                                        {collab.efficiency}%
                                    </span>
                                    <span className="stat-label">Efficiency</span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default EffectivenessAnalytics;