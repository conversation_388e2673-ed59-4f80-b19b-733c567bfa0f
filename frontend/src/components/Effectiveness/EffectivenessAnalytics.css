.effectiveness-analytics {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.effectiveness-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.effectiveness-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.agent-select {
    padding: 8px 12px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
}

.time-controls {
    display: flex;
    gap: 4px;
}

.time-btn {
    padding: 6px 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    color: #5f6368;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.time-btn:hover {
    background: #f8f9fa;
}

.time-btn.active {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.effectiveness-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.metric-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.2s;
}

.metric-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.metric-header h3 {
    margin: 0;
    color: #202124;
    font-size: 16px;
    font-weight: 500;
}

.trend {
    font-size: 18px;
}

.metric-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin-bottom: 8px;
}

.current {
    font-size: 32px;
    font-weight: 600;
    color: #1a73e8;
}

.unit {
    font-size: 14px;
    color: #5f6368;
}

.metric-target {
    font-size: 12px;
    color: #5f6368;
}

.analytics-sections {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.agent-performance,
.reasoning-analysis {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.agent-performance h3,
.reasoning-analysis h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.performance-table {
    display: flex;
    flex-direction: column;
}

.table-header {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.8fr;
    gap: 8px;
    padding: 12px 0;
    border-bottom: 2px solid #e8eaed;
    margin-bottom: 8px;
}

.header-cell {
    font-size: 12px;
    font-weight: 600;
    color: #5f6368;
    text-transform: uppercase;
}

.table-row {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.8fr;
    gap: 8px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.cell {
    font-size: 14px;
    color: #202124;
}

.agent-name {
    font-weight: 500;
}

.score {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.score.excellent {
    background: #e8f5e8;
    color: #137333;
}

.score.good {
    background: #fef7e0;
    color: #b06000;
}

.score.needs-improvement {
    background: #fce8e6;
    color: #d93025;
}

.reasoning-metrics {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.reasoning-item {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
}

.reasoning-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.reasoning-category {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.reasoning-score {
    font-size: 14px;
    font-weight: 600;
    color: #1a73e8;
}

.reasoning-progress {
    margin-bottom: 6px;
}

.progress-bar {
    height: 6px;
    background: #e8eaed;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

.reasoning-improvement {
    font-size: 12px;
    color: #137333;
    font-weight: 500;
}

.collaboration-analysis {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.collaboration-analysis h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.collaboration-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.collaboration-item {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1a73e8;
}

.collaboration-flow {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.from-agent,
.to-agent {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.flow-arrow {
    color: #5f6368;
    font-size: 16px;
}

.collaboration-stats {
    display: flex;
    gap: 16px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #1a73e8;
}

.stat-label {
    font-size: 11px;
    color: #5f6368;
    text-transform: uppercase;
}

@media (max-width: 768px) {
    .effectiveness-analytics {
        padding: 16px;
    }
    
    .effectiveness-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .controls {
        justify-content: space-between;
    }
    
    .effectiveness-overview {
        grid-template-columns: 1fr;
    }
    
    .analytics-sections {
        grid-template-columns: 1fr;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 4px;
    }
    
    .table-header {
        display: none;
    }
    
    .cell {
        padding: 4px 0;
        display: flex;
        justify-content: space-between;
    }
    
    .cell::before {
        content: attr(data-label);
        font-weight: 600;
        color: #5f6368;
        font-size: 12px;
    }
    
    .collaboration-grid {
        grid-template-columns: 1fr;
    }
    
    .collaboration-flow {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}