.performance-tracking {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.tracking-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.tracking-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.agent-select {
    padding: 8px 12px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
}

.time-range {
    display: flex;
    gap: 4px;
}

.range-btn {
    padding: 6px 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    color: #5f6368;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.range-btn:hover {
    background: #f8f9fa;
}

.range-btn.active {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.metric-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.2s;
}

.metric-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.metric-header h3 {
    margin: 0;
    color: #202124;
    font-size: 16px;
    font-weight: 500;
}

.metric-trend {
    font-size: 18px;
}

.metric-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin-bottom: 16px;
}

.current {
    font-size: 32px;
    font-weight: 600;
    color: #1a73e8;
}

.unit {
    font-size: 14px;
    color: #5f6368;
}

.metric-progress {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.progress-bar {
    height: 6px;
    background: #e8eaed;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.progress-fill.green {
    background: linear-gradient(90deg, #34a853, #66bb6a);
}

.progress-fill.orange {
    background: linear-gradient(90deg, #ff9800, #ffa726);
}

.progress-fill.red {
    background: linear-gradient(90deg, #ea4335, #ef5350);
}

.target {
    font-size: 12px;
    color: #5f6368;
}

.resource-utilization {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.resource-utilization h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.resource-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.resource-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.resource-label {
    min-width: 80px;
    font-size: 14px;
    color: #202124;
    font-weight: 500;
}

.resource-bar {
    flex: 1;
    height: 8px;
    background: #e8eaed;
    border-radius: 4px;
    overflow: hidden;
}

.resource-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

.resource-value {
    min-width: 40px;
    font-size: 14px;
    color: #202124;
    font-weight: 500;
    text-align: right;
}

@media (max-width: 768px) {
    .performance-tracking {
        padding: 16px;
    }
    
    .tracking-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .tracking-controls {
        justify-content: space-between;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .resource-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .resource-label {
        min-width: auto;
    }
    
    .resource-value {
        text-align: left;
    }
}