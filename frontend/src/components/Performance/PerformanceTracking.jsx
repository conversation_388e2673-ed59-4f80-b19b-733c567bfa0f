import React, { useState, useEffect } from 'react';
import './PerformanceTracking.css';

const PerformanceTracking = () => {
    const [metrics, setMetrics] = useState({
        throughput: { current: 45, target: 50, trend: 'up' },
        latency: { current: 120, target: 100, trend: 'down' },
        quality: { current: 92, target: 95, trend: 'up' },
        uptime: { current: 99.2, target: 99.5, trend: 'stable' }
    });

    const [timeRange, setTimeRange] = useState('24h');
    const [selectedAgent, setSelectedAgent] = useState('all');

    const agents = [
        { id: 'all', name: 'All Agents' },
        { id: 'hr_agent', name: 'HR Agent' },
        { id: 'general_agent', name: 'General Agent' },
        { id: 'policy_agent', name: 'Policy Agent' }
    ];

    const getMetricColor = (current, target, trend) => {
        if (current >= target) return 'green';
        if (trend === 'up') return 'orange';
        return 'red';
    };

    const getTrendIcon = (trend) => {
        switch (trend) {
            case 'up': return '↗️';
            case 'down': return '↘️';
            case 'stable': return '➡️';
            default: return '➡️';
        }
    };

    return (
        <div className="performance-tracking">
            <div className="tracking-header">
                <h2>Agent Performance Tracking</h2>
                <div className="tracking-controls">
                    <select 
                        value={selectedAgent} 
                        onChange={(e) => setSelectedAgent(e.target.value)}
                        className="agent-select"
                    >
                        {agents.map(agent => (
                            <option key={agent.id} value={agent.id}>{agent.name}</option>
                        ))}
                    </select>
                    <div className="time-range">
                        {['1h', '24h', '7d', '30d'].map(range => (
                            <button
                                key={range}
                                className={`range-btn ${timeRange === range ? 'active' : ''}`}
                                onClick={() => setTimeRange(range)}
                            >
                                {range}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            <div className="metrics-grid">
                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Throughput</h3>
                        <span className="metric-trend">{getTrendIcon(metrics.throughput.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{metrics.throughput.current}</span>
                        <span className="unit">req/min</span>
                    </div>
                    <div className="metric-progress">
                        <div className="progress-bar">
                            <div 
                                className={`progress-fill ${getMetricColor(metrics.throughput.current, metrics.throughput.target, metrics.throughput.trend)}`}
                                style={{ width: `${(metrics.throughput.current / metrics.throughput.target) * 100}%` }}
                            ></div>
                        </div>
                        <span className="target">Target: {metrics.throughput.target}</span>
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Latency</h3>
                        <span className="metric-trend">{getTrendIcon(metrics.latency.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{metrics.latency.current}</span>
                        <span className="unit">ms</span>
                    </div>
                    <div className="metric-progress">
                        <div className="progress-bar">
                            <div 
                                className={`progress-fill ${metrics.latency.current <= metrics.latency.target ? 'green' : 'red'}`}
                                style={{ width: `${Math.min((metrics.latency.target / metrics.latency.current) * 100, 100)}%` }}
                            ></div>
                        </div>
                        <span className="target">Target: ≤{metrics.latency.target}ms</span>
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Quality Score</h3>
                        <span className="metric-trend">{getTrendIcon(metrics.quality.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{metrics.quality.current}</span>
                        <span className="unit">%</span>
                    </div>
                    <div className="metric-progress">
                        <div className="progress-bar">
                            <div 
                                className={`progress-fill ${getMetricColor(metrics.quality.current, metrics.quality.target, metrics.quality.trend)}`}
                                style={{ width: `${metrics.quality.current}%` }}
                            ></div>
                        </div>
                        <span className="target">Target: {metrics.quality.target}%</span>
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Uptime</h3>
                        <span className="metric-trend">{getTrendIcon(metrics.uptime.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{metrics.uptime.current}</span>
                        <span className="unit">%</span>
                    </div>
                    <div className="metric-progress">
                        <div className="progress-bar">
                            <div 
                                className={`progress-fill ${getMetricColor(metrics.uptime.current, metrics.uptime.target, metrics.uptime.trend)}`}
                                style={{ width: `${metrics.uptime.current}%` }}
                            ></div>
                        </div>
                        <span className="target">Target: {metrics.uptime.target}%</span>
                    </div>
                </div>
            </div>

            <div className="resource-utilization">
                <h3>Resource Utilization</h3>
                <div className="resource-grid">
                    <div className="resource-item">
                        <span className="resource-label">CPU</span>
                        <div className="resource-bar">
                            <div className="resource-fill" style={{ width: '65%' }}></div>
                        </div>
                        <span className="resource-value">65%</span>
                    </div>
                    <div className="resource-item">
                        <span className="resource-label">Memory</span>
                        <div className="resource-bar">
                            <div className="resource-fill" style={{ width: '42%' }}></div>
                        </div>
                        <span className="resource-value">42%</span>
                    </div>
                    <div className="resource-item">
                        <span className="resource-label">Network</span>
                        <div className="resource-bar">
                            <div className="resource-fill" style={{ width: '28%' }}></div>
                        </div>
                        <span className="resource-value">28%</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PerformanceTracking;