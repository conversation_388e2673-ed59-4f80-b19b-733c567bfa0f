import React, { useState, useEffect } from 'react';
import './ActivityMonitoring.css';

const ActivityMonitoring = () => {
    const [activities, setActivities] = useState([
        { id: 1, agent: 'HR Agent', action: 'Processed leave request', user: 'john.doe', timestamp: '2024-01-15 10:32:15', status: 'success' },
        { id: 2, agent: 'General Agent', action: 'Answered policy question', user: 'jane.smith', timestamp: '2024-01-15 10:31:45', status: 'success' },
        { id: 3, agent: 'Policy Agent', action: 'Failed to parse document', user: 'system', timestamp: '2024-01-15 10:30:22', status: 'error' },
        { id: 4, agent: 'HR Agent', action: 'Updated employee record', user: 'admin', timestamp: '2024-01-15 10:29:18', status: 'success' },
        { id: 5, agent: 'General Agent', action: 'Routed complex query', user: 'bob.wilson', timestamp: '2024-01-15 10:28:33', status: 'warning' }
    ]);

    const [filter, setFilter] = useState('all');
    const [selectedAgent, setSelectedAgent] = useState('all');

    const agents = [
        { id: 'all', name: 'All Agents' },
        { id: 'hr_agent', name: 'HR Agent' },
        { id: 'general_agent', name: 'General Agent' },
        { id: 'policy_agent', name: 'Policy Agent' }
    ];

    const getStatusIcon = (status) => {
        switch (status) {
            case 'success': return '✅';
            case 'error': return '❌';
            case 'warning': return '⚠️';
            default: return '🔵';
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'success': return 'green';
            case 'error': return 'red';
            case 'warning': return 'orange';
            default: return 'blue';
        }
    };

    const filteredActivities = activities.filter(activity => {
        const matchesFilter = filter === 'all' || activity.status === filter;
        const matchesAgent = selectedAgent === 'all' || activity.agent.toLowerCase().includes(selectedAgent.replace('_', ' '));
        return matchesFilter && matchesAgent;
    });

    const activityStats = {
        total: activities.length,
        success: activities.filter(a => a.status === 'success').length,
        error: activities.filter(a => a.status === 'error').length,
        warning: activities.filter(a => a.status === 'warning').length
    };

    return (
        <div className="activity-monitoring">
            <div className="monitoring-header">
                <h2>Agent Activity Monitoring</h2>
                <div className="monitoring-controls">
                    <select 
                        value={selectedAgent} 
                        onChange={(e) => setSelectedAgent(e.target.value)}
                        className="agent-select"
                    >
                        {agents.map(agent => (
                            <option key={agent.id} value={agent.id}>{agent.name}</option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="activity-stats">
                <div className="stat-card">
                    <span className="stat-value">{activityStats.total}</span>
                    <span className="stat-label">Total Activities</span>
                </div>
                <div className="stat-card success">
                    <span className="stat-value">{activityStats.success}</span>
                    <span className="stat-label">Successful</span>
                </div>
                <div className="stat-card error">
                    <span className="stat-value">{activityStats.error}</span>
                    <span className="stat-label">Errors</span>
                </div>
                <div className="stat-card warning">
                    <span className="stat-value">{activityStats.warning}</span>
                    <span className="stat-label">Warnings</span>
                </div>
            </div>

            <div className="activity-filters">
                {['all', 'success', 'error', 'warning'].map(status => (
                    <button
                        key={status}
                        className={`filter-btn ${filter === status ? 'active' : ''} ${status}`}
                        onClick={() => setFilter(status)}
                    >
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                    </button>
                ))}
            </div>

            <div className="activity-feed">
                <div className="feed-header">
                    <h3>Real-time Activity Feed</h3>
                    <span className="activity-count">{filteredActivities.length} activities</span>
                </div>
                
                <div className="activity-list">
                    {filteredActivities.map(activity => (
                        <div key={activity.id} className={`activity-item ${activity.status}`}>
                            <div className="activity-icon">
                                {getStatusIcon(activity.status)}
                            </div>
                            <div className="activity-content">
                                <div className="activity-main">
                                    <span className="activity-agent">{activity.agent}</span>
                                    <span className="activity-action">{activity.action}</span>
                                </div>
                                <div className="activity-meta">
                                    <span className="activity-user">User: {activity.user}</span>
                                    <span className="activity-time">{activity.timestamp}</span>
                                </div>
                            </div>
                            <div className={`activity-status ${getStatusColor(activity.status)}`}>
                                {activity.status}
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <div className="interaction-graph">
                <h3>Agent Interaction Graph</h3>
                <div className="graph-placeholder">
                    <div className="graph-node hr">HR Agent</div>
                    <div className="graph-node general">General Agent</div>
                    <div className="graph-node policy">Policy Agent</div>
                    <div className="graph-connections">
                        <div className="connection hr-general"></div>
                        <div className="connection general-policy"></div>
                        <div className="connection hr-policy"></div>
                    </div>
                    <div className="graph-stats">
                        <div className="connection-stat">
                            <span className="connection-label">HR ↔ General</span>
                            <span className="connection-count">23 interactions</span>
                        </div>
                        <div className="connection-stat">
                            <span className="connection-label">General ↔ Policy</span>
                            <span className="connection-count">15 interactions</span>
                        </div>
                        <div className="connection-stat">
                            <span className="connection-label">HR ↔ Policy</span>
                            <span className="connection-count">8 interactions</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ActivityMonitoring;