.activity-monitoring {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.monitoring-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.monitoring-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.agent-select {
    padding: 8px 12px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
}

.activity-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: box-shadow 0.2s;
}

.stat-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-card.success {
    border-left: 4px solid #34a853;
}

.stat-card.error {
    border-left: 4px solid #ea4335;
}

.stat-card.warning {
    border-left: 4px solid #ff9800;
}

.stat-value {
    display: block;
    font-size: 32px;
    font-weight: 600;
    color: #1a73e8;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #5f6368;
    font-weight: 500;
}

.activity-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    background: #ffffff;
    color: #5f6368;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.filter-btn:hover {
    background: #f8f9fa;
}

.filter-btn.active {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.filter-btn.success.active {
    background: #34a853;
    border-color: #34a853;
}

.filter-btn.error.active {
    background: #ea4335;
    border-color: #ea4335;
}

.filter-btn.warning.active {
    background: #ff9800;
    border-color: #ff9800;
}

.activity-feed {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
}

.feed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.feed-header h3 {
    margin: 0;
    color: #202124;
    font-size: 18px;
}

.activity-count {
    font-size: 14px;
    color: #5f6368;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 1px solid #f1f3f4;
    border-radius: 6px;
    transition: all 0.2s;
}

.activity-item:hover {
    background: #f8f9fa;
    border-color: #e8eaed;
}

.activity-item.success {
    border-left: 3px solid #34a853;
}

.activity-item.error {
    border-left: 3px solid #ea4335;
}

.activity-item.warning {
    border-left: 3px solid #ff9800;
}

.activity-icon {
    font-size: 18px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.activity-main {
    display: flex;
    gap: 8px;
    align-items: center;
}

.activity-agent {
    font-weight: 500;
    color: #1a73e8;
    font-size: 14px;
}

.activity-action {
    color: #202124;
    font-size: 14px;
}

.activity-meta {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #5f6368;
}

.activity-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.activity-status.green {
    background: #e8f5e8;
    color: #137333;
}

.activity-status.red {
    background: #fce8e6;
    color: #d93025;
}

.activity-status.orange {
    background: #fef7e0;
    color: #b06000;
}

.interaction-graph {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.interaction-graph h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
}

.graph-placeholder {
    position: relative;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.graph-node {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    color: white;
    position: relative;
    z-index: 2;
}

.graph-node.hr {
    background: linear-gradient(135deg, #1a73e8, #4b90ff);
}

.graph-node.general {
    background: linear-gradient(135deg, #34a853, #66bb6a);
}

.graph-node.policy {
    background: linear-gradient(135deg, #ff9800, #ffa726);
}

.graph-connections {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.connection {
    position: absolute;
    height: 2px;
    background: #e8eaed;
    top: 50%;
    transform: translateY(-50%);
}

.connection.hr-general {
    left: 25%;
    width: 25%;
}

.connection.general-policy {
    left: 50%;
    width: 25%;
}

.connection.hr-policy {
    left: 25%;
    width: 50%;
    background: #f1f3f4;
}

.graph-stats {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
    gap: 16px;
}

.connection-stat {
    text-align: center;
    font-size: 12px;
}

.connection-label {
    display: block;
    color: #5f6368;
    margin-bottom: 4px;
}

.connection-count {
    display: block;
    color: #202124;
    font-weight: 500;
}

@media (max-width: 768px) {
    .activity-monitoring {
        padding: 16px;
    }
    
    .monitoring-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .activity-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .activity-filters {
        flex-wrap: wrap;
    }
    
    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .activity-main {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .graph-placeholder {
        height: 150px;
        flex-direction: column;
        gap: 20px;
    }
    
    .graph-node {
        width: 60px;
        height: 60px;
        font-size: 10px;
    }
    
    .graph-stats {
        flex-direction: column;
        gap: 8px;
    }
}