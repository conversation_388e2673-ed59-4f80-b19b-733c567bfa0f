.step-by-step {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.steps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.steps-header h4 {
    margin: 0;
    font-size: 16px;
    color: #202124;
}

.step-counter {
    font-size: 14px;
    color: #5f6368;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
}

.steps-timeline {
    position: relative;
}

.steps-timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e8eaed;
}

.step-item {
    display: flex;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.2s;
}

.step-item:hover {
    transform: translateX(4px);
}

.step-marker {
    position: relative;
    z-index: 1;
    margin-right: 16px;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.step-item.pending .step-number {
    background-color: #f8f9fa;
    color: #5f6368;
    border: 2px solid #e8eaed;
}

.step-item.active .step-number {
    background-color: #1a73e8;
    color: white;
    border: 2px solid #1a73e8;
    box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.2);
}

.step-item.completed .step-number {
    background-color: #34a853;
    color: white;
    border: 2px solid #34a853;
}

.step-content {
    flex: 1;
    padding: 4px 0;
}

.step-title {
    font-size: 15px;
    font-weight: 500;
    color: #202124;
    margin-bottom: 6px;
}

.step-description {
    font-size: 14px;
    color: #5f6368;
    line-height: 1.4;
    margin-bottom: 8px;
}

.step-reasoning {
    font-size: 13px;
    color: #1a73e8;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.step-evidence {
    font-size: 13px;
    color: #202124;
    margin-bottom: 8px;
}

.step-evidence ul {
    margin: 4px 0 0 16px;
    padding: 0;
}

.step-evidence li {
    margin-bottom: 2px;
    color: #5f6368;
}

.step-confidence {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.confidence-label {
    font-size: 12px;
    color: #5f6368;
    font-weight: 500;
}

.confidence-bar {
    flex: 1;
    height: 6px;
    background-color: #e8eaed;
    border-radius: 3px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff5546 0%, #ffa726 50%, #4b90ff 100%);
    transition: width 0.3s ease;
}

.confidence-value {
    font-size: 12px;
    color: #202124;
    font-weight: 500;
    min-width: 35px;
}

.steps-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e8eaed;
}

.nav-button {
    padding: 8px 16px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background-color: #ffffff;
    color: #1a73e8;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.nav-button:hover:not(:disabled) {
    background-color: #f8f9fa;
    border-color: #1a73e8;
}

.nav-button:disabled {
    color: #9aa0a6;
    cursor: not-allowed;
    border-color: #e8eaed;
}

.step-by-step-empty {
    text-align: center;
    color: #5f6368;
    padding: 40px;
    font-style: italic;
}

@media (max-width: 800px) {
    .steps-timeline::before {
        left: 15px;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
    
    .step-marker {
        margin-right: 12px;
    }
    
    .steps-navigation {
        flex-direction: column;
        gap: 8px;
    }
}