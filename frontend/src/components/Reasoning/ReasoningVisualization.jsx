import React, { useState } from 'react';
import './ReasoningVisualization.css';
import ReasoningTree from './ReasoningTree';
import StepByStep from './StepByStep';
import SourceAttribution from '../Sources/SourceAttribution';
import ConfidenceVisualization from '../Confidence/ConfidenceVisualization';

const ReasoningVisualization = ({ reasoningData, isVisible, onClose }) => {
    const [activeView, setActiveView] = useState('tree');

    if (!isVisible || !reasoningData) return null;

    const mockTreeData = {
        root: {
            id: 'root',
            title: 'HR Policy Analysis',
            description: 'Analyzing privilege leave policy for NUVO AI',
            confidence: 0.87,
            reasoning: 'Starting comprehensive policy analysis',
            children: [
                {
                    id: 'search',
                    title: 'Policy Search',
                    description: 'Searching for relevant HR policies',
                    confidence: 0.92,
                    reasoning: 'Found 3 relevant policy documents',
                    children: [
                        {
                            id: 'policy1',
                            title: 'Leave Policy Document',
                            description: 'Main leave policy document found',
                            confidence: 0.95,
                            reasoning: 'High confidence in policy accuracy'
                        }
                    ]
                },
                {
                    id: 'analysis',
                    title: 'Policy Analysis',
                    description: 'Analyzing policy requirements',
                    confidence: 0.83,
                    reasoning: 'Cross-referencing multiple sources',
                    children: []
                }
            ]
        },
        totalNodes: 4,
        totalPaths: 2,
        paths: [
            {
                nodes: ['root', 'search', 'policy1'],
                confidence: 0.91,
                description: 'Direct policy lookup path'
            },
            {
                nodes: ['root', 'analysis'],
                confidence: 0.83,
                description: 'Analytical reasoning path'
            }
        ]
    };

    const mockSteps = [
        {
            title: 'Query Understanding',
            description: 'Analyzing the user query about privilege leave policy',
            reasoning: 'Identified key terms: privilege leave, policy, NUVO AI',
            evidence: ['Query contains specific policy request', 'Organization context provided'],
            confidence: 0.95
        },
        {
            title: 'Knowledge Search',
            description: 'Searching organizational knowledge base',
            reasoning: 'Using semantic search across HR documents',
            evidence: ['Found 3 relevant documents', 'High semantic similarity scores'],
            confidence: 0.88
        },
        {
            title: 'Policy Extraction',
            description: 'Extracting relevant policy information',
            reasoning: 'Parsing structured policy data',
            evidence: ['Policy section identified', 'Eligibility criteria found'],
            confidence: 0.92
        },
        {
            title: 'Response Generation',
            description: 'Generating comprehensive response',
            reasoning: 'Combining policy data with context',
            evidence: ['All requirements addressed', 'Clear explanation provided'],
            confidence: 0.87
        }
    ];

    const mockSources = [
        {
            id: 'src1',
            title: 'NUVO AI Employee Handbook',
            type: 'policy',
            credibility: 0.95,
            relevance: 0.92,
            excerpt: 'Privilege leave is granted to employees with more than 2 years of service...',
            url: '#',
            lastUpdated: '2024-01-15'
        },
        {
            id: 'src2',
            title: 'HR Policy Database',
            type: 'database',
            credibility: 0.88,
            relevance: 0.85,
            excerpt: 'Leave policies are updated annually and require manager approval...',
            url: '#',
            lastUpdated: '2024-02-01'
        }
    ];

    const mockConfidence = {
        confidence: 0.87,
        uncertainty: 0.13,
        factors: [
            {
                name: 'Source Reliability',
                impact: 0.15,
                description: 'Official HR documents provide high reliability'
            },
            {
                name: 'Policy Recency',
                impact: 0.08,
                description: 'Recent policy updates increase confidence'
            },
            {
                name: 'Context Completeness',
                impact: -0.05,
                description: 'Some context details missing'
            }
        ],
        alternatives: [
            {
                title: 'Alternative Interpretation',
                description: 'Policy might have different eligibility criteria',
                confidence: 0.23,
                likelihood: 0.15
            }
        ]
    };

    return (
        <div className="reasoning-visualization-overlay">
            <div className="reasoning-visualization-modal">
                <div className="modal-header">
                    <h3>Reasoning Analysis</h3>
                    <div className="view-tabs">
                        <button 
                            className={`tab ${activeView === 'tree' ? 'active' : ''}`}
                            onClick={() => setActiveView('tree')}
                        >
                            Tree View
                        </button>
                        <button 
                            className={`tab ${activeView === 'steps' ? 'active' : ''}`}
                            onClick={() => setActiveView('steps')}
                        >
                            Step-by-Step
                        </button>
                        <button 
                            className={`tab ${activeView === 'sources' ? 'active' : ''}`}
                            onClick={() => setActiveView('sources')}
                        >
                            Sources
                        </button>
                        <button 
                            className={`tab ${activeView === 'confidence' ? 'active' : ''}`}
                            onClick={() => setActiveView('confidence')}
                        >
                            Confidence
                        </button>
                    </div>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>

                <div className="modal-content">
                    {activeView === 'tree' && (
                        <ReasoningTree 
                            treeData={mockTreeData}
                            onNodeClick={(node) => console.log('Node clicked:', node)}
                        />
                    )}
                    
                    {activeView === 'steps' && (
                        <StepByStep 
                            steps={mockSteps}
                            onStepClick={(index, step) => console.log('Step clicked:', index, step)}
                        />
                    )}
                    
                    {activeView === 'sources' && (
                        <SourceAttribution 
                            sources={mockSources}
                            onSourceClick={(source) => console.log('Source clicked:', source)}
                            highlightedText="<mark>Privilege leave</mark> is granted to employees with <mark>more than 2 years</mark> of service"
                        />
                    )}
                    
                    {activeView === 'confidence' && (
                        <ConfidenceVisualization 
                            confidence={mockConfidence.confidence}
                            uncertainty={mockConfidence.uncertainty}
                            factors={mockConfidence.factors}
                            alternatives={mockConfidence.alternatives}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default ReasoningVisualization;