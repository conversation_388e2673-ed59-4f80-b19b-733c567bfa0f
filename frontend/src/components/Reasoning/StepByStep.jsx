import React, { useState } from 'react';
import './StepByStep.css';

const StepByStep = ({ steps, currentStep = 0, onStepClick }) => {
    const [activeStep, setActiveStep] = useState(currentStep);

    const handleStepClick = (index) => {
        setActiveStep(index);
        onStepClick && onStepClick(index, steps[index]);
    };

    const getStepStatus = (index) => {
        if (index < activeStep) return 'completed';
        if (index === activeStep) return 'active';
        return 'pending';
    };

    if (!steps || steps.length === 0) {
        return <div className="step-by-step-empty">No reasoning steps available</div>;
    }

    return (
        <div className="step-by-step">
            <div className="steps-header">
                <h4>Step-by-Step Reasoning</h4>
                <span className="step-counter">{activeStep + 1} of {steps.length}</span>
            </div>

            <div className="steps-timeline">
                {steps.map((step, index) => (
                    <div 
                        key={index}
                        className={`step-item ${getStepStatus(index)}`}
                        onClick={() => handleStepClick(index)}
                    >
                        <div className="step-marker">
                            <span className="step-number">{index + 1}</span>
                        </div>
                        
                        <div className="step-content">
                            <div className="step-title">{step.title}</div>
                            <div className="step-description">{step.description}</div>
                            
                            {step.reasoning && (
                                <div className="step-reasoning">
                                    <strong>Reasoning:</strong> {step.reasoning}
                                </div>
                            )}
                            
                            {step.evidence && step.evidence.length > 0 && (
                                <div className="step-evidence">
                                    <strong>Evidence:</strong>
                                    <ul>
                                        {step.evidence.map((evidence, idx) => (
                                            <li key={idx}>{evidence}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                            
                            {step.confidence && (
                                <div className="step-confidence">
                                    <span className="confidence-label">Confidence:</span>
                                    <div className="confidence-bar">
                                        <div 
                                            className="confidence-fill"
                                            style={{ width: `${step.confidence * 100}%` }}
                                        ></div>
                                    </div>
                                    <span className="confidence-value">
                                        {Math.round(step.confidence * 100)}%
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            <div className="steps-navigation">
                <button 
                    onClick={() => handleStepClick(Math.max(0, activeStep - 1))}
                    disabled={activeStep === 0}
                    className="nav-button prev"
                >
                    Previous
                </button>
                <button 
                    onClick={() => handleStepClick(Math.min(steps.length - 1, activeStep + 1))}
                    disabled={activeStep === steps.length - 1}
                    className="nav-button next"
                >
                    Next
                </button>
            </div>
        </div>
    );
};

export default StepByStep;