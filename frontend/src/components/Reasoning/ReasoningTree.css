.reasoning-tree {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.tree-header h3 {
    margin: 0;
    font-size: 18px;
    color: #202124;
}

.tree-stats {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #5f6368;
}

.tree-content {
    display: flex;
    gap: 20px;
}

.tree-visualization {
    flex: 2;
}

.reasoning-paths {
    flex: 1;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
}

.reasoning-paths h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #202124;
}

.reasoning-node {
    margin: 8px 0;
}

.reasoning-node.level-0 {
    margin-left: 0;
}

.reasoning-node.level-1 {
    margin-left: 20px;
}

.reasoning-node.level-2 {
    margin-left: 40px;
}

.reasoning-node.level-3 {
    margin-left: 60px;
}

.node-content {
    background-color: #f8f9fa;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.node-content:hover {
    background-color: #f1f3f4;
    border-color: #dadce0;
}

.node-content.selected {
    background-color: #e8f0fe;
    border-color: #1a73e8;
}

.node-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
}

.expand-icon {
    font-size: 12px;
    color: #5f6368;
    transition: transform 0.2s;
    cursor: pointer;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

.node-title {
    font-weight: 500;
    color: #202124;
    flex: 1;
}

.confidence-badge {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.confidence-badge.high {
    background-color: #e8f5e8;
    color: #137333;
}

.confidence-badge.medium {
    background-color: #fef7e0;
    color: #b06000;
}

.confidence-badge.low {
    background-color: #fce8e6;
    color: #d93025;
}

.node-description {
    font-size: 13px;
    color: #5f6368;
    margin-bottom: 4px;
}

.node-reasoning {
    font-size: 12px;
    color: #1a73e8;
    font-style: italic;
}

.node-children {
    margin-top: 8px;
    border-left: 2px solid #e8eaed;
    padding-left: 12px;
}

.path-item {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.path-item:hover {
    background-color: #f8f9fa;
}

.path-item.selected {
    background-color: #e8f0fe;
    border-color: #1a73e8;
}

.path-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.path-name {
    font-weight: 500;
    font-size: 13px;
    color: #202124;
}

.path-confidence {
    padding: 1px 4px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
}

.path-description {
    font-size: 12px;
    color: #5f6368;
}

.reasoning-tree-empty {
    text-align: center;
    color: #5f6368;
    padding: 40px;
    font-style: italic;
}

@media (max-width: 800px) {
    .tree-content {
        flex-direction: column;
    }
    
    .reasoning-paths {
        order: -1;
    }
    
    .reasoning-node.level-1 {
        margin-left: 15px;
    }
    
    .reasoning-node.level-2 {
        margin-left: 30px;
    }
    
    .reasoning-node.level-3 {
        margin-left: 45px;
    }
}