import React, { useState, useEffect } from 'react';

const ReasoningTree = ({ reasoningData }) => {
  const [expandedNodes, setExpandedNodes] = useState(new Set());

  const toggleNode = (nodeId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const renderNode = (node, level = 0) => {
    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = expandedNodes.has(node.id);

    return (
      <div key={node.id} className={`reasoning-node level-${level}`}>
        <div className="node-content" onClick={() => hasChildren && toggleNode(node.id)}>
          <div className="node-header">
            {hasChildren && (
              <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>
                ▶
              </span>
            )}
            <span className="node-title">{node.step}</span>
            <span className="confidence-badge">{(node.confidence * 100).toFixed(0)}%</span>
          </div>
          {node.evidence && node.evidence.length > 0 && (
            <div className="evidence-list">
              {node.evidence.map((evidence, idx) => (
                <span key={idx} className="evidence-item">{evidence}</span>
              ))}
            </div>
          )}
        </div>
        {hasChildren && isExpanded && (
          <div className="children-container">
            {node.children.map(child => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (!reasoningData || !reasoningData.reasoning) {
    return <div className="reasoning-tree">No reasoning data available</div>;
  }

  return (
    <div className="reasoning-tree">
      <h3>Reasoning Process</h3>
      <div className="tree-container">
        {reasoningData.reasoning.map(node => renderNode(node))}
      </div>
    </div>
  );
};

export default ReasoningTree;