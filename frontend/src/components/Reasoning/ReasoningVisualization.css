.reasoning-visualization-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.reasoning-visualization-modal {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e8eaed;
    background-color: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    color: #202124;
}

.view-tabs {
    display: flex;
    gap: 4px;
}

.view-tabs .tab {
    padding: 8px 16px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    background-color: #ffffff;
    color: #5f6368;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.view-tabs .tab:hover {
    background-color: #f1f3f4;
}

.view-tabs .tab.active {
    background-color: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.close-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background-color: #f1f3f4;
    color: #5f6368;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.close-button:hover {
    background-color: #e8eaed;
    color: #202124;
}

.modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.modal-content > div {
    margin: 0;
    border: none;
    border-radius: 0;
}

@media (max-width: 800px) {
    .reasoning-visualization-overlay {
        padding: 10px;
    }
    
    .reasoning-visualization-modal {
        width: 95%;
        max-height: 95vh;
    }
    
    .modal-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }
    
    .view-tabs {
        width: 100%;
        justify-content: space-between;
    }
    
    .view-tabs .tab {
        flex: 1;
        text-align: center;
        padding: 6px 8px;
        font-size: 12px;
    }
    
    .close-button {
        position: absolute;
        top: 16px;
        right: 16px;
    }
}