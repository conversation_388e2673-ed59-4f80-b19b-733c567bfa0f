.content-management {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.content-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.upload-button {
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.upload-button:hover {
    opacity: 0.9;
}

.content-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    gap: 16px;
}

.search-bar {
    flex: 1;
    max-width: 300px;
}

.search-bar input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    font-size: 14px;
}

.search-bar input:focus {
    outline: none;
    border-color: #1a73e8;
}

.filter-tabs {
    display: flex;
    gap: 4px;
}

.filter-tab {
    padding: 8px 16px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    background: #ffffff;
    color: #5f6368;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.filter-tab:hover {
    background: #f8f9fa;
}

.filter-tab.active {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.content-table {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr 1.5fr;
    background: #f8f9fa;
    border-bottom: 1px solid #e8eaed;
}

.header-cell {
    padding: 16px;
    font-weight: 600;
    color: #202124;
    font-size: 14px;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr 1.5fr;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s;
}

.table-row:hover {
    background: #f8f9fa;
}

.cell {
    padding: 16px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #202124;
}

.content-info {
    flex-direction: column;
    align-items: flex-start;
}

.content-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.content-title {
    font-weight: 500;
    color: #202124;
}

.content-size {
    font-size: 12px;
    color: #5f6368;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.green {
    background: #e8f5e8;
    color: #137333;
}

.status-badge.orange {
    background: #fef7e0;
    color: #b06000;
}

.status-badge.blue {
    background: #e8f0fe;
    color: #1a73e8;
}

.status-badge.gray {
    background: #f1f3f4;
    color: #5f6368;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.tag {
    background: #f1f3f4;
    color: #5f6368;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 500;
}

.actions {
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    color: #1a73e8;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.action-btn:hover {
    background: #f8f9fa;
    border-color: #1a73e8;
}

.status-select {
    padding: 4px 8px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: #ffffff;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e8eaed;
}

.modal-header h3 {
    margin: 0;
    color: #202124;
}

.modal-header button {
    background: none;
    border: none;
    font-size: 24px;
    color: #5f6368;
    cursor: pointer;
    padding: 4px;
}

.upload-area {
    padding: 40px 20px;
}

.upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed #e8eaed;
    border-radius: 8px;
    padding: 40px;
    cursor: pointer;
    transition: all 0.2s;
}

.upload-label:hover {
    border-color: #1a73e8;
    background: #f8f9fa;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.upload-text {
    text-align: center;
}

.upload-text p {
    margin: 0;
    color: #202124;
    font-weight: 500;
}

.upload-hint {
    color: #5f6368 !important;
    font-size: 12px !important;
    font-weight: normal !important;
    margin-top: 8px !important;
}

@media (max-width: 768px) {
    .content-management {
        padding: 16px;
    }
    
    .content-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .content-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .search-bar {
        max-width: none;
    }
    
    .filter-tabs {
        justify-content: space-between;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .table-header {
        display: none;
    }
    
    .cell {
        padding: 8px 16px;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .cell::before {
        content: attr(data-label);
        font-weight: 600;
        margin-right: 8px;
        color: #5f6368;
        font-size: 12px;
    }
}