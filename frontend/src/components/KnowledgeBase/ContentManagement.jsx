import React, { useState } from 'react';
import './ContentManagement.css';

const ContentManagement = () => {
    const [content, setContent] = useState([
        { 
            id: 1, 
            title: 'Employee Handbook', 
            type: 'document', 
            status: 'published', 
            tags: ['HR', 'Policy'], 
            lastModified: '2024-01-15',
            size: '2.3 MB'
        },
        { 
            id: 2, 
            title: 'Leave Policy Guidelines', 
            type: 'document', 
            status: 'draft', 
            tags: ['HR', 'Leave'], 
            lastModified: '2024-01-10',
            size: '1.1 MB'
        },
        { 
            id: 3, 
            title: 'Benefits Overview', 
            type: 'document', 
            status: 'review', 
            tags: ['HR', 'Benefits'], 
            lastModified: '2024-01-08',
            size: '850 KB'
        }
    ]);
    const [showUpload, setShowUpload] = useState(false);
    const [filter, setFilter] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');

    const handleFileUpload = (e) => {
        const files = Array.from(e.target.files);
        files.forEach(file => {
            const newContent = {
                id: Date.now() + Math.random(),
                title: file.name,
                type: 'document',
                status: 'draft',
                tags: [],
                lastModified: new Date().toISOString().split('T')[0],
                size: `${(file.size / 1024 / 1024).toFixed(1)} MB`
            };
            setContent(prev => [...prev, newContent]);
        });
        setShowUpload(false);
    };

    const updateStatus = (id, newStatus) => {
        setContent(prev => prev.map(item => 
            item.id === id ? { ...item, status: newStatus } : item
        ));
    };

    const filteredContent = content.filter(item => {
        const matchesFilter = filter === 'all' || item.status === filter;
        const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
        return matchesFilter && matchesSearch;
    });

    const getStatusColor = (status) => {
        switch (status) {
            case 'published': return 'green';
            case 'draft': return 'orange';
            case 'review': return 'blue';
            default: return 'gray';
        }
    };

    return (
        <div className="content-management">
            <div className="content-header">
                <h2>Knowledge Base Management</h2>
                <button className="upload-button" onClick={() => setShowUpload(true)}>
                    📁 Upload Content
                </button>
            </div>

            <div className="content-controls">
                <div className="search-bar">
                    <input
                        type="text"
                        placeholder="Search content..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
                <div className="filter-tabs">
                    {['all', 'published', 'draft', 'review'].map(status => (
                        <button
                            key={status}
                            className={`filter-tab ${filter === status ? 'active' : ''}`}
                            onClick={() => setFilter(status)}
                        >
                            {status.charAt(0).toUpperCase() + status.slice(1)}
                        </button>
                    ))}
                </div>
            </div>

            <div className="content-table">
                <div className="table-header">
                    <div className="header-cell">Content</div>
                    <div className="header-cell">Type</div>
                    <div className="header-cell">Status</div>
                    <div className="header-cell">Tags</div>
                    <div className="header-cell">Modified</div>
                    <div className="header-cell">Actions</div>
                </div>
                
                {filteredContent.map(item => (
                    <div key={item.id} className="table-row">
                        <div className="cell content-info">
                            <div className="content-details">
                                <span className="content-title">{item.title}</span>
                                <span className="content-size">{item.size}</span>
                            </div>
                        </div>
                        <div className="cell">{item.type}</div>
                        <div className="cell">
                            <span className={`status-badge ${getStatusColor(item.status)}`}>
                                {item.status}
                            </span>
                        </div>
                        <div className="cell">
                            <div className="tags">
                                {item.tags.map(tag => (
                                    <span key={tag} className="tag">{tag}</span>
                                ))}
                            </div>
                        </div>
                        <div className="cell">{item.lastModified}</div>
                        <div className="cell actions">
                            <button className="action-btn edit">Edit</button>
                            <select 
                                className="status-select"
                                value={item.status}
                                onChange={(e) => updateStatus(item.id, e.target.value)}
                            >
                                <option value="draft">Draft</option>
                                <option value="review">Review</option>
                                <option value="published">Published</option>
                            </select>
                        </div>
                    </div>
                ))}
            </div>

            {showUpload && (
                <div className="modal-overlay">
                    <div className="modal">
                        <div className="modal-header">
                            <h3>Upload Content</h3>
                            <button onClick={() => setShowUpload(false)}>×</button>
                        </div>
                        <div className="upload-area">
                            <input
                                type="file"
                                multiple
                                accept=".pdf,.doc,.docx,.txt,.md"
                                onChange={handleFileUpload}
                                id="file-upload"
                                style={{ display: 'none' }}
                            />
                            <label htmlFor="file-upload" className="upload-label">
                                <div className="upload-icon">📁</div>
                                <div className="upload-text">
                                    <p>Click to upload files</p>
                                    <p className="upload-hint">PDF, DOC, DOCX, TXT, MD files supported</p>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ContentManagement;