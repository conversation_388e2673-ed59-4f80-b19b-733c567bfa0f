import React from 'react';
import './ContextDisplay.css';
import { assets } from '../../assets/assets.js';

const ContextDisplay = ({ context, confidence }) => {
    if (!context) return null;

    return (
        <div className="context-display">
            <div className="context-header">
                <img src={assets.setting_icon} alt="context" className="context-icon" />
                <span className="context-title">Context</span>
            </div>
            
            <div className="context-content">
                {context.organization && (
                    <div className="context-item organization">
                        <span className="context-label">Organization</span>
                        <span className="context-value">{context.organization}</span>
                    </div>
                )}
                
                {context.department && (
                    <div className="context-item department">
                        <span className="context-label">Department</span>
                        <span className="context-value">{context.department}</span>
                    </div>
                )}
                
                {context.user && (
                    <div className="context-item user">
                        <span className="context-label">User</span>
                        <span className="context-value">{context.user}</span>
                    </div>
                )}
                
                {context.domain && (
                    <div className="context-item domain">
                        <span className="context-label">Domain</span>
                        <span className="context-value">{context.domain}</span>
                    </div>
                )}
            </div>
            
            {confidence && (
                <div className="context-confidence">
                    <div className="confidence-bar">
                        <div 
                            className="confidence-fill" 
                            style={{ width: `${confidence * 100}%` }}
                        ></div>
                    </div>
                    <span className="confidence-text">
                        {Math.round(confidence * 100)}% confidence
                    </span>
                </div>
            )}
        </div>
    );
};

export default ContextDisplay;