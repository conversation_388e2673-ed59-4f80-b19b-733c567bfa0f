.context-display {
    background-color: #f8f9fa;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.context-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.context-icon {
    width: 16px;
    height: 16px;
}

.context-title {
    font-size: 14px;
    font-weight: 500;
    color: #5f6368;
}

.context-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.context-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
}

.context-label {
    font-size: 13px;
    color: #5f6368;
    font-weight: 400;
}

.context-value {
    font-size: 13px;
    color: #202124;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
}

.context-item.organization .context-value {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.context-item.department .context-value {
    background-color: #e6f4ea;
    color: #137333;
}

.context-item.user .context-value {
    background-color: #fef7e0;
    color: #b06000;
}

.context-item.domain .context-value {
    background-color: #f3e8fd;
    color: #7b1fa2;
}

.context-confidence {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e8eaed;
}

.confidence-bar {
    width: 100%;
    height: 4px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff5546 0%, #ffa726 50%, #4b90ff 100%);
    transition: width 0.3s ease;
}

.confidence-text {
    font-size: 12px;
    color: #5f6368;
}

@media (max-width: 800px) {
    .context-display {
        margin: 12px 0;
        padding: 12px;
    }
    
    .context-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}