import React, { useState } from 'react';
import './SourcesPanel.css';

const SourcesPanel = ({ sources = [], isFullView = false, isCompact = false }) => {
    const [expandedSource, setExpandedSource] = useState(null);
    const [filterType, setFilterType] = useState('all');

    const getSourceIcon = (type) => {
        switch (type) {
            case 'document': return '📄';
            case 'database': return '🗄️';
            case 'api': return '🔗';
            case 'knowledge_base': return '📚';
            case 'agent_reasoning': return '🤖';
            case 'vector_search': return '🔍';
            case 'graph_search': return '🕸️';
            default: return '📁';
        }
    };

    const getSourceTypeColor = (type) => {
        switch (type) {
            case 'document': return '#3498db';
            case 'database': return '#e74c3c';
            case 'api': return '#2ecc71';
            case 'knowledge_base': return '#9b59b6';
            case 'agent_reasoning': return '#f39c12';
            case 'vector_search': return '#1abc9c';
            case 'graph_search': return '#e67e22';
            default: return '#95a5a6';
        }
    };

    const formatConfidence = (confidence) => {
        return `${Math.round(confidence * 100)}%`;
    };

    const filteredSources = sources.filter(source => 
        filterType === 'all' || source.type === filterType
    );

    const sourceTypes = [...new Set(sources.map(source => source.type))];

    if (isCompact) {
        return (
            <div className="sources-panel compact">
                <div className="sources-header">
                    <h4>Sources ({sources.length})</h4>
                </div>
                <div className="sources-list compact">
                    {sources.slice(0, 3).map((source, index) => (
                        <div key={index} className="source-item compact">
                            <span className="source-icon">{getSourceIcon(source.type)}</span>
                            <div className="source-info">
                                <span className="source-name">{source.name || `Source ${index + 1}`}</span>
                                <span className="source-confidence">
                                    {formatConfidence(source.confidence || 0.8)}
                                </span>
                            </div>
                        </div>
                    ))}
                    {sources.length > 3 && (
                        <div className="more-sources">
                            +{sources.length - 3} more sources
                        </div>
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className={`sources-panel ${isFullView ? 'full-view' : ''}`}>
            <div className="sources-header">
                <h3>Knowledge Sources</h3>
                <div className="sources-stats">
                    <span className="total-sources">{sources.length} sources</span>
                    <span className="avg-confidence">
                        Avg: {sources.length > 0 ? formatConfidence(
                            sources.reduce((sum, s) => sum + (s.confidence || 0.8), 0) / sources.length
                        ) : '0%'}
                    </span>
                </div>
            </div>

            {isFullView && sourceTypes.length > 1 && (
                <div className="sources-filter">
                    <select 
                        value={filterType} 
                        onChange={(e) => setFilterType(e.target.value)}
                        className="filter-select"
                    >
                        <option value="all">All Types</option>
                        {sourceTypes.map(type => (
                            <option key={type} value={type}>
                                {getSourceIcon(type)} {type.replace('_', ' ').toUpperCase()}
                            </option>
                        ))}
                    </select>
                </div>
            )}

            <div className="sources-list">
                {filteredSources.map((source, index) => (
                    <div 
                        key={index} 
                        className={`source-item ${expandedSource === index ? 'expanded' : ''}`}
                    >
                        <div 
                            className="source-header"
                            onClick={() => setExpandedSource(expandedSource === index ? null : index)}
                        >
                            <div className="source-main">
                                <span className="source-icon">{getSourceIcon(source.type)}</span>
                                <div className="source-details">
                                    <span className="source-name">
                                        {source.name || `${source.type.replace('_', ' ')} Source ${index + 1}`}
                                    </span>
                                    <span 
                                        className="source-type"
                                        style={{ color: getSourceTypeColor(source.type) }}
                                    >
                                        {source.type.replace('_', ' ')}
                                    </span>
                                </div>
                            </div>
                            
                            <div className="source-metrics">
                                <div className="confidence-badge">
                                    <span className="confidence-value">
                                        {formatConfidence(source.confidence || 0.8)}
                                    </span>
                                    <span className="confidence-label">confidence</span>
                                </div>
                                
                                {source.relevance && (
                                    <div className="relevance-badge">
                                        <span className="relevance-value">
                                            {formatConfidence(source.relevance)}
                                        </span>
                                        <span className="relevance-label">relevance</span>
                                    </div>
                                )}
                                
                                <button className="expand-btn">
                                    {expandedSource === index ? '−' : '+'}
                                </button>
                            </div>
                        </div>

                        {expandedSource === index && (
                            <div className="source-content">
                                {source.content && (
                                    <div className="content-section">
                                        <h5>Content Preview</h5>
                                        <p className="content-text">
                                            {source.content.length > 200 
                                                ? `${source.content.substring(0, 200)}...`
                                                : source.content
                                            }
                                        </p>
                                    </div>
                                )}
                                
                                {source.metadata && (
                                    <div className="metadata-section">
                                        <h5>Metadata</h5>
                                        <div className="metadata-grid">
                                            {Object.entries(source.metadata).map(([key, value]) => (
                                                <div key={key} className="metadata-item">
                                                    <span className="metadata-key">{key}:</span>
                                                    <span className="metadata-value">{value}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                                
                                {source.location && (
                                    <div className="location-section">
                                        <h5>Source Location</h5>
                                        <p className="location-text">{source.location}</p>
                                    </div>
                                )}
                                
                                {source.timestamp && (
                                    <div className="timestamp-section">
                                        <span className="timestamp-label">Retrieved:</span>
                                        <span className="timestamp-value">
                                            {new Date(source.timestamp).toLocaleString()}
                                        </span>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>

            {filteredSources.length === 0 && (
                <div className="no-sources">
                    <span className="no-sources-icon">📭</span>
                    <p className="no-sources-text">
                        {filterType === 'all' 
                            ? 'No sources available for this response'
                            : `No ${filterType.replace('_', ' ')} sources found`
                        }
                    </p>
                </div>
            )}

            {isFullView && sources.length > 0 && (
                <div className="sources-summary">
                    <h4>Source Distribution</h4>
                    <div className="source-types-grid">
                        {sourceTypes.map(type => {
                            const typeCount = sources.filter(s => s.type === type).length;
                            const percentage = Math.round((typeCount / sources.length) * 100);
                            
                            return (
                                <div key={type} className="source-type-stat">
                                    <div className="type-header">
                                        <span className="type-icon">{getSourceIcon(type)}</span>
                                        <span className="type-name">{type.replace('_', ' ')}</span>
                                    </div>
                                    <div className="type-metrics">
                                        <span className="type-count">{typeCount}</span>
                                        <span className="type-percentage">({percentage}%)</span>
                                    </div>
                                    <div className="type-bar">
                                        <div 
                                            className="type-fill"
                                            style={{ 
                                                width: `${percentage}%`,
                                                backgroundColor: getSourceTypeColor(type)
                                            }}
                                        ></div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SourcesPanel;
