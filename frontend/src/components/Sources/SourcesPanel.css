.sources-panel {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    font-family: 'Outfit', sans-serif;
}

.sources-panel.compact {
    padding: 12px;
    background: rgba(255, 255, 255, 0.95);
}

.sources-panel.full-view {
    max-width: none;
    padding: 30px;
}

.sources-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ecf0f1;
}

.sources-panel.compact .sources-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
}

.sources-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.sources-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.sources-stats {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #7f8c8d;
}

.total-sources {
    font-weight: 500;
}

.avg-confidence {
    color: #3498db;
    font-weight: 500;
}

.sources-filter {
    margin-bottom: 16px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #2c3e50;
    cursor: pointer;
}

.sources-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.sources-list.compact {
    gap: 8px;
}

.source-item {
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.source-item.compact {
    border: none;
    border-radius: 6px;
    background: rgba(52, 152, 219, 0.05);
    padding: 8px;
}

.source-item:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.source-item.expanded {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
}

.source-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.source-item.compact .source-header {
    padding: 0;
    cursor: default;
}

.source-header:hover {
    background: rgba(52, 152, 219, 0.02);
}

.source-main {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.source-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.source-item.compact .source-icon {
    font-size: 14px;
    width: 16px;
}

.source-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.source-name {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    line-height: 1.3;
}

.source-item.compact .source-name {
    font-size: 12px;
}

.source-type {
    font-size: 12px;
    font-weight: 400;
    text-transform: capitalize;
}

.source-item.compact .source-type {
    display: none;
}

.source-metrics {
    display: flex;
    align-items: center;
    gap: 12px;
}

.source-item.compact .source-metrics {
    gap: 8px;
}

.confidence-badge,
.relevance-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.confidence-value,
.relevance-value {
    font-size: 12px;
    font-weight: 600;
    color: #3498db;
}

.source-item.compact .confidence-value {
    font-size: 11px;
}

.confidence-label,
.relevance-label {
    font-size: 10px;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.source-item.compact .confidence-label,
.source-item.compact .relevance-label {
    display: none;
}

.expand-btn {
    width: 24px;
    height: 24px;
    border: 1px solid #ddd;
    border-radius: 50%;
    background: white;
    color: #7f8c8d;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.source-item.compact .expand-btn {
    display: none;
}

.expand-btn:hover {
    border-color: #3498db;
    color: #3498db;
    background: rgba(52, 152, 219, 0.05);
}

.source-content {
    padding: 16px;
    background: rgba(52, 152, 219, 0.02);
    border-top: 1px solid #ecf0f1;
}

.content-section,
.metadata-section,
.location-section,
.timestamp-section {
    margin-bottom: 16px;
}

.content-section h5,
.metadata-section h5,
.location-section h5 {
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
}

.content-text {
    font-size: 13px;
    color: #5d6d7e;
    line-height: 1.5;
    margin: 0;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

.metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
}

.metadata-item {
    display: flex;
    gap: 4px;
    font-size: 12px;
}

.metadata-key {
    font-weight: 500;
    color: #7f8c8d;
}

.metadata-value {
    color: #2c3e50;
}

.location-text {
    font-size: 12px;
    color: #5d6d7e;
    margin: 0;
    font-family: monospace;
    background: white;
    padding: 6px 8px;
    border-radius: 4px;
}

.timestamp-section {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 11px;
    color: #95a5a6;
    margin-bottom: 0;
}

.timestamp-label {
    font-weight: 500;
}

.more-sources {
    text-align: center;
    padding: 8px;
    font-size: 12px;
    color: #7f8c8d;
    font-style: italic;
}

.no-sources {
    text-align: center;
    padding: 40px 20px;
    color: #95a5a6;
}

.no-sources-icon {
    font-size: 32px;
    margin-bottom: 12px;
    display: block;
}

.no-sources-text {
    font-size: 14px;
    margin: 0;
}

.sources-summary {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #ecf0f1;
}

.sources-summary h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 16px 0;
}

.source-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.source-type-stat {
    padding: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid #ecf0f1;
}

.type-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.type-icon {
    font-size: 16px;
}

.type-name {
    font-size: 13px;
    font-weight: 500;
    color: #2c3e50;
    text-transform: capitalize;
}

.type-metrics {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
}

.type-count {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.type-percentage {
    font-size: 12px;
    color: #7f8c8d;
}

.type-bar {
    height: 4px;
    background: #ecf0f1;
    border-radius: 2px;
    overflow: hidden;
}

.type-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.8s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sources-panel {
        padding: 16px;
    }
    
    .sources-panel.full-view {
        padding: 20px;
    }
    
    .source-header {
        padding: 10px 12px;
    }
    
    .source-main {
        gap: 8px;
    }
    
    .source-metrics {
        gap: 8px;
    }
    
    .metadata-grid {
        grid-template-columns: 1fr;
    }
    
    .source-types-grid {
        grid-template-columns: 1fr;
    }
}
