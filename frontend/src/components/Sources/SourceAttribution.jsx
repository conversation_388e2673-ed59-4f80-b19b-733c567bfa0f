import React, { useState } from 'react';
import './SourceAttribution.css';

const SourceAttribution = ({ sources, onSourceClick, highlightedText }) => {
    const [expandedSources, setExpandedSources] = useState(new Set());

    const toggleSource = (sourceId) => {
        const newExpanded = new Set(expandedSources);
        if (newExpanded.has(sourceId)) {
            newExpanded.delete(sourceId);
        } else {
            newExpanded.add(sourceId);
        }
        setExpandedSources(newExpanded);
    };

    const getCredibilityColor = (credibility) => {
        if (credibility >= 0.8) return 'high';
        if (credibility >= 0.6) return 'medium';
        return 'low';
    };

    const getSourceTypeIcon = (type) => {
        switch (type) {
            case 'policy': return '📋';
            case 'document': return '📄';
            case 'database': return '🗃️';
            case 'external': return '🔗';
            default: return '📝';
        }
    };

    if (!sources || sources.length === 0) {
        return <div className="source-attribution-empty">No sources available</div>;
    }

    return (
        <div className="source-attribution">
            <div className="sources-header">
                <h4>Sources & Citations</h4>
                <span className="source-count">{sources.length} sources</span>
            </div>

            <div className="sources-list">
                {sources.map((source, index) => (
                    <div 
                        key={source.id || index}
                        className={`source-item ${expandedSources.has(source.id) ? 'expanded' : ''}`}
                    >
                        <div 
                            className="source-header"
                            onClick={() => toggleSource(source.id)}
                        >
                            <div className="source-info">
                                <span className="source-icon">
                                    {getSourceTypeIcon(source.type)}
                                </span>
                                <div className="source-details">
                                    <span className="source-title">{source.title}</span>
                                    <span className="source-type">{source.type}</span>
                                </div>
                            </div>
                            
                            <div className="source-meta">
                                <div className={`credibility-indicator ${getCredibilityColor(source.credibility)}`}>
                                    <span className="credibility-score">
                                        {Math.round(source.credibility * 100)}%
                                    </span>
                                </div>
                                <span className="expand-toggle">
                                    {expandedSources.has(source.id) ? '▼' : '▶'}
                                </span>
                            </div>
                        </div>

                        {expandedSources.has(source.id) && (
                            <div className="source-content">
                                {source.excerpt && (
                                    <div className="source-excerpt">
                                        <strong>Excerpt:</strong>
                                        <p>{source.excerpt}</p>
                                    </div>
                                )}
                                
                                {source.relevance && (
                                    <div className="source-relevance">
                                        <span className="relevance-label">Relevance:</span>
                                        <div className="relevance-bar">
                                            <div 
                                                className="relevance-fill"
                                                style={{ width: `${source.relevance * 100}%` }}
                                            ></div>
                                        </div>
                                        <span className="relevance-value">
                                            {Math.round(source.relevance * 100)}%
                                        </span>
                                    </div>
                                )}
                                
                                {source.url && (
                                    <div className="source-link">
                                        <a 
                                            href={source.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                onSourceClick && onSourceClick(source);
                                            }}
                                        >
                                            View Source →
                                        </a>
                                    </div>
                                )}
                                
                                {source.lastUpdated && (
                                    <div className="source-updated">
                                        Last updated: {new Date(source.lastUpdated).toLocaleDateString()}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>

            {highlightedText && (
                <div className="highlighted-evidence">
                    <h5>Evidence Highlights</h5>
                    <div className="evidence-text">
                        <span dangerouslySetInnerHTML={{ __html: highlightedText }}></span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SourceAttribution;