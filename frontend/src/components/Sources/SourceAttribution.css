.source-attribution {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.sources-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.sources-header h4 {
    margin: 0;
    font-size: 16px;
    color: #202124;
}

.source-count {
    font-size: 14px;
    color: #5f6368;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
}

.sources-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.source-item {
    border: 1px solid #e8eaed;
    border-radius: 6px;
    transition: all 0.2s;
}

.source-item:hover {
    border-color: #dadce0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.source-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    cursor: pointer;
}

.source-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.source-icon {
    font-size: 18px;
}

.source-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.source-title {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.source-type {
    font-size: 12px;
    color: #5f6368;
    text-transform: capitalize;
}

.source-meta {
    display: flex;
    align-items: center;
    gap: 12px;
}

.credibility-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.credibility-indicator.high {
    background-color: #e8f5e8;
    color: #137333;
}

.credibility-indicator.medium {
    background-color: #fef7e0;
    color: #b06000;
}

.credibility-indicator.low {
    background-color: #fce8e6;
    color: #d93025;
}

.expand-toggle {
    font-size: 12px;
    color: #5f6368;
    transition: transform 0.2s;
}

.source-item.expanded .expand-toggle {
    transform: rotate(0deg);
}

.source-content {
    padding: 0 12px 12px 12px;
    border-top: 1px solid #f1f3f4;
}

.source-excerpt {
    margin-bottom: 12px;
}

.source-excerpt strong {
    font-size: 13px;
    color: #202124;
}

.source-excerpt p {
    margin: 4px 0 0 0;
    font-size: 13px;
    color: #5f6368;
    line-height: 1.4;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #1a73e8;
}

.source-relevance {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.relevance-label {
    font-size: 12px;
    color: #5f6368;
    font-weight: 500;
    min-width: 70px;
}

.relevance-bar {
    flex: 1;
    height: 4px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
}

.relevance-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

.relevance-value {
    font-size: 12px;
    color: #202124;
    font-weight: 500;
    min-width: 35px;
}

.source-link {
    margin-bottom: 8px;
}

.source-link a {
    color: #1a73e8;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.source-link a:hover {
    text-decoration: underline;
}

.source-updated {
    font-size: 12px;
    color: #9aa0a6;
    font-style: italic;
}

.highlighted-evidence {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e8eaed;
}

.highlighted-evidence h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #202124;
}

.evidence-text {
    background-color: #fffbf0;
    border: 1px solid #fdd663;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.5;
}

.evidence-text mark {
    background-color: #ffeb3b;
    padding: 2px 4px;
    border-radius: 2px;
}

.source-attribution-empty {
    text-align: center;
    color: #5f6368;
    padding: 40px;
    font-style: italic;
}

@media (max-width: 800px) {
    .source-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .source-meta {
        align-self: flex-end;
    }
    
    .source-relevance {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .relevance-bar {
        width: 100%;
    }
}