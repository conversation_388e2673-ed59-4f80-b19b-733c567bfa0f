import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Pie<PERSON>hart, Pie, Cell } from 'recharts';

const SystemMetrics = () => {
    const [performanceData, setPerformanceData] = useState([]);
    const [agentUtilization, setAgentUtilization] = useState([]);
    const [responseTimeData, setResponseTimeData] = useState([]);
    const [systemResourceData, setSystemResourceData] = useState([]);

    useEffect(() => {
        generateMockData();
        const interval = setInterval(generateMockData, 5000);
        return () => clearInterval(interval);
    }, []);

    const generateMockData = () => {
        const now = new Date();
        const timePoints = [];
        
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            timePoints.push({
                time: time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                queries: Math.floor(Math.random() * 100) + 50,
                responseTime: Math.random() * 2 + 0.5,
                successRate: Math.random() * 10 + 90,
                cpuUsage: Math.random() * 30 + 40,
                memoryUsage: Math.random() * 20 + 60,
                networkIO: Math.random() * 50 + 25
            });
        }
        
        setPerformanceData(timePoints);

        // Agent utilization data
        setAgentUtilization([
            { name: 'OrchestratorAgent', utilization: 85, status: 'active' },
            { name: 'ReasoningAgent', utilization: 72, status: 'active' },
            { name: 'CriticAgent', utilization: 45, status: 'active' },
            { name: 'NuvoAiAgent', utilization: 68, status: 'active' },
            { name: 'MerilAgent', utilization: 55, status: 'active' },
            { name: 'HRAgent', utilization: 78, status: 'active' },
            { name: 'TechnicalAgent', utilization: 62, status: 'active' },
            { name: 'FinanceAgent', utilization: 35, status: 'active' },
            { name: 'ITAgent', utilization: 48, status: 'active' },
            { name: 'PolicyAgent', utilization: 25, status: 'idle' }
        ]);

        // Response time distribution
        setResponseTimeData([
            { range: '0-0.5s', count: 45, percentage: 45 },
            { range: '0.5-1s', count: 30, percentage: 30 },
            { range: '1-2s', count: 15, percentage: 15 },
            { range: '2-5s', count: 8, percentage: 8 },
            { range: '5s+', count: 2, percentage: 2 }
        ]);

        // System resources
        setSystemResourceData([
            { name: 'CPU', value: 65, color: '#3498db' },
            { name: 'Memory', value: 78, color: '#e74c3c' },
            { name: 'Storage', value: 45, color: '#2ecc71' },
            { name: 'Network', value: 32, color: '#f39c12' }
        ]);
    };

    const COLORS = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'];

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            return (
                <div className="custom-tooltip">
                    <p className="tooltip-label">{`Time: ${label}`}</p>
                    {payload.map((entry, index) => (
                        <p key={index} className="tooltip-value" style={{ color: entry.color }}>
                            {`${entry.dataKey}: ${entry.value.toFixed(2)}${entry.dataKey === 'responseTime' ? 's' : entry.dataKey === 'successRate' ? '%' : ''}`}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    return (
        <div className="system-metrics">
            <h3>System Performance Metrics</h3>
            
            <div className="metrics-charts">
                {/* Query Volume and Response Time */}
                <div className="chart-container">
                    <div className="chart-title">Query Volume & Response Time (24h)</div>
                    <ResponsiveContainer width="100%" height={200}>
                        <LineChart data={performanceData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                            <XAxis 
                                dataKey="time" 
                                stroke="#a0a0a0" 
                                fontSize={12}
                                interval="preserveStartEnd"
                            />
                            <YAxis stroke="#a0a0a0" fontSize={12} />
                            <Tooltip content={<CustomTooltip />} />
                            <Line 
                                type="monotone" 
                                dataKey="queries" 
                                stroke="#3498db" 
                                strokeWidth={2}
                                dot={false}
                            />
                            <Line 
                                type="monotone" 
                                dataKey="responseTime" 
                                stroke="#e74c3c" 
                                strokeWidth={2}
                                dot={false}
                                yAxisId="right"
                            />
                        </LineChart>
                    </ResponsiveContainer>
                </div>

                {/* Agent Utilization */}
                <div className="chart-container">
                    <div className="chart-title">Agent Utilization</div>
                    <ResponsiveContainer width="100%" height={200}>
                        <BarChart data={agentUtilization} layout="horizontal">
                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                            <XAxis type="number" stroke="#a0a0a0" fontSize={12} />
                            <YAxis 
                                type="category" 
                                dataKey="name" 
                                stroke="#a0a0a0" 
                                fontSize={10}
                                width={80}
                            />
                            <Tooltip 
                                formatter={(value) => [`${value}%`, 'Utilization']}
                                labelStyle={{ color: '#2c3e50' }}
                                contentStyle={{ 
                                    backgroundColor: 'rgba(255,255,255,0.95)', 
                                    border: 'none',
                                    borderRadius: '8px'
                                }}
                            />
                            <Bar 
                                dataKey="utilization" 
                                fill="#3498db"
                                radius={[0, 4, 4, 0]}
                            />
                        </BarChart>
                    </ResponsiveContainer>
                </div>

                {/* Response Time Distribution */}
                <div className="chart-container">
                    <div className="chart-title">Response Time Distribution</div>
                    <ResponsiveContainer width="100%" height={200}>
                        <PieChart>
                            <Pie
                                data={responseTimeData}
                                cx="50%"
                                cy="50%"
                                innerRadius={40}
                                outerRadius={80}
                                paddingAngle={2}
                                dataKey="percentage"
                            >
                                {responseTimeData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                            </Pie>
                            <Tooltip 
                                formatter={(value) => [`${value}%`, 'Percentage']}
                                labelStyle={{ color: '#2c3e50' }}
                                contentStyle={{ 
                                    backgroundColor: 'rgba(255,255,255,0.95)', 
                                    border: 'none',
                                    borderRadius: '8px'
                                }}
                            />
                        </PieChart>
                    </ResponsiveContainer>
                    <div className="pie-legend">
                        {responseTimeData.map((entry, index) => (
                            <div key={entry.range} className="legend-item">
                                <span 
                                    className="legend-color" 
                                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                                ></span>
                                <span className="legend-text">{entry.range}</span>
                            </div>
                        ))}
                    </div>
                </div>

                {/* System Resources */}
                <div className="chart-container">
                    <div className="chart-title">System Resources</div>
                    <div className="resource-meters">
                        {systemResourceData.map((resource) => (
                            <div key={resource.name} className="resource-meter">
                                <div className="resource-header">
                                    <span className="resource-name">{resource.name}</span>
                                    <span className="resource-value">{resource.value}%</span>
                                </div>
                                <div className="resource-bar">
                                    <div 
                                        className="resource-fill"
                                        style={{ 
                                            width: `${resource.value}%`,
                                            backgroundColor: resource.color
                                        }}
                                    ></div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Real-time Stats */}
            <div className="realtime-stats">
                <div className="stat-group">
                    <h4>Current Performance</h4>
                    <div className="stat-items">
                        <div className="stat-item">
                            <span className="stat-label">Active Queries</span>
                            <span className="stat-value">23</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Queue Length</span>
                            <span className="stat-value">5</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Avg Response</span>
                            <span className="stat-value">1.2s</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Success Rate</span>
                            <span className="stat-value">98.5%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SystemMetrics;
