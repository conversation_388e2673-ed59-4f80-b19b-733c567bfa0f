import React, { useState, useEffect } from 'react';
import { assets } from '../../assets/assets';

const KnowledgeManagement = () => {
    const [knowledgeSources, setKnowledgeSources] = useState([]);
    const [selectedSource, setSelectedSource] = useState(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isUploading, setIsUploading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('all');

    useEffect(() => {
        loadKnowledgeSources();
    }, []);

    const loadKnowledgeSources = async () => {
        // Mock data - replace with actual API call
        const mockSources = [
            {
                id: 'kb-001',
                name: 'NuvoAi HR Policies',
                type: 'document',
                organization: 'NuvoAi',
                size: '2.4 MB',
                documents: 45,
                lastUpdated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                status: 'active',
                indexedEntries: 1234,
                accuracy: 96.5,
                usage: 78
            },
            {
                id: 'kb-002',
                name: 'Meril Healthcare Guidelines',
                type: 'document',
                organization: 'Meril',
                size: '1.8 MB',
                documents: 32,
                lastUpdated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                status: 'active',
                indexedEntries: 987,
                accuracy: 94.2,
                usage: 65
            },
            {
                id: 'kb-003',
                name: 'Technical Documentation',
                type: 'wiki',
                organization: 'All',
                size: '5.2 MB',
                documents: 156,
                lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                status: 'active',
                indexedEntries: 2456,
                accuracy: 92.8,
                usage: 89
            },
            {
                id: 'kb-004',
                name: 'Financial Procedures',
                type: 'database',
                organization: 'All',
                size: '3.1 MB',
                documents: 78,
                lastUpdated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                status: 'updating',
                indexedEntries: 1567,
                accuracy: 98.1,
                usage: 42
            },
            {
                id: 'kb-005',
                name: 'Legacy System Docs',
                type: 'document',
                organization: 'All',
                size: '0.9 MB',
                documents: 23,
                lastUpdated: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                status: 'outdated',
                indexedEntries: 456,
                accuracy: 78.3,
                usage: 12
            }
        ];
        
        setKnowledgeSources(mockSources);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return '#2ecc71';
            case 'updating': return '#f39c12';
            case 'outdated': return '#e74c3c';
            case 'error': return '#c0392b';
            default: return '#95a5a6';
        }
    };

    const getTypeIcon = (type) => {
        switch (type) {
            case 'document': return '📄';
            case 'wiki': return '📚';
            case 'database': return '🗄️';
            case 'api': return '🔗';
            default: return '📁';
        }
    };

    const handleFileUpload = async (event) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        setIsUploading(true);
        setUploadProgress(0);

        // Simulate upload progress
        const interval = setInterval(() => {
            setUploadProgress(prev => {
                if (prev >= 100) {
                    clearInterval(interval);
                    setIsUploading(false);
                    // Add new knowledge source
                    const newSource = {
                        id: `kb-${Date.now()}`,
                        name: files[0].name,
                        type: 'document',
                        organization: 'All',
                        size: `${(files[0].size / 1024 / 1024).toFixed(1)} MB`,
                        documents: 1,
                        lastUpdated: new Date(),
                        status: 'active',
                        indexedEntries: Math.floor(Math.random() * 500) + 100,
                        accuracy: Math.random() * 10 + 90,
                        usage: 0
                    };
                    setKnowledgeSources(prev => [newSource, ...prev]);
                    return 0;
                }
                return prev + 10;
            });
        }, 200);
    };

    const handleSourceAction = async (sourceId, action) => {
        console.log(`Performing ${action} on source ${sourceId}`);
        
        if (action === 'reindex') {
            setKnowledgeSources(prev => prev.map(source => 
                source.id === sourceId 
                    ? { ...source, status: 'updating', lastUpdated: new Date() }
                    : source
            ));
            
            // Simulate reindexing
            setTimeout(() => {
                setKnowledgeSources(prev => prev.map(source => 
                    source.id === sourceId 
                        ? { ...source, status: 'active' }
                        : source
                ));
            }, 3000);
        }
        
        if (action === 'delete') {
            setKnowledgeSources(prev => prev.filter(source => source.id !== sourceId));
            if (selectedSource?.id === sourceId) {
                setSelectedSource(null);
            }
        }
    };

    const filteredSources = knowledgeSources.filter(source => {
        const matchesSearch = source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            source.organization.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesType = filterType === 'all' || source.type === filterType;
        return matchesSearch && matchesType;
    });

    const formatDate = (date) => {
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <div className="knowledge-management">
            {/* Controls */}
            <div className="knowledge-controls">
                <div className="search-filter">
                    <input
                        type="text"
                        placeholder="Search knowledge sources..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                    />
                    <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value)}
                        className="type-filter"
                    >
                        <option value="all">All Types</option>
                        <option value="document">Documents</option>
                        <option value="wiki">Wiki</option>
                        <option value="database">Database</option>
                        <option value="api">API</option>
                    </select>
                </div>
                
                <div className="upload-section">
                    <input
                        type="file"
                        id="file-upload"
                        multiple
                        accept=".pdf,.doc,.docx,.txt,.md"
                        onChange={handleFileUpload}
                        style={{ display: 'none' }}
                    />
                    <label htmlFor="file-upload" className="upload-btn">
                        <img src={assets.upload_icon} alt="Upload" />
                        Upload Documents
                    </label>
                    
                    {isUploading && (
                        <div className="upload-progress">
                            <div className="progress-bar">
                                <div 
                                    className="progress-fill"
                                    style={{ width: `${uploadProgress}%` }}
                                ></div>
                            </div>
                            <span className="progress-text">{uploadProgress}%</span>
                        </div>
                    )}
                </div>
            </div>

            {/* Knowledge Sources Grid */}
            <div className="knowledge-grid">
                {filteredSources.map(source => (
                    <div 
                        key={source.id} 
                        className={`knowledge-card ${selectedSource?.id === source.id ? 'selected' : ''}`}
                        onClick={() => setSelectedSource(source)}
                    >
                        <div className="knowledge-header">
                            <div className="source-info">
                                <span className="type-icon">{getTypeIcon(source.type)}</span>
                                <div className="source-details">
                                    <h3>{source.name}</h3>
                                    <span className="source-org">{source.organization}</span>
                                </div>
                            </div>
                            <div className="source-status">
                                <span 
                                    className="status-indicator"
                                    style={{ backgroundColor: getStatusColor(source.status) }}
                                ></span>
                                <span className="status-text">{source.status}</span>
                            </div>
                        </div>

                        <div className="knowledge-metrics">
                            <div className="metric-row">
                                <div className="metric">
                                    <span className="metric-value">{source.documents}</span>
                                    <span className="metric-label">Documents</span>
                                </div>
                                <div className="metric">
                                    <span className="metric-value">{source.indexedEntries.toLocaleString()}</span>
                                    <span className="metric-label">Entries</span>
                                </div>
                                <div className="metric">
                                    <span className="metric-value">{source.size}</span>
                                    <span className="metric-label">Size</span>
                                </div>
                            </div>

                            <div className="accuracy-bar">
                                <span className="accuracy-label">Accuracy: {source.accuracy.toFixed(1)}%</span>
                                <div className="accuracy-progress">
                                    <div 
                                        className="accuracy-fill"
                                        style={{ width: `${source.accuracy}%` }}
                                    ></div>
                                </div>
                            </div>

                            <div className="usage-bar">
                                <span className="usage-label">Usage: {source.usage}%</span>
                                <div className="usage-progress">
                                    <div 
                                        className="usage-fill"
                                        style={{ width: `${source.usage}%` }}
                                    ></div>
                                </div>
                            </div>
                        </div>

                        <div className="knowledge-footer">
                            <span className="last-updated">
                                Updated: {formatDate(source.lastUpdated)}
                            </span>
                        </div>

                        <div className="knowledge-actions">
                            <button 
                                className="action-btn reindex"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleSourceAction(source.id, 'reindex');
                                }}
                                disabled={source.status === 'updating'}
                            >
                                Reindex
                            </button>
                            <button 
                                className="action-btn edit"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleSourceAction(source.id, 'edit');
                                }}
                            >
                                Edit
                            </button>
                            <button 
                                className="action-btn delete"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    if (confirm('Are you sure you want to delete this knowledge source?')) {
                                        handleSourceAction(source.id, 'delete');
                                    }
                                }}
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {/* Knowledge Source Details Panel */}
            {selectedSource && (
                <div className="knowledge-details-panel">
                    <div className="panel-header">
                        <h3>{selectedSource.name} Details</h3>
                        <button 
                            className="close-panel"
                            onClick={() => setSelectedSource(null)}
                        >
                            ×
                        </button>
                    </div>

                    <div className="panel-content">
                        <div className="detail-section">
                            <h4>Source Information</h4>
                            <div className="detail-grid">
                                <div className="detail-item">
                                    <span className="detail-label">Source ID</span>
                                    <span className="detail-value">{selectedSource.id}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Type</span>
                                    <span className="detail-value">{selectedSource.type}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Organization</span>
                                    <span className="detail-value">{selectedSource.organization}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Size</span>
                                    <span className="detail-value">{selectedSource.size}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Documents</span>
                                    <span className="detail-value">{selectedSource.documents}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Indexed Entries</span>
                                    <span className="detail-value">{selectedSource.indexedEntries.toLocaleString()}</span>
                                </div>
                            </div>
                        </div>

                        <div className="detail-section">
                            <h4>Performance Metrics</h4>
                            <div className="performance-metrics">
                                <div className="performance-item">
                                    <span className="performance-label">Accuracy Score</span>
                                    <span className="performance-value">{selectedSource.accuracy.toFixed(1)}%</span>
                                </div>
                                <div className="performance-item">
                                    <span className="performance-label">Usage Rate</span>
                                    <span className="performance-value">{selectedSource.usage}%</span>
                                </div>
                                <div className="performance-item">
                                    <span className="performance-label">Last Updated</span>
                                    <span className="performance-value">{formatDate(selectedSource.lastUpdated)}</span>
                                </div>
                            </div>
                        </div>

                        <div className="detail-section">
                            <h4>Recent Activity</h4>
                            <div className="activity-log">
                                <div className="activity-item">
                                    <span className="activity-time">2 hours ago</span>
                                    <span className="activity-text">Knowledge base reindexed</span>
                                </div>
                                <div className="activity-item">
                                    <span className="activity-time">1 day ago</span>
                                    <span className="activity-text">3 new documents added</span>
                                </div>
                                <div className="activity-item">
                                    <span className="activity-time">3 days ago</span>
                                    <span className="activity-text">Accuracy improved to 96.5%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Summary Statistics */}
            <div className="knowledge-summary">
                <h3>Knowledge Base Summary</h3>
                <div className="summary-stats">
                    <div className="summary-item">
                        <span className="summary-value">{knowledgeSources.length}</span>
                        <span className="summary-label">Total Sources</span>
                    </div>
                    <div className="summary-item">
                        <span className="summary-value">
                            {knowledgeSources.reduce((sum, source) => sum + source.documents, 0)}
                        </span>
                        <span className="summary-label">Total Documents</span>
                    </div>
                    <div className="summary-item">
                        <span className="summary-value">
                            {knowledgeSources.reduce((sum, source) => sum + source.indexedEntries, 0).toLocaleString()}
                        </span>
                        <span className="summary-label">Indexed Entries</span>
                    </div>
                    <div className="summary-item">
                        <span className="summary-value">
                            {(knowledgeSources.reduce((sum, source) => sum + source.accuracy, 0) / knowledgeSources.length).toFixed(1)}%
                        </span>
                        <span className="summary-label">Avg Accuracy</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default KnowledgeManagement;
