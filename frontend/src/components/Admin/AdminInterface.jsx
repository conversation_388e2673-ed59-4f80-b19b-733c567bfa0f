import React, { useState } from 'react';
import './AdminInterface.css';
import OrganizationManagement from '../Organization/OrganizationManagement';
import DepartmentConfig from '../Organization/DepartmentConfig';
import RolePermissions from '../Organization/RolePermissions';
import ContentManagement from '../KnowledgeBase/ContentManagement';
import AgentDeployment from '../AgentConfig/AgentDeployment';
import MonitoringDashboard from '../Monitoring/MonitoringDashboard';
import AnalyticsDashboard from '../Analytics/AnalyticsDashboard';

const AdminInterface = ({ isVisible, onClose }) => {
    const [activeTab, setActiveTab] = useState('organization');
    const [showMonitoring, setShowMonitoring] = useState(false);
    const [showAnalytics, setShowAnalytics] = useState(false);

    if (!isVisible) return null;

    const tabs = [
        { id: 'organization', label: 'Organization', icon: '🏢' },
        { id: 'departments', label: 'Departments', icon: '🏛️' },
        { id: 'roles', label: 'Roles & Permissions', icon: '👥' },
        { id: 'content', label: 'Knowledge Base', icon: '📚' },
        { id: 'agents', label: 'Agent Config', icon: '🤖' },
        { id: 'monitoring', label: 'Monitoring', icon: '📊' },
        { id: 'analytics', label: 'Analytics', icon: '📈' }
    ];

    const handleTabClick = (tabId) => {
        if (tabId === 'monitoring') {
            setShowMonitoring(true);
            return;
        }
        if (tabId === 'analytics') {
            setShowAnalytics(true);
            return;
        }
        setActiveTab(tabId);
    };

    const renderContent = () => {
        switch (activeTab) {
            case 'organization':
                return <OrganizationManagement />;
            case 'departments':
                return <DepartmentConfig />;
            case 'roles':
                return <RolePermissions />;
            case 'content':
                return <ContentManagement />;
            case 'agents':
                return <AgentDeployment />;
            default:
                return <OrganizationManagement />;
        }
    };

    return (
        <div className="admin-interface-overlay">
            <div className="admin-interface">
                <div className="admin-header">
                    <h2>Admin Dashboard</h2>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>

                <div className="admin-content">
                    <div className="admin-sidebar">
                        <nav className="admin-nav">
                            {tabs.map(tab => (
                                <button
                                    key={tab.id}
                                    className={`nav-item ${activeTab === tab.id && tab.id !== 'monitoring' && tab.id !== 'analytics' ? 'active' : ''}`}
                                    onClick={() => handleTabClick(tab.id)}
                                >
                                    <span className="nav-icon">{tab.icon}</span>
                                    <span className="nav-label">{tab.label}</span>
                                </button>
                            ))}
                        </nav>
                    </div>

                    <div className="admin-main">
                        {renderContent()}
                    </div>
                </div>
                
                <MonitoringDashboard 
                    isVisible={showMonitoring}
                    onClose={() => {
                        setShowMonitoring(false);
                        setActiveTab('organization');
                    }}
                />
                
                <AnalyticsDashboard 
                    isVisible={showAnalytics}
                    onClose={() => {
                        setShowAnalytics(false);
                        setActiveTab('organization');
                    }}
                />
            </div>
        </div>
    );
};

export default AdminInterface;