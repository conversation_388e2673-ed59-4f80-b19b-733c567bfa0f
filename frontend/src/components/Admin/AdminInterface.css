.admin-interface-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.admin-interface {
    background: #ffffff;
    border-radius: 12px;
    width: 95%;
    max-width: 1400px;
    height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e8eaed;
    background: #f8f9fa;
}

.admin-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.close-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: #f1f3f4;
    color: #5f6368;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.close-button:hover {
    background: #e8eaed;
    color: #202124;
}

.admin-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.admin-sidebar {
    width: 250px;
    background: #f8f9fa;
    border-right: 1px solid #e8eaed;
    padding: 20px 0;
}

.admin-nav {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
    color: #5f6368;
    font-size: 14px;
}

.nav-item:hover {
    background: #e8eaed;
    color: #202124;
}

.nav-item.active {
    background: #e8f0fe;
    color: #1a73e8;
    border-right: 3px solid #1a73e8;
}

.nav-icon {
    font-size: 18px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-label {
    font-weight: 500;
}

.admin-main {
    flex: 1;
    overflow-y: auto;
    background: #ffffff;
}

.admin-main > div {
    margin: 0;
    padding: 0;
}

/* Override component styles for admin interface */
.admin-main .organization-management,
.admin-main .department-config,
.admin-main .role-permissions,
.admin-main .content-management,
.admin-main .agent-deployment {
    max-width: none;
    height: 100%;
    padding: 24px;
}

.admin-main .management-header,
.admin-main .config-header,
.admin-main .permissions-header,
.admin-main .content-header,
.admin-main .deployment-header {
    margin-bottom: 20px;
}

/* Mobile responsiveness */
@media (max-width: 1024px) {
    .admin-interface {
        width: 98%;
        height: 95vh;
    }
    
    .admin-sidebar {
        width: 200px;
    }
    
    .nav-item {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .nav-icon {
        font-size: 16px;
        width: 20px;
    }
}

@media (max-width: 768px) {
    .admin-interface-overlay {
        padding: 10px;
    }
    
    .admin-interface {
        width: 100%;
        height: 100vh;
        border-radius: 0;
    }
    
    .admin-content {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #e8eaed;
        padding: 12px 0;
    }
    
    .admin-nav {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 12px;
        gap: 8px;
    }
    
    .nav-item {
        flex-shrink: 0;
        padding: 8px 12px;
        border-radius: 6px;
        border-right: none;
        white-space: nowrap;
    }
    
    .nav-item.active {
        background: #1a73e8;
        color: white;
        border-right: none;
    }
    
    .nav-label {
        display: none;
    }
    
    .admin-main {
        flex: 1;
    }
    
    .admin-main .organization-management,
    .admin-main .department-config,
    .admin-main .role-permissions,
    .admin-main .content-management,
    .admin-main .agent-deployment {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .admin-header {
        padding: 16px;
    }
    
    .admin-header h2 {
        font-size: 20px;
    }
    
    .nav-item {
        padding: 6px 8px;
    }
    
    .nav-icon {
        font-size: 14px;
        width: 18px;
    }
}