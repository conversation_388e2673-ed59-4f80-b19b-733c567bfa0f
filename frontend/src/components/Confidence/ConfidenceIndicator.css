.confidence-indicator {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    font-family: 'Outfit', sans-serif;
}

.confidence-indicator.compact {
    padding: 12px;
    background: rgba(255, 255, 255, 0.95);
}

.confidence-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.confidence-indicator.compact .confidence-header {
    margin-bottom: 8px;
}

.confidence-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.confidence-label {
    font-size: 14px;
    font-weight: 500;
    color: #7f8c8d;
}

.confidence-score {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.confidence-percentage {
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
}

.confidence-indicator.compact .confidence-percentage {
    font-size: 16px;
}

.confidence-value {
    font-size: 16px;
    font-weight: 600;
}

.confidence-level {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: 500;
}

.confidence-visual {
    margin-bottom: 20px;
}

.confidence-indicator.compact .confidence-visual {
    margin-bottom: 0;
}

.confidence-bar-container {
    position: relative;
}

.confidence-bar {
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.confidence-indicator.compact .confidence-bar {
    height: 6px;
}

.confidence-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease-in-out;
    position: relative;
}

.confidence-fill::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1px;
}

.confidence-markers {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding: 0 2px;
}

.confidence-indicator.compact .confidence-markers {
    display: none;
}

.marker {
    font-size: 10px;
    color: #95a5a6;
    font-weight: 500;
    position: relative;
}

.marker::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 1px;
    height: 4px;
    background: #bdc3c7;
}

.marker.low {
    color: #e74c3c;
}

.marker.medium {
    color: #f39c12;
}

.marker.high {
    color: #3498db;
}

.marker.very-high {
    color: #2ecc71;
}

.confidence-factors {
    margin-bottom: 16px;
}

.confidence-factors h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 12px 0;
}

.factors-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.factor-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.factor-icon {
    color: #3498db;
    font-weight: bold;
    margin-top: 2px;
}

.factor-text {
    font-size: 13px;
    color: #5d6d7e;
    line-height: 1.4;
}

.confidence-explanation {
    padding-top: 16px;
    border-top: 1px solid #ecf0f1;
}

.confidence-indicator.compact .confidence-explanation {
    display: none;
}

.explanation-text {
    font-size: 13px;
    color: #7f8c8d;
    line-height: 1.5;
    margin: 0;
    font-style: italic;
}

/* Animation for confidence fill */
@keyframes confidenceFill {
    from {
        width: 0%;
    }
    to {
        width: var(--confidence-width);
    }
}

.confidence-fill {
    animation: confidenceFill 1.2s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .confidence-indicator {
        padding: 16px;
    }
    
    .confidence-percentage {
        font-size: 20px;
    }
    
    .confidence-markers {
        margin-top: 6px;
    }
    
    .marker {
        font-size: 9px;
    }
    
    .factor-text {
        font-size: 12px;
    }
    
    .explanation-text {
        font-size: 12px;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .confidence-indicator {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .confidence-header h3 {
        color: #ecf0f1;
    }
    
    .confidence-label {
        color: #bdc3c7;
    }
    
    .confidence-level {
        color: #bdc3c7;
    }
    
    .confidence-bar {
        background: #34495e;
    }
    
    .confidence-factors h4 {
        color: #ecf0f1;
    }
    
    .factor-text {
        color: #bdc3c7;
    }
    
    .explanation-text {
        color: #95a5a6;
    }
    
    .confidence-explanation {
        border-top-color: #34495e;
    }
}
