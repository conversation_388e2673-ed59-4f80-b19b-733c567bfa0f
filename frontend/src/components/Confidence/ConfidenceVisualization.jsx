import React, { useState } from 'react';
import './ConfidenceVisualization.css';

const ConfidenceVisualization = ({ 
    confidence, 
    uncertainty, 
    alternatives = [], 
    factors = [] 
}) => {
    const [activeTab, setActiveTab] = useState('overview');

    const getConfidenceLevel = (conf) => {
        if (conf >= 0.8) return 'high';
        if (conf >= 0.6) return 'medium';
        return 'low';
    };

    const renderOverview = () => (
        <div className="confidence-overview">
            <div className="confidence-main">
                <div className="confidence-circle">
                    <svg width="120" height="120" viewBox="0 0 120 120">
                        <circle
                            cx="60"
                            cy="60"
                            r="50"
                            fill="none"
                            stroke="#e8eaed"
                            strokeWidth="8"
                        />
                        <circle
                            cx="60"
                            cy="60"
                            r="50"
                            fill="none"
                            stroke="#1a73e8"
                            strokeWidth="8"
                            strokeDasharray={`${confidence * 314} 314`}
                            strokeDashoffset="78.5"
                            transform="rotate(-90 60 60)"
                            className="confidence-arc"
                        />
                    </svg>
                    <div className="confidence-text">
                        <span className="confidence-value">
                            {Math.round(confidence * 100)}%
                        </span>
                        <span className="confidence-label">Confidence</span>
                    </div>
                </div>
                
                <div className="confidence-details">
                    <div className={`confidence-badge ${getConfidenceLevel(confidence)}`}>
                        {getConfidenceLevel(confidence).toUpperCase()}
                    </div>
                    
                    {uncertainty && (
                        <div className="uncertainty-info">
                            <span className="uncertainty-label">Uncertainty:</span>
                            <span className="uncertainty-value">±{Math.round(uncertainty * 100)}%</span>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );

    const renderFactors = () => (
        <div className="confidence-factors">
            <h5>Confidence Factors</h5>
            {factors.length > 0 ? (
                <div className="factors-list">
                    {factors.map((factor, index) => (
                        <div key={index} className="factor-item">
                            <div className="factor-header">
                                <span className="factor-name">{factor.name}</span>
                                <span className={`factor-impact ${factor.impact > 0 ? 'positive' : 'negative'}`}>
                                    {factor.impact > 0 ? '+' : ''}{Math.round(factor.impact * 100)}%
                                </span>
                            </div>
                            <div className="factor-description">{factor.description}</div>
                            <div className="factor-bar">
                                <div 
                                    className={`factor-fill ${factor.impact > 0 ? 'positive' : 'negative'}`}
                                    style={{ width: `${Math.abs(factor.impact) * 100}%` }}
                                ></div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="no-factors">No confidence factors available</div>
            )}
        </div>
    );

    const renderAlternatives = () => (
        <div className="alternatives-view">
            <h5>Alternative Hypotheses</h5>
            {alternatives.length > 0 ? (
                <div className="alternatives-list">
                    {alternatives.map((alt, index) => (
                        <div key={index} className="alternative-item">
                            <div className="alternative-header">
                                <span className="alternative-title">{alt.title}</span>
                                <span className={`alternative-confidence ${getConfidenceLevel(alt.confidence)}`}>
                                    {Math.round(alt.confidence * 100)}%
                                </span>
                            </div>
                            <div className="alternative-description">{alt.description}</div>
                            <div className="alternative-likelihood">
                                <span className="likelihood-label">Likelihood:</span>
                                <div className="likelihood-bar">
                                    <div 
                                        className="likelihood-fill"
                                        style={{ width: `${alt.likelihood * 100}%` }}
                                    ></div>
                                </div>
                                <span className="likelihood-value">
                                    {Math.round(alt.likelihood * 100)}%
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="no-alternatives">No alternative hypotheses available</div>
            )}
        </div>
    );

    return (
        <div className="confidence-visualization">
            <div className="confidence-header">
                <h4>Confidence Analysis</h4>
                <div className="confidence-tabs">
                    <button 
                        className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
                        onClick={() => setActiveTab('overview')}
                    >
                        Overview
                    </button>
                    <button 
                        className={`tab ${activeTab === 'factors' ? 'active' : ''}`}
                        onClick={() => setActiveTab('factors')}
                    >
                        Factors
                    </button>
                    <button 
                        className={`tab ${activeTab === 'alternatives' ? 'active' : ''}`}
                        onClick={() => setActiveTab('alternatives')}
                    >
                        Alternatives
                    </button>
                </div>
            </div>

            <div className="confidence-content">
                {activeTab === 'overview' && renderOverview()}
                {activeTab === 'factors' && renderFactors()}
                {activeTab === 'alternatives' && renderAlternatives()}
            </div>
        </div>
    );
};

export default ConfidenceVisualization;