import React from 'react';
import './ConfidenceIndicator.css';

const ConfidenceIndicator = ({ confidence = 0.8, reasoning = [], isCompact = false }) => {
    const getConfidenceColor = (conf) => {
        if (conf >= 0.9) return '#2ecc71';
        if (conf >= 0.7) return '#3498db';
        if (conf >= 0.5) return '#f39c12';
        return '#e74c3c';
    };

    const getConfidenceLabel = (conf) => {
        if (conf >= 0.9) return 'Very High';
        if (conf >= 0.7) return 'High';
        if (conf >= 0.5) return 'Medium';
        return 'Low';
    };

    const confidencePercentage = Math.round(confidence * 100);

    if (isCompact) {
        return (
            <div className="confidence-indicator compact">
                <div className="confidence-header">
                    <span className="confidence-label">Confidence</span>
                    <span className="confidence-value" style={{ color: getConfidenceColor(confidence) }}>
                        {confidencePercentage}%
                    </span>
                </div>
                <div className="confidence-bar">
                    <div 
                        className="confidence-fill"
                        style={{ 
                            width: `${confidencePercentage}%`,
                            backgroundColor: getConfidenceColor(confidence)
                        }}
                    ></div>
                </div>
            </div>
        );
    }

    return (
        <div className="confidence-indicator">
            <div className="confidence-header">
                <h3>Response Confidence</h3>
                <div className="confidence-score">
                    <span 
                        className="confidence-percentage"
                        style={{ color: getConfidenceColor(confidence) }}
                    >
                        {confidencePercentage}%
                    </span>
                    <span className="confidence-level">
                        {getConfidenceLabel(confidence)}
                    </span>
                </div>
            </div>

            <div className="confidence-visual">
                <div className="confidence-bar-container">
                    <div className="confidence-bar">
                        <div 
                            className="confidence-fill"
                            style={{ 
                                width: `${confidencePercentage}%`,
                                backgroundColor: getConfidenceColor(confidence)
                            }}
                        ></div>
                    </div>
                    <div className="confidence-markers">
                        <span className="marker low">Low</span>
                        <span className="marker medium">Medium</span>
                        <span className="marker high">High</span>
                        <span className="marker very-high">Very High</span>
                    </div>
                </div>
            </div>

            {reasoning && reasoning.length > 0 && (
                <div className="confidence-factors">
                    <h4>Confidence Factors</h4>
                    <div className="factors-list">
                        {reasoning.slice(0, 3).map((factor, index) => (
                            <div key={index} className="factor-item">
                                <span className="factor-icon">•</span>
                                <span className="factor-text">{factor}</span>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            <div className="confidence-explanation">
                <p className="explanation-text">
                    {confidence >= 0.9 && "This response is highly reliable with strong supporting evidence."}
                    {confidence >= 0.7 && confidence < 0.9 && "This response is reliable with good supporting evidence."}
                    {confidence >= 0.5 && confidence < 0.7 && "This response has moderate reliability. Consider verifying key points."}
                    {confidence < 0.5 && "This response has low confidence. Please verify information independently."}
                </p>
            </div>
        </div>
    );
};

export default ConfidenceIndicator;
