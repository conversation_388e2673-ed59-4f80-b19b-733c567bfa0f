.confidence-visualization {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.confidence-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.confidence-header h4 {
    margin: 0;
    font-size: 16px;
    color: #202124;
}

.confidence-tabs {
    display: flex;
    gap: 4px;
}

.tab {
    padding: 6px 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background-color: #ffffff;
    color: #5f6368;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
}

.tab:hover {
    background-color: #f8f9fa;
}

.tab.active {
    background-color: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.confidence-content {
    min-height: 200px;
}

.confidence-overview {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.confidence-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.confidence-circle {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.confidence-arc {
    transition: stroke-dasharray 0.5s ease;
}

.confidence-text {
    position: absolute;
    text-align: center;
}

.confidence-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #202124;
}

.confidence-label {
    display: block;
    font-size: 12px;
    color: #5f6368;
    margin-top: 2px;
}

.confidence-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.confidence-badge {
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
}

.confidence-badge.high {
    background-color: #e8f5e8;
    color: #137333;
}

.confidence-badge.medium {
    background-color: #fef7e0;
    color: #b06000;
}

.confidence-badge.low {
    background-color: #fce8e6;
    color: #d93025;
}

.uncertainty-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
}

.uncertainty-label {
    color: #5f6368;
}

.uncertainty-value {
    color: #202124;
    font-weight: 500;
}

.confidence-factors h5,
.alternatives-view h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #202124;
}

.factors-list,
.alternatives-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.factor-item,
.alternative-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
}

.factor-header,
.alternative-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.factor-name,
.alternative-title {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.factor-impact {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 8px;
}

.factor-impact.positive {
    background-color: #e8f5e8;
    color: #137333;
}

.factor-impact.negative {
    background-color: #fce8e6;
    color: #d93025;
}

.alternative-confidence {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 8px;
}

.factor-description,
.alternative-description {
    font-size: 13px;
    color: #5f6368;
    margin-bottom: 8px;
}

.factor-bar {
    height: 4px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
}

.factor-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.factor-fill.positive {
    background-color: #34a853;
}

.factor-fill.negative {
    background-color: #ea4335;
}

.alternative-likelihood {
    display: flex;
    align-items: center;
    gap: 8px;
}

.likelihood-label {
    font-size: 12px;
    color: #5f6368;
    font-weight: 500;
    min-width: 70px;
}

.likelihood-bar {
    flex: 1;
    height: 4px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
}

.likelihood-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff9800, #ffc107);
    transition: width 0.3s ease;
}

.likelihood-value {
    font-size: 12px;
    color: #202124;
    font-weight: 500;
    min-width: 35px;
}

.no-factors,
.no-alternatives {
    text-align: center;
    color: #9aa0a6;
    padding: 20px;
    font-style: italic;
}

@media (max-width: 800px) {
    .confidence-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .confidence-tabs {
        width: 100%;
        justify-content: space-between;
    }
    
    .tab {
        flex: 1;
        text-align: center;
    }
    
    .factor-header,
    .alternative-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .alternative-likelihood {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .likelihood-bar {
        width: 100%;
    }
}