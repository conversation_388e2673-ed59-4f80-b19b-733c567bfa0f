import React, {useContext, useEffect, useRef, useState} from 'react';
import './Main.css';
import {assets} from "../../assets/assets.js";
import {Context} from "../../context/Context.jsx";
import ChatInterface from '../Chat/ChatInterface.jsx';

const Main = () => {
    const {onSent, recentPrompt, showResult, loading, resultData, setInput, input} = useContext(Context);
    const resultRef = useRef(null);
    const [rows, setRows] = useState(1);

    useEffect(() => {
        const updateRows = () => {
            if (window.innerWidth <= 600) {
                setRows(2);
            } else {
                setRows(1);
            }
        };

        updateRows();
        window.addEventListener('resize', updateRows);
        return () => window.removeEventListener('resize', updateRows);
    }, []);

    useEffect(() => {
        if (resultRef.current) {
            resultRef.current.scrollTop = resultRef.current.scrollHeight;
        }
    }, [resultData]);

    return (
        <main className="main">
            {!showResult
                ? <>
                    <nav className="nav">
                        <p>CHaBot - Complete Multi-Agent System</p>
                        <div className="nav-right">
                            <span className="milestone-badge">🎯 8/8 Milestones Complete</span>
                            <img src={assets.user_icon} alt=""/>
                        </div>
                    </nav>
                    <div className="main-container">
                        <div className="greet">
                            <p><span>Hello, Welcome to CHaBot!</span></p>
                            <p>Multi-Organization AI Assistant with Advanced Reasoning</p>
                            <div className="system-status">
                                <span className="status-indicator">🟢 All Systems Operational</span>
                                <span className="milestone-count">✅ All 8 Milestones Complete</span>
                            </div>
                        </div>
                        <div className="cards">
                            <div className="card"
                                 onClick={() => setInput("What is the privilege leave policy at NUVO AI?")}>
                                <p>What is the privilege leave policy at NUVO AI?</p>
                                <img src={assets.compass_icon} alt=""/>
                            </div>
                            <div className="card"
                                 onClick={() => setInput("How do I apply for medical leave?")}>
                                <p>How do I apply for medical leave?</p>
                                <img src={assets.bulb_icon} alt=""/>
                            </div>
                            <div className="card"
                                 onClick={() => setInput("What are the employee benefits available?")}>
                                <p>What are the employee benefits available?</p>
                                <img src={assets.message_icon} alt=""/>
                            </div>
                            <div className="card" onClick={() => setInput("Tell me about the performance review process")}>
                                <p>Tell me about the performance review process</p>
                                <img src={assets.code_icon} alt=""/>
                            </div>
                        </div>
                        <div className="main-bottom">
                            <div className="search-box">
                                <textarea rows={rows} onChange={(e) => setInput(e.target.value)}
                                          onKeyUp={(e) => {
                                              if (e.key === 'Enter') {
                                                  onSent();
                                              }
                                          }}
                                          value={input}
                                          type="text"
                                          placeholder="Ask about organizational policies, procedures, or personnel..."
                                />
                                <div className="icon-container">
                                    <button><img src={assets.gallery_icon} alt=""/></button>
                                    <button><img src={assets.mic_icon} alt=""/></button>
                                    <button type="submit" onClick={() => onSent()}><img src={assets.send_icon} alt=""/></button>
                                </div>
                            </div>
                            <p className="bottom-info">
                                CHaBot Multi-Agent System - All 8 Milestones Complete | Advanced Reasoning | Self-Improving AI
                            </p>
                        </div>
                    </div>
                </>
                :
                <ChatInterface />
            }
        </main>
    );
}

export default Main;
