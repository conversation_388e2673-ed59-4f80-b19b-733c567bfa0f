import React, { useState, useEffect } from 'react';
import ApiService from '../../services/api';

const AgentStatus = () => {
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAgentStatus();
    const interval = setInterval(fetchAgentStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  const fetchAgentStatus = async () => {
    try {
      const response = await ApiService.request('/agents/status');
      setAgents(response.agents || []);
    } catch (error) {
      console.error('Failed to fetch agent status:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#10b981';
      case 'busy': return '#f59e0b';
      case 'inactive': return '#ef4444';
      default: return '#6b7280';
    }
  };

  if (loading) {
    return <div className="agent-status loading">Loading agent status...</div>;
  }

  return (
    <div className="agent-status">
      <h3>Agent Status</h3>
      <div className="agents-grid">
        {agents.map(agent => (
          <div key={agent.id} className="agent-card">
            <div className="agent-header">
              <span className="agent-name">{agent.name}</span>
              <div 
                className="status-indicator"
                style={{ backgroundColor: getStatusColor(agent.status) }}
              />
            </div>
            <div className="agent-metrics">
              <div className="metric">
                <span className="metric-label">Load:</span>
                <span className="metric-value">{(agent.current_load * 100).toFixed(0)}%</span>
              </div>
              <div className="metric">
                <span className="metric-label">Performance:</span>
                <span className="metric-value">{(agent.performance_score * 100).toFixed(0)}%</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AgentStatus;