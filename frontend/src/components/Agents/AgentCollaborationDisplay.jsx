import React, { useState, useEffect, useRef } from 'react';
import './AgentCollaborationDisplay.css';

const AgentCollaborationDisplay = ({ collaborationData, isVisible, onClose }) => {
    const [activeAgents, setActiveAgents] = useState([]);
    const [agentMessages, setAgentMessages] = useState([]);
    const [selectedAgent, setSelectedAgent] = useState(null);
    const [timelinePosition, setTimelinePosition] = useState(0);
    const messagesEndRef = useRef(null);

    useEffect(() => {
        if (isVisible && collaborationData) {
            processCollaborationData();
        }
    }, [isVisible, collaborationData]);

    useEffect(() => {
        scrollToBottom();
    }, [agentMessages]);

    const processCollaborationData = () => {
        if (!collaborationData) return;

        const agents = collaborationData.agents_used || [];
        const messages = collaborationData.agent_interactions || [];
        
        setActiveAgents(agents.map(agentId => ({
            id: agentId,
            name: formatAgentName(agentId),
            type: getAgentType(agentId),
            status: 'active',
            lastActivity: new Date(),
            tasksCompleted: Math.floor(Math.random() * 5) + 1,
            confidence: Math.random() * 0.4 + 0.6
        })));

        setAgentMessages(messages.map((msg, index) => ({
            id: index,
            fromAgent: msg.from_agent || agents[index % agents.length],
            toAgent: msg.to_agent || 'coordinator',
            message: msg.content || msg.message || `Processing task ${index + 1}`,
            timestamp: new Date(Date.now() - (messages.length - index) * 1000),
            type: msg.type || 'task_update',
            status: msg.status || 'completed'
        })));
    };

    const formatAgentName = (agentId) => {
        return agentId.replace(/([A-Z])/g, ' $1')
                    .replace(/^./, str => str.toUpperCase())
                    .replace('Agent', '')
                    .trim();
    };

    const getAgentType = (agentId) => {
        if (agentId.includes('organization') || agentId.includes('org')) return 'organization';
        if (agentId.includes('department') || agentId.includes('dept')) return 'department';
        if (agentId.includes('reasoning')) return 'reasoning';
        if (agentId.includes('tool')) return 'tool';
        if (agentId.includes('critic')) return 'critic';
        if (agentId.includes('knowledge')) return 'knowledge';
        return 'general';
    };

    const getAgentColor = (type) => {
        const colors = {
            organization: '#4CAF50',
            department: '#2196F3',
            reasoning: '#FF9800',
            tool: '#9C27B0',
            critic: '#F44336',
            knowledge: '#607D8B',
            general: '#795548'
        };
        return colors[type] || colors.general;
    };

    const getAgentIcon = (type) => {
        const icons = {
            organization: '🏢',
            department: '🏛️',
            reasoning: '🧠',
            tool: '🔧',
            critic: '🔍',
            knowledge: '📚',
            general: '🤖'
        };
        return icons[type] || icons.general;
    };

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    const formatTime = (timestamp) => {
        return timestamp.toLocaleTimeString('en-US', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        });
    };

    const getMessageTypeIcon = (type) => {
        const icons = {
            task_update: '📋',
            result: '✅',
            request: '❓',
            error: '❌',
            coordination: '🔄'
        };
        return icons[type] || '💬';
    };

    if (!isVisible) return null;

    return (
        <div className="agent-collaboration-overlay">
            <div className="agent-collaboration-container">
                <div className="collaboration-header">
                    <h3>Agent Collaboration Monitor</h3>
                    <div className="collaboration-stats">
                        <span className="stat">
                            <span className="stat-label">Active Agents:</span>
                            <span className="stat-value">{activeAgents.length}</span>
                        </span>
                        <span className="stat">
                            <span className="stat-label">Messages:</span>
                            <span className="stat-value">{agentMessages.length}</span>
                        </span>
                    </div>
                    <button onClick={onClose} className="close-btn">×</button>
                </div>

                <div className="collaboration-content">
                    {/* Active Agents Panel */}
                    <div className="agents-panel">
                        <h4>Active Agents</h4>
                        <div className="agents-grid">
                            {activeAgents.map(agent => (
                                <div 
                                    key={agent.id}
                                    className={`agent-card ${selectedAgent?.id === agent.id ? 'selected' : ''}`}
                                    onClick={() => setSelectedAgent(agent)}
                                    style={{ borderColor: getAgentColor(agent.type) }}
                                >
                                    <div className="agent-header">
                                        <span className="agent-icon">
                                            {getAgentIcon(agent.type)}
                                        </span>
                                        <div className="agent-info">
                                            <div className="agent-name">{agent.name}</div>
                                            <div className="agent-type">{agent.type}</div>
                                        </div>
                                        <div 
                                            className={`agent-status ${agent.status}`}
                                            style={{ backgroundColor: getAgentColor(agent.type) }}
                                        ></div>
                                    </div>
                                    <div className="agent-metrics">
                                        <div className="metric">
                                            <span className="metric-label">Tasks:</span>
                                            <span className="metric-value">{agent.tasksCompleted}</span>
                                        </div>
                                        <div className="metric">
                                            <span className="metric-label">Confidence:</span>
                                            <span className="metric-value">
                                                {(agent.confidence * 100).toFixed(0)}%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Communication Timeline */}
                    <div className="communication-panel">
                        <h4>Agent Communication Timeline</h4>
                        <div className="messages-container">
                            {agentMessages.map(message => (
                                <div key={message.id} className="message-item">
                                    <div className="message-time">
                                        {formatTime(message.timestamp)}
                                    </div>
                                    <div className="message-flow">
                                        <div className="agent-bubble from-agent">
                                            <span className="agent-icon">
                                                {getAgentIcon(getAgentType(message.fromAgent))}
                                            </span>
                                            <span className="agent-name">
                                                {formatAgentName(message.fromAgent)}
                                            </span>
                                        </div>
                                        <div className="message-arrow">
                                            <span className="message-type-icon">
                                                {getMessageTypeIcon(message.type)}
                                            </span>
                                            →
                                        </div>
                                        <div className="agent-bubble to-agent">
                                            <span className="agent-icon">
                                                {getAgentIcon(getAgentType(message.toAgent))}
                                            </span>
                                            <span className="agent-name">
                                                {formatAgentName(message.toAgent)}
                                            </span>
                                        </div>
                                    </div>
                                    <div className="message-content">
                                        <div className="message-text">{message.message}</div>
                                        <div className={`message-status ${message.status}`}>
                                            {message.status}
                                        </div>
                                    </div>
                                </div>
                            ))}
                            <div ref={messagesEndRef} />
                        </div>
                    </div>

                    {/* Agent Details Panel */}
                    {selectedAgent && (
                        <div className="agent-details-panel">
                            <h4>Agent Details</h4>
                            <div className="agent-detail-card">
                                <div className="detail-header">
                                    <span className="detail-icon">
                                        {getAgentIcon(selectedAgent.type)}
                                    </span>
                                    <div>
                                        <div className="detail-name">{selectedAgent.name}</div>
                                        <div className="detail-type">{selectedAgent.type} Agent</div>
                                    </div>
                                </div>
                                
                                <div className="detail-metrics">
                                    <div className="detail-metric">
                                        <span className="metric-label">Status:</span>
                                        <span className={`metric-badge ${selectedAgent.status}`}>
                                            {selectedAgent.status}
                                        </span>
                                    </div>
                                    <div className="detail-metric">
                                        <span className="metric-label">Tasks Completed:</span>
                                        <span className="metric-value">{selectedAgent.tasksCompleted}</span>
                                    </div>
                                    <div className="detail-metric">
                                        <span className="metric-label">Confidence Level:</span>
                                        <div className="confidence-bar">
                                            <div 
                                                className="confidence-fill"
                                                style={{ 
                                                    width: `${selectedAgent.confidence * 100}%`,
                                                    backgroundColor: getAgentColor(selectedAgent.type)
                                                }}
                                            ></div>
                                            <span className="confidence-text">
                                                {(selectedAgent.confidence * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                    </div>
                                    <div className="detail-metric">
                                        <span className="metric-label">Last Activity:</span>
                                        <span className="metric-value">
                                            {selectedAgent.lastActivity.toLocaleTimeString()}
                                        </span>
                                    </div>
                                </div>

                                <div className="agent-capabilities">
                                    <h5>Capabilities</h5>
                                    <div className="capability-tags">
                                        {getAgentCapabilities(selectedAgent.type).map(capability => (
                                            <span key={capability} className="capability-tag">
                                                {capability}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Collaboration Summary */}
                <div className="collaboration-summary">
                    <div className="summary-item">
                        <span className="summary-label">Total Collaboration Time:</span>
                        <span className="summary-value">
                            {agentMessages.length > 0 ? 
                                `${Math.max(1, Math.floor((Date.now() - agentMessages[0].timestamp) / 1000))}s` : 
                                '0s'
                            }
                        </span>
                    </div>
                    <div className="summary-item">
                        <span className="summary-label">Average Response Time:</span>
                        <span className="summary-value">0.8s</span>
                    </div>
                    <div className="summary-item">
                        <span className="summary-label">Success Rate:</span>
                        <span className="summary-value">
                            {agentMessages.length > 0 ? 
                                `${Math.round((agentMessages.filter(m => m.status === 'completed').length / agentMessages.length) * 100)}%` : 
                                '100%'
                            }
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );

    function getAgentCapabilities(type) {
        const capabilities = {
            organization: ['Policy Knowledge', 'Org Structure', 'Personnel Info'],
            department: ['Dept Procedures', 'Team Management', 'Resource Access'],
            reasoning: ['Logic Analysis', 'Pattern Recognition', 'Decision Making'],
            tool: ['Tool Execution', 'Data Processing', 'Calculations'],
            critic: ['Quality Check', 'Fact Verification', 'Consistency Review'],
            knowledge: ['Information Retrieval', 'Content Search', 'Data Fusion'],
            general: ['General Assistance', 'Task Coordination', 'Communication']
        };
        return capabilities[type] || capabilities.general;
    }
};

export default AgentCollaborationDisplay;