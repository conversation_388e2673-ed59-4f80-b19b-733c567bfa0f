.agent-activity {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.activity-header h4 {
    margin: 0;
    font-size: 16px;
    color: #202124;
}

.active-count {
    font-size: 14px;
    color: #34a853;
    background-color: #e8f5e8;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
}

.agent-card {
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #f8f9fa;
}

.agent-card:hover {
    border-color: #dadce0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.agent-card.active {
    border-color: #1a73e8;
    background-color: #e8f0fe;
}

.agent-card.expanded {
    background-color: #ffffff;
}

.agent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.agent-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.agent-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    border-radius: 50%;
    border: 2px solid #e8eaed;
}

.agent-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.agent-name {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.agent-role {
    font-size: 12px;
    color: #5f6368;
}

.agent-status {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-text {
    font-size: 12px;
    color: #5f6368;
    text-transform: capitalize;
}

.agent-expanded {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e8eaed;
}

.agent-activity-detail {
    font-size: 13px;
    color: #202124;
    margin-bottom: 8px;
}

.agent-contributions {
    font-size: 13px;
    color: #202124;
    margin-bottom: 12px;
}

.agent-contributions ul {
    margin: 4px 0 0 16px;
    padding: 0;
}

.agent-contributions li {
    font-size: 12px;
    color: #5f6368;
    margin-bottom: 2px;
}

.agent-performance {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.performance-metric {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.performance-metric span:first-child {
    min-width: 60px;
    color: #5f6368;
    font-weight: 500;
}

.performance-metric span:last-child {
    min-width: 35px;
    color: #202124;
    font-weight: 500;
}

.metric-bar {
    flex: 1;
    height: 4px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@media (max-width: 800px) {
    .agents-grid {
        grid-template-columns: 1fr;
    }
    
    .agent-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .agent-status {
        align-self: flex-end;
    }
}