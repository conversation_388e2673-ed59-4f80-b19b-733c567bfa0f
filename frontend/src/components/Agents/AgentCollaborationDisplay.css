.agent-collaboration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.agent-collaboration-container {
    background: white;
    border-radius: 12px;
    width: 95%;
    max-width: 1400px;
    height: 90%;
    max-height: 900px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.collaboration-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f5f5f5;
    border-radius: 12px 12px 0 0;
}

.collaboration-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.collaboration-stats {
    display: flex;
    gap: 20px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #2196F3;
}

.close-btn {
    background: #f44336;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
}

.close-btn:hover {
    background: #d32f2f;
}

.collaboration-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Agents Panel */
.agents-panel {
    width: 300px;
    background: #fafafa;
    border-right: 1px solid #e0e0e0;
    padding: 20px;
    overflow-y: auto;
}

.agents-panel h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
}

.agents-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.agent-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.agent-card.selected {
    border-color: #2196F3;
    background: #f3f8ff;
}

.agent-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.agent-icon {
    font-size: 20px;
}

.agent-info {
    flex: 1;
}

.agent-name {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.agent-type {
    font-size: 12px;
    color: #666;
    text-transform: capitalize;
}

.agent-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.agent-metrics {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.metric-label {
    color: #666;
    margin-bottom: 2px;
}

.metric-value {
    font-weight: bold;
    color: #333;
}

/* Communication Panel */
.communication-panel {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.communication-panel h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background: #fafafa;
}

.message-item {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #2196F3;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-time {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.message-flow {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.agent-bubble {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: #f0f0f0;
    border-radius: 15px;
    font-size: 12px;
}

.from-agent {
    background: #e3f2fd;
}

.to-agent {
    background: #f3e5f5;
}

.message-arrow {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 14px;
}

.message-type-icon {
    font-size: 16px;
}

.message-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 10px;
}

.message-text {
    flex: 1;
    color: #333;
    font-size: 14px;
    line-height: 1.4;
}

.message-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.message-status.completed {
    background: #4CAF50;
    color: white;
}

.message-status.processing {
    background: #FF9800;
    color: white;
}

.message-status.error {
    background: #f44336;
    color: white;
}

/* Agent Details Panel */
.agent-details-panel {
    width: 300px;
    background: #fafafa;
    border-left: 1px solid #e0e0e0;
    padding: 20px;
    overflow-y: auto;
}

.agent-details-panel h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
}

.agent-detail-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e0e0;
}

.detail-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.detail-icon {
    font-size: 24px;
}

.detail-name {
    font-weight: bold;
    color: #333;
    font-size: 16px;
}

.detail-type {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.detail-metrics {
    margin-bottom: 15px;
}

.detail-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-metric:last-child {
    border-bottom: none;
}

.metric-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.metric-badge.active {
    background: #4CAF50;
    color: white;
}

.confidence-bar {
    position: relative;
    width: 80px;
    height: 16px;
    background: #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    transition: width 0.3s;
}

.confidence-text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

.agent-capabilities h5 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 14px;
}

.capability-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.capability-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
}

/* Collaboration Summary */
.collaboration-summary {
    display: flex;
    justify-content: space-around;
    padding: 15px 20px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    border-radius: 0 0 12px 12px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.summary-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.summary-value {
    font-size: 16px;
    font-weight: bold;
    color: #2196F3;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .agent-collaboration-container {
        width: 98%;
        height: 95%;
    }

    .collaboration-header {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }

    .collaboration-stats {
        gap: 15px;
    }

    .collaboration-content {
        flex-direction: column;
    }

    .agents-panel,
    .agent-details-panel {
        width: 100%;
        max-height: 200px;
        border-right: none;
        border-left: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .communication-panel {
        flex: 1;
    }

    .collaboration-summary {
        flex-direction: column;
        gap: 10px;
    }
}

/* Accessibility */
.agent-card:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

.close-btn:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

/* Scrollbar Styling */
.messages-container::-webkit-scrollbar,
.agents-panel::-webkit-scrollbar,
.agent-details-panel::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track,
.agents-panel::-webkit-scrollbar-track,
.agent-details-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb,
.agents-panel::-webkit-scrollbar-thumb,
.agent-details-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover,
.agents-panel::-webkit-scrollbar-thumb:hover,
.agent-details-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}