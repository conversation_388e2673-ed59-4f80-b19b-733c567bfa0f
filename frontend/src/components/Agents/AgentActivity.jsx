import React, { useState } from 'react';
import './AgentActivity.css';

const AgentActivity = ({ agents, activeAgent, onAgentClick }) => {
    const [expandedAgent, setExpandedAgent] = useState(null);

    const getActivityIcon = (activity) => {
        const icons = {
            'thinking': '🤔',
            'searching': '🔍',
            'analyzing': '📊',
            'coordinating': '🤝',
            'responding': '💬',
            'idle': '⏸️'
        };
        return icons[activity] || '🤖';
    };

    const getStatusColor = (status) => {
        const colors = {
            'active': '#34a853',
            'busy': '#fbbc04',
            'waiting': '#9aa0a6',
            'error': '#ea4335'
        };
        return colors[status] || '#9aa0a6';
    };

    const toggleAgent = (agentId) => {
        setExpandedAgent(expandedAgent === agentId ? null : agentId);
    };

    return (
        <div className="agent-activity">
            <div className="activity-header">
                <h4>Agent Activity</h4>
                <span className="active-count">{agents.filter(a => a.status === 'active').length} active</span>
            </div>

            <div className="agents-grid">
                {agents.map(agent => (
                    <div 
                        key={agent.id}
                        className={`agent-card ${agent.id === activeAgent ? 'active' : ''} ${expandedAgent === agent.id ? 'expanded' : ''}`}
                        onClick={() => {
                            toggleAgent(agent.id);
                            onAgentClick && onAgentClick(agent);
                        }}
                    >
                        <div className="agent-header">
                            <div className="agent-info">
                                <span className="agent-icon">{getActivityIcon(agent.activity)}</span>
                                <div className="agent-details">
                                    <span className="agent-name">{agent.name}</span>
                                    <span className="agent-role">{agent.role}</span>
                                </div>
                            </div>
                            
                            <div className="agent-status">
                                <div 
                                    className="status-dot"
                                    style={{ backgroundColor: getStatusColor(agent.status) }}
                                ></div>
                                <span className="status-text">{agent.status}</span>
                            </div>
                        </div>

                        {expandedAgent === agent.id && (
                            <div className="agent-expanded">
                                <div className="agent-activity-detail">
                                    <strong>Current Activity:</strong> {agent.currentTask || 'Idle'}
                                </div>
                                
                                {agent.contributions && agent.contributions.length > 0 && (
                                    <div className="agent-contributions">
                                        <strong>Recent Contributions:</strong>
                                        <ul>
                                            {agent.contributions.slice(0, 3).map((contrib, idx) => (
                                                <li key={idx}>{contrib}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                                
                                {agent.performance && (
                                    <div className="agent-performance">
                                        <div className="performance-metric">
                                            <span>Accuracy:</span>
                                            <div className="metric-bar">
                                                <div 
                                                    className="metric-fill"
                                                    style={{ width: `${agent.performance.accuracy * 100}%` }}
                                                ></div>
                                            </div>
                                            <span>{Math.round(agent.performance.accuracy * 100)}%</span>
                                        </div>
                                        
                                        <div className="performance-metric">
                                            <span>Speed:</span>
                                            <div className="metric-bar">
                                                <div 
                                                    className="metric-fill"
                                                    style={{ width: `${agent.performance.speed * 100}%` }}
                                                ></div>
                                            </div>
                                            <span>{Math.round(agent.performance.speed * 100)}%</span>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default AgentActivity;