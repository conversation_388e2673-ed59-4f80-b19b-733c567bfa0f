import React, { useState } from 'react';
import './AgentInteractionDisplay.css';
import AgentActivity from './AgentActivity';
import ToolExecution from '../Tools/ToolExecution';
import AgentCollaboration from '../Collaboration/AgentCollaboration';

const AgentInteractionDisplay = ({ interactionData, isVisible, onClose }) => {
    const [activeTab, setActiveTab] = useState('activity');

    if (!isVisible) return null;

    const mockAgents = [
        {
            id: 'org_agent',
            name: 'Organization Agent',
            role: 'Policy Expert',
            status: 'active',
            activity: 'analyzing',
            currentTask: 'Analyzing NUVO AI leave policies',
            contributions: [
                'Retrieved employee handbook',
                'Identified relevant policy sections',
                'Cross-referenced with current regulations'
            ],
            performance: {
                accuracy: 0.92,
                speed: 0.85
            }
        },
        {
            id: 'hr_agent',
            name: 'HR Agent',
            role: 'HR Specialist',
            status: 'busy',
            activity: 'searching',
            currentTask: 'Searching HR database for leave records',
            contributions: [
                'Accessed employee leave history',
                'Validated policy compliance'
            ],
            performance: {
                accuracy: 0.88,
                speed: 0.91
            }
        },
        {
            id: 'reasoning_agent',
            name: 'Reasoning Agent',
            role: 'Logic Processor',
            status: 'waiting',
            activity: 'idle',
            currentTask: null,
            contributions: [
                'Processed logical constraints',
                'Validated reasoning chains'
            ],
            performance: {
                accuracy: 0.95,
                speed: 0.78
            }
        }
    ];

    const mockToolChain = [
        {
            id: 'search_tool',
            name: 'Policy Search',
            type: 'search',
            status: 'completed',
            progress: 100,
            description: 'Semantic search across HR documents',
            input: { query: 'privilege leave policy', organization: 'NUVO AI' },
            output: { documents: 3, relevance_score: 0.92 },
            executionTime: 245
        },
        {
            id: 'db_tool',
            name: 'Database Query',
            type: 'database',
            status: 'running',
            progress: 65,
            description: 'Querying employee database for leave records',
            input: { table: 'employee_leaves', filters: { type: 'privilege' } },
            output: null,
            executionTime: null
        },
        {
            id: 'analysis_tool',
            name: 'Policy Analysis',
            type: 'analysis',
            status: 'pending',
            progress: 0,
            description: 'Analyzing policy requirements and eligibility',
            input: null,
            output: null,
            executionTime: null
        }
    ];

    const mockCollaboration = {
        activeInteractions: 2,
        totalInteractions: 8,
        communications: [
            {
                from: 'Organization Agent',
                to: 'HR Agent',
                message: 'Found 3 relevant policy documents, sharing search results',
                timestamp: '10:32 AM',
                type: 'request'
            },
            {
                from: 'HR Agent',
                to: 'Organization Agent',
                message: 'Received documents, cross-referencing with database',
                timestamp: '10:33 AM',
                type: 'response'
            },
            {
                from: 'Reasoning Agent',
                to: 'All Agents',
                message: 'Ready to process logical constraints when data is available',
                timestamp: '10:34 AM',
                type: 'notification'
            }
        ],
        delegations: [
            {
                delegator: 'Organization Agent',
                assignee: 'HR Agent',
                taskName: 'Validate employee eligibility',
                status: 'in-progress',
                progress: 60
            },
            {
                delegator: 'HR Agent',
                assignee: 'Reasoning Agent',
                taskName: 'Process policy logic',
                status: 'pending',
                progress: 0
            }
        ],
        consensus: [
            {
                topic: 'Policy interpretation accuracy',
                participants: [
                    { name: 'Organization Agent', vote: 'agree' },
                    { name: 'HR Agent', vote: 'agree' },
                    { name: 'Reasoning Agent', vote: 'pending' }
                ],
                result: 'pending',
                agreementScore: 0.67
            }
        ]
    };

    return (
        <div className="agent-interaction-overlay">
            <div className="agent-interaction-modal">
                <div className="modal-header">
                    <h3>Agent Interactions</h3>
                    <div className="interaction-tabs">
                        <button 
                            className={`tab ${activeTab === 'activity' ? 'active' : ''}`}
                            onClick={() => setActiveTab('activity')}
                        >
                            Activity
                        </button>
                        <button 
                            className={`tab ${activeTab === 'tools' ? 'active' : ''}`}
                            onClick={() => setActiveTab('tools')}
                        >
                            Tools
                        </button>
                        <button 
                            className={`tab ${activeTab === 'collaboration' ? 'active' : ''}`}
                            onClick={() => setActiveTab('collaboration')}
                        >
                            Collaboration
                        </button>
                    </div>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>

                <div className="modal-content">
                    {activeTab === 'activity' && (
                        <AgentActivity 
                            agents={mockAgents}
                            activeAgent="org_agent"
                            onAgentClick={(agent) => console.log('Agent clicked:', agent)}
                        />
                    )}
                    
                    {activeTab === 'tools' && (
                        <ToolExecution 
                            toolChain={mockToolChain}
                            activeTools={mockToolChain.filter(t => t.status === 'running')}
                            onToolClick={(tool) => console.log('Tool clicked:', tool)}
                        />
                    )}
                    
                    {activeTab === 'collaboration' && (
                        <AgentCollaboration 
                            collaborationData={mockCollaboration}
                            onInteractionClick={(interaction) => console.log('Interaction clicked:', interaction)}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default AgentInteractionDisplay;