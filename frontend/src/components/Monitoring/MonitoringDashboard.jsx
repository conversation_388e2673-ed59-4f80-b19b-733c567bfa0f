import React, { useState } from 'react';
import './MonitoringDashboard.css';
import PerformanceTracking from '../Performance/PerformanceTracking';
import ActivityMonitoring from '../Activity/ActivityMonitoring';
import DebuggingTools from '../Debugging/DebuggingTools';

const MonitoringDashboard = ({ isVisible, onClose }) => {
    const [activeTab, setActiveTab] = useState('performance');

    if (!isVisible) return null;

    const tabs = [
        { id: 'performance', label: 'Performance', icon: '📊' },
        { id: 'activity', label: 'Activity', icon: '🔄' },
        { id: 'debugging', label: 'Debugging', icon: '🐛' }
    ];

    const renderContent = () => {
        switch (activeTab) {
            case 'performance':
                return <PerformanceTracking />;
            case 'activity':
                return <ActivityMonitoring />;
            case 'debugging':
                return <DebuggingTools />;
            default:
                return <PerformanceTracking />;
        }
    };

    return (
        <div className="monitoring-dashboard-overlay">
            <div className="monitoring-dashboard">
                <div className="dashboard-header">
                    <h2>Agent Monitoring Dashboard</h2>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>

                <div className="dashboard-content">
                    <div className="dashboard-sidebar">
                        <nav className="dashboard-nav">
                            {tabs.map(tab => (
                                <button
                                    key={tab.id}
                                    className={`nav-item ${activeTab === tab.id ? 'active' : ''}`}
                                    onClick={() => setActiveTab(tab.id)}
                                >
                                    <span className="nav-icon">{tab.icon}</span>
                                    <span className="nav-label">{tab.label}</span>
                                </button>
                            ))}
                        </nav>
                        
                        <div className="dashboard-stats">
                            <div className="stat-item">
                                <span className="stat-value">3</span>
                                <span className="stat-label">Active Agents</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-value">92%</span>
                                <span className="stat-label">Avg Quality</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-value">45</span>
                                <span className="stat-label">Req/Min</span>
                            </div>
                        </div>
                    </div>

                    <div className="dashboard-main">
                        {renderContent()}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MonitoringDashboard;