.tool-execution {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.execution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.execution-header h4 {
    margin: 0;
    font-size: 16px;
    color: #202124;
}

.active-tools {
    font-size: 14px;
    color: #1a73e8;
    background-color: #e8f0fe;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.tool-chain {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tool-card {
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #f8f9fa;
}

.tool-card:hover {
    border-color: #dadce0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tool-card.expanded {
    background-color: #ffffff;
    border-color: #1a73e8;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tool-icon {
    font-size: 20px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    border-radius: 50%;
    border: 2px solid #e8eaed;
}

.tool-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.tool-name {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.tool-type {
    font-size: 12px;
    color: #5f6368;
    text-transform: capitalize;
}

.tool-status {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-text {
    font-size: 12px;
    color: #5f6368;
}

.tool-progress {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #202124;
    font-weight: 500;
    min-width: 35px;
}

.tool-expanded {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e8eaed;
}

.tool-description {
    font-size: 13px;
    color: #202124;
    margin-bottom: 8px;
}

.tool-input,
.tool-output {
    font-size: 13px;
    color: #202124;
    margin-bottom: 8px;
}

.tool-input pre,
.tool-output pre {
    background-color: #f8f9fa;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    padding: 8px;
    font-size: 11px;
    color: #5f6368;
    overflow-x: auto;
    margin: 4px 0 0 0;
    max-height: 100px;
    overflow-y: auto;
}

.chain-connector {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 4px 0;
}

.connector-line {
    width: 2px;
    height: 16px;
    background-color: #e8eaed;
}

.connector-arrow {
    font-size: 14px;
    color: #5f6368;
}

.no-tools {
    text-align: center;
    color: #9aa0a6;
    padding: 40px;
    font-style: italic;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@media (max-width: 800px) {
    .tool-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}