import React, { useState } from 'react';
import './ToolExecution.css';

const ToolExecution = ({ toolChain, activeTools, onToolClick }) => {
    const [expandedTool, setExpandedTool] = useState(null);

    const getToolIcon = (toolType) => {
        const icons = {
            'search': '🔍',
            'database': '🗃️',
            'analysis': '📊',
            'validation': '✅',
            'generation': '✨',
            'communication': '📡'
        };
        return icons[toolType] || '🔧';
    };

    const getExecutionStatus = (status) => {
        const statusMap = {
            'running': { color: '#1a73e8', text: 'Running' },
            'completed': { color: '#34a853', text: 'Completed' },
            'failed': { color: '#ea4335', text: 'Failed' },
            'pending': { color: '#9aa0a6', text: 'Pending' }
        };
        return statusMap[status] || statusMap.pending;
    };

    const toggleTool = (toolId) => {
        setExpandedTool(expandedTool === toolId ? null : toolId);
    };

    return (
        <div className="tool-execution">
            <div className="execution-header">
                <h4>Tool Execution</h4>
                <span className="active-tools">{activeTools?.length || 0} active</span>
            </div>

            {toolChain && toolChain.length > 0 ? (
                <div className="tool-chain">
                    {toolChain.map((tool, index) => (
                        <div key={tool.id} className="tool-chain-item">
                            <div 
                                className={`tool-card ${expandedTool === tool.id ? 'expanded' : ''}`}
                                onClick={() => {
                                    toggleTool(tool.id);
                                    onToolClick && onToolClick(tool);
                                }}
                            >
                                <div className="tool-header">
                                    <div className="tool-info">
                                        <span className="tool-icon">{getToolIcon(tool.type)}</span>
                                        <div className="tool-details">
                                            <span className="tool-name">{tool.name}</span>
                                            <span className="tool-type">{tool.type}</span>
                                        </div>
                                    </div>
                                    
                                    <div className="tool-status">
                                        <div 
                                            className="status-indicator"
                                            style={{ backgroundColor: getExecutionStatus(tool.status).color }}
                                        ></div>
                                        <span className="status-text">
                                            {getExecutionStatus(tool.status).text}
                                        </span>
                                    </div>
                                </div>

                                {tool.progress && (
                                    <div className="tool-progress">
                                        <div className="progress-bar">
                                            <div 
                                                className="progress-fill"
                                                style={{ width: `${tool.progress}%` }}
                                            ></div>
                                        </div>
                                        <span className="progress-text">{tool.progress}%</span>
                                    </div>
                                )}

                                {expandedTool === tool.id && (
                                    <div className="tool-expanded">
                                        {tool.description && (
                                            <div className="tool-description">
                                                <strong>Description:</strong> {tool.description}
                                            </div>
                                        )}
                                        
                                        {tool.input && (
                                            <div className="tool-input">
                                                <strong>Input:</strong>
                                                <pre>{JSON.stringify(tool.input, null, 2)}</pre>
                                            </div>
                                        )}
                                        
                                        {tool.output && (
                                            <div className="tool-output">
                                                <strong>Output:</strong>
                                                <pre>{JSON.stringify(tool.output, null, 2)}</pre>
                                            </div>
                                        )}
                                        
                                        {tool.executionTime && (
                                            <div className="tool-timing">
                                                <strong>Execution Time:</strong> {tool.executionTime}ms
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>

                            {index < toolChain.length - 1 && (
                                <div className="chain-connector">
                                    <div className="connector-line"></div>
                                    <div className="connector-arrow">↓</div>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            ) : (
                <div className="no-tools">No tool execution data available</div>
            )}
        </div>
    );
};

export default ToolExecution;