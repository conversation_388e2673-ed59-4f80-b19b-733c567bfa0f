import React, { useState } from 'react';
import './AgentCollaboration.css';

const AgentCollaboration = ({ collaborationData, onInteractionClick }) => {
    const [selectedInteraction, setSelectedInteraction] = useState(null);

    const getInteractionType = (type) => {
        const types = {
            'communication': { icon: '💬', color: '#1a73e8' },
            'delegation': { icon: '📋', color: '#34a853' },
            'consensus': { icon: '🤝', color: '#fbbc04' },
            'coordination': { icon: '🔄', color: '#9c27b0' }
        };
        return types[type] || { icon: '🔗', color: '#9aa0a6' };
    };

    const renderCommunicationFlow = () => {
        if (!collaborationData?.communications) return null;

        return (
            <div className="communication-flow">
                <h5>Communication Flow</h5>
                <div className="flow-timeline">
                    {collaborationData.communications.map((comm, index) => (
                        <div key={index} className="communication-item">
                            <div className="comm-header">
                                <span className="comm-from">{comm.from}</span>
                                <span className="comm-arrow">→</span>
                                <span className="comm-to">{comm.to}</span>
                                <span className="comm-time">{comm.timestamp}</span>
                            </div>
                            <div className="comm-content">{comm.message}</div>
                            {comm.type && (
                                <div className="comm-type">
                                    <span className={`type-badge ${comm.type}`}>
                                        {comm.type}
                                    </span>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    const renderTaskDelegation = () => {
        if (!collaborationData?.delegations) return null;

        return (
            <div className="task-delegation">
                <h5>Task Delegation</h5>
                <div className="delegation-grid">
                    {collaborationData.delegations.map((task, index) => (
                        <div key={index} className="delegation-item">
                            <div className="delegation-header">
                                <span className="delegator">{task.delegator}</span>
                                <span className="delegation-arrow">📋→</span>
                                <span className="assignee">{task.assignee}</span>
                            </div>
                            <div className="task-details">
                                <div className="task-name">{task.taskName}</div>
                                <div className="task-status">
                                    <span className={`status-badge ${task.status}`}>
                                        {task.status}
                                    </span>
                                    {task.progress && (
                                        <div className="task-progress">
                                            <div className="progress-bar">
                                                <div 
                                                    className="progress-fill"
                                                    style={{ width: `${task.progress}%` }}
                                                ></div>
                                            </div>
                                            <span>{task.progress}%</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    const renderConsensusFormation = () => {
        if (!collaborationData?.consensus) return null;

        return (
            <div className="consensus-formation">
                <h5>Consensus Formation</h5>
                <div className="consensus-process">
                    {collaborationData.consensus.map((consensus, index) => (
                        <div key={index} className="consensus-item">
                            <div className="consensus-topic">{consensus.topic}</div>
                            <div className="consensus-participants">
                                <strong>Participants:</strong>
                                <div className="participant-list">
                                    {consensus.participants.map((participant, idx) => (
                                        <div key={idx} className="participant">
                                            <span className="participant-name">{participant.name}</span>
                                            <span className={`vote ${participant.vote}`}>
                                                {participant.vote === 'agree' ? '✅' : 
                                                 participant.vote === 'disagree' ? '❌' : '🤔'}
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className="consensus-result">
                                <span className="result-label">Result:</span>
                                <span className={`result-status ${consensus.result}`}>
                                    {consensus.result}
                                </span>
                                <span className="consensus-score">
                                    {Math.round(consensus.agreementScore * 100)}% agreement
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    return (
        <div className="agent-collaboration">
            <div className="collaboration-header">
                <h4>Agent Collaboration</h4>
                <div className="collaboration-stats">
                    <span>Active: {collaborationData?.activeInteractions || 0}</span>
                    <span>Total: {collaborationData?.totalInteractions || 0}</span>
                </div>
            </div>

            <div className="collaboration-content">
                {renderCommunicationFlow()}
                {renderTaskDelegation()}
                {renderConsensusFormation()}
            </div>

            {!collaborationData && (
                <div className="no-collaboration">
                    No collaboration data available
                </div>
            )}
        </div>
    );
};

export default AgentCollaboration;