.agent-collaboration {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.collaboration-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eaed;
}

.collaboration-header h4 {
    margin: 0;
    font-size: 16px;
    color: #202124;
}

.collaboration-stats {
    display: flex;
    gap: 12px;
    font-size: 14px;
    color: #5f6368;
}

.collaboration-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.communication-flow h5,
.task-delegation h5,
.consensus-formation h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #202124;
    font-weight: 500;
}

.flow-timeline {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.communication-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border-left: 3px solid #1a73e8;
}

.comm-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 13px;
}

.comm-from,
.comm-to {
    font-weight: 500;
    color: #202124;
}

.comm-arrow {
    color: #5f6368;
}

.comm-time {
    margin-left: auto;
    color: #9aa0a6;
    font-size: 12px;
}

.comm-content {
    font-size: 13px;
    color: #5f6368;
    margin-bottom: 6px;
}

.comm-type {
    display: flex;
    justify-content: flex-end;
}

.type-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.type-badge.request {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.type-badge.response {
    background-color: #e8f5e8;
    color: #137333;
}

.type-badge.notification {
    background-color: #fef7e0;
    color: #b06000;
}

.delegation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
}

.delegation-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border-left: 3px solid #34a853;
}

.delegation-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
}

.delegator,
.assignee {
    font-weight: 500;
    color: #202124;
}

.delegation-arrow {
    color: #5f6368;
}

.task-details {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.task-name {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.task-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.pending {
    background-color: #fef7e0;
    color: #b06000;
}

.status-badge.in-progress {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.status-badge.completed {
    background-color: #e8f5e8;
    color: #137333;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #34a853, #66bb6a);
    transition: width 0.3s ease;
}

.consensus-process {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.consensus-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border-left: 3px solid #fbbc04;
}

.consensus-topic {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
    margin-bottom: 8px;
}

.consensus-participants {
    margin-bottom: 8px;
}

.consensus-participants strong {
    font-size: 13px;
    color: #202124;
}

.participant-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 6px;
}

.participant {
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: #ffffff;
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid #e8eaed;
}

.participant-name {
    font-size: 12px;
    color: #202124;
}

.vote {
    font-size: 12px;
}

.consensus-result {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.result-label {
    color: #5f6368;
    font-weight: 500;
}

.result-status {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 500;
    text-transform: capitalize;
}

.result-status.reached {
    background-color: #e8f5e8;
    color: #137333;
}

.result-status.pending {
    background-color: #fef7e0;
    color: #b06000;
}

.consensus-score {
    color: #5f6368;
    font-size: 12px;
}

.no-collaboration {
    text-align: center;
    color: #9aa0a6;
    padding: 40px;
    font-style: italic;
}

@media (max-width: 800px) {
    .collaboration-stats {
        flex-direction: column;
        gap: 4px;
    }
    
    .delegation-grid {
        grid-template-columns: 1fr;
    }
    
    .participant-list {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .consensus-result {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}