.accessibility-panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.accessibility-panel {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e8eaed;
}

.panel-header h3 {
    margin: 0;
    font-size: 18px;
    color: #202124;
}

.close-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background-color: #f1f3f4;
    color: #5f6368;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover,
.close-button:focus {
    background-color: #e8eaed;
    color: #202124;
    outline: 2px solid #1a73e8;
}

.panel-content {
    padding: 20px;
}

.setting-group {
    margin-bottom: 24px;
}

.setting-group h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #202124;
    font-weight: 500;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #202124;
    cursor: pointer;
    min-height: 44px;
}

.setting-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.setting-description {
    margin: 4px 0 0 26px;
    font-size: 13px;
    color: #5f6368;
}

.font-size-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #202124;
    cursor: pointer;
    min-height: 44px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.radio-label:hover,
.radio-label:focus-within {
    background-color: #f8f9fa;
}

.radio-label input[type="radio"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.screen-reader-status {
    font-size: 14px;
    color: #202124;
    margin: 0 0 4px 0;
    font-weight: 500;
}

.keyboard-help ul {
    margin: 0;
    padding: 0 0 0 20px;
    list-style: none;
}

.keyboard-help li {
    margin-bottom: 8px;
    font-size: 13px;
    color: #5f6368;
    display: flex;
    align-items: center;
    gap: 8px;
}

.keyboard-help kbd {
    background-color: #f1f3f4;
    border: 1px solid #dadce0;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 11px;
    font-family: monospace;
    color: #202124;
    min-width: 20px;
    text-align: center;
}

/* High contrast mode styles */
:root.high-contrast .accessibility-panel {
    background-color: #000000;
    color: #ffffff;
    border: 2px solid #ffffff;
}

:root.high-contrast .panel-header {
    border-bottom-color: #ffffff;
}

:root.high-contrast .panel-header h3,
:root.high-contrast .setting-group h4,
:root.high-contrast .setting-label,
:root.high-contrast .radio-label,
:root.high-contrast .screen-reader-status {
    color: #ffffff;
}

:root.high-contrast .setting-description,
:root.high-contrast .keyboard-help li {
    color: #cccccc;
}

:root.high-contrast .close-button {
    background-color: #333333;
    color: #ffffff;
    border: 1px solid #ffffff;
}

:root.high-contrast .close-button:hover,
:root.high-contrast .close-button:focus {
    background-color: #ffffff;
    color: #000000;
}

:root.high-contrast .radio-label:hover,
:root.high-contrast .radio-label:focus-within {
    background-color: #333333;
}

:root.high-contrast .keyboard-help kbd {
    background-color: #333333;
    color: #ffffff;
    border-color: #ffffff;
}

/* Font size adjustments */
:root.font-size-small {
    font-size: 14px;
}

:root.font-size-medium {
    font-size: 16px;
}

:root.font-size-large {
    font-size: 18px;
}

:root.font-size-extra-large {
    font-size: 20px;
}

/* Reduced motion */
:root.reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

@media (max-width: 600px) {
    .accessibility-panel-overlay {
        padding: 10px;
    }
    
    .accessibility-panel {
        max-height: 95vh;
    }
    
    .panel-header,
    .panel-content {
        padding: 16px;
    }
    
    .font-size-controls {
        gap: 4px;
    }
    
    .radio-label {
        padding: 12px 8px;
    }
}