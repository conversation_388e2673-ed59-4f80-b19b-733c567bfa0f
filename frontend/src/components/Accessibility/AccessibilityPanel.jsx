import React, { useState } from 'react';
import './AccessibilityPanel.css';
import useAccessibility from '../../hooks/accessibility/useAccessibility';

const AccessibilityPanel = ({ isVisible, onClose }) => {
    const {
        highContrast,
        fontSize,
        reducedMotion,
        screenReader,
        toggleHighContrast,
        changeFontSize,
        toggleReducedMotion
    } = useAccessibility();

    if (!isVisible) return null;

    return (
        <div className="accessibility-panel-overlay" role="dialog" aria-labelledby="accessibility-title">
            <div className="accessibility-panel">
                <div className="panel-header">
                    <h3 id="accessibility-title">Accessibility Settings</h3>
                    <button 
                        className="close-button"
                        onClick={onClose}
                        aria-label="Close accessibility panel"
                    >
                        ×
                    </button>
                </div>

                <div className="panel-content">
                    <div className="setting-group">
                        <h4>Visual Settings</h4>
                        
                        <div className="setting-item">
                            <label className="setting-label">
                                <input
                                    type="checkbox"
                                    checked={highContrast}
                                    onChange={toggleHighContrast}
                                    aria-describedby="high-contrast-desc"
                                />
                                High Contrast Mode
                            </label>
                            <p id="high-contrast-desc" className="setting-description">
                                Increases contrast for better visibility
                            </p>
                        </div>

                        <div className="setting-item">
                            <label className="setting-label">Font Size</label>
                            <div className="font-size-controls" role="radiogroup" aria-labelledby="font-size-label">
                                {['small', 'medium', 'large', 'extra-large'].map(size => (
                                    <label key={size} className="radio-label">
                                        <input
                                            type="radio"
                                            name="fontSize"
                                            value={size}
                                            checked={fontSize === size}
                                            onChange={() => changeFontSize(size)}
                                        />
                                        {size.charAt(0).toUpperCase() + size.slice(1).replace('-', ' ')}
                                    </label>
                                ))}
                            </div>
                        </div>
                    </div>

                    <div className="setting-group">
                        <h4>Motion Settings</h4>
                        
                        <div className="setting-item">
                            <label className="setting-label">
                                <input
                                    type="checkbox"
                                    checked={reducedMotion}
                                    onChange={toggleReducedMotion}
                                    aria-describedby="reduced-motion-desc"
                                />
                                Reduce Motion
                            </label>
                            <p id="reduced-motion-desc" className="setting-description">
                                Minimizes animations and transitions
                            </p>
                        </div>
                    </div>

                    <div className="setting-group">
                        <h4>Screen Reader</h4>
                        <div className="setting-item">
                            <p className="screen-reader-status">
                                Status: {screenReader ? 'Detected' : 'Not detected'}
                            </p>
                            <p className="setting-description">
                                Screen reader compatibility is automatically enabled
                            </p>
                        </div>
                    </div>

                    <div className="setting-group">
                        <h4>Keyboard Navigation</h4>
                        <div className="keyboard-help">
                            <ul>
                                <li><kbd>Tab</kbd> - Navigate forward</li>
                                <li><kbd>Shift + Tab</kbd> - Navigate backward</li>
                                <li><kbd>Enter</kbd> or <kbd>Space</kbd> - Activate</li>
                                <li><kbd>Escape</kbd> - Close dialogs</li>
                                <li><kbd>Arrow keys</kbd> - Navigate lists</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AccessibilityPanel;