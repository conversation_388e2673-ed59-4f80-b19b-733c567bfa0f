.improvement-analytics {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.improvement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.improvement-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.metric-select {
    padding: 8px 12px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
}

.time-controls {
    display: flex;
    gap: 4px;
}

.time-btn {
    padding: 6px 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    color: #5f6368;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.time-btn:hover {
    background: #f8f9fa;
}

.time-btn.active {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.trends-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.trend-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.2s;
}

.trend-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.trend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.trend-header h3 {
    margin: 0;
    color: #202124;
    font-size: 16px;
    font-weight: 500;
}

.trend-icon {
    font-size: 18px;
}

.trend-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin-bottom: 8px;
}

.current {
    font-size: 32px;
    font-weight: 600;
    color: #1a73e8;
}

.unit {
    font-size: 14px;
    color: #5f6368;
}

.trend-change {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.change {
    font-size: 14px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
}

.change.positive {
    background: #e8f5e8;
    color: #137333;
}

.period {
    font-size: 12px;
    color: #5f6368;
}

.trend-target {
    font-size: 12px;
    color: #5f6368;
}

.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.learning-curve,
.improvement-areas {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.learning-curve h3,
.improvement-areas h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.curve-chart {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.chart-header {
    display: flex;
    justify-content: center;
}

.legend {
    display: flex;
    gap: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #5f6368;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-color.accuracy {
    background: #1a73e8;
}

.legend-color.speed {
    background: #34a853;
}

.legend-color.quality {
    background: #ff9800;
}

.chart-data {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 200px;
    padding: 0 16px;
}

.data-point {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.week-label {
    font-size: 11px;
    color: #5f6368;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.metrics-bars {
    display: flex;
    gap: 2px;
    align-items: end;
    height: 150px;
}

.metric-bar {
    width: 8px;
    height: 100%;
    background: #f1f3f4;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.bar-fill {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-radius: 2px;
    transition: height 0.3s ease;
}

.bar-fill.accuracy {
    background: linear-gradient(to top, #1a73e8, #4b90ff);
}

.bar-fill.speed {
    background: linear-gradient(to top, #34a853, #66bb6a);
}

.bar-fill.quality {
    background: linear-gradient(to top, #ff9800, #ffa726);
}

.areas-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.area-item {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #e8eaed;
}

.area-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.area-name {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.priority-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.priority-badge.high {
    background: #fce8e6;
    color: #d93025;
}

.priority-badge.medium {
    background: #fef7e0;
    color: #b06000;
}

.priority-badge.low {
    background: #e8f5e8;
    color: #137333;
}

.area-progress {
    margin-bottom: 8px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #5f6368;
    margin-bottom: 4px;
}

.progress-bar {
    height: 6px;
    background: #e8eaed;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

.area-timeline {
    font-size: 12px;
    color: #5f6368;
    font-style: italic;
}

.benchmarking {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.benchmarking h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.benchmark-table {
    display: flex;
    flex-direction: column;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 8px;
    padding: 12px 0;
    border-bottom: 2px solid #e8eaed;
    margin-bottom: 8px;
}

.header-cell {
    font-size: 12px;
    font-weight: 600;
    color: #5f6368;
    text-transform: uppercase;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 8px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.cell {
    font-size: 14px;
    color: #202124;
}

.category {
    font-weight: 500;
}

.current {
    font-weight: 600;
    color: #1a73e8;
}

.performance-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.performance-badge.excellent {
    background: #e8f5e8;
    color: #137333;
}

.performance-badge.good {
    background: #e8f0fe;
    color: #1a73e8;
}

.performance-badge.average {
    background: #fef7e0;
    color: #b06000;
}

.performance-badge.poor {
    background: #fce8e6;
    color: #d93025;
}

@media (max-width: 768px) {
    .improvement-analytics {
        padding: 16px;
    }
    
    .improvement-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .controls {
        justify-content: space-between;
    }
    
    .trends-overview {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-data {
        height: 150px;
        padding: 0 8px;
    }
    
    .metrics-bars {
        height: 100px;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 4px;
    }
    
    .table-header {
        display: none;
    }
    
    .cell {
        padding: 4px 0;
        display: flex;
        justify-content: space-between;
    }
    
    .cell::before {
        content: attr(data-label);
        font-weight: 600;
        color: #5f6368;
        font-size: 12px;
    }
}