import React, { useState } from 'react';
import './ImprovementAnalytics.css';

const ImprovementAnalytics = () => {
    const [selectedMetric, setSelectedMetric] = useState('performance');
    const [timeRange, setTimeRange] = useState('30d');

    const learningCurveData = [
        { week: 'Week 1', accuracy: 78, speed: 65, quality: 72 },
        { week: 'Week 2', accuracy: 82, speed: 71, quality: 76 },
        { week: 'Week 3', accuracy: 85, speed: 76, quality: 80 },
        { week: 'Week 4', accuracy: 88, speed: 82, quality: 84 },
        { week: 'Week 5', accuracy: 91, speed: 87, quality: 87 },
        { week: 'Week 6', accuracy: 93, speed: 89, quality: 90 },
        { week: 'Week 7', accuracy: 94, speed: 91, quality: 92 },
        { week: 'Week 8', accuracy: 95, speed: 93, quality: 93 }
    ];

    const performanceTrends = {
        accuracy: { current: 95, change: 12, trend: 'up', target: 96 },
        speed: { current: 93, change: 18, trend: 'up', target: 95 },
        quality: { current: 93, change: 15, trend: 'up', target: 95 },
        satisfaction: { current: 89, change: 8, trend: 'up', target: 90 }
    };

    const benchmarkData = [
        { 
            category: 'Response Time', 
            current: 1.2, 
            industry: 1.8, 
            best: 0.9, 
            unit: 'seconds',
            performance: 'above-average'
        },
        { 
            category: 'Accuracy Rate', 
            current: 95, 
            industry: 87, 
            best: 98, 
            unit: '%',
            performance: 'excellent'
        },
        { 
            category: 'User Satisfaction', 
            current: 4.2, 
            industry: 3.8, 
            best: 4.7, 
            unit: '/5.0',
            performance: 'above-average'
        },
        { 
            category: 'Task Completion', 
            current: 89, 
            industry: 82, 
            best: 94, 
            unit: '%',
            performance: 'above-average'
        }
    ];

    const improvementAreas = [
        { 
            area: 'Complex Query Handling', 
            currentScore: 78, 
            targetScore: 85, 
            priority: 'high',
            estimatedImprovement: '2-3 weeks'
        },
        { 
            area: 'Multi-step Reasoning', 
            currentScore: 82, 
            targetScore: 88, 
            priority: 'medium',
            estimatedImprovement: '3-4 weeks'
        },
        { 
            area: 'Context Retention', 
            currentScore: 85, 
            targetScore: 90, 
            priority: 'medium',
            estimatedImprovement: '2-3 weeks'
        },
        { 
            area: 'Error Recovery', 
            currentScore: 73, 
            targetScore: 82, 
            priority: 'high',
            estimatedImprovement: '4-5 weeks'
        }
    ];

    const getPerformanceColor = (performance) => {
        switch (performance) {
            case 'excellent': return 'excellent';
            case 'above-average': return 'good';
            case 'average': return 'average';
            case 'below-average': return 'poor';
            default: return 'average';
        }
    };

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high': return 'high';
            case 'medium': return 'medium';
            case 'low': return 'low';
            default: return 'medium';
        }
    };

    const getTrendIcon = (trend) => trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';

    return (
        <div className="improvement-analytics">
            <div className="improvement-header">
                <h2>System Improvement Analytics</h2>
                <div className="controls">
                    <select 
                        value={selectedMetric} 
                        onChange={(e) => setSelectedMetric(e.target.value)}
                        className="metric-select"
                    >
                        <option value="performance">Performance</option>
                        <option value="accuracy">Accuracy</option>
                        <option value="speed">Speed</option>
                        <option value="quality">Quality</option>
                    </select>
                    <div className="time-controls">
                        {['7d', '30d', '90d'].map(range => (
                            <button
                                key={range}
                                className={`time-btn ${timeRange === range ? 'active' : ''}`}
                                onClick={() => setTimeRange(range)}
                            >
                                {range}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            <div className="trends-overview">
                {Object.entries(performanceTrends).map(([key, data]) => (
                    <div key={key} className="trend-card">
                        <div className="trend-header">
                            <h3>{key.charAt(0).toUpperCase() + key.slice(1)}</h3>
                            <span className="trend-icon">{getTrendIcon(data.trend)}</span>
                        </div>
                        <div className="trend-value">
                            <span className="current">{data.current}</span>
                            <span className="unit">{key === 'quality' || key === 'accuracy' || key === 'speed' || key === 'satisfaction' ? '%' : ''}</span>
                        </div>
                        <div className="trend-change">
                            <span className="change positive">+{data.change}%</span>
                            <span className="period">improvement</span>
                        </div>
                        <div className="trend-target">
                            Target: {data.target}{key === 'quality' || key === 'accuracy' || key === 'speed' || key === 'satisfaction' ? '%' : ''}
                        </div>
                    </div>
                ))}
            </div>

            <div className="analytics-grid">
                <div className="learning-curve">
                    <h3>Learning Curve Visualization</h3>
                    <div className="curve-chart">
                        <div className="chart-header">
                            <div className="legend">
                                <div className="legend-item">
                                    <div className="legend-color accuracy"></div>
                                    <span>Accuracy</span>
                                </div>
                                <div className="legend-item">
                                    <div className="legend-color speed"></div>
                                    <span>Speed</span>
                                </div>
                                <div className="legend-item">
                                    <div className="legend-color quality"></div>
                                    <span>Quality</span>
                                </div>
                            </div>
                        </div>
                        <div className="chart-data">
                            {learningCurveData.map((data, index) => (
                                <div key={index} className="data-point">
                                    <div className="week-label">{data.week}</div>
                                    <div className="metrics-bars">
                                        <div className="metric-bar">
                                            <div 
                                                className="bar-fill accuracy"
                                                style={{ height: `${data.accuracy}%` }}
                                            ></div>
                                        </div>
                                        <div className="metric-bar">
                                            <div 
                                                className="bar-fill speed"
                                                style={{ height: `${data.speed}%` }}
                                            ></div>
                                        </div>
                                        <div className="metric-bar">
                                            <div 
                                                className="bar-fill quality"
                                                style={{ height: `${data.quality}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                <div className="improvement-areas">
                    <h3>Priority Improvement Areas</h3>
                    <div className="areas-list">
                        {improvementAreas.map((area, index) => (
                            <div key={index} className="area-item">
                                <div className="area-header">
                                    <span className="area-name">{area.area}</span>
                                    <span className={`priority-badge ${getPriorityColor(area.priority)}`}>
                                        {area.priority}
                                    </span>
                                </div>
                                <div className="area-progress">
                                    <div className="progress-info">
                                        <span>Current: {area.currentScore}%</span>
                                        <span>Target: {area.targetScore}%</span>
                                    </div>
                                    <div className="progress-bar">
                                        <div 
                                            className="progress-fill"
                                            style={{ width: `${(area.currentScore / area.targetScore) * 100}%` }}
                                        ></div>
                                    </div>
                                </div>
                                <div className="area-timeline">
                                    Estimated: {area.estimatedImprovement}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="benchmarking">
                <h3>Comparative Benchmarking</h3>
                <div className="benchmark-table">
                    <div className="table-header">
                        <div className="header-cell">Category</div>
                        <div className="header-cell">Current</div>
                        <div className="header-cell">Industry Avg</div>
                        <div className="header-cell">Best in Class</div>
                        <div className="header-cell">Performance</div>
                    </div>
                    {benchmarkData.map((item, index) => (
                        <div key={index} className="table-row">
                            <div className="cell category">{item.category}</div>
                            <div className="cell current">
                                {item.current}{item.unit}
                            </div>
                            <div className="cell industry">
                                {item.industry}{item.unit}
                            </div>
                            <div className="cell best">
                                {item.best}{item.unit}
                            </div>
                            <div className="cell">
                                <span className={`performance-badge ${getPerformanceColor(item.performance)}`}>
                                    {item.performance.replace('-', ' ')}
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default ImprovementAnalytics;