.message-thread {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px 0;
}

.message {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.message:hover {
    background-color: #f8f9fa;
}

.message.user {
    margin-left: 20%;
    background-color: #f0f4f9;
}

.message.assistant {
    margin-right: 20%;
    background-color: #ffffff;
    border: 1px solid #e8eaed;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 12px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.message-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
}

.message-sender {
    font-weight: 500;
    font-size: 14px;
    color: #202124;
}

.message-time {
    font-size: 12px;
    color: #5f6368;
}

.typing-indicator {
    font-size: 12px;
    color: #1a73e8;
    font-style: italic;
}

.confidence-indicator {
    margin-left: auto;
}

.confidence-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.confidence-badge.high {
    background-color: #e8f5e8;
    color: #137333;
}

.confidence-badge.medium {
    background-color: #fef7e0;
    color: #b06000;
}

.confidence-badge.low {
    background-color: #fce8e6;
    color: #d93025;
}

.message-content {
    margin-left: 44px;
}

.message-content p {
    font-size: 16px;
    line-height: 1.5;
    color: #202124;
    margin: 0;
}

.message-context {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.context-org, .context-dept {
    padding: 2px 8px;
    background-color: #e8f0fe;
    color: #1a73e8;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.reasoning-preview {
    margin-left: 44px;
    margin-top: 8px;
}

.reasoning-toggle {
    color: #1a73e8;
    font-size: 13px;
    cursor: pointer;
    text-decoration: underline;
}

.reasoning-toggle:hover {
    color: #1557b0;
}

.loader {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.loader hr {
    border-radius: 4px;
    border: none;
    background: #f6f7f8 linear-gradient(to right, #9ed7ff, #ffffff, #9ed7ff);
    background-size: 800px 50px;
    height: 16px;
    animation: loader 3s infinite linear;
}

@keyframes loader {
    0% { background-position: -800px 0; }
    100% { background-position: 800px 0; }
}

@media (max-width: 800px) {
    .message.user {
        margin-left: 10%;
    }
    
    .message.assistant {
        margin-right: 10%;
    }
    
    .message-content {
        margin-left: 0;
    }
    
    .reasoning-preview {
        margin-left: 0;
    }
}