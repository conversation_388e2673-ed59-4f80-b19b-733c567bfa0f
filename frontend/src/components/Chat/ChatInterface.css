.chat-interface {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #ffffff;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e8eaed;
    background-color: #f8f9fa;
}

.chat-header h2 {
    font-size: 20px;
    font-weight: 500;
    color: #202124;
    margin: 0;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #5f6368;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background-color: #34a853;
}

.status-dot.disconnected {
    background-color: #ea4335;
}

.chat-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 4px;
}

.chat-input {
    padding: 16px 20px;
    border-top: 1px solid #e8eaed;
    background-color: #ffffff;
}

.input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    max-width: 900px;
    margin: 0 auto;
}

.message-input {
    flex: 1;
    min-height: 44px;
    max-height: 120px;
    padding: 12px 16px;
    border: 1px solid #e8eaed;
    border-radius: 22px;
    font-size: 16px;
    font-family: 'Outfit', sans-serif;
    resize: none;
    outline: none;
    transition: border-color 0.2s;
}

.message-input:focus {
    border-color: #1a73e8;
}

.message-input::placeholder {
    color: #9aa0a6;
}

.send-button {
    padding: 12px 24px;
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    color: white;
    border: none;
    border-radius: 22px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s;
}

.send-button:hover:not(:disabled) {
    opacity: 0.9;
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@media (max-width: 800px) {
    .chat-header {
        padding: 12px 16px;
    }
    
    .chat-header h2 {
        font-size: 18px;
    }
    
    .chat-content {
        padding: 16px;
    }
    
    .chat-input {
        padding: 12px 16px;
    }
    
    .input-container {
        gap: 8px;
    }
    
    .message-input {
        font-size: 16px;
        padding: 10px 14px;
    }
    
    .send-button {
        padding: 10px 20px;
        font-size: 13px;
    }
}