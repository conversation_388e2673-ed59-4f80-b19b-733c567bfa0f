import React, { useState } from 'react';
import './MessageThread.css';
import { assets } from '../../assets/assets.js';
import ReasoningVisualization from '../Reasoning/ReasoningVisualization';

const MessageThread = ({ messages, loading, onMessageClick }) => {
    const [showReasoning, setShowReasoning] = useState(false);
    const [selectedMessage, setSelectedMessage] = useState(null);

    const handleReasoningClick = (message) => {
        setSelectedMessage(message);
        setShowReasoning(true);
    };

    return (
        <div className="message-thread">
            {messages.map((message, index) => (
                <div key={index} className={`message ${message.type}`} onClick={() => onMessageClick && onMessageClick(message)}>
                    <div className="message-header">
                        <img 
                            src={message.type === 'user' ? assets.user_icon : assets.gemini_icon} 
                            alt={message.type}
                            className="message-avatar"
                        />
                        <div className="message-meta">
                            <span className="message-sender">{message.type === 'user' ? 'You' : 'CHaBot'}</span>
                            <span className="message-time">{message.timestamp}</span>
                        </div>
                        {message.confidence && (
                            <div className="confidence-indicator">
                                <span className={`confidence-badge ${message.confidence > 0.8 ? 'high' : message.confidence > 0.5 ? 'medium' : 'low'}`}>
                                    {Math.round(message.confidence * 100)}%
                                </span>
                            </div>
                        )}
                    </div>
                    <div className="message-content">
                        <p dangerouslySetInnerHTML={{ __html: message.content }}></p>
                        {message.context && (
                            <div className="message-context">
                                <span className="context-org">{message.context.organization}</span>
                                <span className="context-dept">{message.context.department}</span>
                            </div>
                        )}
                    </div>
                    {message.reasoning && (
                        <div className="reasoning-preview">
                            <span 
                                className="reasoning-toggle"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleReasoningClick(message);
                                }}
                            >
                                View Reasoning
                            </span>
                        </div>
                    )}
                </div>
            ))}
            {loading && (
                <div className="message assistant">
                    <div className="message-header">
                        <img src={assets.gemini_icon} alt="assistant" className="message-avatar" />
                        <div className="message-meta">
                            <span className="message-sender">CHaBot</span>
                            <span className="typing-indicator">is thinking...</span>
                        </div>
                    </div>
                    <div className="message-content">
                        <div className="loader">
                            <hr />
                            <hr />
                            <hr />
                        </div>
                    </div>
                </div>
            )}
            
            <ReasoningVisualization 
                reasoningData={selectedMessage}
                isVisible={showReasoning}
                onClose={() => setShowReasoning(false)}
            />
        </div>
    );
};

export default MessageThread;