import React, { useState, useEffect } from 'react';
import './StreamingResponse.css';

const StreamingResponse = ({ content, isStreaming, onComplete }) => {
    const [displayedContent, setDisplayedContent] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        if (!isStreaming || !content) return;

        const streamInterval = setInterval(() => {
            if (currentIndex < content.length) {
                setDisplayedContent(prev => prev + content[currentIndex]);
                setCurrentIndex(prev => prev + 1);
            } else {
                clearInterval(streamInterval);
                if (onComplete) onComplete();
            }
        }, 30);

        return () => clearInterval(streamInterval);
    }, [content, currentIndex, isStreaming, onComplete]);

    useEffect(() => {
        if (!isStreaming) {
            setDisplayedContent(content || '');
            setCurrentIndex(content?.length || 0);
        }
    }, [isStreaming, content]);

    return (
        <div className="streaming-response">
            <div className="response-content">
                <p dangerouslySetInnerHTML={{ __html: displayedContent }}></p>
                {isStreaming && currentIndex < (content?.length || 0) && (
                    <span className="streaming-cursor">|</span>
                )}
            </div>
        </div>
    );
};

export default StreamingResponse;