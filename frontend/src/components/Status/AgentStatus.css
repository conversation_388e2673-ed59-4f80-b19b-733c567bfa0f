.agent-status {
    background-color: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.agent-status-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8eaed;
}

.status-icon {
    width: 16px;
    height: 16px;
}

.status-title {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.agent-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.agent-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.agent-item:hover {
    background-color: #f8f9fa;
}

.agent-item.active {
    background-color: #e8f0fe;
    border: 1px solid #1a73e8;
}

.agent-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.agent-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.agent-name {
    font-size: 13px;
    font-weight: 500;
    color: #202124;
}

.agent-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.agent-status-text {
    font-size: 12px;
    color: #5f6368;
}

.agent-progress {
    width: 60px;
    height: 3px;
    background-color: #e8eaed;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4b90ff, #1a73e8);
    transition: width 0.3s ease;
    animation: progress 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes progress {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(0);
    }
}

@media (max-width: 800px) {
    .agent-status {
        margin: 12px 0;
        padding: 12px;
    }
    
    .agent-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .agent-details {
        align-items: flex-start;
        width: 100%;
    }
    
    .agent-progress {
        width: 100%;
    }
}