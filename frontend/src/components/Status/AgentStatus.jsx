import React from 'react';
import './AgentStatus.css';
import { assets } from '../../assets/assets.js';

const AgentStatus = ({ agents = [], activeAgent = null, onAgentClick }) => {
    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return '#34a853';
            case 'thinking': return '#fbbc04';
            case 'waiting': return '#9aa0a6';
            default: return '#9aa0a6';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'active': return 'Active';
            case 'thinking': return 'Processing';
            case 'waiting': return 'Standby';
            default: return 'Idle';
        }
    };

    if (!agents.length) return null;

    return (
        <div className="agent-status">
            <div className="agent-status-header">
                <img src={assets.setting_icon} alt="agents" className="status-icon" />
                <span className="status-title">Active Agents</span>
            </div>
            
            <div className="agent-list">
                {agents.map((agent, index) => (
                    <div 
                        key={index} 
                        className={`agent-item ${agent.id === activeAgent ? 'active' : ''}`}
                        onClick={() => onAgentClick && onAgentClick(agent)}
                    >
                        <div className="agent-info">
                            <div 
                                className="agent-status-dot" 
                                style={{ backgroundColor: getStatusColor(agent.status) }}
                            ></div>
                            <span className="agent-name">{agent.name}</span>
                        </div>
                        
                        <div className="agent-details">
                            <span className="agent-status-text">
                                {getStatusText(agent.status)}
                            </span>
                            {agent.progress && (
                                <div className="agent-progress">
                                    <div 
                                        className="progress-bar"
                                        style={{ width: `${agent.progress}%` }}
                                    ></div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default AgentStatus;