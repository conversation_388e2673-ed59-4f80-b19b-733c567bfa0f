.department-config {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.config-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.add-button {
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s;
}

.add-button:hover {
    opacity: 0.9;
}

.departments-table {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    background: #f8f9fa;
    border-bottom: 1px solid #e8eaed;
}

.header-cell {
    padding: 16px;
    font-weight: 600;
    color: #202124;
    font-size: 14px;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s;
}

.table-row:hover {
    background: #f8f9fa;
}

.table-row.inactive {
    opacity: 0.6;
}

.cell {
    padding: 16px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #202124;
}

.dept-name {
    font-weight: 500;
}

.dept-info .name {
    color: #202124;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.active {
    background: #e8f5e8;
    color: #137333;
}

.status-badge.inactive {
    background: #fce8e6;
    color: #d93025;
}

.actions {
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    color: #1a73e8;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.action-btn:hover {
    background: #f8f9fa;
    border-color: #1a73e8;
}

.action-btn.edit {
    color: #1a73e8;
}

.action-btn.toggle {
    color: #ea4335;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: #ffffff;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e8eaed;
}

.modal-header h3 {
    margin: 0;
    color: #202124;
}

.modal-header button {
    background: none;
    border: none;
    font-size: 24px;
    color: #5f6368;
    cursor: pointer;
    padding: 4px;
}

.form-group {
    margin-bottom: 16px;
    padding: 0 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #202124;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1a73e8;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #e8eaed;
}

.form-actions button {
    flex: 1;
    padding: 12px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.form-actions button[type="button"] {
    background: #ffffff;
    border: 1px solid #e8eaed;
    color: #5f6368;
}

.form-actions button[type="submit"] {
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    border: none;
    color: white;
}

.form-actions button:hover {
    opacity: 0.9;
}

@media (max-width: 768px) {
    .department-config {
        padding: 16px;
    }
    
    .config-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .header-cell,
    .cell {
        padding: 12px 16px;
    }
    
    .table-header {
        display: none;
    }
    
    .cell::before {
        content: attr(data-label);
        font-weight: 600;
        margin-right: 8px;
        color: #5f6368;
    }
}