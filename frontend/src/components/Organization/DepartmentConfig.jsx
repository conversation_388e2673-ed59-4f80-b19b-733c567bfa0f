import React, { useState } from 'react';
import './DepartmentConfig.css';

const DepartmentConfig = () => {
    const [departments, setDepartments] = useState([
        { id: 1, name: 'Human Resources', employees: 25, policies: 12, status: 'active' },
        { id: 2, name: 'Engineering', employees: 80, policies: 8, status: 'active' },
        { id: 3, name: 'Marketing', employees: 15, policies: 6, status: 'active' },
        { id: 4, name: 'Finance', employees: 12, policies: 15, status: 'inactive' }
    ]);
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({ name: '', description: '', manager: '' });

    const handleSubmit = (e) => {
        e.preventDefault();
        const newDept = {
            id: Date.now(),
            name: formData.name,
            employees: 0,
            policies: 0,
            status: 'active'
        };
        setDepartments([...departments, newDept]);
        setFormData({ name: '', description: '', manager: '' });
        setShowForm(false);
    };

    const toggleStatus = (id) => {
        setDepartments(depts => depts.map(dept => 
            dept.id === id ? { ...dept, status: dept.status === 'active' ? 'inactive' : 'active' } : dept
        ));
    };

    return (
        <div className="department-config">
            <div className="config-header">
                <h2>Department Configuration</h2>
                <button className="add-button" onClick={() => setShowForm(true)}>
                    + Add Department
                </button>
            </div>

            <div className="departments-table">
                <div className="table-header">
                    <div className="header-cell">Department</div>
                    <div className="header-cell">Employees</div>
                    <div className="header-cell">Policies</div>
                    <div className="header-cell">Status</div>
                    <div className="header-cell">Actions</div>
                </div>
                
                {departments.map(dept => (
                    <div key={dept.id} className={`table-row ${dept.status}`}>
                        <div className="cell dept-name">
                            <div className="dept-info">
                                <span className="name">{dept.name}</span>
                            </div>
                        </div>
                        <div className="cell">{dept.employees}</div>
                        <div className="cell">{dept.policies}</div>
                        <div className="cell">
                            <span className={`status-badge ${dept.status}`}>
                                {dept.status}
                            </span>
                        </div>
                        <div className="cell actions">
                            <button className="action-btn edit">Edit</button>
                            <button 
                                className="action-btn toggle"
                                onClick={() => toggleStatus(dept.id)}
                            >
                                {dept.status === 'active' ? 'Disable' : 'Enable'}
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {showForm && (
                <div className="modal-overlay">
                    <div className="modal">
                        <div className="modal-header">
                            <h3>Add Department</h3>
                            <button onClick={() => setShowForm(false)}>×</button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Department Name</label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Description</label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                />
                            </div>
                            <div className="form-group">
                                <label>Department Manager</label>
                                <input
                                    type="text"
                                    value={formData.manager}
                                    onChange={(e) => setFormData({...formData, manager: e.target.value})}
                                />
                            </div>
                            <div className="form-actions">
                                <button type="button" onClick={() => setShowForm(false)}>Cancel</button>
                                <button type="submit">Create Department</button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DepartmentConfig;