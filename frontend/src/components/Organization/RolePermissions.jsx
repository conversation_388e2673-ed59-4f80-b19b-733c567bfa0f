import React, { useState } from 'react';
import './RolePermissions.css';

const RolePermissions = () => {
    const [roles, setRoles] = useState([
        { 
            id: 1, 
            name: 'Admin', 
            users: 2, 
            permissions: ['read', 'write', 'delete', 'manage_users', 'manage_content'],
            description: 'Full system access'
        },
        { 
            id: 2, 
            name: 'HR Manager', 
            users: 5, 
            permissions: ['read', 'write', 'manage_content'],
            description: 'HR content management'
        },
        { 
            id: 3, 
            name: 'Employee', 
            users: 143, 
            permissions: ['read'],
            description: 'Basic read access'
        }
    ]);
    const [showForm, setShowForm] = useState(false);
    const [selectedRole, setSelectedRole] = useState(null);
    const [formData, setFormData] = useState({ name: '', description: '', permissions: [] });

    const allPermissions = [
        { id: 'read', name: 'Read Content', description: 'View content and conversations' },
        { id: 'write', name: 'Write Content', description: 'Create and edit content' },
        { id: 'delete', name: 'Delete Content', description: 'Remove content and data' },
        { id: 'manage_users', name: 'Manage Users', description: 'Add, edit, and remove users' },
        { id: 'manage_content', name: 'Manage Content', description: 'Approve and organize content' },
        { id: 'manage_agents', name: 'Manage Agents', description: 'Configure and deploy agents' },
        { id: 'view_analytics', name: 'View Analytics', description: 'Access reports and analytics' }
    ];

    const handleSubmit = (e) => {
        e.preventDefault();
        const newRole = {
            id: Date.now(),
            name: formData.name,
            users: 0,
            permissions: formData.permissions,
            description: formData.description
        };
        setRoles([...roles, newRole]);
        setFormData({ name: '', description: '', permissions: [] });
        setShowForm(false);
    };

    const togglePermission = (permissionId) => {
        setFormData(prev => ({
            ...prev,
            permissions: prev.permissions.includes(permissionId)
                ? prev.permissions.filter(p => p !== permissionId)
                : [...prev.permissions, permissionId]
        }));
    };

    const editRole = (role) => {
        setSelectedRole(role);
        setFormData({
            name: role.name,
            description: role.description,
            permissions: role.permissions
        });
        setShowForm(true);
    };

    return (
        <div className="role-permissions">
            <div className="permissions-header">
                <h2>Role & Permission Management</h2>
                <button className="add-button" onClick={() => setShowForm(true)}>
                    + Add Role
                </button>
            </div>

            <div className="roles-grid">
                {roles.map(role => (
                    <div key={role.id} className="role-card">
                        <div className="role-header">
                            <h3>{role.name}</h3>
                            <span className="user-count">{role.users} users</span>
                        </div>
                        <p className="role-description">{role.description}</p>
                        
                        <div className="permissions-list">
                            <h4>Permissions:</h4>
                            <div className="permission-tags">
                                {role.permissions.map(perm => (
                                    <span key={perm} className="permission-tag">
                                        {allPermissions.find(p => p.id === perm)?.name || perm}
                                    </span>
                                ))}
                            </div>
                        </div>
                        
                        <div className="role-actions">
                            <button className="edit-btn" onClick={() => editRole(role)}>
                                Edit Role
                            </button>
                            <button className="users-btn">
                                Manage Users
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {showForm && (
                <div className="modal-overlay">
                    <div className="modal large">
                        <div className="modal-header">
                            <h3>{selectedRole ? 'Edit Role' : 'Add Role'}</h3>
                            <button onClick={() => {
                                setShowForm(false);
                                setSelectedRole(null);
                                setFormData({ name: '', description: '', permissions: [] });
                            }}>×</button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Role Name</label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Description</label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                />
                            </div>
                            <div className="form-group">
                                <label>Permissions</label>
                                <div className="permissions-grid">
                                    {allPermissions.map(permission => (
                                        <div key={permission.id} className="permission-item">
                                            <label className="permission-checkbox">
                                                <input
                                                    type="checkbox"
                                                    checked={formData.permissions.includes(permission.id)}
                                                    onChange={() => togglePermission(permission.id)}
                                                />
                                                <div className="permission-info">
                                                    <span className="permission-name">{permission.name}</span>
                                                    <span className="permission-desc">{permission.description}</span>
                                                </div>
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className="form-actions">
                                <button type="button" onClick={() => {
                                    setShowForm(false);
                                    setSelectedRole(null);
                                    setFormData({ name: '', description: '', permissions: [] });
                                }}>Cancel</button>
                                <button type="submit">
                                    {selectedRole ? 'Update Role' : 'Create Role'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default RolePermissions;