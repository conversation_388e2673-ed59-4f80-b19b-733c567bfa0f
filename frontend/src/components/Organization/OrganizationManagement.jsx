import React, { useState } from 'react';
import './OrganizationManagement.css';

const OrganizationManagement = () => {
    const [organizations, setOrganizations] = useState([
        { id: 1, name: 'NUVO AI', departments: 5, employees: 150, status: 'active' },
        { id: 2, name: 'Tech Corp', departments: 3, employees: 80, status: 'active' }
    ]);
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({ name: '', description: '', domain: '' });

    const handleSubmit = (e) => {
        e.preventDefault();
        const newOrg = {
            id: Date.now(),
            name: formData.name,
            departments: 0,
            employees: 0,
            status: 'active'
        };
        setOrganizations([...organizations, newOrg]);
        setFormData({ name: '', description: '', domain: '' });
        setShowForm(false);
    };

    const toggleStatus = (id) => {
        setOrganizations(orgs => orgs.map(org => 
            org.id === id ? { ...org, status: org.status === 'active' ? 'inactive' : 'active' } : org
        ));
    };

    return (
        <div className="organization-management">
            <div className="management-header">
                <h2>Organization Management</h2>
                <button className="add-button" onClick={() => setShowForm(true)}>
                    + Add Organization
                </button>
            </div>

            <div className="organizations-grid">
                {organizations.map(org => (
                    <div key={org.id} className={`org-card ${org.status}`}>
                        <div className="org-header">
                            <h3>{org.name}</h3>
                            <span className={`status-badge ${org.status}`}>
                                {org.status}
                            </span>
                        </div>
                        <div className="org-stats">
                            <div className="stat">
                                <span className="stat-value">{org.departments}</span>
                                <span className="stat-label">Departments</span>
                            </div>
                            <div className="stat">
                                <span className="stat-value">{org.employees}</span>
                                <span className="stat-label">Employees</span>
                            </div>
                        </div>
                        <div className="org-actions">
                            <button className="edit-btn">Edit</button>
                            <button 
                                className="toggle-btn"
                                onClick={() => toggleStatus(org.id)}
                            >
                                {org.status === 'active' ? 'Deactivate' : 'Activate'}
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {showForm && (
                <div className="modal-overlay">
                    <div className="modal">
                        <div className="modal-header">
                            <h3>Add Organization</h3>
                            <button onClick={() => setShowForm(false)}>×</button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Organization Name</label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Description</label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                />
                            </div>
                            <div className="form-group">
                                <label>Domain</label>
                                <input
                                    type="text"
                                    value={formData.domain}
                                    onChange={(e) => setFormData({...formData, domain: e.target.value})}
                                    placeholder="company.com"
                                />
                            </div>
                            <div className="form-actions">
                                <button type="button" onClick={() => setShowForm(false)}>Cancel</button>
                                <button type="submit">Create Organization</button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default OrganizationManagement;