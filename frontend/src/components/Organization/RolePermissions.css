.role-permissions {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.permissions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.permissions-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.add-button {
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s;
}

.add-button:hover {
    opacity: 0.9;
}

.roles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.role-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.2s;
}

.role-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.role-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.role-header h3 {
    margin: 0;
    color: #202124;
    font-size: 18px;
}

.user-count {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.role-description {
    color: #5f6368;
    margin-bottom: 16px;
    font-size: 14px;
}

.permissions-list h4 {
    margin: 0 0 8px 0;
    color: #202124;
    font-size: 14px;
    font-weight: 500;
}

.permission-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 16px;
}

.permission-tag {
    background: #f1f3f4;
    color: #5f6368;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.role-actions {
    display: flex;
    gap: 8px;
}

.edit-btn, .users-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    color: #1a73e8;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.edit-btn:hover, .users-btn:hover {
    background: #f8f9fa;
    border-color: #1a73e8;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: #ffffff;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal.large {
    max-width: 700px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e8eaed;
}

.modal-header h3 {
    margin: 0;
    color: #202124;
}

.modal-header button {
    background: none;
    border: none;
    font-size: 24px;
    color: #5f6368;
    cursor: pointer;
    padding: 4px;
}

.form-group {
    margin-bottom: 16px;
    padding: 0 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #202124;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1a73e8;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.permissions-grid {
    display: grid;
    gap: 12px;
    margin-top: 8px;
}

.permission-item {
    border: 1px solid #e8eaed;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s;
}

.permission-item:hover {
    background: #f8f9fa;
}

.permission-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
}

.permission-checkbox input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.permission-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.permission-name {
    font-weight: 500;
    color: #202124;
    font-size: 14px;
}

.permission-desc {
    color: #5f6368;
    font-size: 13px;
}

.form-actions {
    display: flex;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #e8eaed;
}

.form-actions button {
    flex: 1;
    padding: 12px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.form-actions button[type="button"] {
    background: #ffffff;
    border: 1px solid #e8eaed;
    color: #5f6368;
}

.form-actions button[type="submit"] {
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    border: none;
    color: white;
}

.form-actions button:hover {
    opacity: 0.9;
}

@media (max-width: 768px) {
    .role-permissions {
        padding: 16px;
    }
    
    .permissions-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .roles-grid {
        grid-template-columns: 1fr;
    }
    
    .modal.large {
        max-width: 95%;
    }
    
    .role-actions {
        flex-direction: column;
    }
}