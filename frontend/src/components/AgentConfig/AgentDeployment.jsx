import React, { useState } from 'react';
import './AgentDeployment.css';

const AgentDeployment = () => {
    const [agents, setAgents] = useState([
        { 
            id: 1, 
            name: 'HR Assistant', 
            type: 'specialized', 
            status: 'active', 
            department: 'HR',
            capabilities: ['policy_lookup', 'leave_management', 'benefits_info'],
            performance: { accuracy: 0.92, speed: 0.85, uptime: 0.99 }
        },
        { 
            id: 2, 
            name: 'General Assistant', 
            type: 'general', 
            status: 'active', 
            department: 'All',
            capabilities: ['general_qa', 'routing', 'basic_info'],
            performance: { accuracy: 0.88, speed: 0.91, uptime: 0.97 }
        },
        { 
            id: 3, 
            name: 'Policy Expert', 
            type: 'specialized', 
            status: 'inactive', 
            department: 'Legal',
            capabilities: ['policy_analysis', 'compliance_check', 'legal_guidance'],
            performance: { accuracy: 0.95, speed: 0.78, uptime: 0.95 }
        }
    ]);
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        type: 'specialized',
        department: '',
        capabilities: []
    });

    const availableCapabilities = [
        'policy_lookup',
        'leave_management', 
        'benefits_info',
        'general_qa',
        'routing',
        'basic_info',
        'policy_analysis',
        'compliance_check',
        'legal_guidance',
        'performance_review',
        'training_info'
    ];

    const handleSubmit = (e) => {
        e.preventDefault();
        const newAgent = {
            id: Date.now(),
            ...formData,
            status: 'inactive',
            performance: { accuracy: 0, speed: 0, uptime: 0 }
        };
        setAgents([...agents, newAgent]);
        setFormData({ name: '', type: 'specialized', department: '', capabilities: [] });
        setShowForm(false);
    };

    const toggleStatus = (id) => {
        setAgents(prev => prev.map(agent => 
            agent.id === id ? { 
                ...agent, 
                status: agent.status === 'active' ? 'inactive' : 'active' 
            } : agent
        ));
    };

    const toggleCapability = (capability) => {
        setFormData(prev => ({
            ...prev,
            capabilities: prev.capabilities.includes(capability)
                ? prev.capabilities.filter(c => c !== capability)
                : [...prev.capabilities, capability]
        }));
    };

    return (
        <div className="agent-deployment">
            <div className="deployment-header">
                <h2>Agent Deployment Management</h2>
                <button className="deploy-button" onClick={() => setShowForm(true)}>
                    🤖 Deploy Agent
                </button>
            </div>

            <div className="agents-grid">
                {agents.map(agent => (
                    <div key={agent.id} className={`agent-card ${agent.status}`}>
                        <div className="agent-header">
                            <div className="agent-info">
                                <h3>{agent.name}</h3>
                                <span className="agent-type">{agent.type}</span>
                            </div>
                            <span className={`status-indicator ${agent.status}`}>
                                {agent.status}
                            </span>
                        </div>

                        <div className="agent-details">
                            <div className="detail-item">
                                <span className="label">Department:</span>
                                <span className="value">{agent.department}</span>
                            </div>
                            
                            <div className="capabilities">
                                <span className="label">Capabilities:</span>
                                <div className="capability-tags">
                                    {agent.capabilities.map(cap => (
                                        <span key={cap} className="capability-tag">
                                            {cap.replace('_', ' ')}
                                        </span>
                                    ))}
                                </div>
                            </div>
                        </div>

                        <div className="performance-metrics">
                            <div className="metric">
                                <span className="metric-label">Accuracy</span>
                                <div className="metric-bar">
                                    <div 
                                        className="metric-fill"
                                        style={{ width: `${agent.performance.accuracy * 100}%` }}
                                    ></div>
                                </div>
                                <span className="metric-value">
                                    {Math.round(agent.performance.accuracy * 100)}%
                                </span>
                            </div>
                            <div className="metric">
                                <span className="metric-label">Speed</span>
                                <div className="metric-bar">
                                    <div 
                                        className="metric-fill"
                                        style={{ width: `${agent.performance.speed * 100}%` }}
                                    ></div>
                                </div>
                                <span className="metric-value">
                                    {Math.round(agent.performance.speed * 100)}%
                                </span>
                            </div>
                            <div className="metric">
                                <span className="metric-label">Uptime</span>
                                <div className="metric-bar">
                                    <div 
                                        className="metric-fill"
                                        style={{ width: `${agent.performance.uptime * 100}%` }}
                                    ></div>
                                </div>
                                <span className="metric-value">
                                    {Math.round(agent.performance.uptime * 100)}%
                                </span>
                            </div>
                        </div>

                        <div className="agent-actions">
                            <button className="config-btn">Configure</button>
                            <button 
                                className={`toggle-btn ${agent.status}`}
                                onClick={() => toggleStatus(agent.id)}
                            >
                                {agent.status === 'active' ? 'Deactivate' : 'Activate'}
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {showForm && (
                <div className="modal-overlay">
                    <div className="modal large">
                        <div className="modal-header">
                            <h3>Deploy New Agent</h3>
                            <button onClick={() => setShowForm(false)}>×</button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Agent Name</label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Agent Type</label>
                                <select
                                    value={formData.type}
                                    onChange={(e) => setFormData({...formData, type: e.target.value})}
                                >
                                    <option value="specialized">Specialized</option>
                                    <option value="general">General</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Department</label>
                                <input
                                    type="text"
                                    value={formData.department}
                                    onChange={(e) => setFormData({...formData, department: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Capabilities</label>
                                <div className="capabilities-grid">
                                    {availableCapabilities.map(capability => (
                                        <label key={capability} className="capability-checkbox">
                                            <input
                                                type="checkbox"
                                                checked={formData.capabilities.includes(capability)}
                                                onChange={() => toggleCapability(capability)}
                                            />
                                            <span>{capability.replace('_', ' ')}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                            <div className="form-actions">
                                <button type="button" onClick={() => setShowForm(false)}>Cancel</button>
                                <button type="submit">Deploy Agent</button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AgentDeployment;