.agent-deployment {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.deployment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.deployment-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.deploy-button {
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.deploy-button:hover {
    opacity: 0.9;
}

.agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.agent-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.2s;
}

.agent-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.agent-card.inactive {
    opacity: 0.7;
    background: #f8f9fa;
}

.agent-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.agent-info h3 {
    margin: 0 0 4px 0;
    color: #202124;
    font-size: 18px;
}

.agent-type {
    background: #f1f3f4;
    color: #5f6368;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.status-indicator.active {
    background: #e8f5e8;
    color: #137333;
}

.status-indicator.inactive {
    background: #fce8e6;
    color: #d93025;
}

.agent-details {
    margin-bottom: 16px;
}

.detail-item {
    display: flex;
    margin-bottom: 8px;
}

.label {
    font-weight: 500;
    color: #5f6368;
    min-width: 100px;
    font-size: 14px;
}

.value {
    color: #202124;
    font-size: 14px;
}

.capabilities {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.capability-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.capability-tag {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: capitalize;
}

.performance-metrics {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.metric {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.metric:last-child {
    margin-bottom: 0;
}

.metric-label {
    font-size: 12px;
    color: #5f6368;
    min-width: 60px;
}

.metric-bar {
    flex: 1;
    height: 6px;
    background: #e8eaed;
    border-radius: 3px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

.metric-value {
    font-size: 12px;
    color: #202124;
    font-weight: 500;
    min-width: 35px;
}

.agent-actions {
    display: flex;
    gap: 8px;
}

.config-btn, .toggle-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.config-btn {
    color: #1a73e8;
}

.config-btn:hover {
    background: #f8f9fa;
    border-color: #1a73e8;
}

.toggle-btn.active {
    color: #ea4335;
}

.toggle-btn.inactive {
    color: #34a853;
}

.toggle-btn:hover {
    background: #f8f9fa;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: #ffffff;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal.large {
    max-width: 700px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e8eaed;
}

.modal-header h3 {
    margin: 0;
    color: #202124;
}

.modal-header button {
    background: none;
    border: none;
    font-size: 24px;
    color: #5f6368;
    cursor: pointer;
    padding: 4px;
}

.form-group {
    margin-bottom: 16px;
    padding: 0 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #202124;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #1a73e8;
}

.capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.capability-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.capability-checkbox:hover {
    background: #f8f9fa;
}

.capability-checkbox input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
}

.capability-checkbox span {
    font-size: 14px;
    color: #202124;
    text-transform: capitalize;
}

.form-actions {
    display: flex;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #e8eaed;
}

.form-actions button {
    flex: 1;
    padding: 12px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.form-actions button[type="button"] {
    background: #ffffff;
    border: 1px solid #e8eaed;
    color: #5f6368;
}

.form-actions button[type="submit"] {
    background: linear-gradient(16deg, #4b90ff, #1a73e8);
    border: none;
    color: white;
}

.form-actions button:hover {
    opacity: 0.9;
}

@media (max-width: 768px) {
    .agent-deployment {
        padding: 16px;
    }
    
    .deployment-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .agents-grid {
        grid-template-columns: 1fr;
    }
    
    .modal.large {
        max-width: 95%;
    }
    
    .capabilities-grid {
        grid-template-columns: 1fr;
    }
    
    .agent-actions {
        flex-direction: column;
    }
}