.mobile-optimized {
    width: 100%;
    height: 100%;
    position: relative;
}

.connection-status {
    position: fixed;
    top: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1001;
    transition: all 0.3s ease;
}

.connection-status.online {
    background-color: #e8f5e8;
    color: #137333;
}

.connection-status.offline {
    background-color: #fce8e6;
    color: #d93025;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.connection-status.online .status-indicator {
    background-color: #34a853;
}

.connection-status.offline .status-indicator {
    background-color: #ea4335;
}

.pending-sync {
    background-color: #ffa726;
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
}

.mobile-content {
    width: 100%;
    height: 100%;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #5f6368;
    font-style: italic;
}

/* Mobile-first responsive design */
.mobile-optimized.mobile {
    font-size: 14px;
}

.mobile-optimized.mobile .connection-status {
    top: 5px;
    right: 5px;
    padding: 4px 8px;
    font-size: 11px;
}

.mobile-optimized.tablet {
    font-size: 15px;
}

.mobile-optimized.desktop {
    font-size: 16px;
}

/* Touch-friendly interactions */
.mobile-optimized[data-touch="true"] button,
.mobile-optimized[data-touch="true"] .clickable {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
}

.mobile-optimized[data-touch="true"] input,
.mobile-optimized[data-touch="true"] textarea {
    min-height: 44px;
    padding: 12px;
    font-size: 16px;
    border-radius: 8px;
}

.mobile-optimized[data-touch="false"] button,
.mobile-optimized[data-touch="false"] .clickable {
    min-height: 32px;
    padding: 8px;
}

.mobile-optimized[data-touch="false"] input,
.mobile-optimized[data-touch="false"] textarea {
    padding: 8px;
    font-size: 14px;
}

/* Adaptive layouts */
.mobile-optimized.mobile .grid {
    grid-template-columns: 1fr;
    gap: 8px;
}

.mobile-optimized.tablet .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.mobile-optimized.desktop .grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

/* Mobile-specific styles */
.mobile-optimized.mobile button,
.mobile-optimized.mobile .clickable {
    font-size: 16px;
}

.mobile-optimized.mobile input,
.mobile-optimized.mobile textarea {
    font-size: 16px;
}

/* Mobile navigation optimizations */
.mobile-optimized.mobile .modal {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    margin: 0;
}

.mobile-optimized.mobile .modal-header {
    padding: 12px 16px;
    position: sticky;
    top: 0;
    background-color: #ffffff;
    z-index: 10;
}

.mobile-optimized.mobile .modal-content {
    padding: 16px;
    padding-bottom: 80px; /* Space for mobile keyboards */
}

/* Performance optimizations */
.mobile-optimized * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

.mobile-optimized input,
.mobile-optimized textarea,
.mobile-optimized [contenteditable] {
    -webkit-user-select: text;
    user-select: text;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .mobile-optimized * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .status-indicator {
        animation: none;
    }
}

/* High contrast support */
@media (prefers-contrast: high) {
    .connection-status.online {
        background-color: #000000;
        color: #ffffff;
        border: 2px solid #ffffff;
    }
    
    .connection-status.offline {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #000000;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .connection-status.online {
        background-color: #1e3a1e;
        color: #81c784;
    }
    
    .connection-status.offline {
        background-color: #3a1e1e;
        color: #ef5350;
    }
    
    .loading-placeholder {
        color: #9aa0a6;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Viewport optimizations */
@viewport {
    width: device-width;
    initial-scale: 1;
    maximum-scale: 5;
    user-scalable: yes;
}

/* Safe area support for notched devices */
.mobile-optimized {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
}