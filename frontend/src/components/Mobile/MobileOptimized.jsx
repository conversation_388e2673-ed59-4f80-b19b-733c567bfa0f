import React, { useState, useEffect, lazy, Suspense } from 'react';
import './MobileOptimized.css';
import { useResponsive } from '../../utils/mobile/responsive';
import offlineQueue from '../../services/offlineQueue';
import { MobileProvider } from '../../context/MobileContext';

// Lazy load components for code splitting
const ReasoningVisualization = lazy(() => import('../Reasoning/ReasoningVisualization'));
const AgentInteractionDisplay = lazy(() => import('../Agents/AgentInteractionDisplay'));

const MobileOptimized = ({ children }) => {
    const { isMobile, isTablet, isTouch, screenSize } = useResponsive();
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [pendingSync, setPendingSync] = useState(0);

    useEffect(() => {
        const handleOnline = () => {
            setIsOnline(true);
            syncOfflineMessages();
        };
        
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        // Check for pending messages
        checkPendingMessages();

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    const syncOfflineMessages = async () => {
        try {
            const results = await offlineQueue.syncMessages();
            const successful = results.filter(r => r.success).length;
            setPendingSync(prev => Math.max(0, prev - successful));
        } catch (error) {
            console.log('Sync failed:', error);
        }
    };

    const checkPendingMessages = async () => {
        try {
            const unsynced = await offlineQueue.getUnsynced();
            setPendingSync(unsynced.length);
        } catch (error) {
            console.log('Failed to check pending messages:', error);
        }
    };

    const handleMessageSend = async (message) => {
        const result = await offlineQueue.handleMessage(message);
        if (result.queued) {
            setPendingSync(prev => prev + 1);
        }
        return result;
    };

    return (
        <div className={`mobile-optimized ${screenSize}`} data-touch={isTouch}>
            {/* Connection status */}
            <div className={`connection-status ${isOnline ? 'online' : 'offline'}`}>
                <span className="status-indicator"></span>
                <span className="status-text">
                    {isOnline ? 'Online' : 'Offline'}
                </span>
                {pendingSync > 0 && (
                    <span className="pending-sync">
                        {pendingSync} pending
                    </span>
                )}
            </div>

            {/* Mobile-optimized content */}
            <div className="mobile-content">
                <Suspense fallback={<div className="loading-placeholder">Loading...</div>}>
                    <MobileProvider value={{
                        isMobile,
                        isTablet,
                        isTouch,
                        screenSize,
                        onMessageSend: handleMessageSend
                    }}>
                        {children}
                    </MobileProvider>
                </Suspense>
            </div>
        </div>
    );
};

export default MobileOptimized;