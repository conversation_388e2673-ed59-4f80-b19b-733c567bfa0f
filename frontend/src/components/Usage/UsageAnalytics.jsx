import React, { useState } from 'react';
import './UsageAnalytics.css';

const UsageAnalytics = () => {
    const [timeRange, setTimeRange] = useState('7d');
    const [selectedMetric, setSelectedMetric] = useState('volume');

    const metrics = {
        volume: {
            current: 1247,
            previous: 1089,
            change: 14.5,
            trend: 'up',
            data: [120, 135, 142, 158, 165, 172, 180]
        },
        quality: {
            current: 4.2,
            previous: 3.9,
            change: 7.7,
            trend: 'up',
            data: [3.8, 3.9, 4.0, 4.1, 4.1, 4.2, 4.2]
        },
        satisfaction: {
            current: 87,
            previous: 82,
            change: 6.1,
            trend: 'up',
            data: [82, 83, 84, 85, 86, 87, 87]
        }
    };

    const departmentData = [
        { name: 'HR', queries: 456, quality: 4.3, satisfaction: 89 },
        { name: 'Engineering', queries: 342, quality: 4.1, satisfaction: 85 },
        { name: 'Marketing', queries: 289, quality: 4.0, satisfaction: 88 },
        { name: 'Finance', queries: 160, quality: 4.4, satisfaction: 91 }
    ];

    const topQueries = [
        { query: 'Leave policy information', count: 89, satisfaction: 4.5 },
        { query: 'Benefits enrollment', count: 67, satisfaction: 4.2 },
        { query: 'Performance review process', count: 54, satisfaction: 4.1 },
        { query: 'Remote work policy', count: 43, satisfaction: 4.3 },
        { query: 'Training opportunities', count: 38, satisfaction: 4.0 }
    ];

    const getChangeColor = (change) => change > 0 ? 'positive' : 'negative';
    const getChangeIcon = (trend) => trend === 'up' ? '↗️' : '↘️';

    return (
        <div className="usage-analytics">
            <div className="analytics-header">
                <h2>Usage Analytics Dashboard</h2>
                <div className="time-controls">
                    {['24h', '7d', '30d', '90d'].map(range => (
                        <button
                            key={range}
                            className={`time-btn ${timeRange === range ? 'active' : ''}`}
                            onClick={() => setTimeRange(range)}
                        >
                            {range}
                        </button>
                    ))}
                </div>
            </div>

            <div className="metrics-overview">
                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Query Volume</h3>
                        <span className="metric-trend">{getChangeIcon(metrics.volume.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{metrics.volume.current.toLocaleString()}</span>
                        <span className="unit">queries</span>
                    </div>
                    <div className="metric-change">
                        <span className={`change ${getChangeColor(metrics.volume.change)}`}>
                            {metrics.volume.change > 0 ? '+' : ''}{metrics.volume.change}%
                        </span>
                        <span className="period">vs last {timeRange}</span>
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>Response Quality</h3>
                        <span className="metric-trend">{getChangeIcon(metrics.quality.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{metrics.quality.current}</span>
                        <span className="unit">/5.0</span>
                    </div>
                    <div className="metric-change">
                        <span className={`change ${getChangeColor(metrics.quality.change)}`}>
                            {metrics.quality.change > 0 ? '+' : ''}{metrics.quality.change}%
                        </span>
                        <span className="period">vs last {timeRange}</span>
                    </div>
                </div>

                <div className="metric-card">
                    <div className="metric-header">
                        <h3>User Satisfaction</h3>
                        <span className="metric-trend">{getChangeIcon(metrics.satisfaction.trend)}</span>
                    </div>
                    <div className="metric-value">
                        <span className="current">{metrics.satisfaction.current}</span>
                        <span className="unit">%</span>
                    </div>
                    <div className="metric-change">
                        <span className={`change ${getChangeColor(metrics.satisfaction.change)}`}>
                            {metrics.satisfaction.change > 0 ? '+' : ''}{metrics.satisfaction.change}%
                        </span>
                        <span className="period">vs last {timeRange}</span>
                    </div>
                </div>
            </div>

            <div className="analytics-grid">
                <div className="chart-section">
                    <h3>Department Breakdown</h3>
                    <div className="department-chart">
                        {departmentData.map(dept => (
                            <div key={dept.name} className="dept-row">
                                <div className="dept-info">
                                    <span className="dept-name">{dept.name}</span>
                                    <span className="dept-queries">{dept.queries} queries</span>
                                </div>
                                <div className="dept-metrics">
                                    <div className="dept-metric">
                                        <span className="metric-label">Quality</span>
                                        <span className="metric-score">{dept.quality}</span>
                                    </div>
                                    <div className="dept-metric">
                                        <span className="metric-label">Satisfaction</span>
                                        <span className="metric-score">{dept.satisfaction}%</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="queries-section">
                    <h3>Top Queries</h3>
                    <div className="queries-list">
                        {topQueries.map((item, index) => (
                            <div key={index} className="query-item">
                                <div className="query-rank">{index + 1}</div>
                                <div className="query-content">
                                    <div className="query-text">{item.query}</div>
                                    <div className="query-stats">
                                        <span className="query-count">{item.count} queries</span>
                                        <span className="query-rating">★ {item.satisfaction}</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="satisfaction-tracking">
                <h3>User Satisfaction Tracking</h3>
                <div className="satisfaction-grid">
                    <div className="satisfaction-breakdown">
                        <div className="rating-bar">
                            <span className="rating-label">5 Stars</span>
                            <div className="rating-progress">
                                <div className="rating-fill" style={{ width: '45%' }}></div>
                            </div>
                            <span className="rating-percent">45%</span>
                        </div>
                        <div className="rating-bar">
                            <span className="rating-label">4 Stars</span>
                            <div className="rating-progress">
                                <div className="rating-fill" style={{ width: '32%' }}></div>
                            </div>
                            <span className="rating-percent">32%</span>
                        </div>
                        <div className="rating-bar">
                            <span className="rating-label">3 Stars</span>
                            <div className="rating-progress">
                                <div className="rating-fill" style={{ width: '15%' }}></div>
                            </div>
                            <span className="rating-percent">15%</span>
                        </div>
                        <div className="rating-bar">
                            <span className="rating-label">2 Stars</span>
                            <div className="rating-progress">
                                <div className="rating-fill" style={{ width: '6%' }}></div>
                            </div>
                            <span className="rating-percent">6%</span>
                        </div>
                        <div className="rating-bar">
                            <span className="rating-label">1 Star</span>
                            <div className="rating-progress">
                                <div className="rating-fill" style={{ width: '2%' }}></div>
                            </div>
                            <span className="rating-percent">2%</span>
                        </div>
                    </div>
                    
                    <div className="satisfaction-summary">
                        <div className="summary-stat">
                            <span className="stat-value">4.2</span>
                            <span className="stat-label">Average Rating</span>
                        </div>
                        <div className="summary-stat">
                            <span className="stat-value">892</span>
                            <span className="stat-label">Total Ratings</span>
                        </div>
                        <div className="summary-stat">
                            <span className="stat-value">77%</span>
                            <span className="stat-label">4+ Stars</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UsageAnalytics;