.usage-analytics {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.analytics-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.time-controls {
    display: flex;
    gap: 4px;
}

.time-btn {
    padding: 6px 12px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    background: #ffffff;
    color: #5f6368;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.time-btn:hover {
    background: #f8f9fa;
}

.time-btn.active {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.metrics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.metric-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.2s;
}

.metric-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.metric-header h3 {
    margin: 0;
    color: #202124;
    font-size: 16px;
    font-weight: 500;
}

.metric-trend {
    font-size: 18px;
}

.metric-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin-bottom: 8px;
}

.current {
    font-size: 32px;
    font-weight: 600;
    color: #1a73e8;
}

.unit {
    font-size: 14px;
    color: #5f6368;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: 8px;
}

.change {
    font-size: 14px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
}

.change.positive {
    background: #e8f5e8;
    color: #137333;
}

.change.negative {
    background: #fce8e6;
    color: #d93025;
}

.period {
    font-size: 12px;
    color: #5f6368;
}

.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.chart-section,
.queries-section {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.chart-section h3,
.queries-section h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.department-chart {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.dept-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dept-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.dept-name {
    font-weight: 500;
    color: #202124;
    font-size: 14px;
}

.dept-queries {
    font-size: 12px;
    color: #5f6368;
}

.dept-metrics {
    display: flex;
    gap: 16px;
}

.dept-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.metric-label {
    font-size: 11px;
    color: #5f6368;
}

.metric-score {
    font-size: 14px;
    font-weight: 500;
    color: #1a73e8;
}

.queries-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.query-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.query-rank {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #1a73e8;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    flex-shrink: 0;
}

.query-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.query-text {
    font-size: 14px;
    color: #202124;
    font-weight: 500;
}

.query-stats {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #5f6368;
}

.query-rating {
    color: #ff9800;
}

.satisfaction-tracking {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.satisfaction-tracking h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.satisfaction-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

.satisfaction-breakdown {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 12px;
}

.rating-label {
    min-width: 60px;
    font-size: 14px;
    color: #202124;
}

.rating-progress {
    flex: 1;
    height: 8px;
    background: #e8eaed;
    border-radius: 4px;
    overflow: hidden;
}

.rating-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff9800, #ffa726);
    transition: width 0.3s ease;
}

.rating-percent {
    min-width: 35px;
    font-size: 14px;
    color: #202124;
    font-weight: 500;
    text-align: right;
}

.satisfaction-summary {
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
}

.summary-stat {
    text-align: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #1a73e8;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #5f6368;
}

@media (max-width: 768px) {
    .usage-analytics {
        padding: 16px;
    }
    
    .analytics-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .metrics-overview {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .satisfaction-grid {
        grid-template-columns: 1fr;
    }
    
    .dept-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .dept-metrics {
        align-self: stretch;
        justify-content: space-around;
    }
}