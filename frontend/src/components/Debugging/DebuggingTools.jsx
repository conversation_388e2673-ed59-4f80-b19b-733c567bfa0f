import React, { useState } from 'react';
import './DebuggingTools.css';

const DebuggingTools = () => {
    const [activeTab, setActiveTab] = useState('trace');
    const [selectedMessage, setSelectedMessage] = useState(null);

    const reasoningTrace = [
        { id: 1, step: 'Query Analysis', status: 'completed', time: '12ms', details: 'Parsed user query: "What is the leave policy?"' },
        { id: 2, step: 'Knowledge Search', status: 'completed', time: '45ms', details: 'Found 3 relevant documents with 0.92 similarity' },
        { id: 3, step: 'Context Building', status: 'completed', time: '23ms', details: 'Built context from HR policies and user profile' },
        { id: 4, step: 'Response Generation', status: 'completed', time: '67ms', details: 'Generated response using GPT-4 with context' },
        { id: 5, step: 'Quality Check', status: 'completed', time: '15ms', details: 'Validated response quality: 94% confidence' }
    ];

    const messages = [
        { id: 1, user: 'john.doe', message: 'What is the leave policy?', agent: 'HR Agent', timestamp: '10:32:15', status: 'success' },
        { id: 2, user: 'jane.smith', message: 'How do I apply for benefits?', agent: 'General Agent', timestamp: '10:31:45', status: 'success' },
        { id: 3, user: 'bob.wilson', message: 'Complex policy question', agent: 'Policy Agent', timestamp: '10:30:22', status: 'error' }
    ];

    const agentState = {
        currentTask: 'Processing leave request',
        memory: {
            shortTerm: ['User asked about leave policy', 'Found relevant documents', 'Generated response'],
            longTerm: ['User john.doe works in Engineering', 'Previous queries about benefits', 'Prefers detailed explanations']
        },
        context: {
            user: 'john.doe',
            department: 'Engineering',
            role: 'Senior Developer',
            previousQueries: 5
        },
        capabilities: ['policy_lookup', 'leave_management', 'benefits_info'],
        performance: {
            accuracy: 0.92,
            speed: 0.85,
            confidence: 0.94
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'green';
            case 'running': return 'blue';
            case 'error': return 'red';
            case 'success': return 'green';
            default: return 'gray';
        }
    };

    const renderReasoningTrace = () => (
        <div className="reasoning-trace">
            <h3>Reasoning Trace Explorer</h3>
            <div className="trace-timeline">
                {reasoningTrace.map((step, index) => (
                    <div key={step.id} className={`trace-step ${step.status}`}>
                        <div className="step-marker">
                            <span className="step-number">{index + 1}</span>
                        </div>
                        <div className="step-content">
                            <div className="step-header">
                                <span className="step-name">{step.step}</span>
                                <span className="step-time">{step.time}</span>
                            </div>
                            <div className="step-details">{step.details}</div>
                            <div className={`step-status ${getStatusColor(step.status)}`}>
                                {step.status}
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );

    const renderMessageInspector = () => (
        <div className="message-inspector">
            <h3>Message Inspector</h3>
            <div className="message-list">
                {messages.map(msg => (
                    <div 
                        key={msg.id} 
                        className={`message-item ${selectedMessage === msg.id ? 'selected' : ''}`}
                        onClick={() => setSelectedMessage(msg.id)}
                    >
                        <div className="message-header">
                            <span className="message-user">{msg.user}</span>
                            <span className="message-time">{msg.timestamp}</span>
                        </div>
                        <div className="message-content">{msg.message}</div>
                        <div className="message-meta">
                            <span className="message-agent">{msg.agent}</span>
                            <span className={`message-status ${getStatusColor(msg.status)}`}>
                                {msg.status}
                            </span>
                        </div>
                    </div>
                ))}
            </div>
            
            {selectedMessage && (
                <div className="message-details">
                    <h4>Message Details</h4>
                    <div className="detail-grid">
                        <div className="detail-item">
                            <span className="detail-label">Request ID:</span>
                            <span className="detail-value">req_{selectedMessage}_2024</span>
                        </div>
                        <div className="detail-item">
                            <span className="detail-label">Processing Time:</span>
                            <span className="detail-value">147ms</span>
                        </div>
                        <div className="detail-item">
                            <span className="detail-label">Tokens Used:</span>
                            <span className="detail-value">234 tokens</span>
                        </div>
                        <div className="detail-item">
                            <span className="detail-label">Confidence:</span>
                            <span className="detail-value">94%</span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );

    const renderStateAnalyzer = () => (
        <div className="state-analyzer">
            <h3>Agent State Analyzer</h3>
            
            <div className="state-section">
                <h4>Current Task</h4>
                <div className="current-task">{agentState.currentTask}</div>
            </div>

            <div className="state-section">
                <h4>Memory</h4>
                <div className="memory-section">
                    <div className="memory-type">
                        <h5>Short-term Memory</h5>
                        <ul>
                            {agentState.memory.shortTerm.map((item, index) => (
                                <li key={index}>{item}</li>
                            ))}
                        </ul>
                    </div>
                    <div className="memory-type">
                        <h5>Long-term Memory</h5>
                        <ul>
                            {agentState.memory.longTerm.map((item, index) => (
                                <li key={index}>{item}</li>
                            ))}
                        </ul>
                    </div>
                </div>
            </div>

            <div className="state-section">
                <h4>Context</h4>
                <div className="context-grid">
                    {Object.entries(agentState.context).map(([key, value]) => (
                        <div key={key} className="context-item">
                            <span className="context-key">{key}:</span>
                            <span className="context-value">{value}</span>
                        </div>
                    ))}
                </div>
            </div>

            <div className="state-section">
                <h4>Capabilities</h4>
                <div className="capabilities">
                    {agentState.capabilities.map(cap => (
                        <span key={cap} className="capability-tag">
                            {cap.replace('_', ' ')}
                        </span>
                    ))}
                </div>
            </div>

            <div className="state-section">
                <h4>Performance Metrics</h4>
                <div className="performance-grid">
                    {Object.entries(agentState.performance).map(([key, value]) => (
                        <div key={key} className="performance-item">
                            <span className="performance-label">{key}:</span>
                            <div className="performance-bar">
                                <div 
                                    className="performance-fill"
                                    style={{ width: `${value * 100}%` }}
                                ></div>
                            </div>
                            <span className="performance-value">{Math.round(value * 100)}%</span>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );

    return (
        <div className="debugging-tools">
            <div className="debugging-header">
                <h2>Agent Debugging Tools</h2>
                <div className="debug-tabs">
                    <button 
                        className={`debug-tab ${activeTab === 'trace' ? 'active' : ''}`}
                        onClick={() => setActiveTab('trace')}
                    >
                        Reasoning Trace
                    </button>
                    <button 
                        className={`debug-tab ${activeTab === 'inspector' ? 'active' : ''}`}
                        onClick={() => setActiveTab('inspector')}
                    >
                        Message Inspector
                    </button>
                    <button 
                        className={`debug-tab ${activeTab === 'state' ? 'active' : ''}`}
                        onClick={() => setActiveTab('state')}
                    >
                        State Analyzer
                    </button>
                </div>
            </div>

            <div className="debug-content">
                {activeTab === 'trace' && renderReasoningTrace()}
                {activeTab === 'inspector' && renderMessageInspector()}
                {activeTab === 'state' && renderStateAnalyzer()}
            </div>
        </div>
    );
};

export default DebuggingTools;