.debugging-tools {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.debugging-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.debugging-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.debug-tabs {
    display: flex;
    gap: 4px;
}

.debug-tab {
    padding: 8px 16px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    background: #ffffff;
    color: #5f6368;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.debug-tab:hover {
    background: #f8f9fa;
}

.debug-tab.active {
    background: #1a73e8;
    color: white;
    border-color: #1a73e8;
}

.debug-content {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 20px;
}

.reasoning-trace h3,
.message-inspector h3,
.state-analyzer h3 {
    margin: 0 0 16px 0;
    color: #202124;
    font-size: 18px;
}

.trace-timeline {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.trace-step {
    display: flex;
    gap: 16px;
    padding: 16px;
    border: 1px solid #f1f3f4;
    border-radius: 8px;
    transition: all 0.2s;
}

.trace-step:hover {
    background: #f8f9fa;
    border-color: #e8eaed;
}

.trace-step.completed {
    border-left: 4px solid #34a853;
}

.trace-step.running {
    border-left: 4px solid #1a73e8;
}

.trace-step.error {
    border-left: 4px solid #ea4335;
}

.step-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1a73e8;
    color: white;
    font-weight: 500;
    font-size: 14px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step-name {
    font-weight: 500;
    color: #202124;
    font-size: 16px;
}

.step-time {
    font-size: 12px;
    color: #5f6368;
    background: #f1f3f4;
    padding: 2px 6px;
    border-radius: 8px;
}

.step-details {
    color: #5f6368;
    font-size: 14px;
    line-height: 1.4;
}

.step-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
    width: fit-content;
}

.step-status.green {
    background: #e8f5e8;
    color: #137333;
}

.step-status.blue {
    background: #e8f0fe;
    color: #1a73e8;
}

.step-status.red {
    background: #fce8e6;
    color: #d93025;
}

.message-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.message-item {
    padding: 16px;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.message-item:hover {
    background: #f8f9fa;
    border-color: #dadce0;
}

.message-item.selected {
    background: #e8f0fe;
    border-color: #1a73e8;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.message-user {
    font-weight: 500;
    color: #1a73e8;
    font-size: 14px;
}

.message-time {
    font-size: 12px;
    color: #5f6368;
}

.message-content {
    color: #202124;
    font-size: 14px;
    margin-bottom: 8px;
}

.message-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-agent {
    font-size: 12px;
    color: #5f6368;
}

.message-status {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 500;
    text-transform: capitalize;
}

.message-details {
    border-top: 1px solid #e8eaed;
    padding-top: 16px;
}

.message-details h4 {
    margin: 0 0 12px 0;
    color: #202124;
    font-size: 16px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.detail-label {
    font-weight: 500;
    color: #5f6368;
    font-size: 14px;
}

.detail-value {
    color: #202124;
    font-size: 14px;
}

.state-section {
    margin-bottom: 24px;
}

.state-section h4 {
    margin: 0 0 12px 0;
    color: #202124;
    font-size: 16px;
    font-weight: 500;
}

.current-task {
    padding: 12px;
    background: #e8f0fe;
    border-radius: 6px;
    color: #1a73e8;
    font-weight: 500;
}

.memory-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.memory-type h5 {
    margin: 0 0 8px 0;
    color: #202124;
    font-size: 14px;
}

.memory-type ul {
    margin: 0;
    padding: 0 0 0 16px;
    list-style: disc;
}

.memory-type li {
    color: #5f6368;
    font-size: 13px;
    margin-bottom: 4px;
}

.context-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.context-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.context-key {
    font-weight: 500;
    color: #5f6368;
    text-transform: capitalize;
}

.context-value {
    color: #202124;
}

.capabilities {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.capability-tag {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.performance-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.performance-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.performance-label {
    min-width: 80px;
    font-weight: 500;
    color: #5f6368;
    text-transform: capitalize;
}

.performance-bar {
    flex: 1;
    height: 6px;
    background: #e8eaed;
    border-radius: 3px;
    overflow: hidden;
}

.performance-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a73e8, #4b90ff);
    transition: width 0.3s ease;
}

.performance-value {
    min-width: 40px;
    font-weight: 500;
    color: #202124;
    text-align: right;
}

@media (max-width: 768px) {
    .debugging-tools {
        padding: 16px;
    }
    
    .debugging-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .debug-tabs {
        justify-content: space-between;
    }
    
    .debug-tab {
        flex: 1;
        text-align: center;
        padding: 6px 8px;
        font-size: 12px;
    }
    
    .trace-step {
        flex-direction: column;
        gap: 12px;
    }
    
    .step-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .detail-grid,
    .context-grid {
        grid-template-columns: 1fr;
    }
    
    .memory-section {
        grid-template-columns: 1fr;
    }
    
    .performance-item {
        flex-direction: column;
        align-items: stretch;
        gap: 6px;
    }
    
    .performance-label {
        min-width: auto;
    }
    
    .performance-value {
        text-align: left;
    }
}