import React, { useState } from 'react';
import './AnalyticsDashboard.css';
import UsageAnalytics from '../Usage/UsageAnalytics';
import EffectivenessAnalytics from '../Effectiveness/EffectivenessAnalytics';
import ImprovementAnalytics from '../Improvement/ImprovementAnalytics';

const AnalyticsDashboard = ({ isVisible, onClose }) => {
    const [activeTab, setActiveTab] = useState('usage');

    if (!isVisible) return null;

    const tabs = [
        { id: 'usage', label: 'Usage Analytics', icon: '📈' },
        { id: 'effectiveness', label: 'Effectiveness', icon: '🎯' },
        { id: 'improvement', label: 'Improvement', icon: '📊' }
    ];

    const renderContent = () => {
        switch (activeTab) {
            case 'usage':
                return <UsageAnalytics />;
            case 'effectiveness':
                return <EffectivenessAnalytics />;
            case 'improvement':
                return <ImprovementAnalytics />;
            default:
                return <UsageAnalytics />;
        }
    };

    return (
        <div className="analytics-dashboard-overlay">
            <div className="analytics-dashboard">
                <div className="dashboard-header">
                    <h2>Advanced Analytics Dashboard</h2>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>

                <div className="dashboard-content">
                    <div className="dashboard-sidebar">
                        <nav className="dashboard-nav">
                            {tabs.map(tab => (
                                <button
                                    key={tab.id}
                                    className={`nav-item ${activeTab === tab.id ? 'active' : ''}`}
                                    onClick={() => setActiveTab(tab.id)}
                                >
                                    <span className="nav-icon">{tab.icon}</span>
                                    <span className="nav-label">{tab.label}</span>
                                </button>
                            ))}
                        </nav>
                        
                        <div className="dashboard-summary">
                            <div className="summary-item">
                                <span className="summary-value">1,247</span>
                                <span className="summary-label">Total Queries</span>
                            </div>
                            <div className="summary-item">
                                <span className="summary-value">4.2</span>
                                <span className="summary-label">Avg Quality</span>
                            </div>
                            <div className="summary-item">
                                <span className="summary-value">89%</span>
                                <span className="summary-label">Success Rate</span>
                            </div>
                            <div className="summary-item">
                                <span className="summary-value">87%</span>
                                <span className="summary-label">Satisfaction</span>
                            </div>
                        </div>
                    </div>

                    <div className="dashboard-main">
                        {renderContent()}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AnalyticsDashboard;