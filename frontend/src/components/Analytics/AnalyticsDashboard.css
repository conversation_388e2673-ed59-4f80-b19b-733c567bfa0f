.analytics-dashboard-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.analytics-dashboard {
    background: #ffffff;
    border-radius: 12px;
    width: 95%;
    max-width: 1400px;
    height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e8eaed;
    background: #f8f9fa;
}

.dashboard-header h2 {
    margin: 0;
    color: #202124;
    font-size: 24px;
}

.close-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: #f1f3f4;
    color: #5f6368;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.close-button:hover {
    background: #e8eaed;
    color: #202124;
}

.dashboard-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.dashboard-sidebar {
    width: 250px;
    background: #f8f9fa;
    border-right: 1px solid #e8eaed;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
}

.dashboard-nav {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 24px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
    color: #5f6368;
    font-size: 14px;
}

.nav-item:hover {
    background: #e8eaed;
    color: #202124;
}

.nav-item.active {
    background: #e8f0fe;
    color: #1a73e8;
    border-right: 3px solid #1a73e8;
}

.nav-icon {
    font-size: 18px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-label {
    font-weight: 500;
}

.dashboard-summary {
    padding: 0 20px;
    border-top: 1px solid #e8eaed;
    padding-top: 20px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    margin-bottom: 12px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e8eaed;
}

.summary-value {
    font-size: 20px;
    font-weight: 600;
    color: #1a73e8;
    margin-bottom: 4px;
}

.summary-label {
    font-size: 11px;
    color: #5f6368;
    text-align: center;
    text-transform: uppercase;
}

.dashboard-main {
    flex: 1;
    overflow-y: auto;
    background: #ffffff;
}

.dashboard-main > div {
    margin: 0;
    padding: 0;
}

/* Override component styles for analytics dashboard */
.dashboard-main .usage-analytics,
.dashboard-main .effectiveness-analytics,
.dashboard-main .improvement-analytics {
    max-width: none;
    height: 100%;
    padding: 24px;
}

.dashboard-main .analytics-header,
.dashboard-main .effectiveness-header,
.dashboard-main .improvement-header {
    margin-bottom: 20px;
}

/* Mobile responsiveness */
@media (max-width: 1024px) {
    .analytics-dashboard {
        width: 98%;
        height: 95vh;
    }
    
    .dashboard-sidebar {
        width: 200px;
    }
    
    .nav-item {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .nav-icon {
        font-size: 16px;
        width: 20px;
    }
}

@media (max-width: 768px) {
    .analytics-dashboard-overlay {
        padding: 10px;
    }
    
    .analytics-dashboard {
        width: 100%;
        height: 100vh;
        border-radius: 0;
    }
    
    .dashboard-content {
        flex-direction: column;
    }
    
    .dashboard-sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #e8eaed;
        padding: 12px 0;
    }
    
    .dashboard-nav {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 12px;
        gap: 8px;
        margin-bottom: 16px;
    }
    
    .nav-item {
        flex-shrink: 0;
        padding: 8px 12px;
        border-radius: 6px;
        border-right: none;
        white-space: nowrap;
    }
    
    .nav-item.active {
        background: #1a73e8;
        color: white;
        border-right: none;
    }
    
    .nav-label {
        display: none;
    }
    
    .dashboard-summary {
        display: flex;
        gap: 8px;
        padding: 0 12px;
        border-top: none;
        padding-top: 0;
    }
    
    .summary-item {
        flex: 1;
        margin-bottom: 0;
        padding: 8px;
    }
    
    .summary-value {
        font-size: 16px;
    }
    
    .summary-label {
        font-size: 9px;
    }
    
    .dashboard-main {
        flex: 1;
    }
    
    .dashboard-main .usage-analytics,
    .dashboard-main .effectiveness-analytics,
    .dashboard-main .improvement-analytics {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 16px;
    }
    
    .dashboard-header h2 {
        font-size: 20px;
    }
    
    .nav-item {
        padding: 6px 8px;
    }
    
    .nav-icon {
        font-size: 14px;
        width: 18px;
    }
    
    .summary-value {
        font-size: 14px;
    }
    
    .summary-label {
        font-size: 8px;
    }
}