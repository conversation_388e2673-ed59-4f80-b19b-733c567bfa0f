import React, { useState, useEffect } from 'react';
import Sidebar from "./components/Sidebar/Sidebar.jsx";
import Main from "./components/Main/Main.jsx";
import MobileOptimized from "./components/Mobile/MobileOptimized.jsx";
import AccessibilityPanel from "./components/Accessibility/AccessibilityPanel.jsx";
import AdminInterface from "./components/Admin/AdminInterface.jsx";
import ChatInterface from './components/Chat/ChatInterface.jsx';
import LoginForm from './components/Auth/LoginForm.jsx';

const App = () => {
    const [showAccessibility, setShowAccessibility] = useState(false);
    const [showAdmin, setShowAdmin] = useState(false);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [showChat, setShowChat] = useState(false);

    useEffect(() => {
        const token = localStorage.getItem('auth_token');
        setIsAuthenticated(!!token);
    }, []);

    const handleLoginSuccess = () => {
        setIsAuthenticated(true);
    };

    const handleLogout = () => {
        localStorage.removeItem('auth_token');
        setIsAuthenticated(false);
        setShowChat(false);
    };

    if (!isAuthenticated) {
        return (
            <div className="login-container">
                <LoginForm onLoginSuccess={handleLoginSuccess} />
            </div>
        );
    }

    return (
        <MobileOptimized>
            <div className="app-container">
                <Sidebar 
                    onAccessibilityClick={() => setShowAccessibility(true)}
                    onAdminClick={() => setShowAdmin(true)}
                    onChatClick={() => setShowChat(true)}
                    onLogout={handleLogout}
                />
                {showChat ? <ChatInterface /> : <Main />}
                
                <AccessibilityPanel 
                    isVisible={showAccessibility}
                    onClose={() => setShowAccessibility(false)}
                />
                
                <AdminInterface 
                    isVisible={showAdmin}
                    onClose={() => setShowAdmin(false)}
                />
            </div>
        </MobileOptimized>
    )
}

export default App;