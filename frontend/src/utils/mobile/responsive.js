export const breakpoints = {
    mobile: '(max-width: 767px)',
    tablet: '(min-width: 768px) and (max-width: 1023px)',
    desktop: '(min-width: 1024px)',
    touch: '(pointer: coarse)',
    hover: '(hover: hover)'
};

export const useResponsive = () => {
    const isMobile = window.matchMedia(breakpoints.mobile).matches;
    const isTablet = window.matchMedia(breakpoints.tablet).matches;
    const isDesktop = window.matchMedia(breakpoints.desktop).matches;
    const isTouch = window.matchMedia(breakpoints.touch).matches;
    const canHover = window.matchMedia(breakpoints.hover).matches;

    return {
        isMobile,
        isTablet,
        isDesktop,
        isTouch,
        canHover,
        screenSize: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop'
    };
};

export const adaptiveLayout = {
    getColumns: (screenSize) => {
        switch (screenSize) {
            case 'mobile': return 1;
            case 'tablet': return 2;
            case 'desktop': return 3;
            default: return 1;
        }
    },
    
    getSpacing: (screenSize) => {
        switch (screenSize) {
            case 'mobile': return '8px';
            case 'tablet': return '12px';
            case 'desktop': return '16px';
            default: return '8px';
        }
    },
    
    getFontSize: (screenSize, base = 16) => {
        switch (screenSize) {
            case 'mobile': return `${base * 0.875}px`;
            case 'tablet': return `${base}px`;
            case 'desktop': return `${base * 1.125}px`;
            default: return `${base}px`;
        }
    }
};

export const touchOptimization = {
    minTouchTarget: '44px',
    touchPadding: '12px',
    
    makeTouchFriendly: (element) => {
        if (!element) return;
        
        element.style.minHeight = touchOptimization.minTouchTarget;
        element.style.minWidth = touchOptimization.minTouchTarget;
        element.style.padding = touchOptimization.touchPadding;
    }
};