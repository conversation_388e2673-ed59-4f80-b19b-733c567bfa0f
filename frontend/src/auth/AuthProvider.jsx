"""
Enhanced Authentication Provider for CHaBot Frontend.
Provides comprehensive authentication state management, token handling, and security features.
"""

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

// Authentication context
const AuthContext = createContext();

// Authentication states
const AUTH_STATES = {
  LOADING: 'loading',
  AUTHENTICATED: 'authenticated',
  UNAUTHENTICATED: 'unauthenticated',
  MFA_REQUIRED: 'mfa_required',
  SESSION_EXPIRED: 'session_expired',
  LOCKED_OUT: 'locked_out'
};

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  TOKEN_REFRESH: 'TOKEN_REFRESH',
  MFA_REQUIRED: 'MFA_REQUIRED',
  MFA_SUCCESS: 'MFA_SUCCESS',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  UPDATE_USER: 'UPDATE_USER',
  SET_PERMISSIONS: 'SET_PERMISSIONS'
};

// Initial state
const initialState = {
  state: AUTH_STATES.LOADING,
  user: null,
  tokens: {
    accessToken: null,
    refreshToken: null,
    idToken: null
  },
  permissions: [],
  roles: [],
  mfaChallenge: null,
  sessionInfo: {
    expiresAt: null,
    lastActivity: null,
    deviceFingerprint: null
  },
  error: null,
  isLoading: false
};

// Auth reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        state: AUTH_STATES.AUTHENTICATED,
        user: action.payload.user,
        tokens: action.payload.tokens,
        permissions: action.payload.permissions || [],
        roles: action.payload.roles || [],
        sessionInfo: {
          ...action.payload.sessionInfo,
          lastActivity: new Date()
        },
        error: null,
        isLoading: false
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        state: AUTH_STATES.UNAUTHENTICATED,
        user: null,
        tokens: initialState.tokens,
        permissions: [],
        roles: [],
        error: action.payload.error,
        isLoading: false
      };

    case AUTH_ACTIONS.MFA_REQUIRED:
      return {
        ...state,
        state: AUTH_STATES.MFA_REQUIRED,
        mfaChallenge: action.payload.challenge,
        error: null,
        isLoading: false
      };

    case AUTH_ACTIONS.MFA_SUCCESS:
      return {
        ...state,
        state: AUTH_STATES.AUTHENTICATED,
        tokens: {
          ...state.tokens,
          ...action.payload.tokens
        },
        mfaChallenge: null,
        sessionInfo: {
          ...state.sessionInfo,
          lastActivity: new Date()
        },
        error: null,
        isLoading: false
      };

    case AUTH_ACTIONS.TOKEN_REFRESH:
      return {
        ...state,
        tokens: {
          ...state.tokens,
          ...action.payload.tokens
        },
        sessionInfo: {
          ...state.sessionInfo,
          expiresAt: action.payload.expiresAt,
          lastActivity: new Date()
        }
      };

    case AUTH_ACTIONS.SESSION_EXPIRED:
      return {
        ...state,
        state: AUTH_STATES.SESSION_EXPIRED,
        error: 'Your session has expired. Please log in again.'
      };

    case AUTH_ACTIONS.ACCOUNT_LOCKED:
      return {
        ...state,
        state: AUTH_STATES.LOCKED_OUT,
        error: action.payload.error,
        isLoading: false
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        state: AUTH_STATES.UNAUTHENTICATED,
        isLoading: false
      };

    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: {
          ...state.user,
          ...action.payload
        }
      };

    case AUTH_ACTIONS.SET_PERMISSIONS:
      return {
        ...state,
        permissions: action.payload.permissions,
        roles: action.payload.roles
      };

    default:
      return state;
  }
}

// Token storage utilities
class TokenStorage {
  static setTokens(tokens) {
    try {
      // Store access token in memory only (more secure)
      window.__chabot_access_token = tokens.accessToken;
      
      // Store refresh token in httpOnly cookie (most secure)
      // This would be set by the server
      
      // Store ID token in sessionStorage
      if (tokens.idToken) {
        sessionStorage.setItem('chabot_id_token', tokens.idToken);
      }
      
      // Store token metadata
      const tokenMetadata = {
        expiresAt: tokens.expiresAt,
        tokenType: tokens.tokenType || 'Bearer'
      };
      sessionStorage.setItem('chabot_token_metadata', JSON.stringify(tokenMetadata));
      
    } catch (error) {
      console.error('Failed to store tokens:', error);
    }
  }

  static getAccessToken() {
    return window.__chabot_access_token || null;
  }

  static getIdToken() {
    try {
      return sessionStorage.getItem('chabot_id_token');
    } catch (error) {
      return null;
    }
  }

  static getTokenMetadata() {
    try {
      const metadata = sessionStorage.getItem('chabot_token_metadata');
      return metadata ? JSON.parse(metadata) : null;
    } catch (error) {
      return null;
    }
  }

  static clearTokens() {
    try {
      delete window.__chabot_access_token;
      sessionStorage.removeItem('chabot_id_token');
      sessionStorage.removeItem('chabot_token_metadata');
      sessionStorage.removeItem('chabot_user_data');
      sessionStorage.removeItem('chabot_permissions');
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  }

  static isTokenExpired(token) {
    if (!token) return true;
    
    try {
      const decoded = jwtDecode(token);
      const currentTime = Date.now() / 1000;
      return decoded.exp < currentTime;
    } catch (error) {
      return true;
    }
  }
}

// Device fingerprinting
class DeviceFingerprint {
  static generate() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);
    
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvas.toDataURL(),
      webgl: this.getWebGLFingerprint()
    };
    
    return btoa(JSON.stringify(fingerprint));
  }

  static getWebGLFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) return 'no-webgl';
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      return debugInfo ? 
        gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 
        'no-debug-info';
    } catch (error) {
      return 'webgl-error';
    }
  }
}

// API client with automatic token handling
class AuthenticatedAPIClient {
  constructor(dispatch) {
    this.dispatch = dispatch;
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
    this.refreshPromise = null;
    
    // Create axios instance
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const token = TokenStorage.getAccessToken();
        if (token && !TokenStorage.isTokenExpired(token)) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // Add device fingerprint
        config.headers['X-Device-Fingerprint'] = DeviceFingerprint.generate();
        
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            await this.refreshToken();
            const token = TokenStorage.getAccessToken();
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            this.dispatch({ type: AUTH_ACTIONS.SESSION_EXPIRED });
            return Promise.reject(refreshError);
          }
        }
        
        return Promise.reject(error);
      }
    );
  }

  async refreshToken() {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }
    
    this.refreshPromise = this.performTokenRefresh();
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  async performTokenRefresh() {
    try {
      const response = await axios.post(`${this.baseURL}/auth/refresh`, {}, {
        withCredentials: true // Include httpOnly refresh token cookie
      });
      
      const { access_token, expires_in } = response.data;
      const expiresAt = new Date(Date.now() + expires_in * 1000);
      
      const tokens = {
        accessToken: access_token,
        expiresAt: expiresAt.toISOString()
      };
      
      TokenStorage.setTokens(tokens);
      
      this.dispatch({
        type: AUTH_ACTIONS.TOKEN_REFRESH,
        payload: { tokens, expiresAt }
      });
      
      return tokens;
    } catch (error) {
      TokenStorage.clearTokens();
      throw error;
    }
  }
}

// Session timeout manager
class SessionManager {
  constructor(dispatch) {
    this.dispatch = dispatch;
    this.timeoutId = null;
    this.warningTimeoutId = null;
    this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
    this.warningTime = 5 * 60 * 1000; // 5 minutes before timeout
    
    this.setupActivityListeners();
  }

  setupActivityListeners() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    events.forEach(event => {
      document.addEventListener(event, this.resetTimeout.bind(this), true);
    });
  }

  resetTimeout() {
    this.clearTimeouts();
    
    // Set warning timeout
    this.warningTimeoutId = setTimeout(() => {
      this.showSessionWarning();
    }, this.sessionTimeout - this.warningTime);
    
    // Set session timeout
    this.timeoutId = setTimeout(() => {
      this.handleSessionTimeout();
    }, this.sessionTimeout);
  }

  showSessionWarning() {
    // Show session timeout warning modal
    const event = new CustomEvent('sessionWarning', {
      detail: { timeRemaining: this.warningTime }
    });
    window.dispatchEvent(event);
  }

  handleSessionTimeout() {
    this.dispatch({ type: AUTH_ACTIONS.SESSION_EXPIRED });
    TokenStorage.clearTokens();
  }

  clearTimeouts() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    if (this.warningTimeoutId) {
      clearTimeout(this.warningTimeoutId);
      this.warningTimeoutId = null;
    }
  }

  extendSession() {
    this.resetTimeout();
  }

  destroy() {
    this.clearTimeouts();
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.removeEventListener(event, this.resetTimeout.bind(this), true);
    });
  }
}

// Main AuthProvider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const apiClientRef = React.useRef(null);
  const sessionManagerRef = React.useRef(null);

  // Initialize API client and session manager
  useEffect(() => {
    apiClientRef.current = new AuthenticatedAPIClient(dispatch);
    sessionManagerRef.current = new SessionManager(dispatch);
    
    return () => {
      if (sessionManagerRef.current) {
        sessionManagerRef.current.destroy();
      }
    };
  }, []);

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = useCallback(async () => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
    
    try {
      const accessToken = TokenStorage.getAccessToken();
      const idToken = TokenStorage.getIdToken();
      
      if (accessToken && !TokenStorage.isTokenExpired(accessToken)) {
        // Validate token with server
        const response = await apiClientRef.current.client.get('/auth/me');
        const userData = response.data;
        
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user: userData.user,
            tokens: { accessToken, idToken },
            permissions: userData.permissions,
            roles: userData.roles,
            sessionInfo: {
              expiresAt: TokenStorage.getTokenMetadata()?.expiresAt,
              deviceFingerprint: DeviceFingerprint.generate()
            }
          }
        });
        
        // Start session management
        sessionManagerRef.current?.resetTimeout();
      } else {
        // Try to refresh token
        try {
          await apiClientRef.current.refreshToken();
          // If successful, reinitialize
          await initializeAuth();
        } catch (error) {
          dispatch({ type: AUTH_ACTIONS.LOGOUT });
        }
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  }, []);

  const login = useCallback(async (credentials) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
    
    try {
      const deviceInfo = {
        fingerprint: DeviceFingerprint.generate(),
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      };
      
      const response = await apiClientRef.current.client.post('/auth/login', {
        ...credentials,
        deviceInfo
      });
      
      const { user, tokens, permissions, roles, mfa_required } = response.data;
      
      if (mfa_required) {
        dispatch({
          type: AUTH_ACTIONS.MFA_REQUIRED,
          payload: { challenge: response.data.mfa_challenge }
        });
        return { success: true, mfaRequired: true };
      }
      
      // Store tokens
      TokenStorage.setTokens(tokens);
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: {
          user,
          tokens,
          permissions,
          roles,
          sessionInfo: {
            expiresAt: tokens.expiresAt,
            deviceFingerprint: deviceInfo.fingerprint
          }
        }
      });
      
      // Start session management
      sessionManagerRef.current?.resetTimeout();
      
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Login failed';
      
      if (error.response?.status === 423) {
        dispatch({
          type: AUTH_ACTIONS.ACCOUNT_LOCKED,
          payload: { error: 'Account locked due to too many failed attempts' }
        });
      } else {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_FAILURE,
          payload: { error: errorMessage }
        });
      }
      
      return { success: false, error: errorMessage };
    }
  }, []);

  const verifyMFA = useCallback(async (challengeId, code) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
    
    try {
      const response = await apiClientRef.current.client.post('/auth/mfa/verify', {
        challenge_id: challengeId,
        code
      });
      
      const { tokens, user, permissions, roles } = response.data;
      
      // Update tokens
      TokenStorage.setTokens(tokens);
      
      dispatch({
        type: AUTH_ACTIONS.MFA_SUCCESS,
        payload: {
          tokens,
          user,
          permissions,
          roles
        }
      });
      
      // Start session management
      sessionManagerRef.current?.resetTimeout();
      
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'MFA verification failed';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: errorMessage }
      });
      
      return { success: false, error: errorMessage };
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      // Notify server of logout
      await apiClientRef.current.client.post('/auth/logout');
    } catch (error) {
      // Continue with logout even if server request fails
      console.error('Logout request failed:', error);
    }
    
    // Clear local storage
    TokenStorage.clearTokens();
    
    // Stop session management
    sessionManagerRef.current?.clearTimeouts();
    
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
  }, []);

  const register = useCallback(async (userData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
    
    try {
      const response = await apiClientRef.current.client.post('/auth/register', userData);
      
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      
      return { success: true, data: response.data };
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Registration failed';
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: errorMessage }
      });
      
      return { success: false, error: errorMessage };
    }
  }, []);

  const updateProfile = useCallback(async (profileData) => {
    try {
      const response = await apiClientRef.current.client.put('/auth/profile', profileData);
      
      dispatch({
        type: AUTH_ACTIONS.UPDATE_USER,
        payload: response.data
      });
      
      return { success: true };
    } catch (error) {
      return { success: false, error: error.response?.data?.detail || 'Profile update failed' };
    }
  }, []);

  const changePassword = useCallback(async (passwordData) => {
    try {
      await apiClientRef.current.client.post('/auth/change-password', passwordData);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.response?.data?.detail || 'Password change failed' };
    }
  }, []);

  const setupMFA = useCallback(async (method, deviceName) => {
    try {
      const response = await apiClientRef.current.client.post('/auth/mfa/setup', {
        method,
        device_name: deviceName
      });
      
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data?.detail || 'MFA setup failed' };
    }
  }, []);

  const hasPermission = useCallback((permission) => {
    return state.permissions.includes(permission);
  }, [state.permissions]);

  const hasRole = useCallback((role) => {
    return state.roles.includes(role);
  }, [state.roles]);

  const hasAnyRole = useCallback((roles) => {
    return roles.some(role => state.roles.includes(role));
  }, [state.roles]);

  const extendSession = useCallback(() => {
    sessionManagerRef.current?.extendSession();
  }, []);

  const getApiClient = useCallback(() => {
    return apiClientRef.current?.client;
  }, []);

  const contextValue = {
    // State
    ...state,
    isAuthenticated: state.state === AUTH_STATES.AUTHENTICATED,
    isMFARequired: state.state === AUTH_STATES.MFA_REQUIRED,
    isSessionExpired: state.state === AUTH_STATES.SESSION_EXPIRED,
    isLockedOut: state.state === AUTH_STATES.LOCKED_OUT,
    
    // Actions
    login,
    logout,
    register,
    verifyMFA,
    updateProfile,
    changePassword,
    setupMFA,
    extendSession,
    
    // Utilities
    hasPermission,
    hasRole,
    hasAnyRole,
    getApiClient
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protected routes
export function withAuth(Component, requiredPermissions = [], requiredRoles = []) {
  return function AuthenticatedComponent(props) {
    const auth = useAuth();
    
    if (!auth.isAuthenticated) {
      return <div>Please log in to access this page.</div>;
    }
    
    if (requiredPermissions.length > 0) {
      const hasRequiredPermissions = requiredPermissions.every(permission => 
        auth.hasPermission(permission)
      );
      
      if (!hasRequiredPermissions) {
        return <div>You don't have permission to access this page.</div>;
      }
    }
    
    if (requiredRoles.length > 0) {
      const hasRequiredRoles = auth.hasAnyRole(requiredRoles);
      
      if (!hasRequiredRoles) {
        return <div>You don't have the required role to access this page.</div>;
      }
    }
    
    return <Component {...props} />;
  };
}

// Protected Route component
export function ProtectedRoute({ children, requiredPermissions = [], requiredRoles = [] }) {
  const auth = useAuth();
  
  if (auth.isLoading) {
    return <div>Loading...</div>;
  }
  
  if (!auth.isAuthenticated) {
    return <div>Please log in to access this page.</div>;
  }
  
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requiredPermissions.every(permission => 
      auth.hasPermission(permission)
    );
    
    if (!hasRequiredPermissions) {
      return <div>You don't have permission to access this page.</div>;
    }
  }
  
  if (requiredRoles.length > 0) {
    const hasRequiredRoles = auth.hasAnyRole(requiredRoles);
    
    if (!hasRequiredRoles) {
      return <div>You don't have the required role to access this page.</div>;
    }
  }
  
  return children;
}

export default AuthProvider;