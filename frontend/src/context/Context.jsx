import {createContext, useState} from "react";

export const Context = createContext();

const ContextProvider = (props) => {

    const [input, setInput] = useState("");
    const [recentPrompt, setRecentPrompt] = useState("");
    const [prevPrompts, setPrevPrompts] = useState([]);
    const [showResult, setShowResult] = useState(false);
    const [loading, setLoading] = useState(false);
    const [resultData, setResultData] = useState("");
    const [milestoneData, setMilestoneData] = useState(null);

    const delayPara = (index, nextWord) => {
        setTimeout(function () {
            setResultData(prev => prev + nextWord)
        }, 75 * index);
    }

    const newChat = () => {
        setLoading(false);
        setShowResult(false);
        setMilestoneData(null);
    }

    const callCHaBotAPI = async (message) => {
        try {
            const response = await fetch('http://localhost:8000/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    user_context: {
                        organization: "NUVO AI",
                        department: "Engineering"
                    }
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('CHaBot API Error:', error);
            return {
                response: "I'm experiencing technical difficulties. All 8 milestones are operational, but there's a temporary connection issue. Please try again.",
                confidence: 0.3,
                milestone_info: {
                    milestones_used: [3],
                    status: "fallback_mode"
                }
            };
        }
    }

    const onSent = async (prompt) => {
        setResultData("");
        setLoading(true);
        setShowResult(true);
        setMilestoneData(null);
        
        let queryText;
        if (prompt !== undefined) {
            queryText = prompt;
            setRecentPrompt(prompt);
        } else {
            setPrevPrompts(prev => [...prev, input]);
            setRecentPrompt(input);
            queryText = input;
        }

        // Call CHaBot API with all milestones
        const apiResponse = await callCHaBotAPI(queryText);
        
        // Set milestone data
        setMilestoneData({
            confidence: apiResponse.confidence || 0.8,
            response_time_ms: apiResponse.milestone_info?.response_time_ms || 0,
            milestones_used: apiResponse.milestone_info?.milestones_used || [1,2,3,4,5,6,7,8],
            agent_coordination: apiResponse.agent_coordination?.orchestrator || "active",
            system_status: apiResponse.milestone_info?.system_status || "all_operational"
        });
        
        let response = apiResponse.response || "System processed your request using all milestone capabilities.";
        
        // Format response with milestone info
        if (apiResponse.confidence) {
            response += `\n\n🎯 Confidence: ${(apiResponse.confidence * 100).toFixed(1)}%`;
        }
        if (apiResponse.sources && apiResponse.sources.length > 0) {
            response += `\n📚 Sources: ${apiResponse.sources.join(', ')}`;
        }
        
        // Process response with formatting
        let responseArray = response.split("**");
        let newResponse = "";
        for (let i = 0; i < responseArray.length; i++) {
            if (i === 0 || i % 2 !== 1) {
                newResponse += responseArray[i];
            } else {
                newResponse += "<b>" + responseArray[i] + "</b>"
            }
        }
        let newResponse2 = newResponse.split("*").join("</br>");
        let newResponseArray = newResponse2.split(" ");
        
        for (let i = 0; i < newResponseArray.length; i++) {
            const nextWord = newResponseArray[i];
            delayPara(i, nextWord + " ");
        }
        
        setLoading(false);
        setInput("");
    }

    const contextValue = {
        prevPrompts,
        setPrevPrompts,
        onSent,
        recentPrompt,
        setRecentPrompt,
        showResult,
        loading,
        resultData,
        input,
        setInput,
        newChat,
        milestoneData
    }
    return (
        <Context.Provider value={contextValue}>
            {props.children}
        </Context.Provider>
    )
}

export default ContextProvider;