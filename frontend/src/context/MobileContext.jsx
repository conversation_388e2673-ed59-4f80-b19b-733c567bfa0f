import React, { createContext, useContext } from 'react';

const MobileContext = createContext({
    isMobile: false,
    isTablet: false,
    isTouch: false,
    screenSize: 'desktop',
    onMessageSend: null
});

export const useMobileContext = () => {
    const context = useContext(MobileContext);
    if (!context) {
        return {
            isMobile: false,
            isTablet: false,
            isTouch: false,
            screenSize: 'desktop',
            onMessageSend: null
        };
    }
    return context;
};

export const MobileProvider = ({ children, value }) => {
    return (
        <MobileContext.Provider value={value}>
            {children}
        </MobileContext.Provider>
    );
};

export default MobileContext;