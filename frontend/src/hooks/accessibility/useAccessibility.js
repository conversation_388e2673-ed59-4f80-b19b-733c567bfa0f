import { useState, useEffect } from 'react';

const useAccessibility = () => {
    const [highContrast, setHighContrast] = useState(false);
    const [fontSize, setFontSize] = useState('medium');
    const [reducedMotion, setReducedMotion] = useState(false);
    const [screenReader, setScreenReader] = useState(false);

    useEffect(() => {
        // Check for saved preferences
        const savedHighContrast = localStorage.getItem('accessibility-high-contrast') === 'true';
        const savedFontSize = localStorage.getItem('accessibility-font-size') || 'medium';
        const savedReducedMotion = localStorage.getItem('accessibility-reduced-motion') === 'true';
        
        setHighContrast(savedHighContrast);
        setFontSize(savedFontSize);
        setReducedMotion(savedReducedMotion);

        // Check for system preferences
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
        
        if (prefersReducedMotion) setReducedMotion(true);
        if (prefersHighContrast) setHighContrast(true);

        // Detect screen reader
        const hasScreenReader = window.navigator.userAgent.includes('NVDA') || 
                               window.navigator.userAgent.includes('JAWS') ||
                               window.speechSynthesis;
        setScreenReader(hasScreenReader);

        // Apply accessibility classes
        updateAccessibilityClasses();
    }, []);

    const updateAccessibilityClasses = () => {
        const root = document.documentElement;
        
        root.classList.toggle('high-contrast', highContrast);
        root.classList.toggle('reduced-motion', reducedMotion);
        root.className = root.className.replace(/font-size-\w+/, '');
        root.classList.add(`font-size-${fontSize}`);
    };

    const toggleHighContrast = () => {
        const newValue = !highContrast;
        setHighContrast(newValue);
        localStorage.setItem('accessibility-high-contrast', newValue);
        updateAccessibilityClasses();
    };

    const changeFontSize = (size) => {
        setFontSize(size);
        localStorage.setItem('accessibility-font-size', size);
        updateAccessibilityClasses();
    };

    const toggleReducedMotion = () => {
        const newValue = !reducedMotion;
        setReducedMotion(newValue);
        localStorage.setItem('accessibility-reduced-motion', newValue);
        updateAccessibilityClasses();
    };

    return {
        highContrast,
        fontSize,
        reducedMotion,
        screenReader,
        toggleHighContrast,
        changeFontSize,
        toggleReducedMotion
    };
};

export default useAccessibility;