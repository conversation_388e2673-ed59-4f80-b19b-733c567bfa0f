import { useEffect, useRef } from 'react';

const useKeyboardNavigation = (containerRef) => {
    const focusableElements = useRef([]);
    const currentFocusIndex = useRef(-1);

    useEffect(() => {
        if (!containerRef.current) return;

        const updateFocusableElements = () => {
            const container = containerRef.current;
            const elements = container.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            focusableElements.current = Array.from(elements);
        };

        const handleKeyDown = (e) => {
            if (!focusableElements.current.length) return;

            switch (e.key) {
                case 'Tab':
                    if (e.shiftKey) {
                        // Shift+Tab - previous element
                        e.preventDefault();
                        currentFocusIndex.current = currentFocusIndex.current <= 0 
                            ? focusableElements.current.length - 1 
                            : currentFocusIndex.current - 1;
                    } else {
                        // Tab - next element
                        e.preventDefault();
                        currentFocusIndex.current = currentFocusIndex.current >= focusableElements.current.length - 1 
                            ? 0 
                            : currentFocusIndex.current + 1;
                    }
                    focusableElements.current[currentFocusIndex.current]?.focus();
                    break;

                case 'Escape':
                    // Close modals or return focus
                    const activeElement = document.activeElement;
                    if (activeElement && activeElement.blur) {
                        activeElement.blur();
                    }
                    break;

                case 'Enter':
                case ' ':
                    // Activate focused element
                    const focused = document.activeElement;
                    if (focused && focused.click && (focused.tagName === 'BUTTON' || focused.getAttribute('role') === 'button')) {
                        e.preventDefault();
                        focused.click();
                    }
                    break;

                case 'ArrowDown':
                case 'ArrowUp':
                    // Navigate lists
                    if (e.target.getAttribute('role') === 'listbox' || e.target.closest('[role="listbox"]')) {
                        e.preventDefault();
                        const direction = e.key === 'ArrowDown' ? 1 : -1;
                        navigateList(direction);
                    }
                    break;
            }
        };

        const navigateList = (direction) => {
            const currentIndex = focusableElements.current.findIndex(el => el === document.activeElement);
            if (currentIndex === -1) return;

            const nextIndex = currentIndex + direction;
            if (nextIndex >= 0 && nextIndex < focusableElements.current.length) {
                focusableElements.current[nextIndex].focus();
            }
        };

        updateFocusableElements();
        containerRef.current.addEventListener('keydown', handleKeyDown);

        // Update focusable elements when DOM changes
        const observer = new MutationObserver(updateFocusableElements);
        observer.observe(containerRef.current, { childList: true, subtree: true });

        return () => {
            if (containerRef.current) {
                containerRef.current.removeEventListener('keydown', handleKeyDown);
            }
            observer.disconnect();
        };
    }, [containerRef]);

    const focusFirst = () => {
        if (focusableElements.current.length > 0) {
            focusableElements.current[0].focus();
            currentFocusIndex.current = 0;
        }
    };

    const focusLast = () => {
        if (focusableElements.current.length > 0) {
            const lastIndex = focusableElements.current.length - 1;
            focusableElements.current[lastIndex].focus();
            currentFocusIndex.current = lastIndex;
        }
    };

    return { focusFirst, focusLast };
};

export default useKeyboardNavigation;