import { useState, useEffect, useRef } from 'react';

const useWebSocket = (url) => {
    const [socket, setSocket] = useState(null);
    const [isConnected, setIsConnected] = useState(false);
    const [messages, setMessages] = useState([]);
    const [error, setError] = useState(null);
    const reconnectTimeoutRef = useRef(null);
    const reconnectAttempts = useRef(0);
    const maxReconnectAttempts = 5;

    const connect = () => {
        // Skip WebSocket in development if backend not available
        if (process.env.NODE_ENV === 'development' && !process.env.VITE_WEBSOCKET_URL) {
            return;
        }
        
        try {
            const ws = new WebSocket(url);
            
            ws.onopen = () => {
                setIsConnected(true);
                setError(null);
                reconnectAttempts.current = 0;
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    setMessages(prev => [...prev, data]);
                } catch (err) {
                    // Silent in development
                }
            };

            ws.onclose = () => {
                setIsConnected(false);
                setSocket(null);
                
                if (reconnectAttempts.current < maxReconnectAttempts) {
                    reconnectAttempts.current++;
                    reconnectTimeoutRef.current = setTimeout(() => {
                        connect();
                    }, 5000);
                }
            };

            ws.onerror = () => {
                setError('Connection failed');
            };

            setSocket(ws);
        } catch (err) {
            setError(err);
        }
    };

    const sendMessage = (message) => {
        if (socket && isConnected) {
            socket.send(JSON.stringify(message));
            return true;
        }
        return false;
    };

    useEffect(() => {
        if (url) {
            connect();
        }

        return () => {
            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
            }
            if (socket) {
                socket.close();
            }
        };
    }, [url]);

    return {
        socket,
        isConnected,
        messages,
        error,
        sendMessage
    };
};

export default useWebSocket;