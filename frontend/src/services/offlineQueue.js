class OfflineQueue {
    constructor() {
        this.dbName = 'CHaBotDB';
        this.version = 1;
        this.storeName = 'messages';
        this.db = null;
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains(this.storeName)) {
                    db.createObjectStore(this.storeName, { keyPath: 'id' });
                }
            };
        });
    }

    async addMessage(message) {
        if (!this.db) await this.init();
        
        const messageWithId = {
            ...message,
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            synced: false
        };

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.add(messageWithId);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(messageWithId);
        });
    }

    async getUnsynced() {
        if (!this.db) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAll();
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                const unsynced = request.result.filter(msg => !msg.synced);
                resolve(unsynced);
            };
        });
    }

    async markSynced(id) {
        if (!this.db) await this.init();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const getRequest = store.get(id);
            
            getRequest.onsuccess = () => {
                const message = getRequest.result;
                if (message) {
                    message.synced = true;
                    const putRequest = store.put(message);
                    putRequest.onsuccess = () => resolve();
                    putRequest.onerror = () => reject(putRequest.error);
                } else {
                    resolve();
                }
            };
            
            getRequest.onerror = () => reject(getRequest.error);
        });
    }

    async syncMessages() {
        const unsynced = await this.getUnsynced();
        const results = [];

        for (const message of unsynced) {
            try {
                const response = await fetch('/api/messages', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(message)
                });

                if (response.ok) {
                    await this.markSynced(message.id);
                    results.push({ success: true, message });
                } else {
                    results.push({ success: false, message, error: 'Server error' });
                }
            } catch (error) {
                results.push({ success: false, message, error: error.message });
            }
        }

        return results;
    }

    isOnline() {
        return navigator.onLine;
    }

    async handleMessage(message) {
        if (this.isOnline()) {
            try {
                const response = await fetch('/api/messages', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(message)
                });

                if (response.ok) {
                    return { success: true, online: true };
                } else {
                    await this.addMessage(message);
                    return { success: true, online: false, queued: true };
                }
            } catch (error) {
                await this.addMessage(message);
                return { success: true, online: false, queued: true };
            }
        } else {
            await this.addMessage(message);
            return { success: true, online: false, queued: true };
        }
    }
}

export default new OfflineQueue();