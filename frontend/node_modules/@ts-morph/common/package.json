{"name": "@ts-morph/common", "version": "0.11.1", "description": "Common functionality for ts-morph packages.", "main": "dist/ts-morph-common.js", "author": "<PERSON>", "license": "MIT", "repository": "git+https://github.com/dsherret/ts-morph.git", "typings": "lib/ts-morph-common.d.ts", "scripts": {"build": "npm run build:declarations && npm run build:node && npm run build:deno", "build:node": "rimraf dist && npm run createLibFile && npm run rollup && ts-node scripts/bundleLocalTs.ts", "build:deno": "rimraf ../../deno/common && rimraf dist-deno && npm run rollup -- --environment BUILD:deno && npm run build:declarations && ts-node scripts/buildDeno.ts", "build:declarations": "ts-node --compiler ttypescript --project scripts/tsconfig.json --transpile-only scripts/buildDeclarations.ts", "createLibFile": "ts-node scripts/createLibFile.ts", "test": "cross-env TS_NODE_COMPILER=\"ttypescript\" TS_NODE_TRANSPILE_ONLY=\"true\" mocha", "test:ci": "npm run test", "test:debug": "npm run test --inspect-brk", "rollup": "rollup --config"}, "dependencies": {"fast-glob": "^3.2.7", "mkdirp": "^1.0.4", "minimatch": "^3.0.4", "path-browserify": "^1.0.1"}, "devDependencies": {"@ts-morph/scripts": "~0.2.0", "@types/chai": "^4.2.22", "@types/mkdirp": "^1.0.2", "@types/mocha": "^9.0.0", "@types/minimatch": "^3.0.5", "@types/node": "^16.11.6", "@types/ts-nameof": "^4.2.1", "chai": "^4.3.4", "cross-env": "^7.0.3", "dts-minify": "^0.2.3", "mocha": "^9.1.3", "rimraf": "^3.0.2", "rollup": "^2.58.3", "rollup-plugin-typescript2": "^0.30.0", "ts-nameof": "^5.0.0", "ts-node": "^10.4.0", "ttypescript": "^1.5.12", "typescript": "4.4.4"}, "publishConfig": {"access": "public"}, "browser": {"fs": false, "os": false, "fs.realpath": false, "mkdirp": false, "dir-glob": false, "graceful-fs": false, "fast-glob": false, "source-map-support": false, "glob-parent": false, "glob": false, "path": false, "crypto": false, "buffer": false, "@microsoft/typescript-etw": false, "inspector": false}}