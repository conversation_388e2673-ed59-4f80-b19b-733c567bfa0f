# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.15](https://github.com/inspect-js/is-typed-array/compare/v1.1.14...v1.1.15) - 2024-12-18

### Commits

- [types] improve types [`d934b49`](https://github.com/inspect-js/is-typed-array/commit/d934b49f7a16d5e20ba437a795b887f1f71ef240)
- [Dev De<PERSON>] update `@types/tape` [`da26511`](https://github.com/inspect-js/is-typed-array/commit/da26511ad7515c50fdc720701d5735b0d8a40800)

## [v1.1.14](https://github.com/inspect-js/is-typed-array/compare/v1.1.13...v1.1.14) - 2024-12-17

### Commits

- [types] use shared config [`eafa7fa`](https://github.com/inspect-js/is-typed-array/commit/eafa7fad2fc8d464a68e218d39a7eab782d9ce76)
- [actions] split out node 10-20, and 20+ [`cd6d5a3`](https://github.com/inspect-js/is-typed-array/commit/cd6d5a3283a1e65cf5885e57daede65a5176fd91)
- [types] use `which-typed-array`’s `TypedArray` type; re-export it [`d7d9fcd`](https://github.com/inspect-js/is-typed-array/commit/d7d9fcd75d538b7f8146dcd9faca5142534a3d45)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/node`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `object-inspect`, `tape` [`65afb42`](https://github.com/inspect-js/is-typed-array/commit/65afb4263ff4f4ee4ee51b284dc7519ce969a666)
- [Dev Deps] update `@types/node`, `has-tostringtag`, `tape` [`9e27ddd`](https://github.com/inspect-js/is-typed-array/commit/9e27ddd62a51ebae46781de0adbd8871341c633c)
- [Tests] replace `aud` with `npm audit` [`ad4defe`](https://github.com/inspect-js/is-typed-array/commit/ad4defe211c77d42b880d13faf7737b8f1adaf13)
- [Tests] use `@arethetypeswrong/cli` [`ac4bcca`](https://github.com/inspect-js/is-typed-array/commit/ac4bcca4ee2215662e79aa21681756984bb0b6d1)
- [Deps] update `which-typed-array` [`c298129`](https://github.com/inspect-js/is-typed-array/commit/c2981299c09cd64d89bf1e496447c0379b45d03a)
- [Deps] update `which-typed-array` [`744c29a`](https://github.com/inspect-js/is-typed-array/commit/744c29aa8d4f9df360082074f7b4f2f0d42d76e5)
- [Dev Deps] add missing peer dep [`94d2f5a`](https://github.com/inspect-js/is-typed-array/commit/94d2f5a11016516823e8d943e0bfc7b29dcb146d)

## [v1.1.13](https://github.com/inspect-js/is-typed-array/compare/v1.1.12...v1.1.13) - 2024-02-01

### Commits

- [patch] add types [`8a8a679`](https://github.com/inspect-js/is-typed-array/commit/8a8a679937d1c4b970c98556460cef2b7fa0bffb)
- [Dev Deps] update `aud`, `has-tostringtag`, `npmignore`, `object-inspect`, `tape` [`8146b60`](https://github.com/inspect-js/is-typed-array/commit/8146b6019a24f502e66e2c224ce5bea8df9f39bc)
- [actions] optimize finishers [`34f875a`](https://github.com/inspect-js/is-typed-array/commit/34f875ace16c4900d6b0ef4688e9e3eb7d502715)
- [Deps] update `which-typed-array` [`19c974f`](https://github.com/inspect-js/is-typed-array/commit/19c974f4bbd93ffc45cb8638b86688bc00f1420b)
- [meta] add `sideEffects` flag [`0b68e5e`](https://github.com/inspect-js/is-typed-array/commit/0b68e5e58684b79110a82a0a51df8beb7574d6a2)

## [v1.1.12](https://github.com/inspect-js/is-typed-array/compare/v1.1.11...v1.1.12) - 2023-07-17

### Commits

- [Refactor] use `which-typed-array` for all internals [`7619405`](https://github.com/inspect-js/is-typed-array/commit/761940532de595f6721fed101b02814dcfa7fe4e)

## [v1.1.11](https://github.com/inspect-js/is-typed-array/compare/v1.1.10...v1.1.11) - 2023-07-17

### Commits

- [Fix] `node &lt; v0.6` lacks proper Object toString behavior [`c94b90d`](https://github.com/inspect-js/is-typed-array/commit/c94b90dc6bc457783d6f8cc208415a49da0933b7)
- [Robustness] use `call-bind` [`573b00b`](https://github.com/inspect-js/is-typed-array/commit/573b00b8deec42ac1ac262415e442ea0b7e1c96b)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`c88c2d4`](https://github.com/inspect-js/is-typed-array/commit/c88c2d479976110478fa4038fe8921251c06a163)

## [v1.1.10](https://github.com/inspect-js/is-typed-array/compare/v1.1.9...v1.1.10) - 2022-11-02

### Commits

- [meta] add `auto-changelog` [`cf6d86b`](https://github.com/inspect-js/is-typed-array/commit/cf6d86bf2f693eca357439d4d12e76d641f91f92)
- [actions] update rebase action to use reusable workflow [`8da51a5`](https://github.com/inspect-js/is-typed-array/commit/8da51a5dce6d2442ae31ccbc2be136f2e04d6bef)
- [Dev Deps] update `aud`, `is-callable`, `object-inspect`, `tape` [`554e3de`](https://github.com/inspect-js/is-typed-array/commit/554e3deec59dec926d0badc628e589ab363e465b)
- [Refactor] use `gopd` instead of an `es-abstract` helper` [`cdaa465`](https://github.com/inspect-js/is-typed-array/commit/cdaa465d5f94bfc9e32475e31209e1c2458a9603)
- [Deps] update `es-abstract` [`677ae4b`](https://github.com/inspect-js/is-typed-array/commit/677ae4b3c8323b59d6650a9254ab945045c33f79)

<!-- auto-changelog-above -->

1.1.9 / 2022-05-13
=================
  * [Refactor] use `foreach` instead of `for-each`
  * [readme] markdown URL cleanup
  * [Deps] update `es-abstract`
  * [meta] use `npmignore` to autogenerate an npmignore file
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape`
  * [actions] reuse common workflows
  * [actions] update codecov uploader

1.1.8 / 2021-08-30
=================
  * [Refactor] use `globalThis` if available (#53)
  * [Deps] update `available-typed-arrays`
  * [Dev Deps] update `@ljharb/eslint-config`

1.1.7 / 2021-08-07
=================
  * [Fix] if Symbol.toStringTag exists but is not present, use Object.prototype.toString
  * [Dev Deps] update `is-callable`, `tape`

1.1.6 / 2021-08-05
=================
  * [Fix] use `has-tostringtag` to behave correctly in the presence of symbol shams
  * [readme] add actions and codecov badges
  * [meta] use `prepublishOnly` script for npm 7+
  * [Deps] update `available-typed-arrays`, `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape`
  * [actions] use `node/install` instead of `node/run`; use `codecov` action

1.1.5 / 2021-02-14
=================
  * [meta] do not publish github action workflow files or nyc output
  * [Deps] update `call-bind`, `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `is-callable`, `tape`

1.1.4 / 2020-12-05
=================
  * [readme] fix repo URLs, remove defunct badges
  * [Deps] update `available-typed-arrays`, `es-abstract`; use `call-bind` where applicable
  * [meta] gitignore nyc output
  * [meta] only audit prod deps
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is-callable`, `make-arrow-function`, `make-generator-function`, `object-inspect`, `tape`; add `aud`
  * [Tests] migrate tests to Github Actions
  * [Tests] run `nyc` on all tests

1.1.3 / 2020-01-24
=================
  * [Refactor] use `es-abstract`’s `callBound`, `available-typed-arrays`, `has-symbols`

1.1.2 / 2020-01-20
=================
  * [Fix] in envs without Symbol.toStringTag, dc8a8cc made arrays return `true`
  * [Tests] add `evalmd` to `prelint`

1.1.1 / 2020-01-18
=================
  * [Robustness] don’t rely on Array.prototype.indexOf existing
  * [meta] remove unused Makefile and associated utilities
  * [meta] add `funding` field; create FUNDING.yml
  * [actions] add automatic rebasing / merge commit blocking
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is-callable`, `replace`, `semver`, `tape`; add `safe-publish-latest`
  * [Tests] use shared travis-ci configs
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops

1.1.0 / 2019-02-16
=================
  * [New] add `BigInt64Array` and `BigUint64Array`
  * [Refactor] use an array instead of an object for storing Typed Array names
  * [meta] ignore `test.html`
  * [Tests] up to `node` `v11.10`, `v10.15`, `v8.15`, `v7.10`, `v6.16`, `v5.10`, `v4.9`
  * [Tests] remove `jscs`
  * [Tests] use `npm audit` instead of `nsp`
  * [Dev Deps] update `eslint`,` @ljharb/eslint-config`, `is-callable`, `tape`, `replace`, `semver`
  * [Dev Deps] remove unused eccheck script + dep

1.0.4 / 2016-03-19
=================
  * [Fix] `Symbol.toStringTag` is on the super-`[[Prototype]]` of Float32Array, not the `[[Prototype]]` (#3)
  * [Tests] up to `node` `v5.9`, `v4.4`
  * [Tests] use pretest/posttest for linting/security
  * [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver`, `is-callable`

1.0.3 / 2015-10-13
=================
  * [Deps] Add missing `foreach` dependency (#1)

1.0.2 / 2015-10-05
=================
  * [Deps] Remove unneeded "isarray" dependency
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`

1.0.1 / 2015-10-02
=================
  * Rerelease: avoid instanceof and the constructor property; work cross-realm; work with Symbol.toStringTag.

1.0.0 / 2015-05-06
=================
  * Initial release.
