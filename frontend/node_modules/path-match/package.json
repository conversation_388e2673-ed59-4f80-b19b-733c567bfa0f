{"name": "path-match", "description": "wrapper around path-to-regexp for easy route parameters", "version": "1.2.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "keywords": ["route", "router", "routing", "path", "regex", "regexp", "param", "params"], "license": "MIT", "repository": "pillarjs/path-match", "dependencies": {"http-errors": "~1.4.0", "path-to-regexp": "^1.0.0"}, "devDependencies": {"istanbul": "^0.4.2", "mocha": "^2.0.0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"]}