{"name": "chokidar", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "version": "3.3.1", "homepage": "https://github.com/paulmillr/chokidar", "author": "<PERSON> (https://paulmillr.com)", "contributors": ["<PERSON> (https://paulmillr.com)", "<PERSON><PERSON>"], "engines": {"node": ">= 8.10.0"}, "main": "index.js", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.3.0"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "devDependencies": {"@types/node": "^12", "chai": "^4.2", "dtslint": "^2.0.0", "eslint": "^6.6.0", "mocha": "^6.2.2", "nyc": "^14.1.1", "rimraf": "^3.0.0", "sinon": "^7.5.0", "sinon-chai": "^3.3.0", "upath": "^1.2.0"}, "files": ["index.js", "lib/*.js", "types/index.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/paulmillr/chokidar.git"}, "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "license": "MIT", "scripts": {"dtslint": "dtslint types", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --exit --timeout 60000", "test": "npm run lint && npm run mocha"}, "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "types": "./types/index.d.ts", "eslintConfig": {"extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 9, "sourceType": "script"}, "env": {"node": true, "es6": true}, "rules": {"array-callback-return": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "no-var": "error", "object-shorthand": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}], "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-destructuring": ["error", {"object": true, "array": false}], "prefer-spread": "error", "prefer-template": "error", "radix": "error", "strict": "error", "quotes": ["error", "single"]}}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}}