{"name": "json-schema-to-ts", "version": "1.6.4", "description": "Infer typescript types from your JSON schemas!", "main": "lib/index.d.ts", "scripts": {"release": "bash scripts/release.bash", "test": "tsc --noEmit && jest --verbose"}, "dependencies": {"@types/json-schema": "^7.0.6", "ts-toolbelt": "^6.15.5"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/jest": "^26.0.4", "ajv": "^6.12.6", "babel-jest": "^26.1.0", "jest": "^26.6.3", "rollup": "^2.45.2", "rollup-plugin-dts": "1.4.10", "rollup-plugin-import-map": "^2.2.2", "typescript": "^3.9.6"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ThomasAribart/json-schema-to-ts.git"}, "keywords": ["json", "schema", "typescript", "type", "ts"], "bugs": {"url": "https://github.com/ThomasAribart/json-schema-to-ts/issues"}, "homepage": "https://github.com/ThomasAribart/json-schema-to-ts#readme"}