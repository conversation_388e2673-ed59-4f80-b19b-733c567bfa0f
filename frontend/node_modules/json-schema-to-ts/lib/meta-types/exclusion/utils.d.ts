import { Get, And, Not } from "../../utils";
import { IsRepresentable } from "../utils";
import { Exclude } from ".";
export declare type CrossValue<V1, P1, R1, V2, P2, R2, X = Exclude<V1, V2>> = {
    sourceValue: V1;
    isPossibleInSource: P1;
    isRequiredInSource: R1;
    isPossibleInExcluded: P2;
    isRequiredInExcluded: R2;
    exclusionValue: X;
    isExclusionValueRepresentable: IsRepresentable<X>;
};
export declare type SourceValue<C> = Get<C, "sourceValue">;
declare type IsPossibleInSource<C> = Get<C, "isPossibleInSource">;
declare type IsRequiredInSource<C> = Get<C, "isRequiredInSource">;
declare type IsPossibleInExcluded<C> = Get<C, "isPossibleInExcluded">;
declare type IsRequiredInExcluded<C> = Get<C, "isRequiredInExcluded">;
export declare type ExclusionValue<C> = Get<C, "exclusionValue">;
export declare type IsExclusionValueRepresentable<C> = Get<C, "isExclusionValueRepresentable">;
export declare type IsOutsideOfSourceScope<C> = And<IsRequiredInExcluded<C>, Not<IsPossibleInSource<C>>>;
export declare type IsOutsideOfExcludedScope<C> = And<IsRequiredInSource<C>, Not<IsPossibleInExcluded<C>>>;
export declare type Propagate<C> = IsExclusionValueRepresentable<C> extends true ? ExclusionValue<C> : SourceValue<C>;
export declare type IsOmittable<C> = And<Not<IsRequiredInSource<C>>, IsRequiredInExcluded<C>>;
export {};
