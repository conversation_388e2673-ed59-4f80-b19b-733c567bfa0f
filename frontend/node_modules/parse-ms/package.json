{"name": "parse-ms", "version": "2.1.0", "description": "Parse milliseconds into an object", "license": "MIT", "repository": "sindresorhus/parse-ms", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["browser", "parse", "time", "ms", "milliseconds", "microseconds", "nanoseconds", "duration", "period", "range", "interval"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}