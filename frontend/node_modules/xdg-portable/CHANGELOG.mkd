<!-- markdownlint-disable heading-increment no-duplicate-heading no-inline-html -->
<!-- spellchecker:ignore () AppVeyor CICD Deno EditorConfig Veyor chglog deps gitattributes maint markdownlint prettierignore repo tmpdir typeof -->
<!-- spellchecker:ignore (names) ChocolateBoy rivy -->
<!-- specific mis-spelled commits--><!-- spell-checker:ignoreRegexp ^.*/cfb3467c82.*$ -->

# CHANGELOG <br/> [xdg-portable](https://github.com/rivy/js.xdg-portable)

---

## [v7.3.0](https://github.com/rivy/js.xdg-portable/compare/v7.2.2...v7.3.0) <small>(2020-12-15)</small>

#### Fixes

* fix ~ remove erroneous devDependency ('fs[@0](https://github.com/0).0.1-security') &ac; [`de6b7e8`](https://github.com/rivy/js.xdg-portable/commit/de6b7e84d9134403dd88e803b074e5f5ff86ceb7)
* fix test type declarations for `configDirs()` and `dataDirs()` &ac; [`35c5691`](https://github.com/rivy/js.xdg-portable/commit/35c5691b07bfaa2187723d0b027983746ed0daee)

#### Documentation

* docs ~ add CHANGELOG spell-checker exceptions &ac; [`e4a51e3`](https://github.com/rivy/js.xdg-portable/commit/e4a51e37fde25fc293604f09f0ff974c174b6691)
* docs ~ README polish &ac; [`c017d0b`](https://github.com/rivy/js.xdg-portable/commit/c017d0bde7086848e9facb45ad38d975ee5794c6)
* docs ~ correct spell-check errors &ac; [`37a31bd`](https://github.com/rivy/js.xdg-portable/commit/37a31bd29580d6fad67d70e483a532b419a52a42)
* docs ~ simplify examples (removing extra developer deps) &ac; [`3000c70`](https://github.com/rivy/js.xdg-portable/commit/3000c70b3b70223eeea6c25a5f0ad7ea7ac43763)

#### Maintenance

* maint ~ reconfigure for `git-changelog` (from GH:rivy-go) &ac; [`d3bd66d`](https://github.com/rivy/js.xdg-portable/commit/d3bd66d8f8cdf35f7ff10e94bba1d5476b917274)
* maint/**CICD**: add GitHub Actions (GHA) CI &ac; [`a292f58`](https://github.com/rivy/js.xdg-portable/commit/a292f58184fb2a73366dad14ac37ad8f1c5837f0)
* maint/**build**: add CHANGELOG.mkd to distribution file list &ac; [`f187500`](https://github.com/rivy/js.xdg-portable/commit/f187500f098d6a877122697a4dc3424211e305b1)
* maint/**build**: update CHANGELOG template with markdown-lint and spell-checker signals &ac; [`cfa2185`](https://github.com/rivy/js.xdg-portable/commit/cfa21854c344f2622c44a9c2cab595007204ff6b)
* maint/**build**: polish package.json formatting &ac; [`e81e8f3`](https://github.com/rivy/js.xdg-portable/commit/e81e8f3e220f3adc0ec56eeb5532212241d5372c)
* maint/**build**: reorganize 'package.json' &ac; [`ff7988a`](https://github.com/rivy/js.xdg-portable/commit/ff7988a805fdb94703b972ba9855b958828af1b1)
* maint/**build**: refine package manifest &ac; [`f5acb16`](https://github.com/rivy/js.xdg-portable/commit/f5acb16743fee411991686a6b6a51e0cba031495)
* maint/**build**: revise and polish npm scripts &ac; [`c6ed305`](https://github.com/rivy/js.xdg-portable/commit/c6ed3058200120a218cb8515005807cfb28066e4)
* maint/**build**: add explanation for NPMrc `package-lock=false` &ac; [`570f464`](https://github.com/rivy/js.xdg-portable/commit/570f4649604751ffbbe1037f41ad904534cb0a4e)
* maint/**build**: update EditorConfig (include more file types and commentary) &ac; [`6428093`](https://github.com/rivy/js.xdg-portable/commit/6428093b199034d3c10ebf123ddd8c68e55a83be)
* maint/**build**: expand/polish `npm run ...` scripts (with dep updates) &ac; [`26f6c52`](https://github.com/rivy/js.xdg-portable/commit/26f6c52e4b073dd332897a5d03cc0cff51dc0316)
* maint/**build**: fix `tsd` complaint (types specification missing from package "files" list) &ac; [`5570547`](https://github.com/rivy/js.xdg-portable/commit/55705473c69c29569dfba5dafd3cf7469bca5f9d)
* maint/**build**: fix package keywords &ac; [`446854f`](https://github.com/rivy/js.xdg-portable/commit/446854f1f7063ddb39e35740a0f0349d3dee16a7)
* maint/**dev**: update 'eslintrc.js' configuration file &ac; [`601de10`](https://github.com/rivy/js.xdg-portable/commit/601de1037886c6d33dee7a186a583415b87bc5b9)
* maint/**dev**: remove XO (`xo`) &ac; [`4d47f8a`](https://github.com/rivy/js.xdg-portable/commit/4d47f8a0e30fc3491c28185b2bb9b3451d576da5)
* maint/**dev**: revise gitignore files to include build artifacts &ac; [`8f9a93e`](https://github.com/rivy/js.xdg-portable/commit/8f9a93ee4bdeeb322de4bb9ad474fbb9a152cb26)
* maint/**dev**: revise gitattributes &ac; [`a291753`](https://github.com/rivy/js.xdg-portable/commit/a2917534a2d4e13aca12c63010dfdc7f38fe6ae1)
* maint/**dev**: fix ESLint configuration file format (JSON => JS) &ac; [`b06aa7c`](https://github.com/rivy/js.xdg-portable/commit/b06aa7c7c77012ab38c4856c821c0d238ad3b05b)
* maint/**dev**: add Prettier (`prettier`) &ac; [`506493b`](https://github.com/rivy/js.xdg-portable/commit/506493bc2f50b16bcdfeae44d346b8bfdd8437db)
* maint/**dev**: add Prettier configuration &ac; [`70b003b`](https://github.com/rivy/js.xdg-portable/commit/70b003b25540a525b7a189394bbd0f54a015f08d)
* maint/**dev**: add Prettier ignore file (to simplify automation) &ac; [`1c13258`](https://github.com/rivy/js.xdg-portable/commit/1c13258aa49f30684b67e5e0e6790a9e394da895)
* maint/**dev**: add notation about `ava` and `nyc` version restrictions with NodeJS-v6 &ac; [`18567d0`](https://github.com/rivy/js.xdg-portable/commit/18567d0062a9256cc8016ad883d06d2aa5120809)
* maint/**dev**: add VSCode settings (ENABLE auto-format on save) &ac; [`1b42a25`](https://github.com/rivy/js.xdg-portable/commit/1b42a25f25138e310e1651ffec0ec0bb5b0e93c3)
* maint/**dev**: add .history (for VSCode plugin) to .gitignore &ac; [`65b33ee`](https://github.com/rivy/js.xdg-portable/commit/65b33eedfdc106d81576d652693fbe7cb668f833)
* maint/**dev**: add '.history' (used by VSCode extension) to .prettierignore &ac; [`792b967`](https://github.com/rivy/js.xdg-portable/commit/792b9677dfa0828a0a0a85216eb55102063ea2f0)
* maint/**dev**: npm script polish &ac; [`0e5ddc0`](https://github.com/rivy/js.xdg-portable/commit/0e5ddc031be944d2e29014ee0e42d87ac21a8e88)
* maint/**dev**: add ESLint (`eslint`) &ac; [`e689bde`](https://github.com/rivy/js.xdg-portable/commit/e689bded7915c7c263e843867ce8aece0b3eb379)

#### Refactoring

* refactor ~ consolidate source code into 'src' directory &ac; [`d4e4ba5`](https://github.com/rivy/js.xdg-portable/commit/d4e4ba53a689168a7750b6f5887ad7bf2076df86)
* refactor ~ consolidate testing code into 'test' directory &ac; [`cdabf1c`](https://github.com/rivy/js.xdg-portable/commit/cdabf1cdd0481e15dc6d2c52a65e1dd2fcca84fe)
* refactor/**polish**: fix ESLint complaints &ac; [`da3bedb`](https://github.com/rivy/js.xdg-portable/commit/da3bedba1b8c9fa78c5d408b3f1b8d60eef8d68f)
* refactor/**polish**: `npx prettier . --write` re-format &ac; [`a4fe2f4`](https://github.com/rivy/js.xdg-portable/commit/a4fe2f4c14e4899352dbe194bd063a79756bc1e9)

---

## [v7.2.2](https://github.com/rivy/js.xdg-portable/compare/v7.2.1...v7.2.2) <small>(2020-09-02)</small>

#### Documentation

* docs ~ update module keywords &ac; [`2959218`](https://github.com/rivy/js.xdg-portable/commit/2959218f2e0e424220b709c69c7e44288f1e6302)

---

## [v7.2.1](https://github.com/rivy/js.xdg-portable/compare/v7.2.0...v7.2.1) <small>(2019-10-04)</small>

#### Maintenance

* maint/**build**: fix missing dev dependencies &ac; [`4ba5ac4`](https://github.com/rivy/js.xdg-portable/commit/4ba5ac454a37e431168e515719bd89624b5c2e51)

---

## [v7.2.0](https://github.com/rivy/js.xdg-portable/compare/v7.1.0...v7.2.0) <small>(2019-10-04)</small>

#### Documentation

* docs ~ update README badges &ac; [`8d2ca20`](https://github.com/rivy/js.xdg-portable/commit/8d2ca209561d7640ba0cafc49d2c858c2b93dd39)
* docs ~ add CHANGELOG &ac; [`14f2fe6`](https://github.com/rivy/js.xdg-portable/commit/14f2fe631e99509f911c7191fad63b5f4c9c438d)

#### Maintenance

* maint/**CI**: add testing for NodeJS v6 &ac; [`be98a00`](https://github.com/rivy/js.xdg-portable/commit/be98a008a056aecb7a59305a20df3bd83dcb5939)
* maint/**build**: refactor lint/test run-scripts &ac; [`5b5b151`](https://github.com/rivy/js.xdg-portable/commit/5b5b151ca96df1b4430b7a041682d42af8dddf2f)
* maint/**build**: add CHANGELOG (using `git-chglog`) configuration &ac; [`f4b46e1`](https://github.com/rivy/js.xdg-portable/commit/f4b46e176d28e5662d0984bbbbf5d0c4831d8808)

#### Refactoring

* refactor ~ support module use back to NodeJS v6 &ac; [`ff43b02`](https://github.com/rivy/js.xdg-portable/commit/ff43b022ec13aac218feac12dcac8e7f907d4ffd)

---

## [v7.1.0](https://github.com/rivy/js.xdg-portable/compare/v7.0.3...v7.1.0) <small>(2019-08-18)</small>

#### Fixes

* fix typescript definitions and testing &ac; [`8c3c6d8`](https://github.com/rivy/js.xdg-portable/commit/8c3c6d820e4471d4a282570b500a319b04802acf)

#### Documentation

* docs ~ fix broken CI README badges by pointing to repo master branch &ac; [`8af5009`](https://github.com/rivy/js.xdg-portable/commit/8af500999bc71f7676868fe9a44dbf7ff0d7a1e4)

---

## [v7.0.3](https://github.com/rivy/js.xdg-portable/compare/v7.0.2...v7.0.3) <small>(2019-07-28)</small>

#### Documentation

* docs ~ fix README usage example &ac; [`963fcd4`](https://github.com/rivy/js.xdg-portable/commit/963fcd4067b04a964428866153018232909880d1)

---

## [v7.0.2](https://github.com/rivy/js.xdg-portable/compare/v7.0.1...v7.0.2) <small>(2019-07-28)</small>

#### Documentation

* docs ~ add example with more object detail &ac; [`338ca45`](https://github.com/rivy/js.xdg-portable/commit/338ca45876ff7dcd465552c6a36b60c1126eae81)
* docs ~ polish README &ac; [`5e76c92`](https://github.com/rivy/js.xdg-portable/commit/5e76c925542968e006964a1259e36dfe2989e1e2)

#### Maintenance

* maint ~ add alternate construction test &ac; [`980f267`](https://github.com/rivy/js.xdg-portable/commit/980f2671bec0538c7a665d67c6983dffb657289d)

#### Refactoring

* refactor ~ clean up internal naming &ac; [`68100c0`](https://github.com/rivy/js.xdg-portable/commit/68100c0d1956827624b45ad6d7d537b6331bcf29)

---

## [v7.0.1](https://github.com/rivy/js.xdg-portable/compare/v7.0.0...v7.0.1) <small>(2019-07-27)</small>

#### Documentation

* docs ~ fix and polish README &ac; [`8da6270`](https://github.com/rivy/js.xdg-portable/commit/8da627034f70beb9b0de88f304bd502380e7782d)

#### Maintenance

* maint ~ add OSX CI testing &ac; [`a80dc4c`](https://github.com/rivy/js.xdg-portable/commit/a80dc4ceab25fd4e2153994f25d9c359d49625e5)
* maint ~ improve code coverage testing and reporting &ac; [`89b2655`](https://github.com/rivy/js.xdg-portable/commit/89b2655d5f1e05c6a93ec4ecd79984257eb6b9d8)

#### Refactoring

* refactor platform testing code &ac; [`d34f19f`](https://github.com/rivy/js.xdg-portable/commit/d34f19f7522fbcca04bb2497a037fe1393a81589)

---

## [v7.0.0](https://github.com/rivy/js.xdg-portable/compare/v6.0.1...v7.0.0) <small>(2019-07-20)</small>

#### Changes

* add npm `cover` script &ac; [`8764397`](https://github.com/rivy/js.xdg-portable/commit/8764397756ad72ee41cd7788c52e15ba6541a177)
* add AppVeyor CI &ac; [`8c7741a`](https://github.com/rivy/js.xdg-portable/commit/8c7741a8166a83af47230c29581867765c1f102b)
* change from property to method interface &ac; [`7b29508`](https://github.com/rivy/js.xdg-portable/commit/7b29508a0f6500a1c8936ada73275411ccb8fea4)
* change ~ remove '.default' export &ac; [`655453f`](https://github.com/rivy/js.xdg-portable/commit/655453f78ad7b06cdc04df2cc41fc567bb5d8e7a)

#### Documentation

* docs ~ add/update README badges &ac; [`4f03c45`](https://github.com/rivy/js.xdg-portable/commit/4f03c454c6a204660873e1c2cacbbe583484af9e)
* docs ~ README update &ac; [`58133d6`](https://github.com/rivy/js.xdg-portable/commit/58133d6c52e7baef5f15c91cdac4489d90e98fd6)

#### Refactoring

* refactor ~ hoist common code from platforms into main module &ac; [`90aa8bc`](https://github.com/rivy/js.xdg-portable/commit/90aa8bce46e71742196f23c5805aa7317a8cb32c)
* refactor ~ improve tests &ac; [`5cb8616`](https://github.com/rivy/js.xdg-portable/commit/5cb86161ab7f95e9f38f35612168199e537cb988)

---

## [v6.0.1](https://github.com/rivy/js.xdg-portable/compare/v6.0.0...v6.0.1) <small>(2019-06-29)</small>

#### Fixes

* fix os.tmpdir() fallback logic &ac; [`c1ee2ea`](https://github.com/rivy/js.xdg-portable/commit/c1ee2ea2e8c8309ae929893e60dc6da4b8fcfbaf)

---

## [v6.0.0](https://github.com/rivy/js.xdg-portable/compare/v4.0.0...v6.0.0) <small>(2019-06-29)</small>

#### Changes

* add eslint support &ac; [`f91b369`](https://github.com/rivy/js.xdg-portable/commit/f91b36991658ae53f35cca4858f354bcbf9e4fc7)
* add os.tmpdir() as a fallback for os.homedir() &ac; [`47cb028`](https://github.com/rivy/js.xdg-portable/commit/47cb028436a80dd85a6cd1e3f509166a8104de57)
* change ~ cleanup type info and tests &ac; [`6bbd9f3`](https://github.com/rivy/js.xdg-portable/commit/6bbd9f307d86c42e15d3501c8f1810a0e2a282c8)
* add STATE directory support &ac; [`1023d63`](https://github.com/rivy/js.xdg-portable/commit/1023d638b3c55b4be4ce1cde8259b4324f907776)
* add example &ac; [`189b29e`](https://github.com/rivy/js.xdg-portable/commit/189b29e41356482c30a0d601f1aa651758975f0b)
* add cross-platform compatiblity &ac; [`cfb3467`](https://github.com/rivy/js.xdg-portable/commit/cfb3467c82e725366c854c578c31d47fe2b0a0f2)

#### Documentation

* docs ~ polish README &ac; [`d678235`](https://github.com/rivy/js.xdg-portable/commit/d67823528a8136bccec723465df99fd830f01db2)
* docs ~ update description and README &ac; [`8e11070`](https://github.com/rivy/js.xdg-portable/commit/8e11070c5bb304bad5e36fe8fc6c8cd87326b74c)

#### Refactoring

* refactor ~ fix lint warnings &ac; [`15555e1`](https://github.com/rivy/js.xdg-portable/commit/15555e16e732e8698b02812fbd3c44b47d42e67b)
* refactor ~ reorder tests &ac; [`dc035e5`](https://github.com/rivy/js.xdg-portable/commit/dc035e5278cf25479d45959dcc28a65d9d34eb5c)
* refactor ~ reorder/sort function definitions &ac; [`52ef262`](https://github.com/rivy/js.xdg-portable/commit/52ef2621f36f523c06b43ee05a29f5a232bdcd63)

---

## [v4.0.0](https://github.com/rivy/js.xdg-portable/compare/v3.0.0...v4.0.0) <small>(2019-04-30)</small>

*No changelog for this release.*

---

## [v3.0.0](https://github.com/rivy/js.xdg-portable/compare/v2.0.0...v3.0.0) <small>(2017-02-13)</small>

#### Dependency Updates

* update tests for latest AVA version &ac; [`85a4aaa`](https://github.com/rivy/js.xdg-portable/commit/85a4aaa0d9ebb91be2f7a7c608c0e03c93b20afe)

---

## [v2.0.0](https://github.com/rivy/js.xdg-portable/compare/v1.0.1...v2.0.0) <small>(2015-06-13)</small>

#### Fixes

* fix XDG_CACHE_HOME + tests &ac; [`d75b14d`](https://github.com/rivy/js.xdg-portable/commit/d75b14d0055ab19e435872ba92c4169284d9042d)

#### Dependency Updates

* Update .travis.yml &ac; [`cd4a8b3`](https://github.com/rivy/js.xdg-portable/commit/cd4a8b3ddb5dfa76bc0b827ef9c8b9fd92dd23e4)

#### Pull Requests

* Merge pull request [#1](https://github.com/rivy/js.xdg-portable/issues/1) from chocolateboy/fix_xdg_cache_home_and_tests

---

## [v1.0.1](https://github.com/rivy/js.xdg-portable/compare/v1.0.0...v1.0.1) <small>(2015-01-14)</small>

*No changelog for this release.*

---

## v1.0.0 <small>(2014-10-06)</small>

*No changelog for this release.*
