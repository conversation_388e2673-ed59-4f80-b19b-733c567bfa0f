{"name": "@vercel/gatsby-plugin-vercel-builder", "version": "2.0.36", "main": "dist/index.js", "files": ["dist", "templates", "gatsby-node.js"], "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/gatsby-plugin-vercel-builder"}, "dependencies": {"@sinclair/typebox": "0.25.24", "@vercel/build-utils": "8.3.2", "@vercel/routing-utils": "3.1.0", "esbuild": "0.14.47", "etag": "1.8.1", "fs-extra": "11.1.0"}, "devDependencies": {"@types/etag": "1.8.0", "@types/fs-extra": "11.0.1", "@types/jest": "27.5.1", "@types/node": "14.18.33", "@types/react": "18.0.26", "jest-junit": "16.0.0", "typescript": "4.9.5"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --bail --runInBand", "test-unit": "pnpm test test/unit.*test.*", "type-check": "tsc --noEmit"}}