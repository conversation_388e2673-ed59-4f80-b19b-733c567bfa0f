{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import util from 'node:util';\n\nexport interface SpawnError extends NodeJS.ErrnoException {\n  spawnargs: string[];\n}\n\n/**\n * A simple type guard for objects.\n *\n * @param obj - A possible object\n */\nexport const isObject = (obj: unknown): obj is Record<string, unknown> =>\n  typeof obj === 'object' && obj !== null;\n\n/**\n * A type guard for `try...catch` errors.\n * @deprecated use `require('node:util').types.isNativeError(error)` instead\n */\nexport const isError = (error: unknown): error is Error => {\n  return util.types.isNativeError(error);\n};\n\nexport const isErrnoException = (\n  error: unknown\n): error is NodeJS.ErrnoException => {\n  return isError(error) && 'code' in error;\n};\n\ninterface ErrorLike {\n  message: string;\n  name?: string;\n  stack?: string;\n}\n\n/**\n * A type guard for error-like objects.\n */\nexport const isErrorLike = (error: unknown): error is ErrorLike =>\n  isObject(error) && 'message' in error;\n\n/**\n * Parses errors to string, useful for getting the error message in a\n * `try...catch` statement.\n */\nexport const errorToString = (error: unknown, fallback?: string): string => {\n  if (isError(error) || isErrorLike(error)) return error.message;\n\n  if (typeof error === 'string') return error;\n\n  return fallback ?? 'An unknown error has ocurred.';\n};\n\n/**\n * Normalizes unknown errors to the Error type, useful for working with errors\n * in a `try...catch` statement.\n */\nexport const normalizeError = (error: unknown): Error => {\n  if (isError(error)) return error;\n\n  const errorMessage = errorToString(error);\n\n  // Copy over additional properties if the object is error-like.\n  return isErrorLike(error)\n    ? Object.assign(new Error(errorMessage), error)\n    : new Error(errorMessage);\n};\n\nexport function isSpawnError(v: unknown): v is SpawnError {\n  return isErrnoException(v) && 'spawnargs' in v;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAiB;AAWV,MAAM,WAAW,CAAC,QACvB,OAAO,QAAQ,YAAY,QAAQ;AAM9B,MAAM,UAAU,CAAC,UAAmC;AACzD,SAAO,iBAAAA,QAAK,MAAM,cAAc,KAAK;AACvC;AAEO,MAAM,mBAAmB,CAC9B,UACmC;AACnC,SAAO,QAAQ,KAAK,KAAK,UAAU;AACrC;AAWO,MAAM,cAAc,CAAC,UAC1B,SAAS,KAAK,KAAK,aAAa;AAM3B,MAAM,gBAAgB,CAAC,OAAgB,aAA8B;AAC1E,MAAI,QAAQ,KAAK,KAAK,YAAY,KAAK;AAAG,WAAO,MAAM;AAEvD,MAAI,OAAO,UAAU;AAAU,WAAO;AAEtC,SAAO,YAAY;AACrB;AAMO,MAAM,iBAAiB,CAAC,UAA0B;AACvD,MAAI,QAAQ,KAAK;AAAG,WAAO;AAE3B,QAAM,eAAe,cAAc,KAAK;AAGxC,SAAO,YAAY,KAAK,IACpB,OAAO,OAAO,IAAI,MAAM,YAAY,GAAG,KAAK,IAC5C,IAAI,MAAM,YAAY;AAC5B;AAEO,SAAS,aAAa,GAA6B;AACxD,SAAO,iBAAiB,CAAC,KAAK,eAAe;AAC/C;", "names": ["util"]}