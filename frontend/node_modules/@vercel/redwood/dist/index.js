"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  build: () => build,
  prepareCache: () => prepareCache,
  version: () => version
});
module.exports = __toCommonJS(src_exports);
var import_path = require("path");
var import_ts_morph = require("ts-morph");
var import_static_config = require("@vercel/static-config");
var import_fs = require("fs");
var import_semver = require("semver");
var import_build_utils = require("@vercel/build-utils");
var import_nft = require("@vercel/nft");
var import_routing_utils = require("@vercel/routing-utils");
var version = 2;
var build = async ({
  workPath,
  files,
  entrypoint,
  meta = {},
  config = {}
}) => {
  await (0, import_build_utils.download)(files, workPath, meta);
  const prefixedEnvs = (0, import_build_utils.getPrefixedEnvVars)({
    envPrefix: "REDWOOD_ENV_",
    envs: process.env
  });
  for (const [key, value] of Object.entries(prefixedEnvs)) {
    process.env[key] = value;
  }
  const { installCommand, buildCommand } = config;
  const mountpoint = (0, import_path.dirname)(entrypoint);
  const entrypointFsDirname = (0, import_path.join)(workPath, mountpoint);
  const nodeVersion = await (0, import_build_utils.getNodeVersion)(
    entrypointFsDirname,
    void 0,
    config,
    meta
  );
  const spawnOpts = (0, import_build_utils.getSpawnOptions)(meta, nodeVersion);
  if (!spawnOpts.env) {
    spawnOpts.env = {};
  }
  const { cliType, lockfileVersion, packageJson } = await (0, import_build_utils.scanParentDirs)(
    entrypointFsDirname,
    true
  );
  spawnOpts.env = (0, import_build_utils.getEnvForPackageManager)({
    cliType,
    lockfileVersion,
    packageJsonPackageManager: packageJson?.packageManager,
    nodeVersion,
    env: spawnOpts.env || {}
  });
  if (typeof installCommand === "string") {
    if (installCommand.trim()) {
      console.log(`Running "install" command: \`${installCommand}\`...`);
      await (0, import_build_utils.execCommand)(installCommand, {
        ...spawnOpts,
        cwd: entrypointFsDirname
      });
    } else {
      console.log(`Skipping "install" command...`);
    }
  } else {
    await (0, import_build_utils.runNpmInstall)(entrypointFsDirname, [], spawnOpts, meta, nodeVersion);
  }
  if (meta.isDev) {
    throw new Error("Detected `@vercel/redwood` dev but this is not supported");
  }
  const pkg = await (0, import_build_utils.readConfigFile)((0, import_path.join)(workPath, "package.json"));
  const toml = await (0, import_build_utils.readConfigFile)(
    (0, import_path.join)(workPath, "redwood.toml")
  );
  if (buildCommand) {
    (0, import_build_utils.debug)(`Executing build command "${buildCommand}"`);
    await (0, import_build_utils.execCommand)(buildCommand, {
      ...spawnOpts,
      cwd: workPath
    });
  } else if (hasScript("vercel-build", pkg)) {
    (0, import_build_utils.debug)(`Executing "yarn vercel-build"`);
    await (0, import_build_utils.runPackageJsonScript)(workPath, "vercel-build", spawnOpts);
  } else if (hasScript("build", pkg)) {
    (0, import_build_utils.debug)(`Executing "yarn build"`);
    await (0, import_build_utils.runPackageJsonScript)(workPath, "build", spawnOpts);
  } else {
    const { devDependencies = {} } = pkg || {};
    const versionRange = devDependencies["@redwoodjs/core"];
    let cmd;
    if (!versionRange || !(0, import_semver.validRange)(versionRange)) {
      console.log(
        "WARNING: Unable to detect RedwoodJS version in package.json devDependencies"
      );
      cmd = "yarn rw deploy vercel";
    } else if ((0, import_semver.intersects)(versionRange, "<0.25.0")) {
      cmd = "yarn rw build && yarn rw db up --no-db-client --auto-approve && yarn rw dataMigrate up";
    } else {
      cmd = "yarn rw deploy vercel";
    }
    await (0, import_build_utils.execCommand)(cmd, {
      ...spawnOpts,
      cwd: workPath
    });
  }
  const apiDir = toml?.web?.apiProxyPath?.replace(/^\//, "") ?? "api";
  const apiDistPath = (0, import_path.join)(workPath, "api", "dist", "functions");
  const webDistPath = (0, import_path.join)(workPath, "web", "dist");
  const lambdaOutputs = {};
  const webDistFiles = await (0, import_build_utils.glob)("**", webDistPath);
  const staticOutputs = {};
  for (const [fileName, fileFsRef] of Object.entries(webDistFiles)) {
    const parsedPath = (0, import_path.parse)(fileFsRef.fsPath);
    if (parsedPath.ext !== ".html") {
      staticOutputs[fileName] = fileFsRef;
    } else {
      const fileNameWithoutExtension = (0, import_path.basename)(fileName, ".html");
      const pathWithoutHtmlExtension = (0, import_path.join)(
        parsedPath.dir,
        fileNameWithoutExtension
      );
      fileFsRef.contentType = "text/html; charset=utf-8";
      staticOutputs[(0, import_path.relative)(webDistPath, pathWithoutHtmlExtension)] = fileFsRef;
    }
  }
  const functionFiles = await (0, import_build_utils.glob)("**/*.{js,ts}", apiDistPath);
  const sourceCache = /* @__PURE__ */ new Map();
  const fsCache = /* @__PURE__ */ new Map();
  const project = new import_ts_morph.Project();
  for (const [funcName, fileFsRef] of Object.entries(functionFiles)) {
    const outputName = (0, import_path.join)(apiDir, (0, import_path.parse)(funcName).name);
    const absEntrypoint = fileFsRef.fsPath;
    const relativeEntrypoint = (0, import_path.relative)(workPath, absEntrypoint);
    const awsLambdaHandler = getAWSLambdaHandler(relativeEntrypoint, "handler");
    let sourceFile = relativeEntrypoint.replace("/dist/", "/src/");
    const sourceFileBase = (0, import_path.basename)(sourceFile, ".js");
    const sourceFileDir = (0, import_path.dirname)(sourceFile);
    if ((0, import_fs.existsSync)((0, import_path.join)(sourceFileDir, `${sourceFileBase}.ts`))) {
      sourceFile = (0, import_path.join)(sourceFileDir, `${sourceFileBase}.ts`);
    }
    const { fileList, esmFileList, warnings } = await (0, import_nft.nodeFileTrace)(
      [absEntrypoint],
      {
        base: workPath,
        processCwd: workPath,
        ts: true,
        mixedModules: true,
        ignore: config.excludeFiles,
        async readFile(fsPath) {
          const relPath = (0, import_path.relative)(workPath, fsPath);
          const cached = sourceCache.get(relPath);
          if (cached)
            return cached.toString();
          if (cached === null)
            return null;
          try {
            const source = (0, import_fs.readFileSync)(fsPath);
            const { mode } = (0, import_fs.lstatSync)(fsPath);
            let entry;
            if ((0, import_build_utils.isSymbolicLink)(mode)) {
              entry = new import_build_utils.FileFsRef({ fsPath, mode });
            } else {
              entry = new import_build_utils.FileBlob({ data: source, mode });
            }
            fsCache.set(relPath, entry);
            sourceCache.set(relPath, source);
            return source.toString();
          } catch (e) {
            if (e.code === "ENOENT" || e.code === "EISDIR") {
              sourceCache.set(relPath, null);
              return null;
            }
            throw e;
          }
        }
      }
    );
    for (const warning of warnings) {
      (0, import_build_utils.debug)(`Warning from trace: ${warning.message}`);
    }
    const lambdaFiles = {};
    const allFiles = [...fileList, ...esmFileList];
    for (const filePath of allFiles) {
      lambdaFiles[filePath] = await import_build_utils.FileFsRef.fromFsPath({
        fsPath: (0, import_path.join)(workPath, filePath)
      });
    }
    lambdaFiles[(0, import_path.relative)(workPath, fileFsRef.fsPath)] = fileFsRef;
    const staticConfig = (0, import_static_config.getConfig)(project, sourceFile);
    const regions = staticConfig?.regions;
    if (regions && !Array.isArray(regions)) {
      throw new Error('"regions" configuration must be an array');
    }
    const lambda = new import_build_utils.NodejsLambda({
      ...staticConfig,
      regions,
      files: lambdaFiles,
      handler: relativeEntrypoint,
      runtime: nodeVersion.runtime,
      shouldAddHelpers: false,
      shouldAddSourcemapSupport: false,
      awsLambdaHandler
    });
    lambdaOutputs[outputName] = lambda;
  }
  const fallbackHtmlPage = (0, import_fs.existsSync)((0, import_path.join)(webDistPath, "200.html")) ? "/200" : "/index";
  const defaultRoutesConfig = (0, import_routing_utils.getTransformedRoutes)({
    // this makes sure we send back 200.html for unprerendered pages
    rewrites: [{ source: "/(.*)", destination: fallbackHtmlPage }],
    cleanUrls: true,
    trailingSlash: false
  });
  if (defaultRoutesConfig.error) {
    throw new Error(defaultRoutesConfig.error.message);
  }
  return {
    output: { ...staticOutputs, ...lambdaOutputs },
    routes: defaultRoutesConfig.routes
  };
};
function getAWSLambdaHandler(filePath, handlerName) {
  const { dir, name } = (0, import_path.parse)(filePath);
  return `${dir}${dir ? import_path.sep : ""}${name}.${handlerName}`;
}
function hasScript(scriptName, pkg) {
  const scripts = pkg && pkg.scripts || {};
  return typeof scripts[scriptName] === "string";
}
var prepareCache = ({ repoRootPath, workPath }) => {
  return (0, import_build_utils.glob)("**/node_modules/**", repoRootPath || workPath);
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  build,
  prepareCache,
  version
});
