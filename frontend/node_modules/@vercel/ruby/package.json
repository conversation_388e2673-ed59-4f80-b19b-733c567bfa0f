{"name": "@vercel/ruby", "author": "<PERSON> <<EMAIL>>", "version": "2.1.0", "license": "Apache-2.0", "main": "./dist/index", "homepage": "https://vercel.com/docs/runtimes#official-runtimes/ruby", "files": ["dist", "vc_init.rb"], "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/ruby"}, "devDependencies": {"@types/fs-extra": "8.0.0", "@types/semver": "6.0.0", "@types/which": "3.0.0", "@vercel/build-utils": "8.0.0", "execa": "2.0.4", "fs-extra": "^7.0.1", "jest-junit": "16.0.0", "semver": "6.3.1", "which": "3.0.0"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --runInBand --bail", "test-e2e": "pnpm test", "type-check": "tsc --noEmit"}}