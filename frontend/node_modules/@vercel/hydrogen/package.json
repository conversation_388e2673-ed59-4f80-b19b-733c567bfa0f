{"name": "@vercel/hydrogen", "version": "1.0.2", "license": "Apache-2.0", "main": "./dist/index.js", "homepage": "https://vercel.com/docs", "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/hydrogen"}, "files": ["dist", "edge-entry.js"], "dependencies": {"@vercel/static-config": "3.0.0", "ts-morph": "12.0.0"}, "devDependencies": {"@types/jest": "27.5.1", "@types/node": "14.18.33", "@vercel/build-utils": "7.5.0", "execa": "3.2.0", "fs-extra": "11.1.0", "jest-junit": "16.0.0"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "test-e2e": "pnpm test test/test.js", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --bail --runInBand", "type-check": "tsc --noEmit"}}