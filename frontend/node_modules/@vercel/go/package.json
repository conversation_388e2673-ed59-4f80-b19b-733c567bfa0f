{"name": "@vercel/go", "version": "3.1.1", "license": "Apache-2.0", "main": "./dist/index", "homepage": "https://vercel.com/docs/runtimes#official-runtimes/go", "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/go"}, "files": ["dist", "*.go"], "devDependencies": {"@tootallnate/once": "1.1.2", "@types/async-retry": "1.4.5", "@types/execa": "^0.9.0", "@types/fs-extra": "^5.0.5", "@types/jest": "28.1.6", "@types/node": "14.18.33", "@types/node-fetch": "^2.3.0", "@types/tar": "6.1.5", "@types/yauzl-promise": "2.1.0", "@vercel/build-utils": "7.11.0", "async-retry": "1.3.3", "execa": "^1.0.0", "fs-extra": "^7.0.0", "jest-junit": "16.0.0", "node-fetch": "^2.2.1", "string-argv": "0.3.1", "tar": "6.2.0", "typescript": "4.3.4", "xdg-app-paths": "5.1.0", "yauzl-promise": "2.1.3"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --runInBand --bail", "test-e2e": "pnpm test", "type-check": "tsc --noEmit"}}