"use strict";
process.chdir(__dirname);
const region = process.env.VERCEL_REGION || process.env.NOW_REGION;
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = region === "dev1" ? "development" : "production";
}
if (process.env.NODE_ENV !== "production" && region !== "dev1") {
  console.warn(
    `Warning: NODE_ENV was incorrectly set to "${process.env.NODE_ENV}", this value is being overridden to "production"`
  );
  process.env.NODE_ENV = "production";
}
// @preserve pre-next-server-target
const NextServer = require("__NEXT_SERVER_PATH__").default;
// @preserve next-server-preload-target
const conf = __NEXT_CONFIG__;
const nextServer = new NextServer({
  conf,
  dir: ".",
  minimalMode: true,
  customServer: false
});
const serve = (handler) => async (req, res) => {
  try {
    // @preserve entryDirectory handler
    await handler(req, res);
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
};
module.exports = serve(nextServer.getRequestHandler());
if (conf.experimental?.ppr && "getRequestHandlerWithMetadata" in nextServer && typeof nextServer.getRequestHandlerWithMetadata === "function") {
  module.exports.getRequestHandlerWithMetadata = (metadata) => serve(nextServer.getRequestHandlerWithMetadata(metadata));
}
if (process.env.NEXT_PRIVATE_PRELOAD_ENTRIES) {
  module.exports.preload = nextServer.unstable_preloadEntries.bind(nextServer);
}
