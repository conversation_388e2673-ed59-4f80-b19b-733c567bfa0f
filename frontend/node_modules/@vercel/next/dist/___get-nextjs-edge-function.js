module.exports = "var d=Object.defineProperty;var m=e=>d(e,\"__esModule\",{value:!0});var y=(e,r)=>{m(e);for(var t in r)d(e,t,{get:r[t],enumerable:!0})};y(exports,{default:()=>x});function h(e){let r={};return e&&e.forEach((t,i)=>{r[i]=t,i.toLowerCase()===\"set-cookie\"&&(r[i]=C(t))}),r}function C(e){let r=[],t=0,i,a,p,n,s;function c(){for(;t<e.length&&/\\s/.test(e.charAt(t));)t+=1;return t<e.length}function o(){return a=e.charAt(t),a!==\"=\"&&a!==\";\"&&a!==\",\"}for(;t<e.length;){for(i=t,s=!1;c();)if(a=e.charAt(t),a===\",\"){for(p=t,t+=1,c(),n=t;t<e.length&&o();)t+=1;t<e.length&&e.charAt(t)===\"=\"?(s=!0,t=n,r.push(e.substring(i,p)),i=t):t=p+1}else t+=1;(!s||t>=e.length)&&r.push(e.substring(i,e.length))}return r}function x(e){let r=e.staticRoutes.map(i=>({regexp:new RegExp(i.namedRegex),page:i.page})),t=e.dynamicRoutes?.map(i=>({regexp:new RegExp(i.namedRegex),page:i.page}))||[];return async function(a,p){let n=new URL(a.url).pathname,s={};if(e.nextConfig?.basePath&&n.startsWith(e.nextConfig.basePath)&&(n=n.replace(e.nextConfig.basePath,\"\")||\"/\"),e.nextConfig?.i18n)for(let o of e.nextConfig.i18n.locales){let g=new RegExp(`^/${o}($|/)`,\"i\");if(n.match(g)){n=n.replace(g,\"/\")||\"/\";break}}for(let o of r)if(o.regexp.exec(n)){s.name=o.page;break}if(!s.name){let o=R(n);for(let g of t||[]){if(o&&!R(g.page))continue;let f=g.regexp.exec(n);if(f){s={name:g.page,params:f.groups};break}}}let c=await _ENTRIES[`middleware_${e.name}`].default.call({},{request:{url:a.url,method:a.method,headers:h(a.headers),ip:u(a.headers,l.Ip),geo:{city:u(a.headers,l.City,!0),country:u(a.headers,l.Country,!0),latitude:u(a.headers,l.Latitude),longitude:u(a.headers,l.Longitude),region:u(a.headers,l.Region,!0)},nextConfig:e.nextConfig,page:s,body:a.body}});return p.waitUntil(c.waitUntil),c.response}}function u(e,r,t=!1){let i=e.get(r)||void 0;return t&&i?decodeURIComponent(i):i}function R(e){return e===\"/api\"||e.startsWith(\"/api/\")}var l;(function(n){n.City=\"x-vercel-ip-city\",n.Country=\"x-vercel-ip-country\",n.Ip=\"x-real-ip\",n.Latitude=\"x-vercel-ip-latitude\",n.Longitude=\"x-vercel-ip-longitude\",n.Region=\"x-vercel-ip-country-region\"})(l||(l={}));\n"