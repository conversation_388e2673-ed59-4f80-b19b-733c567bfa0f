{"name": "@vercel/next", "version": "4.3.2", "license": "Apache-2.0", "main": "./dist/index", "homepage": "https://vercel.com/docs/runtimes#official-runtimes/next-js", "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/next"}, "files": ["dist"], "dependencies": {"@vercel/nft": "0.27.2"}, "devDependencies": {"@types/aws-lambda": "8.10.19", "@types/buffer-crc32": "0.2.0", "@types/bytes": "3.1.1", "@types/convert-source-map": "1.5.2", "@types/find-up": "4.0.0", "@types/fs-extra": "8.0.0", "@types/glob": "7.1.3", "@types/jest": "29.5.5", "@types/next-server": "8.0.0", "@types/node": "14.18.33", "@types/resolve-from": "5.0.1", "@types/semver": "6.0.0", "@types/text-table": "0.2.1", "@types/webpack-sources": "3.2.0", "@vercel/build-utils": "8.3.2", "@vercel/routing-utils": "3.1.0", "async-sema": "3.0.1", "buffer-crc32": "0.2.13", "bytes": "3.1.2", "cheerio": "1.0.0-rc.10", "convert-source-map": "1.8.0", "esbuild": "0.12.22", "escape-string-regexp": "2.0.0", "execa": "2.0.4", "find-up": "4.1.0", "fs-extra": "11.2.0", "get-port": "5.0.0", "jest-junit": "16.0.0", "nanoid": "3.3.4", "ndjson": "2.0.0", "pretty-bytes": "5.3.0", "resolve-from": "5.0.0", "semver": "6.3.1", "set-cookie-parser": "2.4.6", "source-map": "0.7.4", "test-listen": "1.1.0", "text-table": "0.2.0", "webpack-sources": "3.2.3"}, "scripts": {"build": "node build.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --bail --runInBand --testTimeout=360000", "test-unit": "pnpm test test/unit/", "test-next-local": "pnpm test test/integration/*.test.js test/integration/*.test.ts", "test-next-local-legacy": "pnpm test test/integration/legacy/*.test.js", "test-next-local:middleware": "pnpm test test/integration/middleware.test.ts", "test-e2e": "rm -f test/builder-info.json; pnpm test test/fixtures/**/*.test.js", "type-check": "tsc --noEmit"}}