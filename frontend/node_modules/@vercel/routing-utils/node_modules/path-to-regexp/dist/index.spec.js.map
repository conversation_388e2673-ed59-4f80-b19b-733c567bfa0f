{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2BAA6B;AAC7B,sCAAwC;AAiBxC;;GAEG;AACH,IAAM,KAAK,GAAW;IACpB;;OAEG;IACH;QACE,GAAG;QACH,SAAS;QACT,CAAC,GAAG,CAAC;QACL;YACE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YACjD,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC;SACxB;QACD;YACE,CAAC,IAAI,EAAE,GAAG,CAAC;YACX,CAAC,EAAE,EAAE,GAAG,CAAC;YACT,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,CAAC;SACnB;KACF;IACD;QACE,OAAO;QACP,SAAS;QACT,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC7D,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC;YACvB,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC;YAC5B,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;SACjE;QACD;YACE,CAAC,IAAI,EAAE,OAAO,CAAC;YACf,CAAC,EAAE,EAAE,OAAO,CAAC;SACd;KACF;IACD;QACE,QAAQ;QACR,SAAS;QACT,CAAC,QAAQ,CAAC;QACV;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;SACzB;QACD,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACnB;IAED;;OAEG;IACH;QACE,OAAO;QACP;YACE,SAAS,EAAE,IAAI;SAChB;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,OAAO,EAAE,IAAI,CAAC;SAChB;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,OAAO;QACP;YACE,SAAS,EAAE,IAAI;SAChB;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;SACrB;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IAED;;OAEG;IACH;QACE,OAAO;QACP;YACE,MAAM,EAAE,IAAI;SACb;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;SACrB;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,QAAQ;QACR;YACE,MAAM,EAAE,IAAI;SACb;QACD,CAAC,QAAQ,CAAC;QACV;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,SAAS,EAAE,IAAI,CAAC;SAClB;QACD,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACnB;IAED;;OAEG;IACH;QACE,OAAO;QACP;YACE,GAAG,EAAE,KAAK;SACX;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC;YAC1B,CAAC,QAAQ,EAAE,IAAI,CAAC;SACjB;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,QAAQ;QACR;YACE,GAAG,EAAE,KAAK;SACX;QACD,CAAC,QAAQ,CAAC;QACV;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;YACxB,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,CAAC;SAC7B;QACD,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACnB;IACD;QACE,QAAQ;QACR;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE;gBACE,QAAQ;gBACR,CAAC,QAAQ,EAAE,OAAO,CAAC;gBACnB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;aACxD;YACD;gBACE,YAAY;gBACZ,CAAC,YAAY,EAAE,WAAW,CAAC;gBAC3B,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE;aAChE;YACD;gBACE,YAAY;gBACZ,CAAC,YAAY,EAAE,WAAW,CAAC;gBAC3B,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE;gBAC1D,EAAE,MAAM,EAAE,kBAAkB,EAAE;aAC/B;SACF;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,UAAC,CAAC,EAAE,KAAK,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAlB,CAAkB,EAAE,CAAC;YACxE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;SAC5D;KACF;IACD;QACE,SAAS;QACT;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAClC;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;KAC7B;IACD;QACE,EAAE;QACF;YACE,GAAG,EAAE,KAAK;SACX;QACD,EAAE;QACF;YACE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;YACZ,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;SAClB;QACD,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACb;IAED;;OAEG;IACH;QACE,OAAO;QACP;YACE,KAAK,EAAE,KAAK;SACb;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC;YAC1B,CAAC,aAAa,EAAE,IAAI,CAAC;YACrB,CAAC,kBAAkB,EAAE,IAAI,CAAC;YAC1B,CAAC,QAAQ,EAAE,IAAI,CAAC;SACjB;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,QAAQ;QACR;YACE,KAAK,EAAE,KAAK;SACb;QACD,CAAC,QAAQ,CAAC;QACV;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,aAAa,EAAE,IAAI,CAAC;YACrB,CAAC,cAAc,EAAE,IAAI,CAAC;YACtB,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;YACxB,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,CAAC;SAC7B;QACD,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACnB;IACD;QACE,QAAQ;QACR;YACE,KAAK,EAAE,KAAK;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;QACjC;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,UAAC,CAAC,EAAE,KAAK,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAlB,CAAkB,EAAE,CAAC;YACxE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;SAC5D;KACF;IACD;QACE,SAAS;QACT;YACE,KAAK,EAAE,KAAK;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAClC;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;KAC7B;IACD;QACE,EAAE;QACF;YACE,KAAK,EAAE,KAAK;SACb;QACD,EAAE;QACF;YACE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;YACZ,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC;SACnB;QACD,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACb;IAED;;OAEG;IACH;QACE,OAAO;QACP;YACE,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,IAAI;SACb;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC;SAC3B;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,QAAQ;QACR;YACE,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,IAAI;SACb;QACD,CAAC,QAAQ,CAAC;QACV;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC;YACvB,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAC;SAC5B;QACD,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACnB;IACD;QACE,YAAY;QACZ;YACE,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,IAAI;SACb;QACD,CAAC,YAAY,CAAC;QACd;YACE,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC,CAAC;YAC9B,CAAC,gBAAgB,EAAE,IAAI,CAAC;YACxB,CAAC,kBAAkB,EAAE,CAAC,YAAY,CAAC,CAAC;SACrC;QACD,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KACvB;IACD;QACE,QAAQ;QACR;YACE,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SACjC;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SAC1B;KACF;IACD;QACE,SAAS;QACT;YACE,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAClC;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,UAAU,CAAC,CAAC;KACnC;IACD;QACE,OAAO;QACP;YACE,KAAK,EAAE,KAAK;YACZ,GAAG,EAAE,KAAK;SACX;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC;YAC1B,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,CAAC;SAChC;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,QAAQ;QACR;YACE,KAAK,EAAE,KAAK;YACZ,GAAG,EAAE,KAAK;SACX;QACD,CAAC,QAAQ,CAAC;QACV;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;YACxB,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC;SACjC;QACD,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACnB;IACD;QACE,YAAY;QACZ;YACE,KAAK,EAAE,KAAK;YACZ,GAAG,EAAE,KAAK;SACX;QACD,CAAC,YAAY,CAAC;QACd;YACE,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC,CAAC;YAC9B,CAAC,gBAAgB,EAAE,IAAI,CAAC;YACxB,CAAC,kBAAkB,EAAE,CAAC,YAAY,CAAC,CAAC;YACpC,CAAC,uBAAuB,EAAE,CAAC,YAAY,CAAC,CAAC;SAC1C;QACD,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KACvB;IACD;QACE,QAAQ;QACR;YACE,KAAK,EAAE,KAAK;YACZ,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAClC;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SAC1B;KACF;IACD;QACE,SAAS;QACT;YACE,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAClC;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,UAAU,CAAC,CAAC;KACnC;IAED;;OAEG;IACH;QACE,CAAC,MAAM,EAAE,MAAM,CAAC;QAChB,SAAS;QACT,EAAE;QACF;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,UAAU,EAAE,IAAI,CAAC;SACnB;QACD,EAAE;KACH;IAED;;OAEG;IACH;QACE,OAAO;QACP;YACE,GAAG,EAAE,KAAK;SACX;QACD,CAAC,OAAO,CAAC;QACT,CAAC,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IAED;;OAEG;IACH;QACE,QAAQ;QACR,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACrC,CAAC,iBAAiB,EAAE,IAAI,CAAC;YACzB,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YAC9C,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;YAChE;gBACE,0BAA0B;gBAC1B,CAAC,0BAA0B,EAAE,yBAAyB,CAAC;aACxD;YACD,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;SACjE;QACD;YACE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC;YAC7B;gBACE,EAAE,IAAI,EAAE,gBAAgB,EAAE;gBAC1B,mBAAmB;gBACnB,EAAE,MAAM,EAAE,kBAAkB,EAAE;aAC/B;YACD;gBACE,EAAE,IAAI,EAAE,qBAAqB,EAAE;gBAC/B,0BAA0B;gBAC1B,EAAE,MAAM,EAAE,kBAAkB,EAAE;aAC/B;SACF;KACF;IACD;QACE,QAAQ;QACR;YACE,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,SAAS,EAAE,IAAI,CAAC;SAClB;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC,CAAC;KAChC;IACD;QACE,SAAS;QACT;YACE,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACjC,CAAC,UAAU,EAAE,IAAI,CAAC;SACnB;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,SAAS,CAAC,CAAC;KACjC;IACD;QACE,QAAQ;QACR;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YAC9C,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAClC;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC,CAAC;KAChC;IAED;;OAEG;IACH;QACE,SAAS;QACT,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE;gBACE,QAAQ;gBACR,CAAC,QAAQ,EAAE,OAAO,CAAC;gBACnB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;aACxD;YACD,CAAC,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC;YAC9B,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC5D,CAAC,IAAI,EAAE,IAAI,CAAC;SACb;QACD;YACE,CAAC,IAAI,EAAE,EAAE,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC;SAChC;KACF;IACD;QACE,SAAS;QACT;YACE,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,GAAG,EAAE,IAAI,CAAC;YACX,CAAC,IAAI,EAAE,IAAI,CAAC;SACb;QACD;YACE,CAAC,IAAI,EAAE,EAAE,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC;SAChC;KACF;IACD;QACE,UAAU;QACV;YACE,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACjC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YACvB,CAAC,IAAI,EAAE,IAAI,CAAC;SACb;QACD;YACE,CAAC,IAAI,EAAE,GAAG,CAAC;YACX,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,UAAU,CAAC;SACjC;KACF;IACD;QACE,aAAa;QACb,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,MAAM;SACP;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC7B,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAClC;QACD;YACE,CAAC,IAAI,EAAE,MAAM,CAAC;YACd,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC;SAC9B;KACF;IACD;QACE,aAAa;QACb,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,MAAM;SACP;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC7B,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAClC;QACD;YACE,CAAC,SAAS,EAAE,MAAM,CAAC;YACnB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC;SAC9B;KACF;IACD;QACE,aAAa;QACb,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,MAAM;SACP;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC7B,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACjC,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;SAC9C;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC;KAChC;IAED;;OAEG;IACH;QACE,SAAS;QACT,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;YAClB;gBACE,QAAQ;gBACR,CAAC,QAAQ,EAAE,OAAO,CAAC;gBACnB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;aAC1D;YACD;gBACE,mBAAmB;gBACnB,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;gBACzC;oBACE,IAAI,EAAE,mBAAmB;oBACzB,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;iBAC7C;aACF;YACD,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;SACpB;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC;YAC/B,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC;SACtC;KACF;IACD;QACE,eAAe;QACf,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,cAAc,EAAE,IAAI,CAAC;YACtB,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;SAClD;QACD;YACE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC;YACvB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,MAAM,CAAC;YACvB,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC;SAChC;KACF;IACD;QACE,wBAAwB;QACxB,SAAS;QACT;YACE,QAAQ;YACR;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,UAAU;aACpB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YACxC,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YACpD,CAAC,aAAa,EAAE,IAAI,CAAC;SACtB;QACD;YACE,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC;YACzB,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,YAAY,CAAC;YAC9B,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE,iBAAiB,CAAC;SAC9C;KACF;IACD;QACE,wBAAwB;QACxB,SAAS;QACT;YACE,QAAQ;YACR;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;YACD,OAAO;SACR;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,aAAa,EAAE,IAAI,CAAC;YACrB,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAC/C,CAAC,qBAAqB,EAAE,IAAI,CAAC;SAC9B;QACD,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,iBAAiB,CAAC,CAAC;KACtC;IAED;;OAEG;IACH;QACE,SAAS;QACT,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC5D,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;YACnB;gBACE,QAAQ;gBACR,CAAC,QAAQ,EAAE,OAAO,CAAC;gBACnB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;aAC1D;YACD;gBACE,mBAAmB;gBACnB,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;gBACzC;oBACE,IAAI,EAAE,mBAAmB;oBACzB,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;iBAC7C;aACF;SACF;QACD;YACE,CAAC,EAAE,EAAE,EAAE,CAAC;YACR,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAClB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC;YAC/B,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC;SACvC;KACF;IACD;QACE,sBAAsB;QACtB,SAAS;QACT;YACE,QAAQ;YACR;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,QAAQ;aAClB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACjC,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YACxC,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YACpD,CAAC,YAAY,EAAE,IAAI,CAAC;SACrB;QACD;YACE,CAAC,EAAE,EAAE,QAAQ,CAAC;YACd,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC;YACvB,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC;YACtB,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,eAAe,CAAC;YACpC,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,gBAAgB,CAAC;SAC5C;KACF;IAED;;OAEG;IACH;QACE,cAAc;QACd,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,MAAM,EAAE,IAAI,CAAC;YACd,CAAC,UAAU,EAAE,IAAI,CAAC;SACnB;QACD;YACE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC;YACvB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC9C,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SAC1B;KACF;IACD;QACE,cAAc;QACd;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,MAAM,EAAE,IAAI,CAAC;YACd,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC9B;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC,CAAC;KAC5B;IACD;QACE,YAAY;QACZ,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,IAAI;aACd;SACF;QACD;YACE,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;YACtE,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;SACvE;QACD;YACE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;YACnB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;YACnE;gBACE,EAAE,IAAI,EAAE,aAAa,EAAE;gBACvB,kBAAkB;gBAClB,EAAE,MAAM,EAAE,kBAAkB,EAAE;aAC/B;SACF;KACF;IACD;QACE,iBAAiB;QACjB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,QAAQ;aAClB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,QAAQ,EAAE,IAAI,CAAC;SACjB;QACD;YACE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACrB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YACzC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC;YACxB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC/C,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SAC3B;KACF;IACD;QACE,oBAAoB;QACpB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,WAAW;aACrB;SACF;QACD;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC5B,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC5B,CAAC,MAAM,EAAE,IAAI,CAAC;SACf;QACD;YACE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC;YAC5B,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC;YACxB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC/C,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC;SAC7B;KACF;IACD;QACE,kBAAkB;QAClB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,SAAS;aACnB;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACrC,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACrC,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACrC,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YAC7D,CAAC,SAAS,EAAE,IAAI,CAAC;SAClB;QACD;YACE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC;YACtC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,cAAc,CAAC;YACjD,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC;YAC1B,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YACpD,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC;YAC1B,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;SACrD;KACF;IAED;;OAEG;IACH;QACE,MAAM;QACN,SAAS;QACT,CAAC,MAAM,CAAC;QACR;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,OAAO,EAAE,IAAI,CAAC;SAChB;QACD,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACjB;IACD;QACE,OAAO;QACP,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7B,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAChC;QACD;YACE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACpB,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC;YACtB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC;SAC7B;KACF;IACD;QACE,OAAO;QACP;YACE,MAAM,EAAE,IAAI;SACb;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7B,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,QAAQ,EAAE,IAAI,CAAC;SACjB;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;KAC/B;IACD;QACE,OAAO;QACP;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7B,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACrC;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;KAC/B;IACD;QACE,QAAQ;QACR,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7B,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YACrB,CAAC,cAAc,EAAE,IAAI,CAAC;SACvB;QACD;YACE,CAAC,EAAE,EAAE,EAAE,CAAC;YACR,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACpB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC;SAC7B;KACF;IACD;QACE,WAAW;QACX,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;SACtC;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACpB,CAAC,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC;YAC/B,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC;SACvC;KACF;IAED;;OAEG;IACH;QACE,YAAY;QACZ,SAAS;QACT,CAAC,YAAY,CAAC;QACd;YACE,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC,CAAC;YAC9B,CAAC,aAAa,EAAE,IAAI,CAAC;SACtB;QACD,CAAC,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;KACrB;IACD;QACE,aAAa;QACb,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,OAAO;SACR;QACD;YACE,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACtC,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YACzC,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;SACzD;QACD;YACE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACpB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,WAAW,CAAC;SAC/B;KACF;IAED;;OAEG;IACH;QACE,qBAAqB;QACrB,SAAS;QACT;YACE,OAAO;YACP;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACtC,CAAC,gBAAgB,EAAE,IAAI,CAAC;SACzB;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACtB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,WAAW,CAAC;SACjC;KACF;IACD;QACE,mCAAmC;QACnC,SAAS;QACT;YACE,OAAO;YACP;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,YAAY,EAAE,IAAI,CAAC;YACpB,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SACtD;QACD;YACE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC;YAC7B,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,eAAe,CAAC;SACrC;KACF;IACD;QACE,kBAAkB;QAClB,SAAS;QACT;YACE,OAAO;YACP;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACtC,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;SACnD;QACD;YACE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACtB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,WAAW,CAAC;YAChC,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,eAAe,CAAC;SAC9C;KACF;IACD;QACE,qBAAqB;QACrB;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE,OAAO;YACP;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACtC,CAAC,gBAAgB,EAAE,IAAI,CAAC;SACzB;QACD,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,WAAW,CAAC,CAAC;KACnC;IACD;QACE,gBAAgB;QAChB,SAAS;QACT;YACE,OAAO;YACP;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YACxC,CAAC,gBAAgB,EAAE,IAAI,CAAC;SACzB;QACD;YACE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACtB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,YAAY,CAAC;SAClC;KACF;IAED;;OAEG;IACH;QACE,gBAAgB;QAChB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACjD,CAAC,QAAQ,EAAE,IAAI,CAAC;YAChB,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;SACjE;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,YAAY,CAAC;SACjD;KACF;IACD;QACE,mBAAmB;QACnB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACjD,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;SACjE;QACD;YACE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC;YAC7B,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACrC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,YAAY,CAAC;SACjD;KACF;IACD;QACE,iBAAiB;QACjB;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACjD,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;SACjE;QACD;YACE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC;YAC7B,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC;YAChD,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;YACrC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,YAAY,CAAC;SACjD;KACF;IACD;QACE,oBAAoB;QACpB;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE,OAAO;YACP;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,IAAI;aACd;YACD,GAAG;SACJ;QACD;YACE,CAAC,WAAW,EAAE,IAAI,CAAC;YACnB,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC5B,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SACtC;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC;YAC3B,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,YAAY,CAAC;SAClC;KACF;IAED;;OAEG;IACH;QACE,SAAS;QACT,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,MAAM,EAAE,IAAI,CAAC;YACd,CAAC,UAAU,EAAE,IAAI,CAAC;SACnB;QACD;YACE,CAAC,EAAE,EAAE,IAAI,CAAC;YACV,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SACzB;KACF;IACD;QACE,SAAS;QACT;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,MAAM,EAAE,IAAI,CAAC;YACd,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAC7B,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC5B;QACD,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC,CAAC;KAC3B;IACD;QACE,UAAU;QACV,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,MAAM;aAChB;SACF;QACD;YACE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YACvB,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC1B;QACD;YACE,CAAC,EAAE,EAAE,EAAE,CAAC;YACR,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SACzB;KACF;IACD;QACE,OAAO;QACP,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,IAAI;aACd;SACF;QACD;YACE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,eAAe,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;SACrD;QACD;YACE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;YAClB,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SACzB;KACF;IACD;QACE,4BAA4B;QAC5B,SAAS;QACT;YACE,WAAW;YACX;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,UAAU;aACpB;YACD,GAAG;SACJ;QACD,CAAC,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,CAAC;QACnD,EAAE;KACH;IACD;QACE,WAAW;QACX,SAAS;QACT;YACE;gBACE,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,EAAE;aACZ;SACF;QACD;YACE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;YACZ,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;SACvB;QACD;YACE,CAAC,IAAI,EAAE,EAAE,CAAC;YACV,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC;SACvB;KACF;IACD;QACE,UAAU;QACV,SAAS;QACT;YACE;gBACE,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ;SACF;QACD;YACE,CAAC,GAAG,EAAE,IAAI,CAAC;YACX,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;SACvB;QACD,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;KACzB;IACD;QACE,SAAS;QACT,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,IAAI;aACd;SACF;QACD;YACE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAChC;QACD,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;KAC3B;IAED;;OAEG;IACH,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACrE;QACE,MAAM;QACN,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ;SACF;QACD,CAAC,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,CAAC;QAC7D,EAAE;KACH;IACD;QACE,SAAS;QACT,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ;SACF;QACD;YACE,CAAC,MAAM,EAAE,IAAI,CAAC;YACd,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC1B;QACD,EAAE;KACH;IAED;;OAEG;IACH;QACE,CAAC,OAAO,EAAE,SAAS,CAAC;QACpB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ;SACF;QACD,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QACjC,EAAE;KACH;IACD;QACE,CAAC,cAAc,EAAE,MAAM,CAAC;QACxB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;YACD;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;YACpC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SACtC;QACD,EAAE;KACH;IAED;;OAEG;IACH;QACE,CAAC,QAAQ,EAAE,cAAc,CAAC;QAC1B,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YACvC,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SACpD;QACD,EAAE;KACH;IACD;QACE,CAAC,cAAc,EAAE,qBAAqB,CAAC;QACvC,SAAS;QACT;YACE;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ;YACD;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,EAAE;aACZ;SACF;QACD;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YACvC,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SACpD;QACD,EAAE;KACH;IAED;;OAEG;IACH;QACE,QAAQ;QACR,SAAS;QACT,EAAE;QACF,CAAC,CAAC,oBAAoB,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC;QAChD,EAAE;KACH;IAED;;OAEG;IACH;QACE,gBAAgB;QAChB,SAAS;QACT,CAAC,YAAY,CAAC;QACd;YACE,CAAC,UAAU,EAAE,IAAI,CAAC;YAClB,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC,CAAC;SAC/B;QACD,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KACvB;IACD;QACE,6BAA6B;QAC7B,SAAS;QACT,CAAC,iBAAiB,CAAC;QACnB,CAAC,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;KAC5B;IACD;QACE,kCAAkC;QAClC,SAAS;QACT;YACE,QAAQ;YACR;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,OAAO;aACjB;YACD;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,OAAO;aACjB;SACF;QACD;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAC5C,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YACjD,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SAClD;QACD;YACE,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,YAAY,CAAC;YAC/B,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,YAAY,CAAC;YAC/B,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;SACxB;KACF;IAED;;OAEG;IACH;QACE,+BAA+B;QAC/B,SAAS;QACT;YACE,GAAG;YACH;gBACE,IAAI,EAAE,EAAE;gBACR,MAAM,EAAE,QAAQ;gBAChB,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,EAAE;aACZ;YACD,OAAO;YACP;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,MAAM;aAChB;YACD,MAAM;SACP;QACD;YACE,CAAC,eAAe,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;SACxD;QACD,EAAE;KACH;IAED;;OAEG;IACH;QACE,YAAY;QACZ,SAAS;QACT;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD,CAAC,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;KACnC;IACD;QACE,qBAAqB;QACrB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,YAAY;SACb;QACD,EAAE;QACF,EAAE;KACH;IACD;QACE,mCAAmC;QACnC,SAAS;QACT;YACE;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,UAAU;aACpB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,SAAS;aACnB;SACF;QACD;YACE,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAC1D,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;YACzE,CAAC,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;SACvE;QACD;YACE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC;YAC5C,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,cAAc,CAAC;SACrD;KACF;IACD;QACE,UAAU;QACV,SAAS;QACT;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;KAC5B;IACD;QACE,WAAW;QACX,SAAS;QACT;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,KAAK;SACN;QACD;YACE,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAC3C,CAAC,MAAM,EAAE,IAAI,CAAC;SACf;QACD;YACE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,SAAS,CAAC;YAC3B,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,eAAe,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;YACrE,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,aAAa,CAAC;SACzC;KACF;IACD;QACE,aAAa;QACb,SAAS;QACT;YACE,GAAG;YACH;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,KAAK;SACN;QACD;YACE,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SAC9B;QACD;YACE,CAAC,EAAE,EAAE,MAAM,CAAC;YACZ,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,SAAS,CAAC;SAC5B;KACF;IACD;QACE,kBAAkB;QAClB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;YACH;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,GAAG;SACJ;QACD;YACE,CAAC,eAAe,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;SAC/C;QACD;YACE,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,eAAe,CAAC;YACjD,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,UAAU,CAAC;SAC/B;KACF;IACD;QACE,sCAAsC;QACtC,SAAS;QACT;YACE;gBACE,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,kBAAkB;aAC5B;YACD;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,OAAO;aACjB;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAClD,CAAC,SAAS,EAAE,IAAI,CAAC;SAClB;QACD;YACE,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC;YACjC,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC;SAC/B;KACF;IACD;QACE,kBAAkB;QAClB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,MAAM;SACP;QACD;YACE,CAAC,OAAO,EAAE,IAAI,CAAC;YACf,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YACxC,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;YAC5C,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAChD,CAAC,WAAW,EAAE,IAAI,CAAC;SACpB;QACD;YACE,CAAC,EAAE,EAAE,MAAM,CAAC;YACZ,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC;YAC5B,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC;YAC5B,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,cAAc,CAAC;SAC7C;KACF;IACD;QACE,2BAA2B;QAC3B,SAAS;QACT;YACE;gBACE,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,cAAc;aACxB;YACD,MAAM;SACP;QACD;YACE,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;YAC5C,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAChD,CAAC,WAAW,EAAE,IAAI,CAAC;SACpB;QACD,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC;KACpC;IAED;;OAEG;IACH;QACE,OAAO;QACP,SAAS;QACT;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;QAC9B;YACE,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC;YAC1B,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;SAChE;KACF;IACD,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IAC1E;QACE,OAAO;QACP,EAAE,MAAM,EAAE,SAAS,EAAE;QACrB,CAAC,OAAO,CAAC;QACT,CAAC,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,WAAW;QACX,SAAS;QACT,CAAC,WAAW,CAAC;QACb;YACE,CAAC,UAAU,EAAE,IAAI,CAAC;YAClB,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,CAAC;SAC7B;QACD,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;KACtB;IAED;;OAEG;IACH;QACE,aAAa;QACb;YACE,SAAS,EAAE,GAAG;SACf;QACD;YACE;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,UAAU;aACpB;YACD,MAAM;SACP;QACD;YACE,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAC3C,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;SACzC;QACD;YACE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,aAAa,CAAC;YACtC,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,YAAY,CAAC;SACrC;KACF;IACD;QACE,kBAAkB;QAClB;YACE,SAAS,EAAE,GAAG;SACf;QACD;YACE,MAAM;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,UAAU;aACpB;YACD,MAAM;SACP;QACD;YACE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;YACrD,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;SACnD;QACD;YACE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,kBAAkB,CAAC;YAC3C,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,iBAAiB,CAAC;SAC1C;KACF;IACD;QACE,cAAc;QACd,EAAE;QACF;YACE,SAAS;YACT;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACvC,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACxC;QACD;YACE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,aAAa,CAAC;YAC/B,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,aAAa,CAAC;SAChC;KACF;IACD;QACE,SAAS;QACT;YACE,SAAS,EAAE,GAAG;YACd,GAAG,EAAE,KAAK;SACX;QACD,CAAC,SAAS,CAAC;QACX;YACE,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC,YAAY,EAAE,IAAI,CAAC;SACrB;QACD,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KACpB;IAED;;OAEG;IACH;QACE,OAAO;QACP;YACE,QAAQ,EAAE,GAAG;SACd;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC,QAAQ,EAAE,IAAI,CAAC;SACjB;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IACD;QACE,OAAO;QACP;YACE,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,IAAI;SACb;QACD,CAAC,OAAO,CAAC;QACT;YACE,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC,qBAAqB,EAAE,IAAI,CAAC;SAC9B;QACD,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAClB;IAED;;OAEG;IACH;QACE,iBAAiB;QACjB,EAAE;QACF;YACE;gBACE,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,cAAc;gBACvB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;aACb;YACD;gBACE,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,cAAc;gBACvB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;aACd;SACF;QACD;YACE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;YAC9B,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SAC7B;QACD;YACE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACxB,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,UAAU,CAAC;SACzC;KACF;IACD;QACE,kCAAkC;QAClC,EAAE;QACF;YACE,MAAM;YACN;gBACE,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,cAAc;gBACvB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;aACd;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,cAAc;gBACvB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;aACd;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,cAAc;gBACvB,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;aACd;SACF;QACD;YACE,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAC1D,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YACjD,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/C,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC7C,CAAC,oBAAoB,EAAE,IAAI,CAAC;YAC5B,CAAC,iBAAiB,EAAE,IAAI,CAAC;SAC1B;QACD;YACE,CAAC,EAAE,EAAE,MAAM,CAAC;YACZ,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,WAAW,CAAC;YAChC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,WAAW,CAAC;SACjC;KACF;IAED;;OAEG;IACH;QACE,aAAa;QACb;YACE,SAAS,EAAE,IAAI;SAChB;QACD;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,KAAK;aACf;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,MAAM,EAAE,IAAI,CAAC;SACf;QACD;YACE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC;SACxB;KACF;IACD;QACE,aAAa;QACb,EAAE;QACF;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,KAAK;aACf;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC1B;QACD;YACE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;SAC1B;KACF;IAED;;OAEG;IACH;QACE,0BAA0B;QAC1B,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,kBAAkB;aAC5B;SACF;QACD;YACE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzB,CAAC,MAAM,EAAE,IAAI,CAAC;YACd,CAAC,UAAU,EAAE,IAAI,CAAC;YAClB,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACrC,CAAC,UAAU,EAAE,IAAI,CAAC;SACnB;QACD;YACE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,MAAM,CAAC;YACvB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,UAAU,CAAC;YAC/B,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC;YACvB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC;YACzB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC;YACjC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC;SAC5B;KACF;IACD;QACE,wBAAwB;QACxB,SAAS;QACT;YACE;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,gBAAgB;aAC1B;SACF;QACD;YACE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,QAAQ,EAAE,IAAI,CAAC;SACjB;QACD;YACE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC;YAC7B,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC;SAC1B;KACF;IAED;;OAEG;IACH;QACE,iBAAiB;QACjB,SAAS;QACT;YACE,OAAO;YACP;gBACE,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,GAAG;aACb;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAC9C,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SAC3C;QACD,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,WAAW,CAAC,CAAC;KACjC;IAED;;OAEG;IACH;QACE,4BAA4B;QAC5B,SAAS;QACT;YACE,WAAW;YACX;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;YACD,YAAY;SACb;QACD,CAAC,CAAC,yBAAyB,EAAE,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,yBAAyB,CAAC,CAAC;KAC9C;IACD;QACE,gBAAgB;QAChB;YACE,GAAG,EAAE,KAAK;SACX;QACD;YACE,WAAW;YACX;gBACE,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,cAAc;aACxB;SACF;QACD;YACE,CAAC,eAAe,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC,oBAAoB,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAChD,CAAC,wBAAwB,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC,yBAAyB,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;SACtD;QACD;YACE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,eAAe,CAAC;YACjC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC;SACrB;KACF;CACF,CAAC;AAEF;;GAEG;AACH,QAAQ,CAAC,gBAAgB,EAAE;IACzB,IAAM,SAAS,GAAG,WAAW,CAAC;IAE9B,IAAM,UAAU,GAAG;QACjB,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,GAAG;QACX,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,cAAc;KACxB,CAAC;IAEF,QAAQ,CAAC,WAAW,EAAE;QACpB,EAAE,CAAC,iDAAiD,EAAE;YACpD,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACnC,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACvC,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;YAElD,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACrC,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACzC,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;YAEpD,YAAY,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YACxC,YAAY,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5C,YAAY,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE;YAC1D,IAAM,IAAI,GAAuB,EAAE,CAAC;YACpC,IAAM,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;YAEtE,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,MAAM,CAAC;gBACL,YAAY,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;YAC3C,MAAM,CAAC;gBACL,YAAY,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,uCAAuC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;YACvC,MAAM,CAAC;gBACL,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;YACpC,MAAM,CAAC;gBACL,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE;YACjC,MAAM,CAAC;gBACL,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;YAClC,MAAM,CAAC;gBACL,YAAY,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;YACvC,MAAM,CAAC;gBACL,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE;QACjB,IAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAE7C,EAAE,CAAC,kDAAkD,EAAE;YACrD,IAAM,EAAE,GAAG,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE/C,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE;YAC9D,IAAM,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEjD,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,OAAO,EAAE;QAChB,KAAK,CAAC,OAAO,CAAC,UAAS,IAAI;YAClB,IAAA,cAAI,EAAE,cAAI,EAAE,gBAAM,EAAE,oBAAU,EAAE,sBAAY,CAAS;YAE5D,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC3B,IAAM,IAAI,GAAuB,EAAE,CAAC;gBACpC,IAAM,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBAEvD,6DAA6D;gBAC7D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBAC5B,EAAE,CAAC,cAAc,EAAE;wBACjB,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACzD,CAAC,CAAC,CAAC;oBAEH,QAAQ,CAAC,SAAS,EAAE;wBAClB,YAAY,CAAC,OAAO,CAAC,UAAS,EAAE;4BACvB,IAAA,cAAM,EAAE,cAAM,EAAE,eAAO,CAAO;4BACrC,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,wBACnC,IAAI,GACJ,OAAO,EACV,CAAC;4BAEH,IAAI,MAAM,KAAK,IAAI,EAAE;gCACnB,EAAE,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oCACjD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gCACzC,CAAC,CAAC,CAAC;6BACJ;iCAAM;gCACL,EAAE,CACA,2BAA2B,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAClD;oCACE,MAAM,CAAC;wCACL,MAAM,CAAC,MAAM,CAAC,CAAC;oCACjB,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gCACxB,CAAC,CACF,CAAC;6BACH;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;iBACJ;qBAAM;oBACL,EAAE,CAAC,mBAAmB,EAAE;wBACtB,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAClB,MAAM,CAAC,MAAM,CAAC,UAAS,KAAK;4BAC1B,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;wBACnC,CAAC,CAAC,CACH,CAAC;oBACJ,CAAC,CAAC,CAAC;iBACJ;gBAED,QAAQ,CACN,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EACtD;oBACE,UAAU,CAAC,OAAO,CAAC,UAAS,EAAE;wBACrB,IAAA,gBAAQ,EAAE,eAAO,EAAE,cAAM,EAAE,eAAO,CAAO;wBAChD,IAAM,OAAO,GAAG,aACd,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,eACd,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAG,CAAC;wBAElC,EAAE,CAAC,OAAO,EAAE;4BACV,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBAC9C,CAAC,CAAC,CAAC;wBAEH,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;4BACpD,IAAM,OAAK,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BAEhD,EAAE,CAAC,OAAO,GAAG,SAAS,EAAE;gCACtB,MAAM,CAAC,OAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;4BAC1C,CAAC,CAAC,CAAC;yBACJ;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE/C,MAAM,CAAC;gBACL,MAAM,EAAE,CAAC;YACX,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAEnD,MAAM,CAAC;gBACL,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,OAAO,CACR,IAAI,SAAS,CAAC,+CAA+C,CAAC,CAC/D,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC;gBACL,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE;YACrD,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,CAAC;gBACL,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,OAAO,CACR,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAChE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAEpD,MAAM,CAAC;gBACL,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,OAAO,CACR,IAAI,SAAS,CAAC,iDAAiD,CAAC,CACjE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,SAAS,IAAI,CAAC,EAAU,EAAE,GAAW;IACnC,IAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE3B,OAAO,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpD,CAAC", "sourcesContent": ["import * as util from \"util\";\nimport * as pathToRegexp from \"./index\";\n\ntype Test = [\n  pathToRegexp.Path,\n  (pathToRegexp.TokensToRegexpOptions & pathToRegexp.ParseOptions) | undefined,\n  pathToRegexp.Token[],\n  Array<\n    [\n      string,\n      (string | undefined)[] | null,\n      pathToRegexp.Match?,\n      pathToRegexp.RegexpToFunctionOptions?\n    ]\n  >,\n  Array<[any, string | null, pathToRegexp.TokensToFunctionOptions?]>\n];\n\n/**\n * An array of test cases with expected inputs and outputs.\n */\nconst TESTS: Test[] = [\n  /**\n   * Simple paths.\n   */\n  [\n    \"/\",\n    undefined,\n    [\"/\"],\n    [\n      [\"/\", [\"/\"], { path: \"/\", index: 0, params: {} }],\n      [\"/route\", null, false]\n    ],\n    [\n      [null, \"/\"],\n      [{}, \"/\"],\n      [{ id: 123 }, \"/\"]\n    ]\n  ],\n  [\n    \"/test\",\n    undefined,\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"], { path: \"/test\", index: 0, params: {} }],\n      [\"/route\", null, false],\n      [\"/test/route\", null, false],\n      [\"/test/\", [\"/test/\"], { path: \"/test/\", index: 0, params: {} }]\n    ],\n    [\n      [null, \"/test\"],\n      [{}, \"/test\"]\n    ]\n  ],\n  [\n    \"/test/\",\n    undefined,\n    [\"/test/\"],\n    [\n      [\"/test\", null],\n      [\"/test/\", [\"/test/\"]],\n      [\"/test//\", [\"/test//\"]]\n    ],\n    [[null, \"/test/\"]]\n  ],\n\n  /**\n   * Case-sensitive paths.\n   */\n  [\n    \"/test\",\n    {\n      sensitive: true\n    },\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"]],\n      [\"/TEST\", null]\n    ],\n    [[null, \"/test\"]]\n  ],\n  [\n    \"/TEST\",\n    {\n      sensitive: true\n    },\n    [\"/TEST\"],\n    [\n      [\"/test\", null],\n      [\"/TEST\", [\"/TEST\"]]\n    ],\n    [[null, \"/TEST\"]]\n  ],\n\n  /**\n   * Strict mode.\n   */\n  [\n    \"/test\",\n    {\n      strict: true\n    },\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"]],\n      [\"/test/\", null],\n      [\"/TEST\", [\"/TEST\"]]\n    ],\n    [[null, \"/test\"]]\n  ],\n  [\n    \"/test/\",\n    {\n      strict: true\n    },\n    [\"/test/\"],\n    [\n      [\"/test\", null],\n      [\"/test/\", [\"/test/\"]],\n      [\"/test//\", null]\n    ],\n    [[null, \"/test/\"]]\n  ],\n\n  /**\n   * Non-ending mode.\n   */\n  [\n    \"/test\",\n    {\n      end: false\n    },\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"]],\n      [\"/test/\", [\"/test/\"]],\n      [\"/test/route\", [\"/test\"]],\n      [\"/route\", null]\n    ],\n    [[null, \"/test\"]]\n  ],\n  [\n    \"/test/\",\n    {\n      end: false\n    },\n    [\"/test/\"],\n    [\n      [\"/test\", null],\n      [\"/test/route\", [\"/test/\"]],\n      [\"/test//\", [\"/test//\"]],\n      [\"/test//route\", [\"/test/\"]]\n    ],\n    [[null, \"/test/\"]]\n  ],\n  [\n    \"/:test\",\n    {\n      end: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\n        \"/route\",\n        [\"/route\", \"route\"],\n        { path: \"/route\", index: 0, params: { test: \"route\" } }\n      ],\n      [\n        \"/caf%C3%A9\",\n        [\"/caf%C3%A9\", \"caf%C3%A9\"],\n        { path: \"/caf%C3%A9\", index: 0, params: { test: \"caf%C3%A9\" } }\n      ],\n      [\n        \"/caf%C3%A9\",\n        [\"/caf%C3%A9\", \"caf%C3%A9\"],\n        { path: \"/caf%C3%A9\", index: 0, params: { test: \"café\" } },\n        { decode: decodeURIComponent }\n      ]\n    ],\n    [\n      [{}, null],\n      [{ test: \"abc\" }, \"/abc\"],\n      [{ test: \"a+b\" }, \"/a+b\"],\n      [{ test: \"a+b\" }, \"/test\", { encode: (_, token) => String(token.name) }],\n      [{ test: \"a+b\" }, \"/a%2Bb\", { encode: encodeURIComponent }]\n    ]\n  ],\n  [\n    \"/:test/\",\n    {\n      end: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"/\"\n    ],\n    [\n      [\"/route\", null],\n      [\"/route/\", [\"/route/\", \"route\"]]\n    ],\n    [[{ test: \"abc\" }, \"/abc/\"]]\n  ],\n  [\n    \"\",\n    {\n      end: false\n    },\n    [],\n    [\n      [\"\", [\"\"]],\n      [\"/\", [\"/\"]],\n      [\"route\", [\"\"]],\n      [\"/route\", [\"\"]],\n      [\"/route/\", [\"\"]]\n    ],\n    [[null, \"\"]]\n  ],\n\n  /**\n   * Non-starting mode.\n   */\n  [\n    \"/test\",\n    {\n      start: false\n    },\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"]],\n      [\"/test/\", [\"/test/\"]],\n      [\"/route/test\", [\"/test\"]],\n      [\"/test/route\", null],\n      [\"/route/test/deep\", null],\n      [\"/route\", null]\n    ],\n    [[null, \"/test\"]]\n  ],\n  [\n    \"/test/\",\n    {\n      start: false\n    },\n    [\"/test/\"],\n    [\n      [\"/test\", null],\n      [\"/test/route\", null],\n      [\"/test//route\", null],\n      [\"/test//\", [\"/test//\"]],\n      [\"/route/test/\", [\"/test/\"]]\n    ],\n    [[null, \"/test/\"]]\n  ],\n  [\n    \"/:test\",\n    {\n      start: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [[\"/route\", [\"/route\", \"route\"]]],\n    [\n      [{}, null],\n      [{ test: \"abc\" }, \"/abc\"],\n      [{ test: \"a+b\" }, \"/a+b\"],\n      [{ test: \"a+b\" }, \"/test\", { encode: (_, token) => String(token.name) }],\n      [{ test: \"a+b\" }, \"/a%2Bb\", { encode: encodeURIComponent }]\n    ]\n  ],\n  [\n    \"/:test/\",\n    {\n      start: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"/\"\n    ],\n    [\n      [\"/route\", null],\n      [\"/route/\", [\"/route/\", \"route\"]]\n    ],\n    [[{ test: \"abc\" }, \"/abc/\"]]\n  ],\n  [\n    \"\",\n    {\n      start: false\n    },\n    [],\n    [\n      [\"\", [\"\"]],\n      [\"/\", [\"/\"]],\n      [\"route\", [\"\"]],\n      [\"/route\", [\"\"]],\n      [\"/route/\", [\"/\"]]\n    ],\n    [[null, \"\"]]\n  ],\n\n  /**\n   * Combine modes.\n   */\n  [\n    \"/test\",\n    {\n      end: false,\n      strict: true\n    },\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"]],\n      [\"/test/\", [\"/test\"]],\n      [\"/test/route\", [\"/test\"]]\n    ],\n    [[null, \"/test\"]]\n  ],\n  [\n    \"/test/\",\n    {\n      end: false,\n      strict: true\n    },\n    [\"/test/\"],\n    [\n      [\"/test\", null],\n      [\"/test/\", [\"/test/\"]],\n      [\"/test//\", [\"/test/\"]],\n      [\"/test/route\", [\"/test/\"]]\n    ],\n    [[null, \"/test/\"]]\n  ],\n  [\n    \"/test.json\",\n    {\n      end: false,\n      strict: true\n    },\n    [\"/test.json\"],\n    [\n      [\"/test.json\", [\"/test.json\"]],\n      [\"/test.json.hbs\", null],\n      [\"/test.json/route\", [\"/test.json\"]]\n    ],\n    [[null, \"/test.json\"]]\n  ],\n  [\n    \"/:test\",\n    {\n      end: false,\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\"]],\n      [\"/route/\", [\"/route\", \"route\"]]\n    ],\n    [\n      [{}, null],\n      [{ test: \"abc\" }, \"/abc\"]\n    ]\n  ],\n  [\n    \"/:test/\",\n    {\n      end: false,\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"/\"\n    ],\n    [\n      [\"/route\", null],\n      [\"/route/\", [\"/route/\", \"route\"]]\n    ],\n    [[{ test: \"foobar\" }, \"/foobar/\"]]\n  ],\n  [\n    \"/test\",\n    {\n      start: false,\n      end: false\n    },\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"]],\n      [\"/test/\", [\"/test/\"]],\n      [\"/test/route\", [\"/test\"]],\n      [\"/route/test/deep\", [\"/test\"]]\n    ],\n    [[null, \"/test\"]]\n  ],\n  [\n    \"/test/\",\n    {\n      start: false,\n      end: false\n    },\n    [\"/test/\"],\n    [\n      [\"/test\", null],\n      [\"/test/\", [\"/test/\"]],\n      [\"/test//\", [\"/test//\"]],\n      [\"/test/route\", [\"/test/\"]],\n      [\"/route/test/deep\", [\"/test/\"]]\n    ],\n    [[null, \"/test/\"]]\n  ],\n  [\n    \"/test.json\",\n    {\n      start: false,\n      end: false\n    },\n    [\"/test.json\"],\n    [\n      [\"/test.json\", [\"/test.json\"]],\n      [\"/test.json.hbs\", null],\n      [\"/test.json/route\", [\"/test.json\"]],\n      [\"/route/test.json/deep\", [\"/test.json\"]]\n    ],\n    [[null, \"/test.json\"]]\n  ],\n  [\n    \"/:test\",\n    {\n      start: false,\n      end: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\"]],\n      [\"/route/\", [\"/route/\", \"route\"]]\n    ],\n    [\n      [{}, null],\n      [{ test: \"abc\" }, \"/abc\"]\n    ]\n  ],\n  [\n    \"/:test/\",\n    {\n      end: false,\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"/\"\n    ],\n    [\n      [\"/route\", null],\n      [\"/route/\", [\"/route/\", \"route\"]]\n    ],\n    [[{ test: \"foobar\" }, \"/foobar/\"]]\n  ],\n\n  /**\n   * Arrays of simple paths.\n   */\n  [\n    [\"/one\", \"/two\"],\n    undefined,\n    [],\n    [\n      [\"/one\", [\"/one\"]],\n      [\"/two\", [\"/two\"]],\n      [\"/three\", null],\n      [\"/one/two\", null]\n    ],\n    []\n  ],\n\n  /**\n   * Non-ending simple path.\n   */\n  [\n    \"/test\",\n    {\n      end: false\n    },\n    [\"/test\"],\n    [[\"/test/route\", [\"/test\"]]],\n    [[null, \"/test\"]]\n  ],\n\n  /**\n   * Single named parameter.\n   */\n  [\n    \"/:test\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\"]],\n      [\"/another\", [\"/another\", \"another\"]],\n      [\"/something/else\", null],\n      [\"/route.json\", [\"/route.json\", \"route.json\"]],\n      [\"/something%2Felse\", [\"/something%2Felse\", \"something%2Felse\"]],\n      [\n        \"/something%2Felse%2Fmore\",\n        [\"/something%2Felse%2Fmore\", \"something%2Felse%2Fmore\"]\n      ],\n      [\"/;,:@&=+$-_.!~*()\", [\"/;,:@&=+$-_.!~*()\", \";,:@&=+$-_.!~*()\"]]\n    ],\n    [\n      [{ test: \"route\" }, \"/route\"],\n      [\n        { test: \"something/else\" },\n        \"/something%2Felse\",\n        { encode: encodeURIComponent }\n      ],\n      [\n        { test: \"something/else/more\" },\n        \"/something%2Felse%2Fmore\",\n        { encode: encodeURIComponent }\n      ]\n    ]\n  ],\n  [\n    \"/:test\",\n    {\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\"]],\n      [\"/route/\", null]\n    ],\n    [[{ test: \"route\" }, \"/route\"]]\n  ],\n  [\n    \"/:test/\",\n    {\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"/\"\n    ],\n    [\n      [\"/route/\", [\"/route/\", \"route\"]],\n      [\"/route//\", null]\n    ],\n    [[{ test: \"route\" }, \"/route/\"]]\n  ],\n  [\n    \"/:test\",\n    {\n      end: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route.json\", [\"/route.json\", \"route.json\"]],\n      [\"/route//\", [\"/route\", \"route\"]]\n    ],\n    [[{ test: \"route\" }, \"/route\"]]\n  ],\n\n  /**\n   * Optional named parameter.\n   */\n  [\n    \"/:test?\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\n        \"/route\",\n        [\"/route\", \"route\"],\n        { path: \"/route\", index: 0, params: { test: \"route\" } }\n      ],\n      [\"/route/nested\", null, false],\n      [\"/\", [\"/\", undefined], { path: \"/\", index: 0, params: {} }],\n      [\"//\", null]\n    ],\n    [\n      [null, \"\"],\n      [{ test: \"foobar\" }, \"/foobar\"]\n    ]\n  ],\n  [\n    \"/:test?\",\n    {\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\"]],\n      [\"/\", null], // Questionable behaviour.\n      [\"//\", null]\n    ],\n    [\n      [null, \"\"],\n      [{ test: \"foobar\" }, \"/foobar\"]\n    ]\n  ],\n  [\n    \"/:test?/\",\n    {\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"/\"\n    ],\n    [\n      [\"/route\", null],\n      [\"/route/\", [\"/route/\", \"route\"]],\n      [\"/\", [\"/\", undefined]],\n      [\"//\", null]\n    ],\n    [\n      [null, \"/\"],\n      [{ test: \"foobar\" }, \"/foobar/\"]\n    ]\n  ],\n  [\n    \"/:test?/bar\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"/bar\"\n    ],\n    [\n      [\"/bar\", [\"/bar\", undefined]],\n      [\"/foo/bar\", [\"/foo/bar\", \"foo\"]]\n    ],\n    [\n      [null, \"/bar\"],\n      [{ test: \"foo\" }, \"/foo/bar\"]\n    ]\n  ],\n  [\n    \"/:test?-bar\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"-bar\"\n    ],\n    [\n      [\"-bar\", [\"-bar\", undefined]],\n      [\"/-bar\", null],\n      [\"/foo-bar\", [\"/foo-bar\", \"foo\"]]\n    ],\n    [\n      [undefined, \"-bar\"],\n      [{ test: \"foo\" }, \"/foo-bar\"]\n    ]\n  ],\n  [\n    \"/:test*-bar\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"*\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"-bar\"\n    ],\n    [\n      [\"-bar\", [\"-bar\", undefined]],\n      [\"/-bar\", null],\n      [\"/foo-bar\", [\"/foo-bar\", \"foo\"]],\n      [\"/foo/baz-bar\", [\"/foo/baz-bar\", \"foo/baz\"]]\n    ],\n    [[{ test: \"foo\" }, \"/foo-bar\"]]\n  ],\n\n  /**\n   * Repeated one or more times parameters.\n   */\n  [\n    \"/:test+\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"+\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/\", null, false],\n      [\n        \"/route\",\n        [\"/route\", \"route\"],\n        { path: \"/route\", index: 0, params: { test: [\"route\"] } }\n      ],\n      [\n        \"/some/basic/route\",\n        [\"/some/basic/route\", \"some/basic/route\"],\n        {\n          path: \"/some/basic/route\",\n          index: 0,\n          params: { test: [\"some\", \"basic\", \"route\"] }\n        }\n      ],\n      [\"//\", null, false]\n    ],\n    [\n      [{}, null],\n      [{ test: \"foobar\" }, \"/foobar\"],\n      [{ test: [\"a\", \"b\", \"c\"] }, \"/a/b/c\"]\n    ]\n  ],\n  [\n    \"/:test(\\\\d+)+\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"+\",\n        pattern: \"\\\\d+\"\n      }\n    ],\n    [\n      [\"/abc/456/789\", null],\n      [\"/123/456/789\", [\"/123/456/789\", \"123/456/789\"]]\n    ],\n    [\n      [{ test: \"abc\" }, null],\n      [{ test: 123 }, \"/123\"],\n      [{ test: [1, 2, 3] }, \"/1/2/3\"]\n    ]\n  ],\n  [\n    \"/route.:ext(json|xml)+\",\n    undefined,\n    [\n      \"/route\",\n      {\n        name: \"ext\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"+\",\n        pattern: \"json|xml\"\n      }\n    ],\n    [\n      [\"/route\", null],\n      [\"/route.json\", [\"/route.json\", \"json\"]],\n      [\"/route.xml.json\", [\"/route.xml.json\", \"xml.json\"]],\n      [\"/route.html\", null]\n    ],\n    [\n      [{ ext: \"foobar\" }, null],\n      [{ ext: \"xml\" }, \"/route.xml\"],\n      [{ ext: [\"xml\", \"json\"] }, \"/route.xml.json\"]\n    ]\n  ],\n  [\n    \"/route.:ext(\\\\w+)/test\",\n    undefined,\n    [\n      \"/route\",\n      {\n        name: \"ext\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\w+\"\n      },\n      \"/test\"\n    ],\n    [\n      [\"/route\", null],\n      [\"/route.json\", null],\n      [\"/route.xml/test\", [\"/route.xml/test\", \"xml\"]],\n      [\"/route.json.gz/test\", null]\n    ],\n    [[{ ext: \"xml\" }, \"/route.xml/test\"]]\n  ],\n\n  /**\n   * Repeated zero or more times parameters.\n   */\n  [\n    \"/:test*\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"*\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/\", [\"/\", undefined], { path: \"/\", index: 0, params: {} }],\n      [\"//\", null, false],\n      [\n        \"/route\",\n        [\"/route\", \"route\"],\n        { path: \"/route\", index: 0, params: { test: [\"route\"] } }\n      ],\n      [\n        \"/some/basic/route\",\n        [\"/some/basic/route\", \"some/basic/route\"],\n        {\n          path: \"/some/basic/route\",\n          index: 0,\n          params: { test: [\"some\", \"basic\", \"route\"] }\n        }\n      ]\n    ],\n    [\n      [{}, \"\"],\n      [{ test: [] }, \"\"],\n      [{ test: \"foobar\" }, \"/foobar\"],\n      [{ test: [\"foo\", \"bar\"] }, \"/foo/bar\"]\n    ]\n  ],\n  [\n    \"/route.:ext([a-z]+)*\",\n    undefined,\n    [\n      \"/route\",\n      {\n        name: \"ext\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"*\",\n        pattern: \"[a-z]+\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", undefined]],\n      [\"/route.json\", [\"/route.json\", \"json\"]],\n      [\"/route.json.xml\", [\"/route.json.xml\", \"json.xml\"]],\n      [\"/route.123\", null]\n    ],\n    [\n      [{}, \"/route\"],\n      [{ ext: [] }, \"/route\"],\n      [{ ext: \"123\" }, null],\n      [{ ext: \"foobar\" }, \"/route.foobar\"],\n      [{ ext: [\"foo\", \"bar\"] }, \"/route.foo.bar\"]\n    ]\n  ],\n\n  /**\n   * Custom named parameters.\n   */\n  [\n    \"/:test(\\\\d+)\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+\"\n      }\n    ],\n    [\n      [\"/123\", [\"/123\", \"123\"]],\n      [\"/abc\", null],\n      [\"/123/abc\", null]\n    ],\n    [\n      [{ test: \"abc\" }, null],\n      [{ test: \"abc\" }, \"/abc\", { validate: false }],\n      [{ test: \"123\" }, \"/123\"]\n    ]\n  ],\n  [\n    \"/:test(\\\\d+)\",\n    {\n      end: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+\"\n      }\n    ],\n    [\n      [\"/123\", [\"/123\", \"123\"]],\n      [\"/abc\", null],\n      [\"/123/abc\", [\"/123\", \"123\"]]\n    ],\n    [[{ test: \"123\" }, \"/123\"]]\n  ],\n  [\n    \"/:test(.*)\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \".*\"\n      }\n    ],\n    [\n      [\"/anything/goes/here\", [\"/anything/goes/here\", \"anything/goes/here\"]],\n      [\"/;,:@&=/+$-_.!/~*()\", [\"/;,:@&=/+$-_.!/~*()\", \";,:@&=/+$-_.!/~*()\"]]\n    ],\n    [\n      [{ test: \"\" }, \"/\"],\n      [{ test: \"abc\" }, \"/abc\"],\n      [{ test: \"abc/123\" }, \"/abc%2F123\", { encode: encodeURIComponent }],\n      [\n        { test: \"abc/123/456\" },\n        \"/abc%2F123%2F456\",\n        { encode: encodeURIComponent }\n      ]\n    ]\n  ],\n  [\n    \"/:route([a-z]+)\",\n    undefined,\n    [\n      {\n        name: \"route\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[a-z]+\"\n      }\n    ],\n    [\n      [\"/abcde\", [\"/abcde\", \"abcde\"]],\n      [\"/12345\", null]\n    ],\n    [\n      [{ route: \"\" }, null],\n      [{ route: \"\" }, \"/\", { validate: false }],\n      [{ route: \"123\" }, null],\n      [{ route: \"123\" }, \"/123\", { validate: false }],\n      [{ route: \"abc\" }, \"/abc\"]\n    ]\n  ],\n  [\n    \"/:route(this|that)\",\n    undefined,\n    [\n      {\n        name: \"route\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"this|that\"\n      }\n    ],\n    [\n      [\"/this\", [\"/this\", \"this\"]],\n      [\"/that\", [\"/that\", \"that\"]],\n      [\"/foo\", null]\n    ],\n    [\n      [{ route: \"this\" }, \"/this\"],\n      [{ route: \"foo\" }, null],\n      [{ route: \"foo\" }, \"/foo\", { validate: false }],\n      [{ route: \"that\" }, \"/that\"]\n    ]\n  ],\n  [\n    \"/:path(abc|xyz)*\",\n    undefined,\n    [\n      {\n        name: \"path\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"*\",\n        pattern: \"abc|xyz\"\n      }\n    ],\n    [\n      [\"/abc\", [\"/abc\", \"abc\"]],\n      [\"/abc/abc\", [\"/abc/abc\", \"abc/abc\"]],\n      [\"/xyz/xyz\", [\"/xyz/xyz\", \"xyz/xyz\"]],\n      [\"/abc/xyz\", [\"/abc/xyz\", \"abc/xyz\"]],\n      [\"/abc/xyz/abc/xyz\", [\"/abc/xyz/abc/xyz\", \"abc/xyz/abc/xyz\"]],\n      [\"/xyzxyz\", null]\n    ],\n    [\n      [{ path: \"abc\" }, \"/abc\"],\n      [{ path: [\"abc\", \"xyz\"] }, \"/abc/xyz\"],\n      [{ path: [\"xyz\", \"abc\", \"xyz\"] }, \"/xyz/abc/xyz\"],\n      [{ path: \"abc123\" }, null],\n      [{ path: \"abc123\" }, \"/abc123\", { validate: false }],\n      [{ path: \"abcxyz\" }, null],\n      [{ path: \"abcxyz\" }, \"/abcxyz\", { validate: false }]\n    ]\n  ],\n\n  /**\n   * Prefixed slashes could be omitted.\n   */\n  [\n    \"test\",\n    undefined,\n    [\"test\"],\n    [\n      [\"test\", [\"test\"]],\n      [\"/test\", null]\n    ],\n    [[null, \"test\"]]\n  ],\n  [\n    \":test\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"route\", [\"route\", \"route\"]],\n      [\"/route\", null],\n      [\"route/\", [\"route/\", \"route\"]]\n    ],\n    [\n      [{ test: \"\" }, null],\n      [{}, null],\n      [{ test: null }, null],\n      [{ test: \"route\" }, \"route\"]\n    ]\n  ],\n  [\n    \":test\",\n    {\n      strict: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"route\", [\"route\", \"route\"]],\n      [\"/route\", null],\n      [\"route/\", null]\n    ],\n    [[{ test: \"route\" }, \"route\"]]\n  ],\n  [\n    \":test\",\n    {\n      end: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"route\", [\"route\", \"route\"]],\n      [\"/route\", null],\n      [\"route/\", [\"route/\", \"route\"]],\n      [\"route/foobar\", [\"route\", \"route\"]]\n    ],\n    [[{ test: \"route\" }, \"route\"]]\n  ],\n  [\n    \":test?\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"route\", [\"route\", \"route\"]],\n      [\"/route\", null],\n      [\"\", [\"\", undefined]],\n      [\"route/foobar\", null]\n    ],\n    [\n      [{}, \"\"],\n      [{ test: \"\" }, null],\n      [{ test: \"route\" }, \"route\"]\n    ]\n  ],\n  [\n    \"{:test/}+\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"\",\n        suffix: \"/\",\n        modifier: \"+\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"route/\", [\"route/\", \"route\"]],\n      [\"/route\", null],\n      [\"\", null],\n      [\"foo/bar/\", [\"foo/bar/\", \"foo/bar\"]]\n    ],\n    [\n      [{}, null],\n      [{ test: \"\" }, null],\n      [{ test: [\"route\"] }, \"route/\"],\n      [{ test: [\"foo\", \"bar\"] }, \"foo/bar/\"]\n    ]\n  ],\n\n  /**\n   * Formats.\n   */\n  [\n    \"/test.json\",\n    undefined,\n    [\"/test.json\"],\n    [\n      [\"/test.json\", [\"/test.json\"]],\n      [\"/route.json\", null]\n    ],\n    [[{}, \"/test.json\"]]\n  ],\n  [\n    \"/:test.json\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \".json\"\n    ],\n    [\n      [\"/.json\", null],\n      [\"/test.json\", [\"/test.json\", \"test\"]],\n      [\"/route.json\", [\"/route.json\", \"route\"]],\n      [\"/route.json.json\", [\"/route.json.json\", \"route.json\"]]\n    ],\n    [\n      [{ test: \"\" }, null],\n      [{ test: \"foo\" }, \"/foo.json\"]\n    ]\n  ],\n\n  /**\n   * Format params.\n   */\n  [\n    \"/test.:format(\\\\w+)\",\n    undefined,\n    [\n      \"/test\",\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\w+\"\n      }\n    ],\n    [\n      [\"/test.html\", [\"/test.html\", \"html\"]],\n      [\"/test.hbs.html\", null]\n    ],\n    [\n      [{}, null],\n      [{ format: \"\" }, null],\n      [{ format: \"foo\" }, \"/test.foo\"]\n    ]\n  ],\n  [\n    \"/test.:format(\\\\w+).:format(\\\\w+)\",\n    undefined,\n    [\n      \"/test\",\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\w+\"\n      },\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\w+\"\n      }\n    ],\n    [\n      [\"/test.html\", null],\n      [\"/test.hbs.html\", [\"/test.hbs.html\", \"hbs\", \"html\"]]\n    ],\n    [\n      [{ format: \"foo.bar\" }, null],\n      [{ format: \"foo\" }, \"/test.foo.foo\"]\n    ]\n  ],\n  [\n    \"/test{.:format}+\",\n    undefined,\n    [\n      \"/test\",\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"+\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/test.html\", [\"/test.html\", \"html\"]],\n      [\"/test.hbs.html\", [\"/test.hbs.html\", \"hbs.html\"]]\n    ],\n    [\n      [{ format: [] }, null],\n      [{ format: \"foo\" }, \"/test.foo\"],\n      [{ format: [\"foo\", \"bar\"] }, \"/test.foo.bar\"]\n    ]\n  ],\n  [\n    \"/test.:format(\\\\w+)\",\n    {\n      end: false\n    },\n    [\n      \"/test\",\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\w+\"\n      }\n    ],\n    [\n      [\"/test.html\", [\"/test.html\", \"html\"]],\n      [\"/test.hbs.html\", null]\n    ],\n    [[{ format: \"foo\" }, \"/test.foo\"]]\n  ],\n  [\n    \"/test.:format.\",\n    undefined,\n    [\n      \"/test\",\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \".\"\n    ],\n    [\n      [\"/test.html.\", [\"/test.html.\", \"html\"]],\n      [\"/test.hbs.html\", null]\n    ],\n    [\n      [{ format: \"\" }, null],\n      [{ format: \"foo\" }, \"/test.foo.\"]\n    ]\n  ],\n\n  /**\n   * Format and path params.\n   */\n  [\n    \"/:test.:format\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route.html\", [\"/route.html\", \"route\", \"html\"]],\n      [\"/route\", null],\n      [\"/route.html.json\", [\"/route.html.json\", \"route\", \"html.json\"]]\n    ],\n    [\n      [{}, null],\n      [{ test: \"route\", format: \"foo\" }, \"/route.foo\"]\n    ]\n  ],\n  [\n    \"/:test{.:format}?\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\", undefined]],\n      [\"/route.json\", [\"/route.json\", \"route\", \"json\"]],\n      [\"/route.json.html\", [\"/route.json.html\", \"route\", \"json.html\"]]\n    ],\n    [\n      [{ test: \"route\" }, \"/route\"],\n      [{ test: \"route\", format: \"\" }, null],\n      [{ test: \"route\", format: \"foo\" }, \"/route.foo\"]\n    ]\n  ],\n  [\n    \"/:test.:format?\",\n    {\n      end: false\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\", undefined]],\n      [\"/route.json\", [\"/route.json\", \"route\", \"json\"]],\n      [\"/route.json.html\", [\"/route.json.html\", \"route\", \"json.html\"]]\n    ],\n    [\n      [{ test: \"route\" }, \"/route\"],\n      [{ test: \"route\", format: undefined }, \"/route\"],\n      [{ test: \"route\", format: \"\" }, null],\n      [{ test: \"route\", format: \"foo\" }, \"/route.foo\"]\n    ]\n  ],\n  [\n    \"/test.:format(.*)z\",\n    {\n      end: false\n    },\n    [\n      \"/test\",\n      {\n        name: \"format\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \".*\"\n      },\n      \"z\"\n    ],\n    [\n      [\"/test.abc\", null],\n      [\"/test.z\", [\"/test.z\", \"\"]],\n      [\"/test.abcz\", [\"/test.abcz\", \"abc\"]]\n    ],\n    [\n      [{}, null],\n      [{ format: \"\" }, \"/test.z\"],\n      [{ format: \"foo\" }, \"/test.fooz\"]\n    ]\n  ],\n\n  /**\n   * Unnamed params.\n   */\n  [\n    \"/(\\\\d+)\",\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+\"\n      }\n    ],\n    [\n      [\"/123\", [\"/123\", \"123\"]],\n      [\"/abc\", null],\n      [\"/123/abc\", null]\n    ],\n    [\n      [{}, null],\n      [{ \"0\": \"123\" }, \"/123\"]\n    ]\n  ],\n  [\n    \"/(\\\\d+)\",\n    {\n      end: false\n    },\n    [\n      {\n        name: 0,\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+\"\n      }\n    ],\n    [\n      [\"/123\", [\"/123\", \"123\"]],\n      [\"/abc\", null],\n      [\"/123/abc\", [\"/123\", \"123\"]],\n      [\"/123/\", [\"/123/\", \"123\"]]\n    ],\n    [[{ \"0\": \"123\" }, \"/123\"]]\n  ],\n  [\n    \"/(\\\\d+)?\",\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"\\\\d+\"\n      }\n    ],\n    [\n      [\"/\", [\"/\", undefined]],\n      [\"/123\", [\"/123\", \"123\"]]\n    ],\n    [\n      [{}, \"\"],\n      [{ \"0\": \"123\" }, \"/123\"]\n    ]\n  ],\n  [\n    \"/(.*)\",\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \".*\"\n      }\n    ],\n    [\n      [\"/\", [\"/\", \"\"]],\n      [\"/route\", [\"/route\", \"route\"]],\n      [\"/route/nested\", [\"/route/nested\", \"route/nested\"]]\n    ],\n    [\n      [{ \"0\": \"\" }, \"/\"],\n      [{ \"0\": \"123\" }, \"/123\"]\n    ]\n  ],\n  [\n    \"/route\\\\(\\\\\\\\(\\\\d+\\\\\\\\)\\\\)\",\n    undefined,\n    [\n      \"/route(\\\\\",\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+\\\\\\\\\"\n      },\n      \")\"\n    ],\n    [[\"/route(\\\\123\\\\)\", [\"/route(\\\\123\\\\)\", \"123\\\\\"]]],\n    []\n  ],\n  [\n    \"{/login}?\",\n    undefined,\n    [\n      {\n        name: \"\",\n        prefix: \"/login\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"\"\n      }\n    ],\n    [\n      [\"/\", [\"/\"]],\n      [\"/login\", [\"/login\"]]\n    ],\n    [\n      [null, \"\"],\n      [{ \"\": \"\" }, \"/login\"]\n    ]\n  ],\n  [\n    \"{/login}\",\n    undefined,\n    [\n      {\n        name: \"\",\n        prefix: \"/login\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\"\n      }\n    ],\n    [\n      [\"/\", null],\n      [\"/login\", [\"/login\"]]\n    ],\n    [[{ \"\": \"\" }, \"/login\"]]\n  ],\n  [\n    \"{/(.*)}\",\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \".*\"\n      }\n    ],\n    [\n      [\"/\", [\"/\", \"\"]],\n      [\"/login\", [\"/login\", \"login\"]]\n    ],\n    [[{ 0: \"test\" }, \"/test\"]]\n  ],\n\n  /**\n   * Regexps.\n   */\n  [/.*/, undefined, [], [[\"/match/anything\", [\"/match/anything\"]]], []],\n  [\n    /(.*)/,\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\"\n      }\n    ],\n    [[\"/match/anything\", [\"/match/anything\", \"/match/anything\"]]],\n    []\n  ],\n  [\n    /\\/(\\d+)/,\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\"\n      }\n    ],\n    [\n      [\"/abc\", null],\n      [\"/123\", [\"/123\", \"123\"]]\n    ],\n    []\n  ],\n\n  /**\n   * Mixed arrays.\n   */\n  [\n    [\"/test\", /\\/(\\d+)/],\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\"\n      }\n    ],\n    [[\"/test\", [\"/test\", undefined]]],\n    []\n  ],\n  [\n    [\"/:test(\\\\d+)\", /(.*)/],\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+\"\n      },\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\"\n      }\n    ],\n    [\n      [\"/123\", [\"/123\", \"123\", undefined]],\n      [\"/abc\", [\"/abc\", undefined, \"/abc\"]]\n    ],\n    []\n  ],\n\n  /**\n   * Correct names and indexes.\n   */\n  [\n    [\"/:test\", \"/route/:test\"],\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/test\", [\"/test\", \"test\", undefined]],\n      [\"/route/test\", [\"/route/test\", undefined, \"test\"]]\n    ],\n    []\n  ],\n  [\n    [/^\\/([^\\/]+)$/, /^\\/route\\/([^\\/]+)$/],\n    undefined,\n    [\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\"\n      },\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\"\n      }\n    ],\n    [\n      [\"/test\", [\"/test\", \"test\", undefined]],\n      [\"/route/test\", [\"/route/test\", undefined, \"test\"]]\n    ],\n    []\n  ],\n\n  /**\n   * Ignore non-matching groups in regexps.\n   */\n  [\n    /(?:.*)/,\n    undefined,\n    [],\n    [[\"/anything/you/want\", [\"/anything/you/want\"]]],\n    []\n  ],\n\n  /**\n   * Respect escaped characters.\n   */\n  [\n    \"/\\\\(testing\\\\)\",\n    undefined,\n    [\"/(testing)\"],\n    [\n      [\"/testing\", null],\n      [\"/(testing)\", [\"/(testing)\"]]\n    ],\n    [[null, \"/(testing)\"]]\n  ],\n  [\n    \"/.\\\\+\\\\*\\\\?\\\\{\\\\}=^!\\\\:$[]|\",\n    undefined,\n    [\"/.+*?{}=^!:$[]|\"],\n    [[\"/.+*?{}=^!:$[]|\", [\"/.+*?{}=^!:$[]|\"]]],\n    [[null, \"/.+*?{}=^!:$[]|\"]]\n  ],\n  [\n    \"/test\\\\/:uid(u\\\\d+)?:cid(c\\\\d+)?\",\n    undefined,\n    [\n      \"/test/\",\n      {\n        name: \"uid\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"u\\\\d+\"\n      },\n      {\n        name: \"cid\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"c\\\\d+\"\n      }\n    ],\n    [\n      [\"/test\", null],\n      [\"/test/\", [\"/test/\", undefined, undefined]],\n      [\"/test/u123\", [\"/test/u123\", \"u123\", undefined]],\n      [\"/test/c123\", [\"/test/c123\", undefined, \"c123\"]]\n    ],\n    [\n      [{ uid: \"u123\" }, \"/test/u123\"],\n      [{ cid: \"c123\" }, \"/test/c123\"],\n      [{ cid: \"u123\" }, null]\n    ]\n  ],\n\n  /**\n   * Unnamed group prefix.\n   */\n  [\n    \"/{apple-}?icon-:res(\\\\d+).png\",\n    undefined,\n    [\n      \"/\",\n      {\n        name: \"\",\n        prefix: \"apple-\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"\"\n      },\n      \"icon-\",\n      {\n        name: \"res\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+\"\n      },\n      \".png\"\n    ],\n    [\n      [\"/icon-240.png\", [\"/icon-240.png\", \"240\"]],\n      [\"/apple-icon-240.png\", [\"/apple-icon-240.png\", \"240\"]]\n    ],\n    []\n  ],\n\n  /**\n   * Random examples.\n   */\n  [\n    \"/:foo/:bar\",\n    undefined,\n    [\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      {\n        name: \"bar\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [[\"/match/route\", [\"/match/route\", \"match\", \"route\"]]],\n    [[{ foo: \"a\", bar: \"b\" }, \"/a/b\"]]\n  ],\n  [\n    \"/:foo\\\\(test\\\\)/bar\",\n    undefined,\n    [\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"(test)/bar\"\n    ],\n    [],\n    []\n  ],\n  [\n    \"/:remote([\\\\w-.]+)/:user([\\\\w-]+)\",\n    undefined,\n    [\n      {\n        name: \"remote\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[\\\\w-.]+\"\n      },\n      {\n        name: \"user\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[\\\\w-]+\"\n      }\n    ],\n    [\n      [\"/endpoint/user\", [\"/endpoint/user\", \"endpoint\", \"user\"]],\n      [\"/endpoint/user-name\", [\"/endpoint/user-name\", \"endpoint\", \"user-name\"]],\n      [\"/foo.bar/user-name\", [\"/foo.bar/user-name\", \"foo.bar\", \"user-name\"]]\n    ],\n    [\n      [{ remote: \"foo\", user: \"bar\" }, \"/foo/bar\"],\n      [{ remote: \"foo.bar\", user: \"uno\" }, \"/foo.bar/uno\"]\n    ]\n  ],\n  [\n    \"/:foo\\\\?\",\n    undefined,\n    [\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"?\"\n    ],\n    [[\"/route?\", [\"/route?\", \"route\"]]],\n    [[{ foo: \"bar\" }, \"/bar?\"]]\n  ],\n  [\n    \"/:foo+baz\",\n    undefined,\n    [\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"+\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"baz\"\n    ],\n    [\n      [\"/foobaz\", [\"/foobaz\", \"foo\"]],\n      [\"/foo/barbaz\", [\"/foo/barbaz\", \"foo/bar\"]],\n      [\"/baz\", null]\n    ],\n    [\n      [{ foo: \"foo\" }, \"/foobaz\"],\n      [{ foo: \"foo/bar\" }, \"/foo%2Fbarbaz\", { encode: encodeURIComponent }],\n      [{ foo: [\"foo\", \"bar\"] }, \"/foo/barbaz\"]\n    ]\n  ],\n  [\n    \"\\\\/:pre?baz\",\n    undefined,\n    [\n      \"/\",\n      {\n        name: \"pre\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"baz\"\n    ],\n    [\n      [\"/foobaz\", [\"/foobaz\", \"foo\"]],\n      [\"/baz\", [\"/baz\", undefined]]\n    ],\n    [\n      [{}, \"/baz\"],\n      [{ pre: \"foo\" }, \"/foobaz\"]\n    ]\n  ],\n  [\n    \"/:foo\\\\(:bar?\\\\)\",\n    undefined,\n    [\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"(\",\n      {\n        name: \"bar\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \")\"\n    ],\n    [\n      [\"/hello(world)\", [\"/hello(world)\", \"hello\", \"world\"]],\n      [\"/hello()\", [\"/hello()\", \"hello\", undefined]]\n    ],\n    [\n      [{ foo: \"hello\", bar: \"world\" }, \"/hello(world)\"],\n      [{ foo: \"hello\" }, \"/hello()\"]\n    ]\n  ],\n  [\n    \"/:postType(video|audio|text)(\\\\+.+)?\",\n    undefined,\n    [\n      {\n        name: \"postType\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"video|audio|text\"\n      },\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"\\\\+.+\"\n      }\n    ],\n    [\n      [\"/video\", [\"/video\", \"video\", undefined]],\n      [\"/video+test\", [\"/video+test\", \"video\", \"+test\"]],\n      [\"/video+\", null]\n    ],\n    [\n      [{ postType: \"video\" }, \"/video\"],\n      [{ postType: \"random\" }, null]\n    ]\n  ],\n  [\n    \"/:foo?/:bar?-ext\",\n    undefined,\n    [\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      {\n        name: \"bar\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"-ext\"\n    ],\n    [\n      [\"/-ext\", null],\n      [\"-ext\", [\"-ext\", undefined, undefined]],\n      [\"/foo-ext\", [\"/foo-ext\", \"foo\", undefined]],\n      [\"/foo/bar-ext\", [\"/foo/bar-ext\", \"foo\", \"bar\"]],\n      [\"/foo/-ext\", null]\n    ],\n    [\n      [{}, \"-ext\"],\n      [{ foo: \"foo\" }, \"/foo-ext\"],\n      [{ bar: \"bar\" }, \"/bar-ext\"],\n      [{ foo: \"foo\", bar: \"bar\" }, \"/foo/bar-ext\"]\n    ]\n  ],\n  [\n    \"/:required/:optional?-ext\",\n    undefined,\n    [\n      {\n        name: \"required\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      {\n        name: \"optional\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"-ext\"\n    ],\n    [\n      [\"/foo-ext\", [\"/foo-ext\", \"foo\", undefined]],\n      [\"/foo/bar-ext\", [\"/foo/bar-ext\", \"foo\", \"bar\"]],\n      [\"/foo/-ext\", null]\n    ],\n    [[{ required: \"foo\" }, \"/foo-ext\"]]\n  ],\n\n  /**\n   * Unicode characters.\n   */\n  [\n    \"/:foo\",\n    undefined,\n    [\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [[\"/café\", [\"/café\", \"café\"]]],\n    [\n      [{ foo: \"café\" }, \"/café\"],\n      [{ foo: \"café\" }, \"/caf%C3%A9\", { encode: encodeURIComponent }]\n    ]\n  ],\n  [\"/café\", undefined, [\"/café\"], [[\"/café\", [\"/café\"]]], [[null, \"/café\"]]],\n  [\n    \"/café\",\n    { encode: encodeURI },\n    [\"/café\"],\n    [[\"/caf%C3%A9\", [\"/caf%C3%A9\"]]],\n    [[null, \"/café\"]]\n  ],\n  [\n    \"packages/\",\n    undefined,\n    [\"packages/\"],\n    [\n      [\"packages\", null],\n      [\"packages/\", [\"packages/\"]]\n    ],\n    [[null, \"packages/\"]]\n  ],\n\n  /**\n   * Hostnames.\n   */\n  [\n    \":domain.com\",\n    {\n      delimiter: \".\"\n    },\n    [\n      {\n        name: \"domain\",\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\.]+?\"\n      },\n      \".com\"\n    ],\n    [\n      [\"example.com\", [\"example.com\", \"example\"]],\n      [\"github.com\", [\"github.com\", \"github\"]]\n    ],\n    [\n      [{ domain: \"example\" }, \"example.com\"],\n      [{ domain: \"github\" }, \"github.com\"]\n    ]\n  ],\n  [\n    \"mail.:domain.com\",\n    {\n      delimiter: \".\"\n    },\n    [\n      \"mail\",\n      {\n        name: \"domain\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\.]+?\"\n      },\n      \".com\"\n    ],\n    [\n      [\"mail.example.com\", [\"mail.example.com\", \"example\"]],\n      [\"mail.github.com\", [\"mail.github.com\", \"github\"]]\n    ],\n    [\n      [{ domain: \"example\" }, \"mail.example.com\"],\n      [{ domain: \"github\" }, \"mail.github.com\"]\n    ]\n  ],\n  [\n    \"example.:ext\",\n    {},\n    [\n      \"example\",\n      {\n        name: \"ext\",\n        prefix: \".\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"example.com\", [\"example.com\", \"com\"]],\n      [\"example.org\", [\"example.org\", \"org\"]]\n    ],\n    [\n      [{ ext: \"com\" }, \"example.com\"],\n      [{ ext: \"org\" }, \"example.org\"]\n    ]\n  ],\n  [\n    \"this is\",\n    {\n      delimiter: \" \",\n      end: false\n    },\n    [\"this is\"],\n    [\n      [\"this is a test\", [\"this is\"]],\n      [\"this isn't\", null]\n    ],\n    [[null, \"this is\"]]\n  ],\n\n  /**\n   * Ends with.\n   */\n  [\n    \"/test\",\n    {\n      endsWith: \"?\"\n    },\n    [\"/test\"],\n    [\n      [\"/test\", [\"/test\"]],\n      [\"/test?query=string\", [\"/test\"]],\n      [\"/test/?query=string\", [\"/test/\"]],\n      [\"/testx\", null]\n    ],\n    [[null, \"/test\"]]\n  ],\n  [\n    \"/test\",\n    {\n      endsWith: \"?\",\n      strict: true\n    },\n    [\"/test\"],\n    [\n      [\"/test?query=string\", [\"/test\"]],\n      [\"/test/?query=string\", null]\n    ],\n    [[null, \"/test\"]]\n  ],\n\n  /**\n   * Custom prefixes.\n   */\n  [\n    \"{$:foo}{$:bar}?\",\n    {},\n    [\n      {\n        name: \"foo\",\n        pattern: \"[^\\\\/#\\\\?]+?\",\n        prefix: \"$\",\n        suffix: \"\",\n        modifier: \"\"\n      },\n      {\n        name: \"bar\",\n        pattern: \"[^\\\\/#\\\\?]+?\",\n        prefix: \"$\",\n        suffix: \"\",\n        modifier: \"?\"\n      }\n    ],\n    [\n      [\"$x\", [\"$x\", \"x\", undefined]],\n      [\"$x$y\", [\"$x$y\", \"x\", \"y\"]]\n    ],\n    [\n      [{ foo: \"foo\" }, \"$foo\"],\n      [{ foo: \"foo\", bar: \"bar\" }, \"$foo$bar\"]\n    ]\n  ],\n  [\n    \"name/:attr1?{-:attr2}?{-:attr3}?\",\n    {},\n    [\n      \"name\",\n      {\n        name: \"attr1\",\n        pattern: \"[^\\\\/#\\\\?]+?\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"?\"\n      },\n      {\n        name: \"attr2\",\n        pattern: \"[^\\\\/#\\\\?]+?\",\n        prefix: \"-\",\n        suffix: \"\",\n        modifier: \"?\"\n      },\n      {\n        name: \"attr3\",\n        pattern: \"[^\\\\/#\\\\?]+?\",\n        prefix: \"-\",\n        suffix: \"\",\n        modifier: \"?\"\n      }\n    ],\n    [\n      [\"name/test\", [\"name/test\", \"test\", undefined, undefined]],\n      [\"name/1\", [\"name/1\", \"1\", undefined, undefined]],\n      [\"name/1-2\", [\"name/1-2\", \"1\", \"2\", undefined]],\n      [\"name/1-2-3\", [\"name/1-2-3\", \"1\", \"2\", \"3\"]],\n      [\"name/foo-bar/route\", null],\n      [\"name/test/route\", null]\n    ],\n    [\n      [{}, \"name\"],\n      [{ attr1: \"test\" }, \"name/test\"],\n      [{ attr2: \"attr\" }, \"name-attr\"]\n    ]\n  ],\n\n  /**\n   * Case-sensitive compile tokensToFunction params.\n   */\n  [\n    \"/:test(abc)\",\n    {\n      sensitive: true\n    },\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"abc\"\n      }\n    ],\n    [\n      [\"/abc\", [\"/abc\", \"abc\"]],\n      [\"/ABC\", null]\n    ],\n    [\n      [{ test: \"abc\" }, \"/abc\"],\n      [{ test: \"ABC\" }, null]\n    ]\n  ],\n  [\n    \"/:test(abc)\",\n    {},\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"abc\"\n      }\n    ],\n    [\n      [\"/abc\", [\"/abc\", \"abc\"]],\n      [\"/ABC\", [\"/ABC\", \"ABC\"]]\n    ],\n    [\n      [{ test: \"abc\" }, \"/abc\"],\n      [{ test: \"ABC\" }, \"/ABC\"]\n    ]\n  ],\n\n  /**\n   * Nested parentheses.\n   */\n  [\n    \"/:test(\\\\d+(?:\\\\.\\\\d+)?)\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"\\\\d+(?:\\\\.\\\\d+)?\"\n      }\n    ],\n    [\n      [\"/123\", [\"/123\", \"123\"]],\n      [\"/abc\", null],\n      [\"/123/abc\", null],\n      [\"/123.123\", [\"/123.123\", \"123.123\"]],\n      [\"/123.abc\", null]\n    ],\n    [\n      [{ test: 123 }, \"/123\"],\n      [{ test: 123.123 }, \"/123.123\"],\n      [{ test: \"abc\" }, null],\n      [{ test: \"123\" }, \"/123\"],\n      [{ test: \"123.123\" }, \"/123.123\"],\n      [{ test: \"123.abc\" }, null]\n    ]\n  ],\n  [\n    \"/:test((?!login)[^/]+)\",\n    undefined,\n    [\n      {\n        name: \"test\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"(?!login)[^/]+\"\n      }\n    ],\n    [\n      [\"/route\", [\"/route\", \"route\"]],\n      [\"/login\", null]\n    ],\n    [\n      [{ test: \"route\" }, \"/route\"],\n      [{ test: \"login\" }, null]\n    ]\n  ],\n\n  /**\n   * https://github.com/pillarjs/path-to-regexp/issues/206\n   */\n  [\n    \"/user(s)?/:user\",\n    undefined,\n    [\n      \"/user\",\n      {\n        name: 0,\n        prefix: \"\",\n        suffix: \"\",\n        modifier: \"?\",\n        pattern: \"s\"\n      },\n      {\n        name: \"user\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/user/123\", [\"/user/123\", undefined, \"123\"]],\n      [\"/users/123\", [\"/users/123\", \"s\", \"123\"]]\n    ],\n    [[{ user: \"123\" }, \"/user/123\"]]\n  ],\n\n  /**\n   * https://github.com/pillarjs/path-to-regexp/issues/209\n   */\n  [\n    \"/whatever/:foo\\\\?query=str\",\n    undefined,\n    [\n      \"/whatever\",\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      },\n      \"?query=str\"\n    ],\n    [[\"/whatever/123?query=str\", [\"/whatever/123?query=str\", \"123\"]]],\n    [[{ foo: \"123\" }, \"/whatever/123?query=str\"]]\n  ],\n  [\n    \"/whatever/:foo\",\n    {\n      end: false\n    },\n    [\n      \"/whatever\",\n      {\n        name: \"foo\",\n        prefix: \"/\",\n        suffix: \"\",\n        modifier: \"\",\n        pattern: \"[^\\\\/#\\\\?]+?\"\n      }\n    ],\n    [\n      [\"/whatever/123\", [\"/whatever/123\", \"123\"]],\n      [\"/whatever/123/path\", [\"/whatever/123\", \"123\"]],\n      [\"/whatever/123#fragment\", [\"/whatever/123\", \"123\"]],\n      [\"/whatever/123?query=str\", [\"/whatever/123\", \"123\"]]\n    ],\n    [\n      [{ foo: \"123\" }, \"/whatever/123\"],\n      [{ foo: \"#\" }, null]\n    ]\n  ]\n];\n\n/**\n * Dynamically generate the entire test suite.\n */\ndescribe(\"path-to-regexp\", function() {\n  const TEST_PATH = \"/user/:id\";\n\n  const TEST_PARAM = {\n    name: \"id\",\n    prefix: \"/\",\n    suffix: \"\",\n    modifier: \"\",\n    pattern: \"[^\\\\/#\\\\?]+?\"\n  };\n\n  describe(\"arguments\", function() {\n    it(\"should work without different call combinations\", function() {\n      pathToRegexp.pathToRegexp(\"/test\");\n      pathToRegexp.pathToRegexp(\"/test\", []);\n      pathToRegexp.pathToRegexp(\"/test\", undefined, {});\n\n      pathToRegexp.pathToRegexp(/^\\/test/);\n      pathToRegexp.pathToRegexp(/^\\/test/, []);\n      pathToRegexp.pathToRegexp(/^\\/test/, undefined, {});\n\n      pathToRegexp.pathToRegexp([\"/a\", \"/b\"]);\n      pathToRegexp.pathToRegexp([\"/a\", \"/b\"], []);\n      pathToRegexp.pathToRegexp([\"/a\", \"/b\"], undefined, {});\n    });\n\n    it(\"should accept an array of keys as the second argument\", function() {\n      const keys: pathToRegexp.Key[] = [];\n      const re = pathToRegexp.pathToRegexp(TEST_PATH, keys, { end: false });\n\n      expect(keys).toEqual([TEST_PARAM]);\n      expect(exec(re, \"/user/123/show\")).toEqual([\"/user/123\", \"123\"]);\n    });\n\n    it(\"should throw on non-capturing pattern\", function() {\n      expect(function() {\n        pathToRegexp.pathToRegexp(\"/:foo(?:\\\\d+(\\\\.\\\\d+)?)\");\n      }).toThrow(new TypeError('Pattern cannot start with \"?\" at 6'));\n    });\n\n    it(\"should throw on nested capturing group\", function() {\n      expect(function() {\n        pathToRegexp.pathToRegexp(\"/:foo(\\\\d+(\\\\.\\\\d+)?)\");\n      }).toThrow(new TypeError(\"Capturing groups are not allowed at 9\"));\n    });\n\n    it(\"should throw on unbalanced pattern\", function() {\n      expect(function() {\n        pathToRegexp.pathToRegexp(\"/:foo(abc\");\n      }).toThrow(new TypeError(\"Unbalanced pattern at 5\"));\n    });\n\n    it(\"should throw on missing pattern\", function() {\n      expect(function() {\n        pathToRegexp.pathToRegexp(\"/:foo()\");\n      }).toThrow(new TypeError(\"Missing pattern at 5\"));\n    });\n\n    it(\"should throw on missing name\", function() {\n      expect(function() {\n        pathToRegexp.pathToRegexp(\"/:(test)\");\n      }).toThrow(new TypeError(\"Missing parameter name at 1\"));\n    });\n\n    it(\"should throw on nested groups\", function() {\n      expect(function() {\n        pathToRegexp.pathToRegexp(\"/{a{b:foo}}\");\n      }).toThrow(new TypeError(\"Unexpected OPEN at 3, expected CLOSE\"));\n    });\n\n    it(\"should throw on misplaced modifier\", function() {\n      expect(function() {\n        pathToRegexp.pathToRegexp(\"/foo?\");\n      }).toThrow(new TypeError(\"Unexpected MODIFIER at 4, expected END\"));\n    });\n  });\n\n  describe(\"tokens\", function() {\n    const tokens = pathToRegexp.parse(TEST_PATH);\n\n    it(\"should expose method to compile tokens to regexp\", function() {\n      const re = pathToRegexp.tokensToRegexp(tokens);\n\n      expect(exec(re, \"/user/123\")).toEqual([\"/user/123\", \"123\"]);\n    });\n\n    it(\"should expose method to compile tokens to a path function\", function() {\n      const fn = pathToRegexp.tokensToFunction(tokens);\n\n      expect(fn({ id: 123 })).toEqual(\"/user/123\");\n    });\n  });\n\n  describe(\"rules\", function() {\n    TESTS.forEach(function(test) {\n      const [path, opts, tokens, matchCases, compileCases] = test;\n\n      describe(util.inspect(path), function() {\n        const keys: pathToRegexp.Key[] = [];\n        const re = pathToRegexp.pathToRegexp(path, keys, opts);\n\n        // Parsing and compiling is only supported with string input.\n        if (typeof path === \"string\") {\n          it(\"should parse\", function() {\n            expect(pathToRegexp.parse(path, opts)).toEqual(tokens);\n          });\n\n          describe(\"compile\", function() {\n            compileCases.forEach(function(io) {\n              const [params, result, options] = io;\n              const toPath = pathToRegexp.compile(path, {\n                ...opts,\n                ...options\n              });\n\n              if (result !== null) {\n                it(\"should compile using \" + util.inspect(params), function() {\n                  expect(toPath(params)).toEqual(result);\n                });\n              } else {\n                it(\n                  \"should not compile using \" + util.inspect(params),\n                  function() {\n                    expect(function() {\n                      toPath(params);\n                    }).toThrow(TypeError);\n                  }\n                );\n              }\n            });\n          });\n        } else {\n          it(\"should parse keys\", function() {\n            expect(keys).toEqual(\n              tokens.filter(function(token) {\n                return typeof token !== \"string\";\n              })\n            );\n          });\n        }\n\n        describe(\n          \"match\" + (opts ? \" using \" + util.inspect(opts) : \"\"),\n          function() {\n            matchCases.forEach(function(io) {\n              const [pathname, matches, params, options] = io;\n              const message = `should ${\n                matches ? \"\" : \"not \"\n              }match ${util.inspect(pathname)}`;\n\n              it(message, function() {\n                expect(exec(re, pathname)).toEqual(matches);\n              });\n\n              if (typeof path === \"string\" && params !== undefined) {\n                const match = pathToRegexp.match(path, options);\n\n                it(message + \" params\", function() {\n                  expect(match(pathname)).toEqual(params);\n                });\n              }\n            });\n          }\n        );\n      });\n    });\n  });\n\n  describe(\"compile errors\", function() {\n    it(\"should throw when a required param is undefined\", function() {\n      const toPath = pathToRegexp.compile(\"/a/:b/c\");\n\n      expect(function() {\n        toPath();\n      }).toThrow(new TypeError('Expected \"b\" to be a string'));\n    });\n\n    it(\"should throw when it does not match the pattern\", function() {\n      const toPath = pathToRegexp.compile(\"/:foo(\\\\d+)\");\n\n      expect(function() {\n        toPath({ foo: \"abc\" });\n      }).toThrow(\n        new TypeError('Expected \"foo\" to match \"\\\\d+\", but got \"abc\"')\n      );\n    });\n\n    it(\"should throw when expecting a repeated value\", function() {\n      const toPath = pathToRegexp.compile(\"/:foo+\");\n\n      expect(function() {\n        toPath({ foo: [] });\n      }).toThrow(new TypeError('Expected \"foo\" to not be empty'));\n    });\n\n    it(\"should throw when not expecting a repeated value\", function() {\n      const toPath = pathToRegexp.compile(\"/:foo\");\n\n      expect(function() {\n        toPath({ foo: [] });\n      }).toThrow(\n        new TypeError('Expected \"foo\" to not repeat, but got an array')\n      );\n    });\n\n    it(\"should throw when repeated value does not match\", function() {\n      const toPath = pathToRegexp.compile(\"/:foo(\\\\d+)+\");\n\n      expect(function() {\n        toPath({ foo: [1, 2, 3, \"a\"] });\n      }).toThrow(\n        new TypeError('Expected all \"foo\" to match \"\\\\d+\", but got \"a\"')\n      );\n    });\n  });\n});\n\n/**\n * Execute a regular expression and return a flat array for comparison.\n *\n * @param  {RegExp} re\n * @param  {String} str\n * @return {Array}\n */\nfunction exec(re: RegExp, str: string) {\n  const match = re.exec(str);\n\n  return match && Array.prototype.slice.call(match);\n}\n"]}