{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/providers/native/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,4CAAoB;AACpB,iDAA2B;AAC3B,kDAAgC;AAChC,+BAAiC;AAEjC,gEAAkC;AAClC,0DAAkC;AAClC,+CAAgD;AAChD,+BAA0D;AAC1D,iDAAoD;AACpD,yDAAqD;AASrD,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;AAC3C,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,8BAA8B,CAAC,CAAC;AAC1D,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,mBAAS,CAAC,CAAC;AAEtC,MAAqB,cAAc;IAMlC,YAAY,EAAU,EAAE,MAAoB;QAC3C,MAAM,OAAO,GAAG;YACf,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YACrC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;SACvC,CAAC;QACF,MAAM,IAAI,GAAG;YACZ,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,EAAE;YACP,oBAAoB,EAAE,IAAA,YAAE,EAAC,IAAI,CAAC;YAE9B,4DAA4D;YAC5D,sDAAsD;YAEtD,mEAAmE;YACnE,gBAAgB;YAChB,uCAAuC;YAEvC,oEAAoE;YACpE,8BAA8B;SAC9B,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAA,yBAAU,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,oBAAoB,EAAE,GAAG,CAAC,EAAE;YACxC,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,qBAAqB,EAAE,GAAG,CAAC,EAAE;YACzC,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACJ,CAAC;IAEK,aAAa;;YAClB,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;YACvE,MAAM,MAAM,GAAG,IAAA,WAAI,EAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAA,WAAI,EACrB,OAAO,CAAC,QAAQ,EAChB,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CACpC,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,8BAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,IAAA,sBAAM,EAAC,MAAM,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;YACrC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,OAAO,EAAiB,CAAC;YAEjD,KAAK,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,IAAA,cAAO,EAAC,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,IAAA,eAAQ,EAAC,OAAO,CAAC,CAAC;YAC9D,MAAM,UAAU,GACf,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;YACjE,MAAM,YAAY,GAAG,cAAc,YAAY,EAAE,CAAC;YAClD,MAAM,aAAa,GAAG,eAAe,OAAO,IAAI,IAAA,YAAI,GAAE,CAAC,OAAO,CAC7D,KAAK,EACL,EAAE,CACF,EAAE,CAAC;YAEJ,+EAA+E;YAC/E,MAAM,GAAG;gBACR,oDAAoD;gBACpD,IAAI,EAAE,GAAG,MAAM,GAAG,gBAAS,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAChD,IAAI,EAAE,aAAa,IAGhB,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBAEvD,sBAAsB;gBACtB,QAAQ,EAAE,MAAM,CAAC,OAAO,EACxB,UAAU,EAAE,MAAM,EAClB,iBAAiB,EAAE,MAAM,CAAC,WAAW,EACrC,qBAAqB,EAAE,MAAM,CAAC,eAAe,EAC7C,kBAAkB,EAAE,MAAM,EAC1B,iBAAiB,EAAE,cAAc,MAAM,CAAC,OAAO,EAAE,EACjD,wBAAwB,EAAE,YAAY,EACtC,2BAA2B,EAAE,OAAO,EACpC,+BAA+B,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC,EACjE,sBAAsB,EAAE,aAAa,IAAI,EAAE,EAC3C,yBAAyB,EAAE,YAAY,EACvC,0BAA0B,EAAE,aAAa,EACzC,kBAAkB,EAAE,OAAO,CAAC,QAAQ,EACpC,gBAAgB,EAAE,OAAO,EACzB,EAAE,EAAE,MAAM,GACV,CAAC;YAEF,IAAI,GAAG,GAAW,SAAS,CAAC;YAC5B,MAAM,IAAI,GAAa,EAAE,CAAC;YAC1B,IAAI,KAAK,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrB,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC;aACvB;YAED,MAAM,IAAI,GAAG,IAAA,qBAAK,EAAC,GAAG,EAAE,IAAI,EAAE;gBAC7B,GAAG;gBACH,GAAG,EAAE,OAAO;gBACZ,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;aACvC,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAEnC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAO,IAAI,EAAE,MAAM,EAAE,EAAE;gBACtC,KAAK,CACJ,iDAAiD,EACjD,IAAI,CAAC,GAAG,EACR,IAAI,EACJ,MAAM,CACN,CAAC;gBACF,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAI,MAAM,EAAE;oBACX,KAAK,CAAC,kCAAkC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;oBACpD,MAAM,CAAC,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBAC9B;qBAAM;oBACN,KAAK,CACJ,2EAA2E,EAC3E,IAAI,CAAC,GAAG,CACR,CAAC;iBACF;YACF,CAAC,CAAA,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAEK,cAAc,CAAC,IAAkB;;YACtC,IAAI;gBACH,oEAAoE;gBACpE,2DAA2D;gBAC3D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE3B,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvC,MAAM,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACzB;YAAC,OAAO,GAAG,EAAE;gBACb,kEAAkE;gBAClE,6DAA6D;gBAC7D,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAC3D,KAAK,CACJ,mCAAmC,EACnC,IAAI,CAAC,GAAG,EACR,GAAG,CAAC,OAAO,CACX,CAAC;iBACF;qBAAM;oBACN,MAAM,GAAG,CAAC;iBACV;aACD;QACF,CAAC;KAAA;IAED,aAAa,CAAC,IAAkB;QAC/B,wCAAwC;QACxC,IAAI,CAAC,KAAK,EAAE;YACX,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;SAClC;IACF,CAAC;IAED,eAAe,CAAC,IAAkB;QACjC,wCAAwC;QACxC,IAAI,CAAC,KAAK,EAAE;YACX,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;SAClC;IACF,CAAC;IAEK,MAAM,CAAC,MAAoB;;YAChC,IAAI,MAAoB,CAAC;YACzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE1C,IAAI,MAAM,CAAC,YAAY,EAAE;gBACxB,yDAAyD;gBACzD,uDAAuD;gBACvD,KAAK,CAAC,gCAAgC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClD,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC;gBACpD,IAAI,SAAS,EAAE;oBACd,KAAK,CACJ,+CAA+C,EAC/C,IAAI,CAAC,GAAG,CACR,CAAC;oBACF,wDAAwD;oBACxD,2DAA2D;oBAC3D,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC9B,OAAO,SAAS,CAAC;iBACjB;gBACD,KAAK,CAAC,sCAAsC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;aACxD;iBAAM;gBACN,uDAAuD;gBACvD,4CAA4C;gBAC5C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aAC3B;YAED,IAAI;gBACH,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACrC;YAAC,OAAO,GAAG,EAAE;gBACb,MAAM,GAAG;oBACR,UAAU,EAAE,GAAG;oBACf,aAAa,EAAE,WAAW;oBAC1B,eAAe,EAAE,SAAS;oBAC1B,yDAAyD;oBACzD,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;wBACvB,YAAY,EAAE,GAAG,CAAC,OAAO;qBACzB,CAAC;iBACF,CAAC;aACF;YAED,IAAI,MAAM,CAAC,aAAa,KAAK,WAAW,EAAE;gBACzC,8DAA8D;gBAC9D,kEAAkE;gBAClE,qDAAqD;gBACrD,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC9B;iBAAM;gBACN,sDAAsD;gBACtD,sDAAsD;gBACtD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACzB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,OAAO,MAAM,CAAC;QACf,CAAC;KAAA;IAEK,OAAO;;YACZ,KAAK,CAAC,eAAe,CAAC,CAAC;YACvB,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QACnB,CAAC;KAAA;CACD;AAjOD,iCAiOC"}