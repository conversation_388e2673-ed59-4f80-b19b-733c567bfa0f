{"version": 3, "file": "unzip.js", "sourceRoot": "", "sources": ["../../src/unzip.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2BAA4B;AAC5B,0DAA6B;AAC7B,8DAA+B;AAC/B,kDAAgC;AAChC,+BAA+C;AAC/C,uCAAsE;AACtE,0EAAgD;AAChD,iDAKuB;AAEc,wFALpC,uBAAO,OAKoC;AAAnC,4FAJA,oBAAW,OAIA;AAAE,8FAHP,0BAAa,OAGO;AAEnC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,mBAAmB,CAAC,CAAC;AAE/C,SAAsB,WAAW,CAChC,IAAqB,EACrB,SAAiB,IAAA,WAAM,GAAE;;QAEzB,MAAM,GAAG,GAAG,IAAA,WAAI,EACf,MAAM,EACN,YAAY,IAAI,CAAC,MAAM,EAAE;aACvB,QAAQ,CAAC,EAAE,CAAC;aACZ,SAAS,CAAC,CAAC,CAAC,EAAE,CAChB,CAAC;QACF,IAAI,GAAY,CAAC;QACjB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC1B,KAAK,CAAC,6CAA6C,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACvE,GAAG,GAAG,MAAM,IAAA,0BAAa,EAAC,IAAI,CAAC,CAAC;SAChC;aAAM;YACN,KAAK,CAAC,6BAA6B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YAChD,GAAG,GAAG,MAAM,IAAA,oBAAW,EAAC,IAAI,CAAC,CAAC;SAC9B;QACD,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACtB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;QAClB,KAAK,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;QACvC,OAAO,GAAG,CAAC;IACZ,CAAC;CAAA;AAtBD,kCAsBC;AAED,MAAM,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE,CAChC,IAAI,mBAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,sBAAsB,KAAK,EAAE,EAAE,CAAC,CAAC;AAMzD,SAAsB,KAAK,CAC1B,OAAgB,EAChB,GAAW,EACX,OAAqB,EAAE;;QAEvB,IAAI,KAAY,CAAC;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QAC9B,OAAO,CAAC,KAAK,GAAG,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GACb,KAAK,KAAK,CAAC;gBACV,CAAC,CAAC,KAAK,CAAC,QAAQ;gBAChB,CAAC,CAAC,KAAK,CAAC,QAAQ;qBACb,KAAK,CAAC,GAAG,CAAC;qBACV,KAAK,CAAC,KAAK,CAAC;qBACZ,IAAI,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACrC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBAC/B,KAAK,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;gBACzC,MAAM,IAAA,iBAAM,EAAC,QAAQ,CAAC,CAAC;aACvB;iBAAM;gBACN,MAAM,CAAC,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;oBACvC,KAAK,CAAC,cAAc,EAAE;oBACtB,iCAAiC;oBACjC,IAAA,iBAAM,EAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC;iBACzB,CAAC,CAAC;gBACH,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;oBAC1B,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAA,2BAAe,EAAC,WAAW,CAAC,CAAC,CAAC;oBAC5D,KAAK,CAAC,kCAAkC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBAC9D,MAAM,IAAA,kBAAO,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;iBAClC;qBAAM;oBACN,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjC,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;oBACvC,IAAI,OAAO,KAAK,CAAC,EAAE;wBAClB,KAAK,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;qBACxC;yBAAM;wBACN,KAAK,CACJ,wCAAwC,EACxC,QAAQ,EACR,SAAS,EACT,MAAM,CAAC,IAAI,CAAC,CACZ,CAAC;qBACF;oBACD,IAAI;wBACH,MAAM,IAAA,iBAAM,EAAC,QAAQ,CAAC,CAAC;qBACvB;oBAAC,OAAO,GAAG,EAAE;wBACb,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;4BAC1B,MAAM,GAAG,CAAC;yBACV;qBACD;oBACD,MAAM,UAAU,GAAG,IAAA,4BAAiB,EAAC,QAAQ,EAAE;wBAC9C,IAAI,EAAE,OAAO;qBACb,CAAC,CAAC;oBACH,MAAM,IAAA,qBAAI,EACT,WAAW,EACX,UAAU,CACV,CAAC;iBACF;aACD;SACD;IACF,CAAC;CAAA;AA5DD,sBA4DC"}