{"version": 3, "file": "bootstrap.js", "sourceRoot": "", "sources": ["../../../../src/runtimes/nodejs/bootstrap.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;GAEG;AACH,gDAAwB;AA8BxB,MAAM,YAAY,GAAG,qBAAqB,CAAC;AAE3C,MAAM,EACL,wBAAwB,EACxB,2BAA2B,EAC3B,+BAA+B,EAC/B,yBAAyB,EACzB,0BAA0B,EAC1B,gBAAgB,EAChB,QAAQ,EACR,sBAAsB,EACtB,GAAG,OAAO,CAAC,GAAG,CAAC;AAEhB,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AAEzB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAEvD,KAAK,EAAE,CAAC;AAER,kDAAkD;AAClD,SAAS,SAAS,CAAC,EAAE;IACpB,OAAO,UAAS,GAAG,IAAI;QACtB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACzB,IAAI,GAAG;oBAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,MAAM,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,IAAI,OAAO,CAAC,KAAK,WAAW,EAAE;gBAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;aACX;QACF,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC;AACH,CAAC;AAED,SAAe,KAAK;;QACnB,IAAI,OAAO,CAAC;QACZ,IAAI;YACH,OAAO,GAAG,UAAU,EAAE,CAAC;SACvB;QAAC,OAAO,CAAC,EAAE;YACX,MAAM,SAAS,CAAC,CAAC,CAAC,CAAC;YACnB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACvB;QACD,IAAI;YACH,MAAM,aAAa,CAAC,OAAO,CAAC,CAAC;SAC7B;QAAC,OAAO,CAAC,EAAE;YACX,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACvB;IACF,CAAC;CAAA;AAED,SAAe,aAAa,CAAC,OAAO;;QACnC,OAAO,IAAI,EAAE;YACZ,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,MAAM,cAAc,EAAE,CAAC;YAClD,IAAI,MAAM,CAAC;YACX,IAAI;gBACH,MAAM,GAAG,MAAM,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;aACvC;YAAC,OAAO,CAAC,EAAE;gBACX,MAAM,WAAW,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAC9B,SAAS;aACT;YACD,MAAM,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SACtC;IACF,CAAC;CAAA;AAED,SAAe,SAAS,CAAC,GAAG;;QAC3B,OAAO,SAAS,CAAC,GAAG,YAAY,aAAa,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;CAAA;AAED,SAAe,cAAc;;QAC5B,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,YAAY,kBAAkB,EAAE,CAAC,CAAC;QAEvE,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE;YAC3B,MAAM,IAAI,KAAK,CACd,yCAAyC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAC9D,CAAC;SACF;QAED,IAAI,GAAG,CAAC,OAAO,CAAC,yBAAyB,CAAC,EAAE;YAC3C,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;SACtE;aAAM;YACN,OAAO,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;SACpC;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAElE,MAAM,OAAO,GAAkB;YAC9B,8BAA8B,EAAE,KAAK;YACrC,YAAY,EAAE,yBAAyB;YACvC,aAAa,EAAE,0BAA0B;YACzC,YAAY,EAAE,wBAAwB;YACtC,eAAe,EAAE,+BAA+B;YAChD,eAAe,EAAE,2BAA2B;YAC5C,QAAQ,EAAE,YAAY;YACtB,YAAY;YACZ,kBAAkB,EAAE,GAAG,CAAC,OAAO,CAAC,qCAAqC,CAAC;YACtE,wBAAwB,EAAE,GAAG,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE;SACvD,CAAC;QAEF,IAAI,GAAG,CAAC,OAAO,CAAC,+BAA+B,CAAC,EAAE;YACjD,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CACjC,GAAG,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAC5C,CAAC;SACF;QAED,IAAI,GAAG,CAAC,OAAO,CAAC,iCAAiC,CAAC,EAAE;YACnD,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAC5B,GAAG,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAC9C,CAAC;SACF;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEnC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;IAC3B,CAAC;CAAA;AAED,SAAe,cAAc,CAAC,MAAM,EAAE,OAAO;;QAC5C,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC;YACzB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,GAAG,YAAY,eAAe,OAAO,CAAC,YAAY,WAAW;YACnE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;SAC5B,CAAC,CAAC;QACH,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE;YAC3B,MAAM,IAAI,KAAK,CACd,6CAA6C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAClE,CAAC;SACF;IACF,CAAC;CAAA;AAED,SAAe,WAAW,CAAC,GAAG,EAAE,OAAO;;QACtC,OAAO,SAAS,CACf,GAAG,YAAY,eAAe,OAAO,CAAC,YAAY,QAAQ,EAC1D,GAAG,CACH,CAAC;IACH,CAAC;CAAA;AAED,SAAe,SAAS,CAAC,IAAI,EAAE,GAAG;;QACjC,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC;YACzB,MAAM,EAAE,MAAM;YACd,IAAI;YACJ,OAAO,EAAE;gBACR,cAAc,EAAE,kBAAkB;gBAClC,oCAAoC,EAAE,SAAS,CAAC,SAAS;aACzD;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;SAC/B,CAAC,CAAC;QACH,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI,cAAc,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACvE;IACF,CAAC;CAAA;AAED,SAAS,UAAU;IAClB,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE1D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,eAAe,QAAQ,EAAE,CAAC,CAAC;KAC3C;IAED,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC;IAC3C,MAAM,UAAU,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEpE,IAAI,GAAG,CAAC;IACR,IAAI;QACH,GAAG,GAAG,OAAO,CAAC,GAAG,gBAAgB,IAAI,UAAU,EAAE,CAAC,CAAC;KACnD;IAAC,OAAO,CAAC,EAAE;QACX,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAkB,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,4BAA4B,UAAU,GAAG,CAAC,CAAC;SAC3D;QACD,MAAM,CAAC,CAAC;KACR;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;IAErC,IAAI,WAAW,IAAI,IAAI,EAAE;QACxB,MAAM,IAAI,KAAK,CACd,YAAY,WAAW,wBAAwB,UAAU,GAAG,CAC5D,CAAC;KACF;SAAM,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;QAC7C,MAAM,IAAI,KAAK,CACd,YAAY,WAAW,WAAW,UAAU,qBAAqB,CACjE,CAAC;KACF;IAED,OAAO,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;AACvE,CAAC;AAED,SAAe,OAAO,CAAC,OAAO;;QAC7B,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAEpB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBACvC,MAAM,IAAI,GAAG,EAAE,CAAC;gBAChB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBACxC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAClB,OAAO,CAAC;oBACP,UAAU,EAAE,GAAG,CAAC,UAAU;oBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;iBAC1C,CAAC,CACF,CAAC;gBACF,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACxB,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC;CAAA;AAED,SAAS,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE;IAC5C,OAAO;QACN,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;KAC9C,CAAC;AACH,CAAC"}