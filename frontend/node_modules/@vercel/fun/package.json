{"name": "@vercel/fun", "version": "1.1.0", "description": "Local Lambda development environment", "main": "dist/src/index", "typings": "dist/src/index", "license": "Apache-2.0", "repository": "vercel/fun", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "echo \"Node.js version: $(node -v)\\n\" && yarn build && jest --verbose test/test.js", "test-codecov": "nyc npm test", "report-codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "lint:staged": "lint-staged", "prettier": "prettier --write --single-quote './{src,test}/**/*.ts'", "prepublishOnly": "npm run build && rm -rf dist/test"}, "files": ["dist/src"], "dependencies": {"@tootallnate/once": "2.0.0", "async-listen": "1.2.0", "debug": "4.1.1", "execa": "3.2.0", "fs-extra": "8.1.0", "generic-pool": "3.4.2", "micro": "9.3.5-canary.3", "ms": "2.1.1", "node-fetch": "2.6.7", "path-match": "1.2.4", "promisepipe": "3.0.0", "semver": "7.3.5", "stat-mode": "0.3.0", "stream-to-promise": "2.2.0", "tar": "4.4.18", "tree-kill": "1.2.2", "uid-promise": "1.0.0", "uuid": "3.3.2", "xdg-app-paths": "5.1.0", "yauzl-promise": "2.1.3"}, "devDependencies": {"@types/debug": "4.1.7", "@types/fs-extra": "8.1.0", "@types/generic-pool": "3.1.9", "@types/jest": "27.0.2", "@types/micro": "7.3.6", "@types/node": "10.12.29", "@types/node-fetch": "2.5.0", "@types/tar": "4.0.0", "@types/uuid": "8.3.1", "@types/yauzl-promise": "2.1.0", "codecov": "3.7.1", "cpy-cli": "2.0.0", "jest": "27.3.1", "lint-staged": "9.2.5", "nyc": "14.1.1", "pkg": "4.3.7", "pre-commit": "1.2.2", "prettier": "1.15.3", "rimraf": "2.6.3", "source-map-support": "0.5.10", "ts-jest": "27.0.7", "typescript": "4.4.4"}, "pre-commit": "lint:staged", "jest": {"preset": "ts-jest", "globals": {"ts-jest": {"diagnostics": false, "isolatedModules": true}}, "testEnvironment": "node"}, "lint-staged": {"*.ts": ["prettier --write --single-quote", "git add"]}, "packageManager": "pnpm@8.7.4", "engines": {"node": ">= 10"}}