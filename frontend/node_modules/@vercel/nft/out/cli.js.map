{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;AAEA,+BAAgE;AAChE,6CAA4D;AAC5D,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,sBAAQ,CAAC;AACrC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,uDAAkD;AAGlD,SAAS,UAAU,CACjB,IAAY,EACZ,OAA6B,EAC7B,MAAgB,EAChB,GAAW;IAEX,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEjC,IACE,CAAC,MAAM;QACP,CAAC,MAAM,CAAC,OAAO;QACf,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,EAC5B;QACA,OAAO;KACR;IAED,KAAK,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;QACjC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;KAC1C;AACH,CAAC;AAED,KAAK,UAAU,GAAG,CAChB,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EACxB,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAC5B,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAC3B,SAAS,GAAG,MAAM,EAClB,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE;IAEnB,MAAM,IAAI,GAAG;QACX,EAAE,EAAE,IAAI;QACR,IAAI,EAAE,GAAG;QACT,YAAY,EAAE,IAAI;QAClB,GAAG,EAAE,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO;KAC5C,CAAC;IACF,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,+BAAa,EACtE,CAAC,UAAU,CAAC,EACZ,IAAI,CACL,CAAC;IACF,MAAM,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/D,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,MAAM,KAAK,OAAO,EAAE;QACtB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClB,IAAI,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE;YACrB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzB,KAAK,IAAI,OAAO,IAAI,QAAQ,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;aACjC;SACF;KACF;SAAM,IAAI,MAAM,KAAK,OAAO,EAAE;QAC7B,MAAM,CAAC,IAAI,CAAC,IAAA,WAAI,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;QAClC,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;YACxB,MAAM,GAAG,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACzB,MAAM,IAAI,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACtC,MAAM,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAC3B;KACF;SAAM,IAAI,MAAM,KAAK,MAAM,EAAE;QAC5B,MAAM,cAAc,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,KAAK,CAAC;QAC5D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;YACxB,MAAM,KAAK,GAAG,IAAA,uBAAS,EAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC9B,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC;aACrB;iBAAM;gBACL,MAAM,IAAI,GAAG,IAAA,sBAAQ,EAAC,CAAC,CAAC,CAAC;gBACzB,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;aACpB;SACF;QACD,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC;KACrC;SAAM,IAAI,MAAM,KAAK,KAAK,EAAE;QAC3B,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;QACD,MAAM,mBAAmB,GAAG,CAC1B,IAAA,iBAAU,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAA,eAAQ,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAC7D,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAG,CAAC,CAAC;QAEzB,UAAU,CAAC,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;KACvD;SAAM;QACL,MAAM,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CACT,kEAAkE,CACnE,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QACrE,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QACzE,MAAM,CAAC,IAAI,CACT,yEAAyE,CAC1E,CAAC;KACH;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;IAC3B,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CAC9C;AAED,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC"}