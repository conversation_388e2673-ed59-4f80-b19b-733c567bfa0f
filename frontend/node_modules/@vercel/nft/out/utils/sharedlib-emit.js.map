{"version": 3, "file": "sharedlib-emit.js", "sourceRoot": "", "sources": ["../../src/utils/sharedlib-emit.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,yDAAoD;AAGpD,IAAI,aAAa,GAAG,EAAE,CAAC;AACvB,QAAQ,YAAE,CAAC,QAAQ,EAAE,EAAE;IACrB,KAAK,QAAQ;QACX,aAAa,GAAG,wBAAwB,CAAC;QACzC,MAAM;IACR,KAAK,OAAO;QACV,aAAa,GAAG,WAAW,CAAC;QAC5B,MAAM;IACR;QACE,aAAa,GAAG,eAAe,CAAC;CACnC;AAED,+EAA+E;AACxE,KAAK,UAAU,aAAa,CAAC,IAAY,EAAE,GAAQ;IACxD,mDAAmD;IACnD,MAAM,OAAO,GAAG,IAAA,iCAAc,EAAC,IAAI,CAAC,CAAC;IACrC,IAAI,CAAC,OAAO;QAAE,OAAO;IAErB,MAAM,KAAK,GAAG,MAAM,IAAI,OAAO,CAAW,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAC5D,IAAA,cAAI,EACF,OAAO,GAAG,aAAa,EACvB,EAAE,MAAM,EAAE,OAAO,GAAG,uBAAuB,EAAE,GAAG,EAAE,IAAI,EAAE,EACxD,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CACrD,CACF,CAAC;IACF,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAChF,CAAC;AAbD,sCAaC"}