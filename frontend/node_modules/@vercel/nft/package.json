{"name": "@vercel/nft", "version": "0.27.2", "repository": "vercel/nft", "license": "MIT", "main": "./out/index.js", "types": "./out/index.d.ts", "bin": {"nft": "./out/cli.js"}, "files": ["out"], "scripts": {"build": "tsc", "git-pre-commit": "prettier --write . && git add -A", "prepublishOnly": "tsc && rm out/utils/*.d.ts && rm out/tsconfig.tsbuildinfo", "prettier-check": "prettier --check .", "prettier-fix": "prettier --write .", "test": "jest --verbose", "test-verbose": "tsc --sourceMap && jest --verbose --coverage --globals \"{\\\"coverage\\\":true}\""}, "prettier": "@vercel/style-guide/prettier", "dependencies": {"@mapbox/node-pre-gyp": "^1.0.5", "@rollup/pluginutils": "^4.0.0", "acorn": "^8.6.0", "acorn-import-attributes": "^1.9.5", "async-sema": "^3.1.1", "bindings": "^1.4.0", "estree-walker": "2.0.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "micromatch": "^4.0.2", "node-gyp-build": "^4.2.2", "resolve-from": "^5.0.0"}, "devDependencies": {"@azure/cosmos": "^2.1.7", "@bugsnag/js": "^6.3.2", "@datadog/pprof": "^5.2.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/bigquery": "^4.1.4", "@google-cloud/firestore": "^7.6.0", "@opentelemetry/api": "^1.7.0", "@sentry/node": "^5.5.0", "@tpluscode/sparql-builder": "^0.3.12", "@types/bindings": "^1.3.0", "@types/estree": "^0.0.47", "@types/glob": "^7.1.2", "@types/graceful-fs": "^4.1.5", "@types/micromatch": "^4.0.1", "@types/node": "^14.14.37", "@vercel/git-hooks": "^1.0.0", "@vercel/style-guide": "^5.2.0", "analytics-node": "^3.4.0-beta.1", "apollo-server-express": "^2.14.2", "argon2": "^0.31.1", "auth0": "^3.0.1", "axios": "^1.6.0", "azure-storage": "^2.10.3", "bcrypt": "^5.0.1", "browserify-middleware": "^8.1.1", "bull": "^3.10.0", "bullmq": "^4.10.0", "camaro": "^6.1.0", "chromeless": "^1.5.2", "consolidate": "^0.15.1", "copy": "^0.3.2", "cowsay": "^1.4.0", "es-get-iterator": "^1.1.0", "esbuild": "^0.14.12", "esm": "^3.2.25", "express": "^4.19.2", "fast-glob": "^3.1.1", "fetch-h2": "^2.2.0", "firebase": "^7", "firebase-admin": "^12.0.0", "fluent-ffmpeg": "^2.1.2", "geo-tz": "^7.0.1", "graphql": "^14.4.2", "highlights": "^3.1.6", "hot-shots": "^6.3.0", "ioredis": "^4.11.1", "isomorphic-unfetch": "^3.0.0", "jest": "^27.4.5", "jimp": "^0.6.4", "jugglingdb": "^2.0.1", "koa": "^2.7.0", "leveldown": "^5.6.0", "lighthouse": "^5.1.0", "loopback": "^3.26.0", "mailgun": "^0.5.0", "mariadb": "^2.0.5", "memcached": "^2.2.2", "microtime": "^3.0.0", "mongoose": "^5.13.20", "mysql": "^2.17.1", "oracledb": "^6.2.0", "paraphrase": "1.8.0", "passport": "^0.6.0", "passport-google-oauth": "^2.0.0", "passport-trakt": "^1.0.4", "path-platform": "^0.11.15", "pdf2json": "^2.0.0", "pdfkit": "^0.14.0", "pg": "^7.11.0", "phantomjs-prebuilt": "^2.1.16", "pixelmatch": "^5.2.1", "playwright-core": "^1.17.1", "polyfill-library": "3.93.0", "prettier": "^3.2.5", "prismjs": "^1.27.0", "pug": "^3.0.1", "react": "^16.14.0", "react-dom": "^16.14.0", "redis": "^3.1.1", "remark-parse": "^10.0.0", "remark-prism": "^1.3.6", "rimraf": "^3.0.2", "rxjs": "^6.5.2", "saslprep": "^1.0.3", "semver": "^7.5.2", "sequelize": "^6.29.0", "serialport": "^12.0.0", "sharp": "^0.33.1", "shiki": "^0.14.5", "socket.io": "^2.4.0", "socket.io-client": "^2.2.0", "stripe": "^7.4.0", "swig": "^1.4.2", "tiny-json-http": "^7.1.2", "twilio": "^4.20.0", "typescript": "^4.8.4", "uglify-js": "^3.6.0", "unified": "^10.1.0", "vm2": "^3.9.18", "vue": "^2.6.10", "vue-server-renderer": "^2.6.10", "when": "^3.7.8", "zeromq": "^6.0.0-beta.19"}, "packageManager": "npm@10.2.5", "engines": {"node": ">=16"}, "publishConfig": {"access": "public"}}