{"name": "@vercel/remix-builder", "version": "2.1.10", "license": "Apache-2.0", "main": "./dist/index.js", "homepage": "https://vercel.com/docs", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/remix"}, "files": ["dist", "defaults"], "dependencies": {"@vercel/error-utils": "2.0.2", "@vercel/nft": "0.27.2", "@vercel/static-config": "3.0.0", "ts-morph": "12.0.0"}, "devDependencies": {"@remix-run/dev": "npm:@vercel/remix-run-dev@2.10.2", "@types/jest": "27.5.1", "@types/node": "14.18.33", "@types/semver": "7.3.13", "@vercel/build-utils": "8.3.2", "glob": "10.3.16", "jest-junit": "16.0.0", "path-to-regexp": "6.2.1", "semver": "7.5.2", "vitest": "1.3.1"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --bail --runInBand", "vitest-run": "vitest", "vitest-unit": "glob --absolute 'test/unit/**/*.test.ts'", "vitest-e2e": "glob --absolute test/integration-*.test.ts", "type-check": "tsc --noEmit"}}