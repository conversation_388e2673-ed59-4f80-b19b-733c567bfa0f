import {
  createRequestHandler as createRemix<PERSON>equestHandler,
  writeReadableStreamToWritable,
  installGlobals,
} from '@remix-run/node';

installGlobals({
  nativeFetch:
    parseInt(process.versions.node, 10) >= 20 &&
    process.env.VERCEL_REMIX_NATIVE_FETCH === '1',
});

import * as build from '@remix-run/dev/server-build';

const handleRequest = createRemixRequestHandler(
  build.default || build,
  process.env.NODE_ENV
);

function toWebHeaders(nodeHeaders) {
  const headers = new Headers();

  for (const key in nodeHeaders) {
    const header = nodeHeaders[key];
    // set-cookie is an array (maybe others)
    if (Array.isArray(header)) {
      for (const value of header) {
        headers.append(key, value);
      }
    } else {
      headers.append(key, header);
    }
  }

  return headers;
}

function toNodeHeaders(webHeaders) {
  return webHeaders.raw?.() || [...webHeaders].flat();
}

function createRemixRequest(req, res) {
  const host = req.headers['x-forwarded-host'] || req.headers.host;
  const protocol = req.headers['x-forwarded-proto'] || 'https';
  const url = new URL(req.url, `${protocol}://${host}`);

  // Abort action/loaders once we can no longer write a response
  const controller = new AbortController();
  res.on('close', () => controller.abort());

  const init = {
    method: req.method,
    headers: toWebHeaders(req.headers),
    signal: controller.signal,
  };

  if (req.method !== 'GET' && req.method !== 'HEAD') {
    init.body = req;
  }

  return new Request(url.href, init);
}

async function sendRemixResponse(res, nodeResponse) {
  res.statusMessage = nodeResponse.statusText;
  res.writeHead(
    nodeResponse.status,
    nodeResponse.statusText,
    toNodeHeaders(nodeResponse.headers)
  );

  if (nodeResponse.body) {
    await writeReadableStreamToWritable(nodeResponse.body, res);
  } else {
    res.end();
  }
}

export default async (req, res) => {
  const request = createRemixRequest(req, res);
  const response = await handleRequest(request);
  await sendRemixResponse(res, response);
};
