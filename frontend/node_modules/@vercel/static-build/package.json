{"name": "@vercel/static-build", "version": "2.5.14", "license": "Apache-2.0", "main": "./dist/index", "homepage": "https://vercel.com/docs/build-step", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/static-build"}, "dependencies": {"@vercel/gatsby-plugin-vercel-analytics": "1.0.11", "@vercel/gatsby-plugin-vercel-builder": "2.0.36", "@vercel/static-config": "3.0.0", "ts-morph": "12.0.0"}, "devDependencies": {"@types/aws-lambda": "8.10.64", "@types/cross-spawn": "6.0.0", "@types/fs-extra": "9.0.13", "@types/jest": "27.4.1", "@types/ms": "0.7.31", "@types/node": "14.18.33", "@types/node-fetch": "2.5.4", "@types/promise-timeout": "1.3.0", "@types/semver": "7.3.13", "@vercel/build-utils": "8.3.2", "@vercel/error-utils": "2.0.2", "@vercel/frameworks": "3.0.3", "@vercel/fs-detectors": "5.2.5", "@vercel/routing-utils": "3.1.0", "execa": "3.2.0", "fs-extra": "10.0.0", "get-port": "5.0.0", "is-port-reachable": "2.0.1", "jest-junit": "16.0.0", "ms": "2.1.2", "node-fetch": "2.6.7", "rc9": "1.2.0", "semver": "7.5.2", "tree-kill": "1.2.2"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --bail --runInBand", "test-unit": "pnpm test test/build.test.ts test/hugo.test.ts test/gatsby.test.ts test/prepare-cache.test.ts", "test-e2e": "pnpm test test/integration-*.test.js", "type-check": "tsc --noEmit"}}