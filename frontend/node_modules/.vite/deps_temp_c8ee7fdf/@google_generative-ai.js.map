{"version": 3, "sources": ["../../@google/generative-ai/dist/index.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Possible roles.\n * @public\n */\nconst POSSIBLE_ROLES = [\"user\", \"model\", \"function\", \"system\"];\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nvar HarmCategory;\n(function (HarmCategory) {\n    HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n    HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n    HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n    HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n    HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n})(HarmCategory || (HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nvar HarmBlockThreshold;\n(function (HarmBlockThreshold) {\n    // Threshold is unspecified.\n    HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n    // Content with NEGLIGIBLE will be allowed.\n    HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n    // Content with NEGLIGIBLE and LOW will be allowed.\n    HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n    // Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed.\n    HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n    // All content will be allowed.\n    HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(HarmBlockThreshold || (HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nvar HarmProbability;\n(function (HarmProbability) {\n    // Probability is unspecified.\n    HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n    // Content has a negligible chance of being unsafe.\n    HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n    // Content has a low chance of being unsafe.\n    HarmProbability[\"LOW\"] = \"LOW\";\n    // Content has a medium chance of being unsafe.\n    HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n    // Content has a high chance of being unsafe.\n    HarmProbability[\"HIGH\"] = \"HIGH\";\n})(HarmProbability || (HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nvar BlockReason;\n(function (BlockReason) {\n    // A blocked reason was not specified.\n    BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n    // Content was blocked by safety settings.\n    BlockReason[\"SAFETY\"] = \"SAFETY\";\n    // Content was blocked, but the reason is uncategorized.\n    BlockReason[\"OTHER\"] = \"OTHER\";\n})(BlockReason || (BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nvar FinishReason;\n(function (FinishReason) {\n    // Default value. This value is unused.\n    FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n    // Natural stop point of the model or provided stop sequence.\n    FinishReason[\"STOP\"] = \"STOP\";\n    // The maximum number of tokens as specified in the request was reached.\n    FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n    // The candidate content was flagged for safety reasons.\n    FinishReason[\"SAFETY\"] = \"SAFETY\";\n    // The candidate content was flagged for recitation reasons.\n    FinishReason[\"RECITATION\"] = \"RECITATION\";\n    // Unknown reason.\n    FinishReason[\"OTHER\"] = \"OTHER\";\n})(FinishReason || (FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nvar TaskType;\n(function (TaskType) {\n    TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n    TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n    TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n    TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n    TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n    TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(TaskType || (TaskType = {}));\n/**\n * @public\n */\nvar FunctionCallingMode;\n(function (FunctionCallingMode) {\n    // Unspecified function calling mode. This value should not be used.\n    FunctionCallingMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Default model behavior, model decides to predict either a function call\n    // or a natural language repspose.\n    FunctionCallingMode[\"AUTO\"] = \"AUTO\";\n    // Model is constrained to always predicting a function call only.\n    // If \"allowed_function_names\" are set, the predicted function call will be\n    // limited to any one of \"allowed_function_names\", else the predicted\n    // function call will be any one of the provided \"function_declarations\".\n    FunctionCallingMode[\"ANY\"] = \"ANY\";\n    // Model will not predict any function call. Model behavior is same as when\n    // not passing any function declarations.\n    FunctionCallingMode[\"NONE\"] = \"NONE\";\n})(FunctionCallingMode || (FunctionCallingMode = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nvar FunctionDeclarationSchemaType;\n(function (FunctionDeclarationSchemaType) {\n    /** String type. */\n    FunctionDeclarationSchemaType[\"STRING\"] = \"STRING\";\n    /** Number type. */\n    FunctionDeclarationSchemaType[\"NUMBER\"] = \"NUMBER\";\n    /** Integer type. */\n    FunctionDeclarationSchemaType[\"INTEGER\"] = \"INTEGER\";\n    /** Boolean type. */\n    FunctionDeclarationSchemaType[\"BOOLEAN\"] = \"BOOLEAN\";\n    /** Array type. */\n    FunctionDeclarationSchemaType[\"ARRAY\"] = \"ARRAY\";\n    /** Object type. */\n    FunctionDeclarationSchemaType[\"OBJECT\"] = \"OBJECT\";\n})(FunctionDeclarationSchemaType || (FunctionDeclarationSchemaType = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Basic error type for this SDK.\n * @public\n */\nclass GoogleGenerativeAIError extends Error {\n    constructor(message) {\n        super(`[GoogleGenerativeAI Error]: ${message}`);\n    }\n}\n/**\n * Errors in the contents of a response from the model. This includes parsing\n * errors, or responses including a safety block reason.\n * @public\n */\nclass GoogleGenerativeAIResponseError extends GoogleGenerativeAIError {\n    constructor(message, response) {\n        super(message);\n        this.response = response;\n    }\n}\n/**\n * Error class covering HTTP errors when calling the server. Includes HTTP\n * status, statusText, and optional details, if provided in the server response.\n * @public\n */\nclass GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {\n    constructor(message, status, statusText, errorDetails) {\n        super(message);\n        this.status = status;\n        this.statusText = statusText;\n        this.errorDetails = errorDetails;\n    }\n}\n/**\n * Errors in the contents of a request originating from user input.\n * @public\n */\nclass GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst DEFAULT_API_VERSION = \"v1beta\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.11.5\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n    Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n    Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n    Task[\"COUNT_TOKENS\"] = \"countTokens\";\n    Task[\"EMBED_CONTENT\"] = \"embedContent\";\n    Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\nclass RequestUrl {\n    constructor(model, task, apiKey, stream, requestOptions) {\n        this.model = model;\n        this.task = task;\n        this.apiKey = apiKey;\n        this.stream = stream;\n        this.requestOptions = requestOptions;\n    }\n    toString() {\n        var _a, _b;\n        const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n        const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n        let url = `${baseUrl}/${apiVersion}/${this.model}:${this.task}`;\n        if (this.stream) {\n            url += \"?alt=sse\";\n        }\n        return url;\n    }\n}\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders(requestOptions) {\n    const clientHeaders = [];\n    if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiClient) {\n        clientHeaders.push(requestOptions.apiClient);\n    }\n    clientHeaders.push(`${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`);\n    return clientHeaders.join(\" \");\n}\nasync function getHeaders(url) {\n    const headers = new Headers();\n    headers.append(\"Content-Type\", \"application/json\");\n    headers.append(\"x-goog-api-client\", getClientHeaders(url.requestOptions));\n    headers.append(\"x-goog-api-key\", url.apiKey);\n    let customHeaders = url.requestOptions.customHeaders;\n    if (customHeaders) {\n        if (!(customHeaders instanceof Headers)) {\n            try {\n                customHeaders = new Headers(customHeaders);\n            }\n            catch (e) {\n                throw new GoogleGenerativeAIRequestInputError(`unable to convert customHeaders value ${JSON.stringify(customHeaders)} to Headers: ${e.message}`);\n            }\n        }\n        for (const [headerName, headerValue] of customHeaders.entries()) {\n            if (headerName === \"x-goog-api-key\") {\n                throw new GoogleGenerativeAIRequestInputError(`Cannot set reserved header name ${headerName}`);\n            }\n            else if (headerName === \"x-goog-api-client\") {\n                throw new GoogleGenerativeAIRequestInputError(`Header name ${headerName} can only be set using the apiClient field`);\n            }\n            headers.append(headerName, headerValue);\n        }\n    }\n    return headers;\n}\nasync function constructRequest(model, task, apiKey, stream, body, requestOptions) {\n    const url = new RequestUrl(model, task, apiKey, stream, requestOptions);\n    return {\n        url: url.toString(),\n        fetchOptions: Object.assign(Object.assign({}, buildFetchOptions(requestOptions)), { method: \"POST\", headers: await getHeaders(url), body }),\n    };\n}\n/**\n * Wrapper for _makeRequestInternal that automatically uses native fetch,\n * allowing _makeRequestInternal to be tested with a mocked fetch function.\n */\nasync function makeRequest(model, task, apiKey, stream, body, requestOptions) {\n    return _makeRequestInternal(model, task, apiKey, stream, body, requestOptions, fetch);\n}\nasync function _makeRequestInternal(model, task, apiKey, stream, body, requestOptions, \n// Allows this to be stubbed for tests\nfetchFn = fetch) {\n    const url = new RequestUrl(model, task, apiKey, stream, requestOptions);\n    let response;\n    try {\n        const request = await constructRequest(model, task, apiKey, stream, body, requestOptions);\n        response = await fetchFn(request.url, request.fetchOptions);\n        if (!response.ok) {\n            let message = \"\";\n            let errorDetails;\n            try {\n                const json = await response.json();\n                message = json.error.message;\n                if (json.error.details) {\n                    message += ` ${JSON.stringify(json.error.details)}`;\n                    errorDetails = json.error.details;\n                }\n            }\n            catch (e) {\n                // ignored\n            }\n            throw new GoogleGenerativeAIFetchError(`Error fetching from ${url.toString()}: [${response.status} ${response.statusText}] ${message}`, response.status, response.statusText, errorDetails);\n        }\n    }\n    catch (e) {\n        let err = e;\n        if (!(e instanceof GoogleGenerativeAIFetchError ||\n            e instanceof GoogleGenerativeAIRequestInputError)) {\n            err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n            err.stack = e.stack;\n        }\n        throw err;\n    }\n    return response;\n}\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction buildFetchOptions(requestOptions) {\n    const fetchOptions = {};\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n        const abortController = new AbortController();\n        const signal = abortController.signal;\n        setTimeout(() => abortController.abort(), requestOptions.timeout);\n        fetchOptions.signal = signal;\n    }\n    return fetchOptions;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nfunction addHelpers(response) {\n    response.text = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning text from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getText(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Text not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return \"\";\n    };\n    /**\n     * TODO: remove at next major version\n     */\n    response.functionCall = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            console.warn(`response.functionCall() is deprecated. ` +\n                `Use response.functionCalls() instead.`);\n            return getFunctionCalls(response)[0];\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    response.functionCalls = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getFunctionCalls(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    return response;\n}\n/**\n * Returns all text found in all parts of first candidate.\n */\nfunction getText(response) {\n    var _a, _b, _c, _d;\n    const textStrings = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.text) {\n                textStrings.push(part.text);\n            }\n        }\n    }\n    if (textStrings.length > 0) {\n        return textStrings.join(\"\");\n    }\n    else {\n        return \"\";\n    }\n}\n/**\n * Returns functionCall of first candidate.\n */\nfunction getFunctionCalls(response) {\n    var _a, _b, _c, _d;\n    const functionCalls = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.functionCall) {\n                functionCalls.push(part.functionCall);\n            }\n        }\n    }\n    if (functionCalls.length > 0) {\n        return functionCalls;\n    }\n    else {\n        return undefined;\n    }\n}\nconst badFinishReasons = [FinishReason.RECITATION, FinishReason.SAFETY];\nfunction hadBadFinishReason(candidate) {\n    return (!!candidate.finishReason &&\n        badFinishReasons.includes(candidate.finishReason));\n}\nfunction formatBlockErrorMessage(response) {\n    var _a, _b, _c;\n    let message = \"\";\n    if ((!response.candidates || response.candidates.length === 0) &&\n        response.promptFeedback) {\n        message += \"Response was blocked\";\n        if ((_a = response.promptFeedback) === null || _a === void 0 ? void 0 : _a.blockReason) {\n            message += ` due to ${response.promptFeedback.blockReason}`;\n        }\n        if ((_b = response.promptFeedback) === null || _b === void 0 ? void 0 : _b.blockReasonMessage) {\n            message += `: ${response.promptFeedback.blockReasonMessage}`;\n        }\n    }\n    else if ((_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0]) {\n        const firstCandidate = response.candidates[0];\n        if (hadBadFinishReason(firstCandidate)) {\n            message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n            if (firstCandidate.finishMessage) {\n                message += `: ${firstCandidate.finishMessage}`;\n            }\n        }\n    }\n    return message;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nfunction processStream(response) {\n    const inputStream = response.body.pipeThrough(new TextDecoderStream(\"utf8\", { fatal: true }));\n    const responseStream = getResponseStream(inputStream);\n    const [stream1, stream2] = responseStream.tee();\n    return {\n        stream: generateResponseSequence(stream1),\n        response: getResponsePromise(stream2),\n    };\n}\nasync function getResponsePromise(stream) {\n    const allResponses = [];\n    const reader = stream.getReader();\n    while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n            return addHelpers(aggregateResponses(allResponses));\n        }\n        allResponses.push(value);\n    }\n}\nfunction generateResponseSequence(stream) {\n    return __asyncGenerator(this, arguments, function* generateResponseSequence_1() {\n        const reader = stream.getReader();\n        while (true) {\n            const { value, done } = yield __await(reader.read());\n            if (done) {\n                break;\n            }\n            yield yield __await(addHelpers(value));\n        }\n    });\n}\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nfunction getResponseStream(inputStream) {\n    const reader = inputStream.getReader();\n    const stream = new ReadableStream({\n        start(controller) {\n            let currentText = \"\";\n            return pump();\n            function pump() {\n                return reader.read().then(({ value, done }) => {\n                    if (done) {\n                        if (currentText.trim()) {\n                            controller.error(new GoogleGenerativeAIError(\"Failed to parse stream\"));\n                            return;\n                        }\n                        controller.close();\n                        return;\n                    }\n                    currentText += value;\n                    let match = currentText.match(responseLineRE);\n                    let parsedResponse;\n                    while (match) {\n                        try {\n                            parsedResponse = JSON.parse(match[1]);\n                        }\n                        catch (e) {\n                            controller.error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match[1]}\"`));\n                            return;\n                        }\n                        controller.enqueue(parsedResponse);\n                        currentText = currentText.substring(match[0].length);\n                        match = currentText.match(responseLineRE);\n                    }\n                    return pump();\n                });\n            }\n        },\n    });\n    return stream;\n}\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nfunction aggregateResponses(responses) {\n    const lastResponse = responses[responses.length - 1];\n    const aggregatedResponse = {\n        promptFeedback: lastResponse === null || lastResponse === void 0 ? void 0 : lastResponse.promptFeedback,\n    };\n    for (const response of responses) {\n        if (response.candidates) {\n            for (const candidate of response.candidates) {\n                const i = candidate.index;\n                if (!aggregatedResponse.candidates) {\n                    aggregatedResponse.candidates = [];\n                }\n                if (!aggregatedResponse.candidates[i]) {\n                    aggregatedResponse.candidates[i] = {\n                        index: candidate.index,\n                    };\n                }\n                // Keep overwriting, the last one will be final\n                aggregatedResponse.candidates[i].citationMetadata =\n                    candidate.citationMetadata;\n                aggregatedResponse.candidates[i].finishReason = candidate.finishReason;\n                aggregatedResponse.candidates[i].finishMessage =\n                    candidate.finishMessage;\n                aggregatedResponse.candidates[i].safetyRatings =\n                    candidate.safetyRatings;\n                /**\n                 * Candidates should always have content and parts, but this handles\n                 * possible malformed responses.\n                 */\n                if (candidate.content && candidate.content.parts) {\n                    if (!aggregatedResponse.candidates[i].content) {\n                        aggregatedResponse.candidates[i].content = {\n                            role: candidate.content.role || \"user\",\n                            parts: [],\n                        };\n                    }\n                    const newPart = {};\n                    for (const part of candidate.content.parts) {\n                        if (part.text) {\n                            newPart.text = part.text;\n                        }\n                        if (part.functionCall) {\n                            newPart.functionCall = part.functionCall;\n                        }\n                        if (Object.keys(newPart).length === 0) {\n                            newPart.text = \"\";\n                        }\n                        aggregatedResponse.candidates[i].content.parts.push(newPart);\n                    }\n                }\n            }\n        }\n    }\n    return aggregatedResponse;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function generateContentStream(apiKey, model, params, requestOptions) {\n    const response = await makeRequest(model, Task.STREAM_GENERATE_CONTENT, apiKey, \n    /* stream */ true, JSON.stringify(params), requestOptions);\n    return processStream(response);\n}\nasync function generateContent(apiKey, model, params, requestOptions) {\n    const response = await makeRequest(model, Task.GENERATE_CONTENT, apiKey, \n    /* stream */ false, JSON.stringify(params), requestOptions);\n    const responseJson = await response.json();\n    const enhancedResponse = addHelpers(responseJson);\n    return {\n        response: enhancedResponse,\n    };\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction formatSystemInstruction(input) {\n    // null or undefined\n    if (input == null) {\n        return undefined;\n    }\n    else if (typeof input === \"string\") {\n        return { role: \"system\", parts: [{ text: input }] };\n    }\n    else if (input.text) {\n        return { role: \"system\", parts: [input] };\n    }\n    else if (input.parts) {\n        if (!input.role) {\n            return { role: \"system\", parts: input.parts };\n        }\n        else {\n            return input;\n        }\n    }\n}\nfunction formatNewContent(request) {\n    let newParts = [];\n    if (typeof request === \"string\") {\n        newParts = [{ text: request }];\n    }\n    else {\n        for (const partOrString of request) {\n            if (typeof partOrString === \"string\") {\n                newParts.push({ text: partOrString });\n            }\n            else {\n                newParts.push(partOrString);\n            }\n        }\n    }\n    return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(parts) {\n    const userContent = { role: \"user\", parts: [] };\n    const functionContent = { role: \"function\", parts: [] };\n    let hasUserContent = false;\n    let hasFunctionContent = false;\n    for (const part of parts) {\n        if (\"functionResponse\" in part) {\n            functionContent.parts.push(part);\n            hasFunctionContent = true;\n        }\n        else {\n            userContent.parts.push(part);\n            hasUserContent = true;\n        }\n    }\n    if (hasUserContent && hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.\");\n    }\n    if (!hasUserContent && !hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"No content is provided for sending chat message.\");\n    }\n    if (hasUserContent) {\n        return userContent;\n    }\n    return functionContent;\n}\nfunction formatGenerateContentInput(params) {\n    let formattedRequest;\n    if (params.contents) {\n        formattedRequest = params;\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedRequest = { contents: [content] };\n    }\n    if (params.systemInstruction) {\n        formattedRequest.systemInstruction = formatSystemInstruction(params.systemInstruction);\n    }\n    return formattedRequest;\n}\nfunction formatEmbedContentInput(params) {\n    if (typeof params === \"string\" || Array.isArray(params)) {\n        const content = formatNewContent(params);\n        return { content };\n    }\n    return params;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// https://ai.google.dev/api/rest/v1beta/Content#part\nconst VALID_PART_FIELDS = [\n    \"text\",\n    \"inlineData\",\n    \"functionCall\",\n    \"functionResponse\",\n];\nconst VALID_PARTS_PER_ROLE = {\n    user: [\"text\", \"inlineData\"],\n    function: [\"functionResponse\"],\n    model: [\"text\", \"functionCall\"],\n    // System instructions shouldn't be in history anyway.\n    system: [\"text\"],\n};\nfunction validateChatHistory(history) {\n    let prevContent = false;\n    for (const currContent of history) {\n        const { role, parts } = currContent;\n        if (!prevContent && role !== \"user\") {\n            throw new GoogleGenerativeAIError(`First content should be with role 'user', got ${role}`);\n        }\n        if (!POSSIBLE_ROLES.includes(role)) {\n            throw new GoogleGenerativeAIError(`Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(POSSIBLE_ROLES)}`);\n        }\n        if (!Array.isArray(parts)) {\n            throw new GoogleGenerativeAIError(\"Content should have 'parts' property with an array of Parts\");\n        }\n        if (parts.length === 0) {\n            throw new GoogleGenerativeAIError(\"Each Content should have at least one part\");\n        }\n        const countFields = {\n            text: 0,\n            inlineData: 0,\n            functionCall: 0,\n            functionResponse: 0,\n            fileData: 0,\n        };\n        for (const part of parts) {\n            for (const key of VALID_PART_FIELDS) {\n                if (key in part) {\n                    countFields[key] += 1;\n                }\n            }\n        }\n        const validParts = VALID_PARTS_PER_ROLE[role];\n        for (const key of VALID_PART_FIELDS) {\n            if (!validParts.includes(key) && countFields[key] > 0) {\n                throw new GoogleGenerativeAIError(`Content with role '${role}' can't contain '${key}' part`);\n            }\n        }\n        prevContent = true;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = \"SILENT_ERROR\";\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nclass ChatSession {\n    constructor(apiKey, model, params, requestOptions) {\n        this.model = model;\n        this.params = params;\n        this.requestOptions = requestOptions;\n        this._history = [];\n        this._sendPromise = Promise.resolve();\n        this._apiKey = apiKey;\n        if (params === null || params === void 0 ? void 0 : params.history) {\n            validateChatHistory(params.history);\n            this._history = params.history;\n        }\n    }\n    /**\n     * Gets the chat history so far. Blocked prompts are not added to history.\n     * Blocked candidates are not added to history, nor are the prompts that\n     * generated them.\n     */\n    async getHistory() {\n        await this._sendPromise;\n        return this._history;\n    }\n    /**\n     * Sends a chat message and receives a non-streaming\n     * {@link GenerateContentResult}\n     */\n    async sendMessage(request) {\n        var _a, _b, _c, _d, _e;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            contents: [...this._history, newContent],\n        };\n        let finalResult;\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => generateContent(this._apiKey, this.model, generateContentRequest, this.requestOptions))\n            .then((result) => {\n            var _a;\n            if (result.response.candidates &&\n                result.response.candidates.length > 0) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({ parts: [], \n                    // Response seems to come back without a role set.\n                    role: \"model\" }, (_a = result.response.candidates) === null || _a === void 0 ? void 0 : _a[0].content);\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(result.response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n            finalResult = result;\n        });\n        await this._sendPromise;\n        return finalResult;\n    }\n    /**\n     * Sends a chat message and receives the response as a\n     * {@link GenerateContentStreamResult} containing an iterable stream\n     * and a response promise.\n     */\n    async sendMessageStream(request) {\n        var _a, _b, _c, _d, _e;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            contents: [...this._history, newContent],\n        };\n        const streamPromise = generateContentStream(this._apiKey, this.model, generateContentRequest, this.requestOptions);\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => streamPromise)\n            // This must be handled to avoid unhandled rejection, but jump\n            // to the final catch block with a label to not log this error.\n            .catch((_ignored) => {\n            throw new Error(SILENT_ERROR);\n        })\n            .then((streamResult) => streamResult.response)\n            .then((response) => {\n            if (response.candidates && response.candidates.length > 0) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({}, response.candidates[0].content);\n                // Response seems to come back without a role set.\n                if (!responseContent.role) {\n                    responseContent.role = \"model\";\n                }\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n        })\n            .catch((e) => {\n            // Errors in streamPromise are already catchable by the user as\n            // streamPromise is returned.\n            // Avoid duplicating the error message in logs.\n            if (e.message !== SILENT_ERROR) {\n                // Users do not have access to _sendPromise to catch errors\n                // downstream from streamPromise, so they should not throw.\n                console.error(e);\n            }\n        });\n        return streamPromise;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function countTokens(apiKey, model, params, requestOptions) {\n    const response = await makeRequest(model, Task.COUNT_TOKENS, apiKey, false, JSON.stringify(Object.assign(Object.assign({}, params), { model })), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function embedContent(apiKey, model, params, requestOptions) {\n    const response = await makeRequest(model, Task.EMBED_CONTENT, apiKey, false, JSON.stringify(params), requestOptions);\n    return response.json();\n}\nasync function batchEmbedContents(apiKey, model, params, requestOptions) {\n    const requestsWithModel = params.requests.map((request) => {\n        return Object.assign(Object.assign({}, request), { model });\n    });\n    const response = await makeRequest(model, Task.BATCH_EMBED_CONTENTS, apiKey, false, JSON.stringify({ requests: requestsWithModel }), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for generative model APIs.\n * @public\n */\nclass GenerativeModel {\n    constructor(apiKey, modelParams, requestOptions) {\n        this.apiKey = apiKey;\n        if (modelParams.model.includes(\"/\")) {\n            // Models may be named \"models/model-name\" or \"tunedModels/model-name\"\n            this.model = modelParams.model;\n        }\n        else {\n            // If path is not included, assume it's a non-tuned model.\n            this.model = `models/${modelParams.model}`;\n        }\n        this.generationConfig = modelParams.generationConfig || {};\n        this.safetySettings = modelParams.safetySettings || [];\n        this.tools = modelParams.tools;\n        this.toolConfig = modelParams.toolConfig;\n        this.systemInstruction = formatSystemInstruction(modelParams.systemInstruction);\n        this.requestOptions = requestOptions || {};\n    }\n    /**\n     * Makes a single non-streaming call to the model\n     * and returns an object containing a single {@link GenerateContentResponse}.\n     */\n    async generateContent(request) {\n        const formattedParams = formatGenerateContentInput(request);\n        return generateContent(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction }, formattedParams), this.requestOptions);\n    }\n    /**\n     * Makes a single streaming call to the model\n     * and returns an object containing an iterable stream that iterates\n     * over all chunks in the streaming response as well as\n     * a promise that returns the final aggregated response.\n     */\n    async generateContentStream(request) {\n        const formattedParams = formatGenerateContentInput(request);\n        return generateContentStream(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction }, formattedParams), this.requestOptions);\n    }\n    /**\n     * Gets a new {@link ChatSession} instance which can be used for\n     * multi-turn chats.\n     */\n    startChat(startChatParams) {\n        return new ChatSession(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction }, startChatParams), this.requestOptions);\n    }\n    /**\n     * Counts the tokens in the provided request.\n     */\n    async countTokens(request) {\n        const formattedParams = formatGenerateContentInput(request);\n        return countTokens(this.apiKey, this.model, formattedParams, this.requestOptions);\n    }\n    /**\n     * Embeds the provided content.\n     */\n    async embedContent(request) {\n        const formattedParams = formatEmbedContentInput(request);\n        return embedContent(this.apiKey, this.model, formattedParams, this.requestOptions);\n    }\n    /**\n     * Embeds an array of {@link EmbedContentRequest}s.\n     */\n    async batchEmbedContents(batchEmbedContentRequest) {\n        return batchEmbedContents(this.apiKey, this.model, batchEmbedContentRequest, this.requestOptions);\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Top-level class for this SDK\n * @public\n */\nclass GoogleGenerativeAI {\n    constructor(apiKey) {\n        this.apiKey = apiKey;\n    }\n    /**\n     * Gets a {@link GenerativeModel} instance for the provided model name.\n     */\n    getGenerativeModel(modelParams, requestOptions) {\n        if (!modelParams.model) {\n            throw new GoogleGenerativeAIError(`Must provide a model name. ` +\n                `Example: genai.getGenerativeModel({ model: 'my-model-name' })`);\n        }\n        return new GenerativeModel(this.apiKey, modelParams, requestOptions);\n    }\n}\n\nexport { BlockReason, ChatSession, FinishReason, FunctionCallingMode, FunctionDeclarationSchemaType, GenerativeModel, GoogleGenerativeAI, GoogleGenerativeAIError, GoogleGenerativeAIFetchError, GoogleGenerativeAIRequestInputError, GoogleGenerativeAIResponseError, HarmBlockThreshold, HarmCategory, HarmProbability, POSSIBLE_ROLES, TaskType };\n//# sourceMappingURL=index.mjs.map\n"], "mappings": ";;;AAoBA,IAAM,iBAAiB,CAAC,QAAQ,SAAS,YAAY,QAAQ;AAK7D,IAAI;AAAA,CACH,SAAUA,eAAc;AACrB,EAAAA,cAAa,2BAA2B,IAAI;AAC5C,EAAAA,cAAa,2BAA2B,IAAI;AAC5C,EAAAA,cAAa,iCAAiC,IAAI;AAClD,EAAAA,cAAa,0BAA0B,IAAI;AAC3C,EAAAA,cAAa,iCAAiC,IAAI;AACtD,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAKtC,IAAI;AAAA,CACH,SAAUC,qBAAoB;AAE3B,EAAAA,oBAAmB,kCAAkC,IAAI;AAEzD,EAAAA,oBAAmB,qBAAqB,IAAI;AAE5C,EAAAA,oBAAmB,wBAAwB,IAAI;AAE/C,EAAAA,oBAAmB,iBAAiB,IAAI;AAExC,EAAAA,oBAAmB,YAAY,IAAI;AACvC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAKlD,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAExB,EAAAA,iBAAgB,8BAA8B,IAAI;AAElD,EAAAA,iBAAgB,YAAY,IAAI;AAEhC,EAAAA,iBAAgB,KAAK,IAAI;AAEzB,EAAAA,iBAAgB,QAAQ,IAAI;AAE5B,EAAAA,iBAAgB,MAAM,IAAI;AAC9B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAK5C,IAAI;AAAA,CACH,SAAUC,cAAa;AAEpB,EAAAA,aAAY,4BAA4B,IAAI;AAE5C,EAAAA,aAAY,QAAQ,IAAI;AAExB,EAAAA,aAAY,OAAO,IAAI;AAC3B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAKpC,IAAI;AAAA,CACH,SAAUC,eAAc;AAErB,EAAAA,cAAa,2BAA2B,IAAI;AAE5C,EAAAA,cAAa,MAAM,IAAI;AAEvB,EAAAA,cAAa,YAAY,IAAI;AAE7B,EAAAA,cAAa,QAAQ,IAAI;AAEzB,EAAAA,cAAa,YAAY,IAAI;AAE7B,EAAAA,cAAa,OAAO,IAAI;AAC5B,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAKtC,IAAI;AAAA,CACH,SAAUC,WAAU;AACjB,EAAAA,UAAS,uBAAuB,IAAI;AACpC,EAAAA,UAAS,iBAAiB,IAAI;AAC9B,EAAAA,UAAS,oBAAoB,IAAI;AACjC,EAAAA,UAAS,qBAAqB,IAAI;AAClC,EAAAA,UAAS,gBAAgB,IAAI;AAC7B,EAAAA,UAAS,YAAY,IAAI;AAC7B,GAAG,aAAa,WAAW,CAAC,EAAE;AAI9B,IAAI;AAAA,CACH,SAAUC,sBAAqB;AAE5B,EAAAA,qBAAoB,kBAAkB,IAAI;AAG1C,EAAAA,qBAAoB,MAAM,IAAI;AAK9B,EAAAA,qBAAoB,KAAK,IAAI;AAG7B,EAAAA,qBAAoB,MAAM,IAAI;AAClC,GAAG,wBAAwB,sBAAsB,CAAC,EAAE;AAuBpD,IAAI;AAAA,CACH,SAAUC,gCAA+B;AAEtC,EAAAA,+BAA8B,QAAQ,IAAI;AAE1C,EAAAA,+BAA8B,QAAQ,IAAI;AAE1C,EAAAA,+BAA8B,SAAS,IAAI;AAE3C,EAAAA,+BAA8B,SAAS,IAAI;AAE3C,EAAAA,+BAA8B,OAAO,IAAI;AAEzC,EAAAA,+BAA8B,QAAQ,IAAI;AAC9C,GAAG,kCAAkC,gCAAgC,CAAC,EAAE;AAsBxE,IAAM,0BAAN,cAAsC,MAAM;AAAA,EACxC,YAAY,SAAS;AACjB,UAAM,+BAA+B,OAAO,EAAE;AAAA,EAClD;AACJ;AAMA,IAAM,kCAAN,cAA8C,wBAAwB;AAAA,EAClE,YAAY,SAAS,UAAU;AAC3B,UAAM,OAAO;AACb,SAAK,WAAW;AAAA,EACpB;AACJ;AAMA,IAAM,+BAAN,cAA2C,wBAAwB;AAAA,EAC/D,YAAY,SAAS,QAAQ,YAAY,cAAc;AACnD,UAAM,OAAO;AACb,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACxB;AACJ;AAKA,IAAM,sCAAN,cAAkD,wBAAwB;AAC1E;AAkBA,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAK5B,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAI;AAAA,CACH,SAAUC,OAAM;AACb,EAAAA,MAAK,kBAAkB,IAAI;AAC3B,EAAAA,MAAK,yBAAyB,IAAI;AAClC,EAAAA,MAAK,cAAc,IAAI;AACvB,EAAAA,MAAK,eAAe,IAAI;AACxB,EAAAA,MAAK,sBAAsB,IAAI;AACnC,GAAG,SAAS,OAAO,CAAC,EAAE;AACtB,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,OAAO,MAAM,QAAQ,QAAQ,gBAAgB;AACrD,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,WAAW;AACP,QAAI,IAAI;AACR,UAAM,eAAe,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AACtG,UAAM,YAAY,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAChG,QAAI,MAAM,GAAG,OAAO,IAAI,UAAU,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI;AAC7D,QAAI,KAAK,QAAQ;AACb,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,iBAAiB,gBAAgB;AACtC,QAAM,gBAAgB,CAAC;AACvB,MAAI,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,WAAW;AAC1F,kBAAc,KAAK,eAAe,SAAS;AAAA,EAC/C;AACA,gBAAc,KAAK,GAAG,kBAAkB,IAAI,eAAe,EAAE;AAC7D,SAAO,cAAc,KAAK,GAAG;AACjC;AACA,eAAe,WAAW,KAAK;AAC3B,QAAM,UAAU,IAAI,QAAQ;AAC5B,UAAQ,OAAO,gBAAgB,kBAAkB;AACjD,UAAQ,OAAO,qBAAqB,iBAAiB,IAAI,cAAc,CAAC;AACxE,UAAQ,OAAO,kBAAkB,IAAI,MAAM;AAC3C,MAAI,gBAAgB,IAAI,eAAe;AACvC,MAAI,eAAe;AACf,QAAI,EAAE,yBAAyB,UAAU;AACrC,UAAI;AACA,wBAAgB,IAAI,QAAQ,aAAa;AAAA,MAC7C,SACO,GAAG;AACN,cAAM,IAAI,oCAAoC,yCAAyC,KAAK,UAAU,aAAa,CAAC,gBAAgB,EAAE,OAAO,EAAE;AAAA,MACnJ;AAAA,IACJ;AACA,eAAW,CAAC,YAAY,WAAW,KAAK,cAAc,QAAQ,GAAG;AAC7D,UAAI,eAAe,kBAAkB;AACjC,cAAM,IAAI,oCAAoC,mCAAmC,UAAU,EAAE;AAAA,MACjG,WACS,eAAe,qBAAqB;AACzC,cAAM,IAAI,oCAAoC,eAAe,UAAU,4CAA4C;AAAA,MACvH;AACA,cAAQ,OAAO,YAAY,WAAW;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO;AACX;AACA,eAAe,iBAAiB,OAAO,MAAM,QAAQ,QAAQ,MAAM,gBAAgB;AAC/E,QAAM,MAAM,IAAI,WAAW,OAAO,MAAM,QAAQ,QAAQ,cAAc;AACtE,SAAO;AAAA,IACH,KAAK,IAAI,SAAS;AAAA,IAClB,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,cAAc,CAAC,GAAG,EAAE,QAAQ,QAAQ,SAAS,MAAM,WAAW,GAAG,GAAG,KAAK,CAAC;AAAA,EAC9I;AACJ;AAKA,eAAe,YAAY,OAAO,MAAM,QAAQ,QAAQ,MAAM,gBAAgB;AAC1E,SAAO,qBAAqB,OAAO,MAAM,QAAQ,QAAQ,MAAM,gBAAgB,KAAK;AACxF;AACA,eAAe,qBAAqB,OAAO,MAAM,QAAQ,QAAQ,MAAM,gBAEvE,UAAU,OAAO;AACb,QAAM,MAAM,IAAI,WAAW,OAAO,MAAM,QAAQ,QAAQ,cAAc;AACtE,MAAI;AACJ,MAAI;AACA,UAAM,UAAU,MAAM,iBAAiB,OAAO,MAAM,QAAQ,QAAQ,MAAM,cAAc;AACxF,eAAW,MAAM,QAAQ,QAAQ,KAAK,QAAQ,YAAY;AAC1D,QAAI,CAAC,SAAS,IAAI;AACd,UAAI,UAAU;AACd,UAAI;AACJ,UAAI;AACA,cAAM,OAAO,MAAM,SAAS,KAAK;AACjC,kBAAU,KAAK,MAAM;AACrB,YAAI,KAAK,MAAM,SAAS;AACpB,qBAAW,IAAI,KAAK,UAAU,KAAK,MAAM,OAAO,CAAC;AACjD,yBAAe,KAAK,MAAM;AAAA,QAC9B;AAAA,MACJ,SACO,GAAG;AAAA,MAEV;AACA,YAAM,IAAI,6BAA6B,uBAAuB,IAAI,SAAS,CAAC,MAAM,SAAS,MAAM,IAAI,SAAS,UAAU,KAAK,OAAO,IAAI,SAAS,QAAQ,SAAS,YAAY,YAAY;AAAA,IAC9L;AAAA,EACJ,SACO,GAAG;AACN,QAAI,MAAM;AACV,QAAI,EAAE,aAAa,gCACf,aAAa,sCAAsC;AACnD,YAAM,IAAI,wBAAwB,uBAAuB,IAAI,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AACvF,UAAI,QAAQ,EAAE;AAAA,IAClB;AACA,UAAM;AAAA,EACV;AACA,SAAO;AACX;AAMA,SAAS,kBAAkB,gBAAgB;AACvC,QAAM,eAAe,CAAC;AACtB,OAAK,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,YAAY,GAAG;AAC/F,UAAM,kBAAkB,IAAI,gBAAgB;AAC5C,UAAM,SAAS,gBAAgB;AAC/B,eAAW,MAAM,gBAAgB,MAAM,GAAG,eAAe,OAAO;AAChE,iBAAa,SAAS;AAAA,EAC1B;AACA,SAAO;AACX;AAsBA,SAAS,WAAW,UAAU;AAC1B,WAAS,OAAO,MAAM;AAClB,QAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,UAAI,SAAS,WAAW,SAAS,GAAG;AAChC,gBAAQ,KAAK,qBAAqB,SAAS,WAAW,MAAM,6HAEU;AAAA,MAC1E;AACA,UAAI,mBAAmB,SAAS,WAAW,CAAC,CAAC,GAAG;AAC5C,cAAM,IAAI,gCAAgC,GAAG,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,MAC9F;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B,WACS,SAAS,gBAAgB;AAC9B,YAAM,IAAI,gCAAgC,uBAAuB,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAClH;AACA,WAAO;AAAA,EACX;AAIA,WAAS,eAAe,MAAM;AAC1B,QAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,UAAI,SAAS,WAAW,SAAS,GAAG;AAChC,gBAAQ,KAAK,qBAAqB,SAAS,WAAW,MAAM,uIAEU;AAAA,MAC1E;AACA,UAAI,mBAAmB,SAAS,WAAW,CAAC,CAAC,GAAG;AAC5C,cAAM,IAAI,gCAAgC,GAAG,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,MAC9F;AACA,cAAQ,KAAK,8EAC8B;AAC3C,aAAO,iBAAiB,QAAQ,EAAE,CAAC;AAAA,IACvC,WACS,SAAS,gBAAgB;AAC9B,YAAM,IAAI,gCAAgC,gCAAgC,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAC3H;AACA,WAAO;AAAA,EACX;AACA,WAAS,gBAAgB,MAAM;AAC3B,QAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,UAAI,SAAS,WAAW,SAAS,GAAG;AAChC,gBAAQ,KAAK,qBAAqB,SAAS,WAAW,MAAM,uIAEU;AAAA,MAC1E;AACA,UAAI,mBAAmB,SAAS,WAAW,CAAC,CAAC,GAAG;AAC5C,cAAM,IAAI,gCAAgC,GAAG,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,MAC9F;AACA,aAAO,iBAAiB,QAAQ;AAAA,IACpC,WACS,SAAS,gBAAgB;AAC9B,YAAM,IAAI,gCAAgC,gCAAgC,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAC3H;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIA,SAAS,QAAQ,UAAU;AACvB,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,cAAc,CAAC;AACrB,OAAK,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACpI,eAAW,SAAS,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACnJ,UAAI,KAAK,MAAM;AACX,oBAAY,KAAK,KAAK,IAAI;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,YAAY,SAAS,GAAG;AACxB,WAAO,YAAY,KAAK,EAAE;AAAA,EAC9B,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,iBAAiB,UAAU;AAChC,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,gBAAgB,CAAC;AACvB,OAAK,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACpI,eAAW,SAAS,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACnJ,UAAI,KAAK,cAAc;AACnB,sBAAc,KAAK,KAAK,YAAY;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,cAAc,SAAS,GAAG;AAC1B,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,IAAM,mBAAmB,CAAC,aAAa,YAAY,aAAa,MAAM;AACtE,SAAS,mBAAmB,WAAW;AACnC,SAAQ,CAAC,CAAC,UAAU,gBAChB,iBAAiB,SAAS,UAAU,YAAY;AACxD;AACA,SAAS,wBAAwB,UAAU;AACvC,MAAI,IAAI,IAAI;AACZ,MAAI,UAAU;AACd,OAAK,CAAC,SAAS,cAAc,SAAS,WAAW,WAAW,MACxD,SAAS,gBAAgB;AACzB,eAAW;AACX,SAAK,KAAK,SAAS,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AACpF,iBAAW,WAAW,SAAS,eAAe,WAAW;AAAA,IAC7D;AACA,SAAK,KAAK,SAAS,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB;AAC3F,iBAAW,KAAK,SAAS,eAAe,kBAAkB;AAAA,IAC9D;AAAA,EACJ,YACU,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,GAAG;AAC5E,UAAM,iBAAiB,SAAS,WAAW,CAAC;AAC5C,QAAI,mBAAmB,cAAc,GAAG;AACpC,iBAAW,gCAAgC,eAAe,YAAY;AACtE,UAAI,eAAe,eAAe;AAC9B,mBAAW,KAAK,eAAe,aAAa;AAAA,MAChD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAmBA,SAAS,QAAQ,GAAG;AAChB,SAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AACvE;AAEA,SAAS,iBAAiB,SAAS,YAAY,WAAW;AACtD,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AACpH,WAAS,KAAK,GAAG;AAAE,QAAI,EAAE,CAAC,EAAG,GAAE,CAAC,IAAI,SAAU,GAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,UAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AACzI,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI;AAAE,WAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IAAG,SAAS,GAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAK,GAAG;AAAE,MAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,OAAQ,QAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAuBA,IAAM,iBAAiB;AASvB,SAAS,cAAc,UAAU;AAC7B,QAAM,cAAc,SAAS,KAAK,YAAY,IAAI,kBAAkB,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;AAC5F,QAAM,iBAAiB,kBAAkB,WAAW;AACpD,QAAM,CAAC,SAAS,OAAO,IAAI,eAAe,IAAI;AAC9C,SAAO;AAAA,IACH,QAAQ,yBAAyB,OAAO;AAAA,IACxC,UAAU,mBAAmB,OAAO;AAAA,EACxC;AACJ;AACA,eAAe,mBAAmB,QAAQ;AACtC,QAAM,eAAe,CAAC;AACtB,QAAM,SAAS,OAAO,UAAU;AAChC,SAAO,MAAM;AACT,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI,MAAM;AACN,aAAO,WAAW,mBAAmB,YAAY,CAAC;AAAA,IACtD;AACA,iBAAa,KAAK,KAAK;AAAA,EAC3B;AACJ;AACA,SAAS,yBAAyB,QAAQ;AACtC,SAAO,iBAAiB,MAAM,WAAW,UAAU,6BAA6B;AAC5E,UAAM,SAAS,OAAO,UAAU;AAChC,WAAO,MAAM;AACT,YAAM,EAAE,OAAO,KAAK,IAAI,MAAM,QAAQ,OAAO,KAAK,CAAC;AACnD,UAAI,MAAM;AACN;AAAA,MACJ;AACA,YAAM,MAAM,QAAQ,WAAW,KAAK,CAAC;AAAA,IACzC;AAAA,EACJ,CAAC;AACL;AAMA,SAAS,kBAAkB,aAAa;AACpC,QAAM,SAAS,YAAY,UAAU;AACrC,QAAM,SAAS,IAAI,eAAe;AAAA,IAC9B,MAAM,YAAY;AACd,UAAI,cAAc;AAClB,aAAO,KAAK;AACZ,eAAS,OAAO;AACZ,eAAO,OAAO,KAAK,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM;AAC3C,cAAI,MAAM;AACN,gBAAI,YAAY,KAAK,GAAG;AACpB,yBAAW,MAAM,IAAI,wBAAwB,wBAAwB,CAAC;AACtE;AAAA,YACJ;AACA,uBAAW,MAAM;AACjB;AAAA,UACJ;AACA,yBAAe;AACf,cAAI,QAAQ,YAAY,MAAM,cAAc;AAC5C,cAAI;AACJ,iBAAO,OAAO;AACV,gBAAI;AACA,+BAAiB,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,YACxC,SACO,GAAG;AACN,yBAAW,MAAM,IAAI,wBAAwB,iCAAiC,MAAM,CAAC,CAAC,GAAG,CAAC;AAC1F;AAAA,YACJ;AACA,uBAAW,QAAQ,cAAc;AACjC,0BAAc,YAAY,UAAU,MAAM,CAAC,EAAE,MAAM;AACnD,oBAAQ,YAAY,MAAM,cAAc;AAAA,UAC5C;AACA,iBAAO,KAAK;AAAA,QAChB,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAKA,SAAS,mBAAmB,WAAW;AACnC,QAAM,eAAe,UAAU,UAAU,SAAS,CAAC;AACnD,QAAM,qBAAqB;AAAA,IACvB,gBAAgB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AAAA,EAC7F;AACA,aAAW,YAAY,WAAW;AAC9B,QAAI,SAAS,YAAY;AACrB,iBAAW,aAAa,SAAS,YAAY;AACzC,cAAM,IAAI,UAAU;AACpB,YAAI,CAAC,mBAAmB,YAAY;AAChC,6BAAmB,aAAa,CAAC;AAAA,QACrC;AACA,YAAI,CAAC,mBAAmB,WAAW,CAAC,GAAG;AACnC,6BAAmB,WAAW,CAAC,IAAI;AAAA,YAC/B,OAAO,UAAU;AAAA,UACrB;AAAA,QACJ;AAEA,2BAAmB,WAAW,CAAC,EAAE,mBAC7B,UAAU;AACd,2BAAmB,WAAW,CAAC,EAAE,eAAe,UAAU;AAC1D,2BAAmB,WAAW,CAAC,EAAE,gBAC7B,UAAU;AACd,2BAAmB,WAAW,CAAC,EAAE,gBAC7B,UAAU;AAKd,YAAI,UAAU,WAAW,UAAU,QAAQ,OAAO;AAC9C,cAAI,CAAC,mBAAmB,WAAW,CAAC,EAAE,SAAS;AAC3C,+BAAmB,WAAW,CAAC,EAAE,UAAU;AAAA,cACvC,MAAM,UAAU,QAAQ,QAAQ;AAAA,cAChC,OAAO,CAAC;AAAA,YACZ;AAAA,UACJ;AACA,gBAAM,UAAU,CAAC;AACjB,qBAAW,QAAQ,UAAU,QAAQ,OAAO;AACxC,gBAAI,KAAK,MAAM;AACX,sBAAQ,OAAO,KAAK;AAAA,YACxB;AACA,gBAAI,KAAK,cAAc;AACnB,sBAAQ,eAAe,KAAK;AAAA,YAChC;AACA,gBAAI,OAAO,KAAK,OAAO,EAAE,WAAW,GAAG;AACnC,sBAAQ,OAAO;AAAA,YACnB;AACA,+BAAmB,WAAW,CAAC,EAAE,QAAQ,MAAM,KAAK,OAAO;AAAA,UAC/D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAkBA,eAAe,sBAAsB,QAAQ,OAAO,QAAQ,gBAAgB;AACxE,QAAM,WAAW,MAAM;AAAA,IAAY;AAAA,IAAO,KAAK;AAAA,IAAyB;AAAA;AAAA,IAC3D;AAAA,IAAM,KAAK,UAAU,MAAM;AAAA,IAAG;AAAA,EAAc;AACzD,SAAO,cAAc,QAAQ;AACjC;AACA,eAAe,gBAAgB,QAAQ,OAAO,QAAQ,gBAAgB;AAClE,QAAM,WAAW,MAAM;AAAA,IAAY;AAAA,IAAO,KAAK;AAAA,IAAkB;AAAA;AAAA,IACpD;AAAA,IAAO,KAAK,UAAU,MAAM;AAAA,IAAG;AAAA,EAAc;AAC1D,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,mBAAmB,WAAW,YAAY;AAChD,SAAO;AAAA,IACH,UAAU;AAAA,EACd;AACJ;AAkBA,SAAS,wBAAwB,OAAO;AAEpC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX,WACS,OAAO,UAAU,UAAU;AAChC,WAAO,EAAE,MAAM,UAAU,OAAO,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE;AAAA,EACtD,WACS,MAAM,MAAM;AACjB,WAAO,EAAE,MAAM,UAAU,OAAO,CAAC,KAAK,EAAE;AAAA,EAC5C,WACS,MAAM,OAAO;AAClB,QAAI,CAAC,MAAM,MAAM;AACb,aAAO,EAAE,MAAM,UAAU,OAAO,MAAM,MAAM;AAAA,IAChD,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,SAAS;AAC/B,MAAI,WAAW,CAAC;AAChB,MAAI,OAAO,YAAY,UAAU;AAC7B,eAAW,CAAC,EAAE,MAAM,QAAQ,CAAC;AAAA,EACjC,OACK;AACD,eAAW,gBAAgB,SAAS;AAChC,UAAI,OAAO,iBAAiB,UAAU;AAClC,iBAAS,KAAK,EAAE,MAAM,aAAa,CAAC;AAAA,MACxC,OACK;AACD,iBAAS,KAAK,YAAY;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,+CAA+C,QAAQ;AAClE;AASA,SAAS,+CAA+C,OAAO;AAC3D,QAAM,cAAc,EAAE,MAAM,QAAQ,OAAO,CAAC,EAAE;AAC9C,QAAM,kBAAkB,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;AACtD,MAAI,iBAAiB;AACrB,MAAI,qBAAqB;AACzB,aAAW,QAAQ,OAAO;AACtB,QAAI,sBAAsB,MAAM;AAC5B,sBAAgB,MAAM,KAAK,IAAI;AAC/B,2BAAqB;AAAA,IACzB,OACK;AACD,kBAAY,MAAM,KAAK,IAAI;AAC3B,uBAAiB;AAAA,IACrB;AAAA,EACJ;AACA,MAAI,kBAAkB,oBAAoB;AACtC,UAAM,IAAI,wBAAwB,4HAA4H;AAAA,EAClK;AACA,MAAI,CAAC,kBAAkB,CAAC,oBAAoB;AACxC,UAAM,IAAI,wBAAwB,kDAAkD;AAAA,EACxF;AACA,MAAI,gBAAgB;AAChB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,2BAA2B,QAAQ;AACxC,MAAI;AACJ,MAAI,OAAO,UAAU;AACjB,uBAAmB;AAAA,EACvB,OACK;AAED,UAAM,UAAU,iBAAiB,MAAM;AACvC,uBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE;AAAA,EAC7C;AACA,MAAI,OAAO,mBAAmB;AAC1B,qBAAiB,oBAAoB,wBAAwB,OAAO,iBAAiB;AAAA,EACzF;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,QAAQ;AACrC,MAAI,OAAO,WAAW,YAAY,MAAM,QAAQ,MAAM,GAAG;AACrD,UAAM,UAAU,iBAAiB,MAAM;AACvC,WAAO,EAAE,QAAQ;AAAA,EACrB;AACA,SAAO;AACX;AAmBA,IAAM,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,uBAAuB;AAAA,EACzB,MAAM,CAAC,QAAQ,YAAY;AAAA,EAC3B,UAAU,CAAC,kBAAkB;AAAA,EAC7B,OAAO,CAAC,QAAQ,cAAc;AAAA;AAAA,EAE9B,QAAQ,CAAC,MAAM;AACnB;AACA,SAAS,oBAAoB,SAAS;AAClC,MAAI,cAAc;AAClB,aAAW,eAAe,SAAS;AAC/B,UAAM,EAAE,MAAM,MAAM,IAAI;AACxB,QAAI,CAAC,eAAe,SAAS,QAAQ;AACjC,YAAM,IAAI,wBAAwB,iDAAiD,IAAI,EAAE;AAAA,IAC7F;AACA,QAAI,CAAC,eAAe,SAAS,IAAI,GAAG;AAChC,YAAM,IAAI,wBAAwB,4CAA4C,IAAI,yBAAyB,KAAK,UAAU,cAAc,CAAC,EAAE;AAAA,IAC/I;AACA,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,YAAM,IAAI,wBAAwB,6DAA6D;AAAA,IACnG;AACA,QAAI,MAAM,WAAW,GAAG;AACpB,YAAM,IAAI,wBAAwB,4CAA4C;AAAA,IAClF;AACA,UAAM,cAAc;AAAA,MAChB,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,UAAU;AAAA,IACd;AACA,eAAW,QAAQ,OAAO;AACtB,iBAAW,OAAO,mBAAmB;AACjC,YAAI,OAAO,MAAM;AACb,sBAAY,GAAG,KAAK;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,aAAa,qBAAqB,IAAI;AAC5C,eAAW,OAAO,mBAAmB;AACjC,UAAI,CAAC,WAAW,SAAS,GAAG,KAAK,YAAY,GAAG,IAAI,GAAG;AACnD,cAAM,IAAI,wBAAwB,sBAAsB,IAAI,oBAAoB,GAAG,QAAQ;AAAA,MAC/F;AAAA,IACJ;AACA,kBAAc;AAAA,EAClB;AACJ;AAqBA,IAAM,eAAe;AAOrB,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,QAAQ,OAAO,QAAQ,gBAAgB;AAC/C,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,WAAW,CAAC;AACjB,SAAK,eAAe,QAAQ,QAAQ;AACpC,SAAK,UAAU;AACf,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,0BAAoB,OAAO,OAAO;AAClC,WAAK,WAAW,OAAO;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,aAAa;AACf,UAAM,KAAK;AACX,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,SAAS;AACvB,QAAI,IAAI,IAAI,IAAI,IAAI;AACpB,UAAM,KAAK;AACX,UAAM,aAAa,iBAAiB,OAAO;AAC3C,UAAM,yBAAyB;AAAA,MAC3B,iBAAiB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC3E,mBAAmB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC7E,QAAQ,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAClE,aAAa,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACvE,oBAAoB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC9E,UAAU,CAAC,GAAG,KAAK,UAAU,UAAU;AAAA,IAC3C;AACA,QAAI;AAEJ,SAAK,eAAe,KAAK,aACpB,KAAK,MAAM,gBAAgB,KAAK,SAAS,KAAK,OAAO,wBAAwB,KAAK,cAAc,CAAC,EACjG,KAAK,CAAC,WAAW;AAClB,UAAIC;AACJ,UAAI,OAAO,SAAS,cAChB,OAAO,SAAS,WAAW,SAAS,GAAG;AACvC,aAAK,SAAS,KAAK,UAAU;AAC7B,cAAM,kBAAkB,OAAO,OAAO;AAAA,UAAE,OAAO,CAAC;AAAA;AAAA,UAE5C,MAAM;AAAA,QAAQ,IAAIA,MAAK,OAAO,SAAS,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC,EAAE,OAAO;AACzG,aAAK,SAAS,KAAK,eAAe;AAAA,MACtC,OACK;AACD,cAAM,oBAAoB,wBAAwB,OAAO,QAAQ;AACjE,YAAI,mBAAmB;AACnB,kBAAQ,KAAK,mCAAmC,iBAAiB,wCAAwC;AAAA,QAC7G;AAAA,MACJ;AACA,oBAAc;AAAA,IAClB,CAAC;AACD,UAAM,KAAK;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,kBAAkB,SAAS;AAC7B,QAAI,IAAI,IAAI,IAAI,IAAI;AACpB,UAAM,KAAK;AACX,UAAM,aAAa,iBAAiB,OAAO;AAC3C,UAAM,yBAAyB;AAAA,MAC3B,iBAAiB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC3E,mBAAmB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC7E,QAAQ,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAClE,aAAa,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACvE,oBAAoB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC9E,UAAU,CAAC,GAAG,KAAK,UAAU,UAAU;AAAA,IAC3C;AACA,UAAM,gBAAgB,sBAAsB,KAAK,SAAS,KAAK,OAAO,wBAAwB,KAAK,cAAc;AAEjH,SAAK,eAAe,KAAK,aACpB,KAAK,MAAM,aAAa,EAGxB,MAAM,CAAC,aAAa;AACrB,YAAM,IAAI,MAAM,YAAY;AAAA,IAChC,CAAC,EACI,KAAK,CAAC,iBAAiB,aAAa,QAAQ,EAC5C,KAAK,CAAC,aAAa;AACpB,UAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,aAAK,SAAS,KAAK,UAAU;AAC7B,cAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,SAAS,WAAW,CAAC,EAAE,OAAO;AAExE,YAAI,CAAC,gBAAgB,MAAM;AACvB,0BAAgB,OAAO;AAAA,QAC3B;AACA,aAAK,SAAS,KAAK,eAAe;AAAA,MACtC,OACK;AACD,cAAM,oBAAoB,wBAAwB,QAAQ;AAC1D,YAAI,mBAAmB;AACnB,kBAAQ,KAAK,yCAAyC,iBAAiB,wCAAwC;AAAA,QACnH;AAAA,MACJ;AAAA,IACJ,CAAC,EACI,MAAM,CAAC,MAAM;AAId,UAAI,EAAE,YAAY,cAAc;AAG5B,gBAAQ,MAAM,CAAC;AAAA,MACnB;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AAkBA,eAAe,YAAY,QAAQ,OAAO,QAAQ,gBAAgB;AAC9D,QAAM,WAAW,MAAM,YAAY,OAAO,KAAK,cAAc,QAAQ,OAAO,KAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,cAAc;AAC/J,SAAO,SAAS,KAAK;AACzB;AAkBA,eAAe,aAAa,QAAQ,OAAO,QAAQ,gBAAgB;AAC/D,QAAM,WAAW,MAAM,YAAY,OAAO,KAAK,eAAe,QAAQ,OAAO,KAAK,UAAU,MAAM,GAAG,cAAc;AACnH,SAAO,SAAS,KAAK;AACzB;AACA,eAAe,mBAAmB,QAAQ,OAAO,QAAQ,gBAAgB;AACrE,QAAM,oBAAoB,OAAO,SAAS,IAAI,CAAC,YAAY;AACvD,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,CAAC;AAAA,EAC9D,CAAC;AACD,QAAM,WAAW,MAAM,YAAY,OAAO,KAAK,sBAAsB,QAAQ,OAAO,KAAK,UAAU,EAAE,UAAU,kBAAkB,CAAC,GAAG,cAAc;AACnJ,SAAO,SAAS,KAAK;AACzB;AAsBA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,QAAQ,aAAa,gBAAgB;AAC7C,SAAK,SAAS;AACd,QAAI,YAAY,MAAM,SAAS,GAAG,GAAG;AAEjC,WAAK,QAAQ,YAAY;AAAA,IAC7B,OACK;AAED,WAAK,QAAQ,UAAU,YAAY,KAAK;AAAA,IAC5C;AACA,SAAK,mBAAmB,YAAY,oBAAoB,CAAC;AACzD,SAAK,iBAAiB,YAAY,kBAAkB,CAAC;AACrD,SAAK,QAAQ,YAAY;AACzB,SAAK,aAAa,YAAY;AAC9B,SAAK,oBAAoB,wBAAwB,YAAY,iBAAiB;AAC9E,SAAK,iBAAiB,kBAAkB,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,SAAS;AAC3B,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,WAAO,gBAAgB,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,mBAAmB,KAAK,kBAAkB,GAAG,eAAe,GAAG,KAAK,cAAc;AAAA,EACpR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,sBAAsB,SAAS;AACjC,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,WAAO,sBAAsB,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,mBAAmB,KAAK,kBAAkB,GAAG,eAAe,GAAG,KAAK,cAAc;AAAA,EAC1R;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,iBAAiB;AACvB,WAAO,IAAI,YAAY,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,mBAAmB,KAAK,kBAAkB,GAAG,eAAe,GAAG,KAAK,cAAc;AAAA,EACpR;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,YAAY,SAAS;AACvB,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,WAAO,YAAY,KAAK,QAAQ,KAAK,OAAO,iBAAiB,KAAK,cAAc;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,aAAa,SAAS;AACxB,UAAM,kBAAkB,wBAAwB,OAAO;AACvD,WAAO,aAAa,KAAK,QAAQ,KAAK,OAAO,iBAAiB,KAAK,cAAc;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,mBAAmB,0BAA0B;AAC/C,WAAO,mBAAmB,KAAK,QAAQ,KAAK,OAAO,0BAA0B,KAAK,cAAc;AAAA,EACpG;AACJ;AAsBA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAY,QAAQ;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,aAAa,gBAAgB;AAC5C,QAAI,CAAC,YAAY,OAAO;AACpB,YAAM,IAAI,wBAAwB,0FACiC;AAAA,IACvE;AACA,WAAO,IAAI,gBAAgB,KAAK,QAAQ,aAAa,cAAc;AAAA,EACvE;AACJ;", "names": ["HarmCategory", "HarmBlockThreshold", "HarmProbability", "BlockReason", "FinishReason", "TaskType", "FunctionCallingMode", "FunctionDeclarationSchemaType", "Task", "_a"]}