# Changelog

## 1.0.0

* Initial release

## 1.0.1

* Fix: `.patch` method maintain `this` context in method calls
* README update
* README typo

## 1.0.2

* Fix: `.clone()` with `subclassZipFile` option breaks `lazyEntries: false` (closes #1)
* Remove unnecessary instanceof checks in patchers

## 1.0.3

* `.clone()` with `subclassZipFile` option forward all events to subclass instance
* Fix: Changelog
* Fix: README typo
* README formatting

## 1.0.4

* Run Travis CI tests on Node v10
* Update dev dependencies
