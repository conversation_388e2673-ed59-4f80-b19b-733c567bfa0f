{"name": "minipass", "version": "2.9.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "files": ["index.js"], "tap": {"check-coverage": true}}