/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { RequestOptions } from "../../types";
import { FilesTask } from "./constants";
export declare class FilesRequestUrl {
    task: FilesTask;
    apiKey: string;
    requestOptions?: RequestOptions;
    private _url;
    constructor(task: FilesTask, apiKey: string, requestOptions?: RequestOptions);
    appendPath(path: string): void;
    appendParam(key: string, value: string): void;
    toString(): string;
}
export declare function getHeaders(url: FilesRequestUrl): Headers;
export declare function makeFilesRequest(url: FilesRequestUrl, headers: Headers, body?: Blob, fetchFn?: typeof fetch): Promise<Response>;
