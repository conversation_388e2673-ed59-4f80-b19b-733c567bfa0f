{"name": "stream-to-array", "description": "Concatenate a readable stream's data into a single array", "version": "2.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "license": "MIT", "repository": "stream-utils/stream-to-array", "dependencies": {"any-promise": "^1.1.0"}, "devDependencies": {"bluebird": "^3.1.1", "istanbul": "^0.4.2", "mocha": "^2.3.3"}, "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "keywords": ["stream", "streams", "buffer", "array", "concat"]}