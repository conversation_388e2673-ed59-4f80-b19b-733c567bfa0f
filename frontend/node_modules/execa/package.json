{"name": "execa", "version": "3.2.0", "description": "Process execution for humans", "license": "MIT", "repository": "sindresorhus/execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts", "lib"], "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "p-finally": "^2.0.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.1.0", "coveralls": "^3.0.4", "get-node": "^5.0.0", "is-running": "^2.1.0", "nyc": "^14.1.1", "p-event": "^4.1.0", "tempfile": "^3.0.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}}