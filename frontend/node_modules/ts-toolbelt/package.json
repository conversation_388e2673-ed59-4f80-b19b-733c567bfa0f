{"name": "ts-toolbelt", "version": "6.15.5", "description": "Get the most out of TypeScript", "keywords": ["safe", "tools", "types", "typesafe", "typescript"], "homepage": "https://github.com/millsp/ts-toolbelt", "repository": {"type": "git", "url": "https://github.com/millsp/ts-toolbelt"}, "license": "Apache-2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/millsp"}, "main": "out/index.js", "types": "out/index.d.ts", "files": ["out"], "scripts": {"build:clean": "bash scr/build/clean.sh", "build:code": "bash scr/build/code.sh", "build:docs": "bash scr/build/docs.sh", "build:types": "bash scr/build/types.sh", "ci:branch:ad": "bash scr/ci/branch/ad.sh", "ci:branch:bd": "bash scr/ci/branch/bd.sh", "ci:docs:ad": "bash scr/ci/docs/ad.sh", "ci:docs:bd": "bash scr/ci/docs/bd.sh", "ci:master:ad": "bash scr/ci/master/ad.sh", "ci:master:bd": "bash scr/ci/master/bd.sh", "ci:test:ad": "bash scr/ci/test/ad.sh", "ci:test:bd": "bash scr/ci/test/bd.sh", "release": "bash scr/release.sh", "test": "bash scr/test/all.sh", "test:code": "bash scr/test/code.sh", "test:lint": "bash scr/test/lint.sh", "test:types": "bash scr/test/types.sh"}, "dependencies": {}, "devDependencies": {"@commitlint/cli": "^8.3.0", "@commitlint/config-conventional": "^8.3.0", "@typescript-eslint/parser": "^3.0.0", "eledoc": "^0.2.0", "eslint": "^7.0.0", "eslint-plugin-fp": "^2.3.0", "eslint-plugin-react": "^7.20.0", "husky": "^4.2.5", "npx": "^10.2.0", "sort-package-json": "^1.42.0", "standard-version": "^8.0.0", "ts-node": "^8.10.0", "tslib": "^2.0.0", "typedoc": "^0.17.0", "typescript": "next"}}