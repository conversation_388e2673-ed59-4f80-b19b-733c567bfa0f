{"name": "async-listen", "description": "`net.Server#listen()` helper that returns a Promise for async / await", "version": "3.0.1", "main": "./dist/index.js", "repository": "vercel/async-listen", "keywords": ["async", "await", "es6", "http", "listen", "net", "promise", "server"], "packageManager": "pnpm@7.33.3", "devDependencies": {"@types/node": "^18.15.10", "@types/tap": "^15.0.8", "tap": "^16.3.4", "ts-node": "^10.9.1", "typescript": "^5.0.2"}, "engines": {"node": ">= 14"}, "files": ["dist"], "scripts": {"build": "tsc", "prepublishOnly": "tsc", "test": "tap --no-check-coverage --ts"}, "license": "MIT", "types": "./dist/index.d.ts"}