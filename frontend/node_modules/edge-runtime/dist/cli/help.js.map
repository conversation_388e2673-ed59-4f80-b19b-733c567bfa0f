{"version": 3, "file": "help.js", "sourceRoot": "", "sources": ["../../src/cli/help.ts"], "names": [], "mappings": ";;;AAAA,2CAAuC;AAGvC,MAAM,KAAK,GAAgB;IACzB,IAAI,EAAE,0BAA0B;IAChC,IAAI,EAAE,uBAAuB;IAC7B,MAAM,EAAE,qBAAqB;IAC7B,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE,+BAA+B;CACtC,CAAA;AAEM,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC;iBACT,IAAA,gBAAG,EAAC,mBAAmB,CAAC;;IAErC,IAAA,gBAAG,EAAC,QAAQ,CAAC;;EAEf,iBAAiB,CAAC,KAAK,CAAC;CACzB,CAAA;AANY,QAAA,IAAI,QAMhB;AAED,SAAS,YAAY,CAAC,OAAoB;IACxC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAC7D,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;AAC1C,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAoB;IAC7C,MAAM,gBAAgB,GAAG,YAAY,CAAC,OAAO,CAAC,CAAA;IAE9C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;SACpC,GAAG,CACF,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,EAAE,EAAE,CACrB,SAAS,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,IAAA,gBAAG,EAAC,WAAW,CAAC,EAAE,CAC9D;SACA,IAAI,CAAC,IAAI,CAAC,CAAA;IAEb,OAAO,GAAG,OAAO,EAAE,CAAA;AACrB,CAAC", "sourcesContent": ["import { dim, white } from 'picocolors'\ninterface HelpOptions extends Record<string, string> {}\n\nconst flags: HelpOptions = {\n  eval: 'Evaluate an input script',\n  help: 'Display this message.',\n  listen: 'Run as HTTP server.',\n  port: 'Specify a port to use.',\n  repl: 'Start an interactive session.',\n}\n\nexport const help = () => `\n  edge-runtime ${dim('[<flags>] [input]')}\n\n  ${dim('Flags:')}\n\n${getSectionSummary(flags)}\n`\n\nfunction getPadLength(options: HelpOptions) {\n  const lengths = Object.keys(options).map((key) => key.length)\n  return Math.max.apply(null, lengths) + 1\n}\n\nfunction getSectionSummary(options: HelpOptions) {\n  const summaryPadLength = getPadLength(options)\n\n  const summary = Object.entries(options)\n    .map(\n      ([key, description]) =>\n        `    --${key.padEnd(summaryPadLength)} ${dim(description)}`,\n    )\n    .join('\\n')\n\n  return `${summary}`\n}\n"]}