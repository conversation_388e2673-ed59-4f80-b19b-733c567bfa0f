{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../src/cli/logger.ts"], "names": [], "mappings": ";;;;;;AAAA,iDAAmD;AAGnD,4DAA6B;AAE7B,MAAM,SAAS,GACb,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,SAAS;IAC5C,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;IAC3C,CAAC,CAAC,IAAI,CAAA;AAEG,QAAA,MAAM,GAAG,IAAA,qBAAY,GAAE,CAAA;AAEpC;;;GAGG;AACH,SAAgB,YAAY;IAC1B,MAAM,MAAM,GAAG,UAAU,OAAe,EAAE,IAAoB;QAC5D,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IACtB,CAAW,CAAA;IAEX,MAAM,CAAC,IAAI,GAAG,MAAM,CAAA;IACpB,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;IAC3E,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;IAC3E,MAAM,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;IAC7E,MAAM,CAAC,MAAM,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAA;IAC7C,OAAO,MAAM,CAAA;AACf,CAAC;AAXD,oCAWC;AAED,SAAS,KAAK,CACZ,OAAe,EACf,EACE,KAAK,GAAG,OAAO,EACf,UAAU,GAAG,IAAI,EACjB,aAAa,GAAG,KAAK,MACJ,EAAE;IAErB,IAAI,CAAC,SAAS;QAAE,OAAM;IACtB,MAAM,QAAQ,GAAG,oBAAI,CAAC,KAAK,CAAc,CAAA;IACzC,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA;IACpD,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;IAC3C,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;AAC1D,CAAC", "sourcesContent": ["import { createFormat } from '@edge-runtime/format'\nimport type { Logger, LoggerOptions } from '../types'\nimport type { Formatter } from 'picocolors/types'\nimport pico from 'picocolors'\n\nconst isEnabled =\n  process.env.EDGE_RUNTIME_LOGGING !== undefined\n    ? Boolean(process.env.EDGE_RUNTIME_LOGGING)\n    : true\n\nexport const format = createFormat()\n\n/**\n * Creates basic logger with colors that can be used from the CLI and the\n * server logs.\n */\nexport function createLogger() {\n  const logger = function (message: string, opts?: LoggerOptions) {\n    print(message, opts)\n  } as Logger\n\n  logger.info = logger\n  logger.error = (message, opts) => print(message, { color: 'red', ...opts })\n  logger.debug = (message, opts) => print(message, { color: 'dim', ...opts })\n  logger.warn = (message, opts) => print(message, { color: 'yellow', ...opts })\n  logger.quotes = (str: string) => `\\`${str}\\``\n  return logger\n}\n\nfunction print(\n  message: string,\n  {\n    color = 'white',\n    withHeader = true,\n    withBreakline = false,\n  }: LoggerOptions = {},\n) {\n  if (!isEnabled) return\n  const colorize = pico[color] as Formatter\n  const header = withHeader ? `${colorize('ƒ')} ` : ''\n  const separator = withBreakline ? '\\n' : ''\n  console.log(`${header}${separator}${colorize(message)}`)\n}\n"]}