{"version": 3, "file": "repl.js", "sourceRoot": "", "sources": ["../../src/cli/repl.ts"], "names": [], "mappings": ";;;;;;AAAA,iDAAmD;AACnD,gDAA6B;AAC7B,2BAA4B;AAC5B,+BAA2B;AAE3B,kDAA6C;AAE7C,MAAM,MAAM,GAAG,IAAA,qBAAY,GAAE,CAAA;AAE7B,MAAM,MAAM,GAA0B,CAAC,MAAM,EAAE,EAAE;IAC/C,OAAO,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;AAC1E,CAAC,CAAA;AAED,MAAM,IAAI,GAAG,cAAU,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAA;AAsBjD,oBAAI;AArBb,IAAI,CAAC,YAAY,CAAC,IAAA,WAAI,EAAC,IAAA,YAAO,GAAE,EAAE,4BAA4B,CAAC,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;AAE1E,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAC9C,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAClC,CAAA;AAED,MAAM,OAAO,GAAG,IAAI,0BAAW,EAAE,CAAA;AAEjC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC;KACxC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KACtC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAC7D,CAAA;AAEH,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE;IACjD,YAAY,EAAE,KAAK;IACnB,UAAU,EAAE,KAAK;IACjB,QAAQ,EAAE,KAAK;IACf,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW;CACnC,CAAC,CAAA", "sourcesContent": ["import { createFormat } from '@edge-runtime/format'\nimport createRepl from 'repl'\nimport { homedir } from 'os'\nimport { join } from 'path'\n\nimport { EdgeRuntime } from '../edge-runtime'\n\nconst format = createFormat()\n\nconst writer: createRepl.REPLWriter = (output) => {\n  return typeof output === 'function' ? output.toString() : format(output)\n}\n\nconst repl = createRepl.start({ prompt: 'ƒ => ', writer })\nrepl.setupHistory(join(homedir(), '.edge_runtime_repl_history'), () => {})\n\nObject.getOwnPropertyNames(repl.context).forEach(\n  (mod) => delete repl.context[mod],\n)\n\nconst runtime = new EdgeRuntime()\n\nObject.getOwnPropertyNames(runtime.context)\n  .filter((key) => !key.startsWith('__'))\n  .forEach((key) =>\n    Object.assign(repl.context, { [key]: runtime.context[key] }),\n  )\n\nObject.defineProperty(repl.context, 'EdgeRuntime', {\n  configurable: false,\n  enumerable: false,\n  writable: false,\n  value: runtime.context.EdgeRuntime,\n})\n\nexport { repl }\n"]}