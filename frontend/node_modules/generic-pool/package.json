{"name": "generic-pool", "description": "Generic resource pooling for Node.JS", "version": "3.4.2", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://poetro.hu/"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "url": "http://www.developmentseed.org/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://somethingdoug.com/"}, {"name": "calibr"}, {"name": "<PERSON>", "email": "j<PERSON><PERSON><PERSON>@redventures.com>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "sandfox", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["pool", "pooling", "throttle"], "main": "index.js", "repository": {"type": "git", "url": "http://github.com/coopernurse/node-pool.git"}, "devDependencies": {"@types/node": "^8.5.1", "eslint": "^4.9.0", "eslint-config-prettier": "^2.6.0", "eslint-plugin-prettier": "^2.3.1", "eslint-plugin-promise": "^3.3.0", "prettier": "^1.7.4", "tap": "^8.0.0", "typescript": "^2.6.2"}, "engines": {"node": ">= 4"}, "scripts": {"lint": "eslint lib test index.js .eslintrc.js", "lint-fix": "eslint --fix lib test index.js .eslintrc.js", "test": "tap test/*-test.js "}, "license": "MIT"}