{"name": "web-vitals", "version": "0.2.4", "description": "Easily measure performance metrics in JavaScript", "main": "dist/web-vitals.es5.umd.min.js", "module": "dist/web-vitals.es5.min.js", "typings": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "run-s clean build:ts build:js", "build:ts": "tsc -b", "build:js": "rollup -c", "clean": "rm -rf dist tsconfig.tsbuildinfo", "dev": "run-p watch serve", "lint": "eslint \"*.js\" \"src/**/*.ts\" \"test/**/*.js\"", "lint:fix": "eslint --fix \"*.js\" \"src/**/*.ts\" \"test/**/*.js\"", "postversion": "git push --follow-tags", "release:major": "npm version major -m 'Release v%s' && npm publish", "release:minor": "npm version minor -m 'Release v%s' && npm publish", "release:patch": "npm version patch -m 'Release v%s' && npm publish", "test": "npm-run-all build -p -r test:*", "test:e2e": "wdio wdio.conf.js", "test:server": "node test/server.js", "watch": "run-p watch:*", "watch:ts": "tsc -b -w", "watch:js": "rollup -c -w", "version": "run-s build"}, "keywords": ["crux", "performance", "metrics", "CLS", "FCP", "FID", "LCP", "TTFB"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://philipwalton.com"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/GoogleChrome/web-vitals.git"}, "bugs": {"url": "https://github.com/GoogleChrome/web-vitals/issues"}, "husky": {"hooks": {"pre-commit": "npm run lint"}}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/preset-env": "^7.10.4", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "@wdio/cli": "^6.3.4", "@wdio/local-runner": "^6.3.4", "@wdio/mocha-framework": "^6.3.0", "@wdio/selenium-standalone-service": "^6.1.14", "@wdio/spec-reporter": "^6.3.0", "babel-eslint": "^10.1.0", "body-parser": "^1.19.0", "chromedriver": "^84.0.1", "eslint": "^7.5.0", "eslint-config-google": "^0.14.0", "express": "^4.17.1", "fs-extra": "^9.0.1", "husky": "^4.2.5", "npm-run-all": "^4.1.5", "nunjucks": "^3.2.2", "rollup": "^2.23.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^6.1.0", "typescript": "^3.9.7", "wdio-chromedriver-service": "^6.0.3"}}