{"name": "time-span", "version": "4.0.0", "description": "Simplified high resolution timing", "license": "MIT", "repository": "sindresorhus/time-span", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["time", "span", "elapsed", "process", "hrtime", "highres", "timing", "perf", "performance", "bench", "benchmark", "profiling", "measure", "seconds", "milliseconds", "nanoseconds"], "dependencies": {"convert-hrtime": "^3.0.0"}, "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "in-range": "^2.0.0", "tsd": "^0.11.0", "xo": "^0.24.0"}}