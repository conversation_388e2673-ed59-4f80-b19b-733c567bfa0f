{"name": "http-errors", "description": "Create HTTP error objects", "version": "1.4.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "jshttp/http-errors", "dependencies": {"inherits": "2.0.1", "statuses": ">= 1.2.1 < 2"}, "devDependencies": {"istanbul": "0.4.2", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["http", "error"], "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"]}