#!/usr/bin/env python3
"""
Database Deployment Script for CHaBot
Deploys and configures all required databases.
"""

import subprocess
import time
import sys
import os
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseDeployer:
    """Handles deployment of all CHaBot databases."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.database_dir = self.project_root.parent / "database"
        
    def run_command(self, command, cwd=None, check=True):
        """Run a shell command."""
        try:
            logger.info(f"Running: {command}")
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd or self.project_root,
                capture_output=True, 
                text=True,
                check=check
            )
            if result.stdout:
                logger.info(f"Output: {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {e}")
            if e.stderr:
                logger.error(f"Error: {e.stderr}")
            if check:
                raise
            return e
    
    def install_dependencies(self):
        """Install required Python dependencies."""
        logger.info("📦 Installing Python dependencies...")
        
        try:
            # Install requirements
            self.run_command("pip install -r requirements.txt")
            logger.info("✅ Python dependencies installed successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            return False
    
    def deploy_docker_databases(self):
        """Deploy databases using Docker Compose."""
        logger.info("🐳 Deploying databases with Docker Compose...")
        
        try:
            # Change to database directory
            os.chdir(self.database_dir)
            
            # Stop any existing containers
            logger.info("Stopping existing containers...")
            self.run_command("docker-compose down", check=False)
            
            # Start new containers
            logger.info("Starting database containers...")
            self.run_command("docker-compose up -d")
            
            # Wait for containers to be ready
            logger.info("Waiting for containers to be ready...")
            time.sleep(30)
            
            # Check container status
            result = self.run_command("docker-compose ps")
            logger.info("Container status:")
            logger.info(result.stdout)
            
            logger.info("✅ Docker databases deployed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to deploy Docker databases: {e}")
            return False
        finally:
            # Return to project root
            os.chdir(self.project_root)
    
    def check_database_connectivity(self):
        """Check connectivity to all databases."""
        logger.info("🔍 Checking database connectivity...")
        
        checks = {
            "PostgreSQL": self.check_postgres,
            "Milvus": self.check_milvus,
            "Neo4j": self.check_neo4j,
            "Memgraph": self.check_memgraph,
            "Chroma": self.check_chroma
        }
        
        results = {}
        for db_name, check_func in checks.items():
            try:
                results[db_name] = check_func()
                if results[db_name]:
                    logger.info(f"✅ {db_name} is accessible")
                else:
                    logger.warning(f"⚠️ {db_name} is not accessible")
            except Exception as e:
                logger.error(f"❌ {db_name} check failed: {e}")
                results[db_name] = False
        
        return results
    
    def check_postgres(self):
        """Check PostgreSQL connectivity."""
        try:
            result = self.run_command(
                "docker exec chabot_postgres pg_isready -U chabot_user -d chabot_db",
                check=False
            )
            return result.returncode == 0
        except:
            return False
    
    def check_milvus(self):
        """Check Milvus connectivity."""
        try:
            # Check if Milvus container is running
            result = self.run_command("docker ps | grep milvus", check=False)
            return "milvus" in result.stdout
        except:
            return False
    
    def check_neo4j(self):
        """Check Neo4j connectivity."""
        try:
            # Try to connect to the external Neo4j instance
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('*************', 32355))
            sock.close()
            return result == 0
        except:
            return False
    
    def check_memgraph(self):
        """Check Memgraph connectivity."""
        try:
            result = self.run_command(
                "docker exec chabot_memgraph echo 'RETURN 1;' | nc localhost 7687",
                check=False
            )
            return result.returncode == 0
        except:
            return False
    
    def check_chroma(self):
        """Check Chroma connectivity."""
        try:
            result = self.run_command(
                "curl -f http://localhost:8000/api/v1/heartbeat",
                check=False
            )
            return result.returncode == 0
        except:
            return False
    
    def run_database_tests(self):
        """Run comprehensive database tests."""
        logger.info("🧪 Running database tests...")
        
        try:
            # Run the test script
            result = self.run_command("python test_databases.py")
            
            if result.returncode == 0:
                logger.info("✅ Database tests completed successfully")
                return True
            else:
                logger.warning("⚠️ Some database tests failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Database tests failed: {e}")
            return False
    
    def deploy_all(self):
        """Deploy all databases and run tests."""
        logger.info("🚀 Starting complete database deployment...")
        
        success_count = 0
        total_steps = 4
        
        # Step 1: Install dependencies
        if self.install_dependencies():
            success_count += 1
        
        # Step 2: Deploy Docker databases
        if self.deploy_docker_databases():
            success_count += 1
        
        # Step 3: Check connectivity
        connectivity_results = self.check_database_connectivity()
        connected_dbs = sum(connectivity_results.values())
        if connected_dbs >= 2:  # At least 2 databases should be connected
            success_count += 1
            logger.info(f"✅ {connected_dbs}/5 databases are accessible")
        else:
            logger.warning(f"⚠️ Only {connected_dbs}/5 databases are accessible")
        
        # Step 4: Run tests
        if self.run_database_tests():
            success_count += 1
        
        # Summary
        logger.info("="*60)
        logger.info("🎯 DEPLOYMENT SUMMARY")
        logger.info("="*60)
        logger.info(f"📊 Steps completed: {success_count}/{total_steps}")
        logger.info(f"🔗 Databases connected: {connected_dbs}/5")
        
        if success_count >= 3:
            logger.info("🎉 Database deployment successful!")
            logger.info("✨ CHaBot database infrastructure is ready!")
        else:
            logger.warning("⚠️ Database deployment partially successful")
            logger.info("🔧 Some manual configuration may be required")
        
        logger.info("="*60)
        
        return success_count >= 3

def main():
    """Main deployment function."""
    deployer = DatabaseDeployer()
    
    try:
        success = deployer.deploy_all()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Deployment failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
