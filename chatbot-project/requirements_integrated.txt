# ============================================================================
# CHaBot Integrated System - Complete Dependencies
# ============================================================================

# ============================================================================
# CORE WEB FRAMEWORK
# ============================================================================
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
starlette==0.27.0
python-multipart==0.0.6

# ============================================================================
# DATABASE DEPENDENCIES
# ============================================================================
# PostgreSQL
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.1
psycopg2-binary==2.9.9

# Redis
redis==5.0.1
aioredis==2.0.1

# Neo4j Graph Database
neo4j==5.15.0
py2neo==2021.2.4

# Vector Databases
chromadb==0.4.18
pymilvus==2.3.4
qdrant-client==1.7.0

# ============================================================================
# AI/ML CORE DEPENDENCIES
# ============================================================================
# Deep Learning Frameworks
torch>=2.2.0
torchvision>=0.16.0
transformers==4.36.0
accelerate==0.25.0

# Machine Learning
scikit-learn==1.3.2
numpy==1.24.3
scipy==1.11.4
pandas==2.1.4

# Embeddings and Vector Operations
sentence-transformers==2.2.2
faiss-cpu==1.7.4
annoy==1.17.3

# ============================================================================
# NLP DEPENDENCIES
# ============================================================================
spacy==3.7.2
nltk==3.8.1
textblob==0.17.1
langdetect==1.0.9
fuzzywuzzy==0.18.0
python-Levenshtein==0.23.0

# ============================================================================
# LLM INTEGRATION
# ============================================================================
# Ollama Integration
ollama==0.1.7
openai==1.3.8
anthropic==0.7.8

# Hugging Face
huggingface-hub==0.19.4
datasets==2.15.0
tokenizers==0.15.0

# ============================================================================
# AGENT FRAMEWORK DEPENDENCIES
# ============================================================================
# Communication
grpcio==1.60.0
grpcio-tools==1.60.0
websockets==12.0
aiohttp==3.9.1
httpx==0.25.2

# Task Queue and Messaging
celery==5.3.4
kombu==5.3.4
billiard==4.2.0

# ============================================================================
# MEMORY SYSTEM DEPENDENCIES
# ============================================================================
# Graph Processing
networkx==3.2.1

# Time Series and Temporal Data
arrow==1.3.0
pendulum==2.1.2
python-dateutil==2.8.2

# ============================================================================
# TOOL INTEGRATION DEPENDENCIES
# ============================================================================
# Mathematical Computing
sympy==1.12
mpmath==1.3.0
numexpr==2.8.8

# Data Processing
openpyxl==3.1.2
xlsxwriter==3.1.9
python-docx==1.1.0
PyPDF2==3.0.1
pdfplumber==0.10.3

# ============================================================================
# REASONING ENGINE DEPENDENCIES
# ============================================================================
# Logic and Reasoning
python-constraint==1.4.0

# Knowledge Representation
rdflib==7.0.0

# ============================================================================
# MONITORING AND OBSERVABILITY
# ============================================================================
# Metrics and Monitoring
prometheus-client==0.19.0
psutil==5.9.6

# Logging and Tracing
structlog==23.2.0
loguru==0.7.2

# ============================================================================
# SECURITY DEPENDENCIES
# ============================================================================
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
pyjwt==2.8.0
bcrypt==4.1.2
python-keycloak==3.7.0

# ============================================================================
# UTILITY DEPENDENCIES
# ============================================================================
# Configuration and Environment
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# CLI and User Interface
click==8.1.7
rich==13.7.0
typer==0.9.0
tqdm==4.66.1
colorama==0.4.6

# HTTP and Networking
requests==2.31.0
urllib3==2.1.0
certifi==2023.11.17

# Data Validation and Serialization
marshmallow==3.20.1
jsonschema==4.20.0

# ============================================================================
# DEVELOPMENT DEPENDENCIES
# ============================================================================
# Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
coverage==7.3.2

# Code Quality
black==23.11.0
flake8==6.1.0
isort==5.13.2
mypy==1.7.1
pylint==3.0.3

# Documentation
sphinx==7.2.6
mkdocs==1.5.3

# ============================================================================
# VISUALIZATION AND ANALYSIS
# ============================================================================
matplotlib==3.8.2
plotly==5.17.0
seaborn==0.13.0

# ============================================================================
# DEPLOYMENT DEPENDENCIES
# ============================================================================
# Container and Orchestration
kubernetes==28.1.0

# Process Management
gunicorn==21.2.0

# ============================================================================
# PERFORMANCE OPTIMIZATION
# ============================================================================
# ONNX Runtime for model optimization
onnxruntime==1.16.3
optimum==1.16.0

# ============================================================================
# COMPATIBILITY AND SYSTEM
# ============================================================================
# System compatibility
wheel==0.42.0
setuptools>=69.0.0
pip>=23.3.0

# Async utilities
aiofiles==23.2.1
anyio==4.2.0

# ============================================================================
# LANGCHAIN INTEGRATION (Optional)
# ============================================================================
langchain==0.0.350
langchain-community==0.0.1

# ============================================================================
# ADDITIONAL UTILITIES
# ============================================================================
# File handling
pathlib2==2.3.7
watchdog==3.0.0

# Caching
diskcache==5.6.3
cachetools==5.3.2

# Serialization
pickle5==0.0.12
dill==0.3.7

# Configuration management
hydra-core==1.3.2
omegaconf==2.3.0

# Progress and UI
alive-progress==3.1.5
tabulate==0.9.0

# Memory profiling
memory-profiler==0.61.0
pympler==0.9

# Async extensions
asyncio-mqtt==0.16.1
aioredis==2.0.1

# Graph visualization
graphviz==0.20.1
pygraphviz==1.11

# Time handling
pytz==2023.3
tzdata==2023.3

# String processing
regex==2023.10.3
unidecode==1.3.7

# Validation
validators==0.22.0
email-validator==2.1.0

# Compression
zstandard==0.22.0
lz4==4.3.2

# Concurrency
concurrent-futures==3.1.1
futures==3.4.0

# System monitoring
py-cpuinfo==9.0.0
GPUtil==1.4.0

# Development tools
ipython==8.18.1
jupyter==1.0.0
notebook==7.0.6
