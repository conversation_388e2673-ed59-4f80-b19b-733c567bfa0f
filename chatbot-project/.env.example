# ============================================================================
# CHaBot Environment Configuration
# Complete configuration template for all system components
# ============================================================================

# ============================================================================
# APPLICATION SETTINGS
# ============================================================================
APP_NAME=CHaBot
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO
DEV_MODE=true
ENVIRONMENT=development

# ============================================================================
# API CONFIGURATION
# ============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
ENABLE_CORS=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# ============================================================================
# SERVICE PORTS
# ============================================================================
AUTH_SERVICE_PORT=8001
CHAT_SERVICE_PORT=8002
AGENT_SERVICE_PORT=8003
ADMIN_SERVICE_PORT=8004
HISTORY_SERVICE_PORT=8005
MEMORY_SERVICE_PORT=8006
USER_SERVICE_PORT=8007

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=60
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_MIN_LENGTH=8
API_KEY_LENGTH=32

# Encryption
ENCRYPTION_KEY=your-encryption-key-32-bytes-long
CIPHER_ALGORITHM=AES-256-GCM

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# PostgreSQL (Primary Database)
DATABASE_URL=postgresql://chabot_user:chabot_password@localhost:5432/chabot_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=chabot_db
POSTGRES_USER=chabot_user
POSTGRES_PASSWORD=chabot_password
POSTGRES_MAX_CONNECTIONS=20
POSTGRES_POOL_SIZE=10

# Redis (Caching & Message Broker)
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=10

# Neo4j (Graph Database) - Updated with provided credentials
NEO4J_URI=bolt://*************:32355
NEO4J_HOST=*************
NEO4J_PORT=32355
NEO4J_USER=neo4j
NEO4J_PASSWORD=admin@123
NEO4J_WEB_URL=http://*************:30102
NEO4J_DATABASE=neo4j
NEO4J_MAX_CONNECTION_LIFETIME=3600

# Milvus (Vector Database)
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_DATABASE=default
MILVUS_COLLECTION_NAME=document_embeddings

# Memgraph (Real-time Graph Database)
MEMGRAPH_HOST=localhost
MEMGRAPH_PORT=7688
MEMGRAPH_USER=
MEMGRAPH_PASSWORD=

# Database Options
MOCK_DATABASES=true
DATABASE_POOL_RECYCLE=3600
DATABASE_ECHO=false

# ============================================================================
# AI & ML CONFIGURATION
# ============================================================================

# LLM Configuration - Ollama Integration
OLLAMA_BASE_URL=https://developer.nuvoai.io/ollama
LLM_MODEL_NAME=llama3.1:8b
LLM_FALLBACK_MODEL=llama3.1:7b
LLM_MODEL_PATH=./ai/llm/models/
LLM_MAX_LENGTH=2048
LLM_TEMPERATURE=0.7
LLM_TOP_P=0.9
LLM_TOP_K=50
LLM_USE_ONNX=false
LLM_DEVICE_MAP=auto
LLM_TORCH_DTYPE=float16

# Embedding Configuration - Ollama Embeddings
EMBEDDING_MODEL=nomic-embed-text
EMBEDDING_MODEL_PATH=./ai/models/embeddings/
EMBEDDING_DIMENSION=768
EMBEDDING_FALLBACK_MODEL=all-minilm
EMBEDDING_BATCH_SIZE=32
EMBEDDING_MAX_LENGTH=512

# Context & Reasoning
MAX_CONTEXT_LENGTH=4000
REASONING_CONFIDENCE_THRESHOLD=0.7
MAX_REASONING_DEPTH=5
REASONING_TIMEOUT_SECONDS=60
TREE_OF_THOUGHTS_MAX_DEPTH=3
TREE_OF_THOUGHTS_BRANCHING_FACTOR=3

# NLP Configuration
NLP_MODEL_PATH=./models/custom_ner_model/
SPACY_MODEL=en_core_web_sm
INTENT_CONFIDENCE_THRESHOLD=0.8
ENTITY_CONFIDENCE_THRESHOLD=0.7

# ============================================================================
# AGENT CONFIGURATION
# ============================================================================
MAX_AGENTS_PER_TYPE=3
AGENT_TIMEOUT_SECONDS=30
AGENT_COMMUNICATION_TIMEOUT=30
AGENT_REGISTRY_URL=http://localhost:8001
AGENT_HEARTBEAT_INTERVAL=10
AGENT_MAX_RETRIES=3
AGENT_BACKOFF_FACTOR=2

# Task Management
TASK_QUEUE_SIZE=100
TASK_TIMEOUT_SECONDS=300
MAX_CONCURRENT_TASKS=10
TASK_RETRY_ATTEMPTS=3

# Agent Memory
AGENT_MEMORY_LIMIT_MB=512
CONVERSATION_MEMORY_LIMIT=100
EPISODIC_MEMORY_LIMIT=50
SEMANTIC_MEMORY_LIMIT=200
WORKING_MEMORY_LIMIT=20

# ============================================================================
# TOOL CONFIGURATION
# ============================================================================
TOOL_EXECUTION_TIMEOUT=30
TOOL_MAX_RETRIES=2
TOOL_SANDBOX_ENABLED=true
TOOL_RESOURCE_LIMIT_CPU=1.0
TOOL_RESOURCE_LIMIT_MEMORY=256

# Calculator Tool
CALCULATOR_MAX_EXPRESSION_LENGTH=1000
CALCULATOR_PRECISION=10

# Database Query Tool
DB_QUERY_TIMEOUT=30
DB_QUERY_MAX_ROWS=1000
DB_QUERY_ALLOWED_OPERATIONS=["SELECT", "INSERT", "UPDATE"]

# ============================================================================
# KNOWLEDGE BASE CONFIGURATION
# ============================================================================
KNOWLEDGE_BASE_PATH=./docs/
DOCUMENT_CHUNK_SIZE=1000
DOCUMENT_CHUNK_OVERLAP=200
DOCUMENT_MAX_SIZE_MB=10
SUPPORTED_FILE_TYPES=["txt", "pdf", "docx", "md"]

# Vector Search
VECTOR_SEARCH_TOP_K=10
VECTOR_SEARCH_SIMILARITY_THRESHOLD=0.7
VECTOR_INDEX_TYPE=IVF_FLAT
VECTOR_INDEX_NLIST=128

# Graph Search
GRAPH_SEARCH_MAX_DEPTH=3
GRAPH_SEARCH_MAX_RESULTS=20
GRAPH_RELATIONSHIP_TYPES=["CONTAINS", "RELATES_TO", "PART_OF"]

# ============================================================================
# MONITORING & LOGGING
# ============================================================================

# Logging
LOG_FORMAT=json
LOG_FILE_PATH=./logs/
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5
LOG_ROTATION=daily

# Performance Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
METRICS_PATH=/metrics
PERFORMANCE_TRACKING=true

# Health Checks
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Tracing
ENABLE_TRACING=true
TRACE_SAMPLING_RATE=0.1
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# ============================================================================
# KUBERNETES & INFRASTRUCTURE
# ============================================================================
KUBERNETES_NAMESPACE=chabot-dev
KUBERNETES_SERVICE_ACCOUNT=chabot-service-account
KUBERNETES_CONFIG_PATH=~/.kube/config

# Service Discovery
SERVICE_DISCOVERY_ENABLED=true
SERVICE_REGISTRY_URL=http://localhost:8500

# Load Balancing
LOAD_BALANCER_ALGORITHM=round_robin
LOAD_BALANCER_HEALTH_CHECK=true

# ============================================================================
# COMMUNICATION & MESSAGING
# ============================================================================

# gRPC Configuration
GRPC_PORT=50051
GRPC_MAX_MESSAGE_SIZE=4194304
GRPC_KEEPALIVE_TIME=30
GRPC_KEEPALIVE_TIMEOUT=5

# Message Broker
MESSAGE_BROKER_TYPE=redis
MESSAGE_QUEUE_SIZE=1000
MESSAGE_TTL_SECONDS=3600
MESSAGE_RETRY_ATTEMPTS=3

# WebSocket
WEBSOCKET_ENABLED=true
WEBSOCKET_PORT=8080
WEBSOCKET_MAX_CONNECTIONS=100

# ============================================================================
# FRONTEND CONFIGURATION
# ============================================================================
FRONTEND_URL=http://localhost:3000
STATIC_FILES_PATH=./frontend/dist/
UPLOAD_MAX_SIZE_MB=50
UPLOAD_ALLOWED_TYPES=["txt", "pdf", "docx", "csv", "json"]

# ============================================================================
# EXTERNAL SERVICES
# ============================================================================

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# Notification Services
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# External APIs
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# ============================================================================
# BACKUP & RECOVERY
# ============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups/
BACKUP_COMPRESSION=gzip

# ============================================================================
# RATE LIMITING
# ============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10
RATE_LIMIT_STORAGE=redis

# ============================================================================
# CACHING
# ============================================================================
CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE_MB=512
CACHE_COMPRESSION=true

# ============================================================================
# DEVELOPMENT & TESTING
# ============================================================================
TESTING_MODE=false
MOCK_EXTERNAL_SERVICES=true
ENABLE_DEBUG_ENDPOINTS=true
PROFILING_ENABLED=false

# Test Database
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/test_chabot_db

# ============================================================================
# FEATURE FLAGS
# ============================================================================
FEATURE_ADVANCED_REASONING=true
FEATURE_MULTI_AGENT_COORDINATION=true
FEATURE_TREE_OF_THOUGHTS=true
FEATURE_SELF_REFLECTION=true
FEATURE_TOOL_INTEGRATION=true
FEATURE_KNOWLEDGE_FUSION=true
FEATURE_REAL_TIME_LEARNING=false
FEATURE_VOICE_INTERFACE=false

# ============================================================================
# RESOURCE LIMITS
# ============================================================================
MAX_MEMORY_USAGE_MB=2048
MAX_CPU_USAGE_PERCENT=80
MAX_DISK_USAGE_PERCENT=85
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT_SECONDS=30

# ============================================================================
# ORGANIZATION SPECIFIC
# ============================================================================
DEFAULT_ORGANIZATION=NuvoAI
SUPPORTED_ORGANIZATIONS=["NuvoAI", "Meril_Life_Sciences", "Meril_Healthcare", "Meril_Diagnostics", "Meril_Endo_Surgery"]
ORGANIZATION_DATA_PATH=./docs/
ORGANIZATION_ISOLATION=true

# ============================================================================
# COMPLIANCE & SECURITY
# ============================================================================
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365
AUDIT_LOGGING=true
AUDIT_LOG_PATH=./logs/audit/
SECURITY_HEADERS=true
CONTENT_SECURITY_POLICY=true

# ============================================================================
# EXPERIMENTAL FEATURES
# ============================================================================
EXPERIMENTAL_FEATURES=false
BETA_FEATURES=false
ALPHA_FEATURES=false