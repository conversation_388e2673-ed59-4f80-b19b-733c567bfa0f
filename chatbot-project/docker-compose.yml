version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: chatbot_db
      POSTGRES_USER: chatbot_user
      POSTGRES_PASSWORD: chatbot_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/databases/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5.15-community
    environment:
      NEO4J_AUTH: neo4j/chatbot_pass
      NEO4J_PLUGINS: '["apoc"]'
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data

  # Milvus Vector Database
  milvus:
    image: milvusdb/milvus:v2.3.4
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    ports:
      - "19530:19530"
    depends_on:
      - etcd
      - minio
    volumes:
      - milvus_data:/var/lib/milvus

  # Etcd for Milvus
  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    volumes:
      - etcd_data:/etcd

  # MinIO for Milvus
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    command: minio server /minio_data
    ports:
      - "9001:9000"
      - "9090:9090"
    volumes:
      - minio_data:/minio_data

  # Redis for caching and agent communication
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Memgraph for real-time graph operations
  memgraph:
    image: memgraph/memgraph:2.11
    ports:
      - "7688:7687"
    volumes:
      - memgraph_data:/var/lib/memgraph
    environment:
      - MEMGRAPH_USER=memgraph
      - MEMGRAPH_PASSWORD=chatbot_pass

volumes:
  postgres_data:
  neo4j_data:
  milvus_data:
  etcd_data:
  minio_data:
  redis_data:
  memgraph_data: