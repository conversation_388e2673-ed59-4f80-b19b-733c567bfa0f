# Contributing to Multi-Organization Chatbot

## Development Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chatbot-project
   ```

2. **Set up Python environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development services**
   ```bash
   docker-compose up -d
   ```

## Coding Standards

### Python Code Style
- Use **Black** for code formatting (line length: 88)
- Use **flake8** for linting
- Use **mypy** for type checking
- Follow PEP 8 guidelines

### Code Quality
- Write comprehensive tests for all new features
- Maintain test coverage above 80%
- Use type hints for all function signatures
- Write clear docstrings for classes and functions

### Git Workflow
1. Create feature branch from `develop`
2. Make changes with clear commit messages
3. Run tests and linting before committing
4. Create merge request to `develop`
5. Ensure CI pipeline passes

## Testing

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=agents --cov=backend

# Run specific test file
pytest tests/test_agent_framework.py -v
```

### Test Structure
- Unit tests in `tests/unit/`
- Integration tests in `tests/integration/`
- Agent-specific tests in `tests/agent/`
- End-to-end tests in `tests/e2e/`

## Agent Development Guidelines

### Creating New Agents
1. Inherit from `BaseAgent`
2. Implement required abstract methods
3. Define clear capabilities
4. Add comprehensive tests
5. Update agent registry

### Agent Communication
- Use structured `AgentMessage` format
- Handle errors gracefully
- Implement proper timeouts
- Log all agent interactions

## Documentation

### Code Documentation
- Use Google-style docstrings
- Document all public APIs
- Include usage examples
- Keep documentation up-to-date

### Architecture Documentation
- Update relevant design documents
- Include sequence diagrams for complex flows
- Document configuration options
- Maintain API documentation

## Pull Request Process

1. **Before submitting**
   - Run full test suite
   - Check code formatting with Black
   - Run linting with flake8
   - Run type checking with mypy

2. **PR Description**
   - Clear description of changes
   - Link to related issues
   - Include testing instructions
   - Note any breaking changes

3. **Review Process**
   - At least one approval required
   - All CI checks must pass
   - Address all review comments
   - Squash commits before merge

## Issue Reporting

### Bug Reports
- Use bug report template
- Include reproduction steps
- Provide system information
- Include relevant logs

### Feature Requests
- Use feature request template
- Describe use case clearly
- Consider implementation impact
- Discuss with maintainers first

## Development Tools

### Recommended VS Code Extensions
- Python
- Black Formatter
- Flake8
- Mypy Type Checker
- Docker
- GitLens

### Pre-commit Hooks
```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run manually
pre-commit run --all-files
```