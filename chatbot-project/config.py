"""Configuration management for CHaBot - Complete Environment Integration."""

import os
from typing import Optional, List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # ============================================================================
    # APPLICATION SETTINGS
    # ============================================================================
    app_name: str = "CHaBot"
    app_version: str = "2.0.0"
    debug: bool = False
    log_level: str = "INFO"
    dev_mode: bool = False
    environment: str = "production"
    
    # ============================================================================
    # API CONFIGURATION
    # ============================================================================
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_workers: int = 1
    enable_cors: bool = True
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8080", "http://localhost:5173"]
    
    # ============================================================================
    # SERVICE PORTS
    # ============================================================================
    auth_service_port: int = 8001
    chat_service_port: int = 8002
    agent_service_port: int = 8003
    admin_service_port: int = 8004
    history_service_port: int = 8005
    memory_service_port: int = 8006
    user_service_port: int = 8007
    
    # ============================================================================
    # SECURITY CONFIGURATION
    # ============================================================================
    secret_key: str = "chabot-super-secret-production-key-32-chars-minimum-length"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 60
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    password_min_length: int = 8
    
    # ============================================================================
    # DATABASE CONFIGURATION
    # ============================================================================
    # PostgreSQL
    database_url: str = "postgresql://chabot_user:chabot_password@localhost:5432/chabot_db"
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "chabot_db"
    postgres_user: str = "chabot_user"
    postgres_password: str = "chabot_password"
    postgres_max_connections: int = 20
    postgres_pool_size: int = 10
    
    # Redis
    redis_url: str = "redis://localhost:6379/0"
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: str = ""
    redis_max_connections: int = 10
    
    # Neo4j
    neo4j_uri: str = "bolt://*************:32355"
    neo4j_host: str = "*************"
    neo4j_port: int = 32355
    neo4j_user: str = "neo4j"
    neo4j_password: str = "admin@123"
    neo4j_web_url: str = "http://*************:30102"
    neo4j_database: str = "neo4j"
    neo4j_max_connection_lifetime: int = 3600

    # Milvus (Already running)
    milvus_host: str = "localhost"
    milvus_port: int = 19530
    milvus_user: str = ""
    milvus_password: str = ""
    milvus_database: str = "default"
    milvus_collection_name: str = "chabot_embeddings"
    milvus_index_type: str = "IVF_FLAT"
    milvus_metric_type: str = "L2"
    milvus_nlist: int = 128

    # Memgraph (Will deploy)
    memgraph_host: str = "localhost"
    memgraph_port: int = 7687
    memgraph_user: str = ""
    memgraph_password: str = ""
    memgraph_database: str = "memgraph"

    # Chroma (Will deploy)
    chroma_host: str = "localhost"
    chroma_port: int = 8001
    chroma_collection_name: str = "chabot_memory"
    chroma_persist_directory: str = "./chroma_db"
    
    # Database Options
    mock_databases: bool = False
    database_pool_recycle: int = 3600
    database_echo: bool = False
    
    # ============================================================================
    # AI & ML CONFIGURATION
    # ============================================================================
    # Ollama Configuration
    ollama_base_url: str = "http://localhost:11434"
    llm_model_name: str = "deepseek-r1:latest"
    llm_fallback_model: str = "llama2:latest"
    llm_max_length: int = 2048
    llm_temperature: float = 0.7
    llm_top_p: float = 0.9
    llm_top_k: int = 50
    
    # Embedding Configuration
    embedding_model: str = "all-MiniLM-L6-v2"
    embedding_model_path: str = "sentence-transformers/all-MiniLM-L6-v2"
    embedding_dimension: int = 384
    embedding_batch_size: int = 32
    embedding_max_length: int = 512
    
    # Context & Reasoning
    max_context_length: int = 4000
    reasoning_confidence_threshold: float = 0.7
    max_reasoning_depth: int = 5
    reasoning_timeout_seconds: int = 60
    tree_of_thoughts_max_depth: int = 3
    tree_of_thoughts_branching_factor: int = 3
    
    # NLP Configuration
    nlp_model_path: str = "./models/custom_ner_model/"
    spacy_model: str = "en_core_web_sm"
    intent_confidence_threshold: float = 0.8
    entity_confidence_threshold: float = 0.7
    
    # ============================================================================
    # AGENT CONFIGURATION
    # ============================================================================
    max_agents_per_type: int = 3
    agent_timeout_seconds: int = 30
    agent_communication_timeout: int = 30
    agent_registry_url: str = "http://localhost:8001"
    agent_heartbeat_interval: int = 10
    agent_max_retries: int = 3
    agent_backoff_factor: int = 2
    
    # Task Management
    task_queue_size: int = 100
    task_timeout_seconds: int = 300
    max_concurrent_tasks: int = 10
    task_retry_attempts: int = 3
    
    # Agent Memory
    agent_memory_limit_mb: int = 512
    conversation_memory_limit: int = 100
    episodic_memory_limit: int = 50
    semantic_memory_limit: int = 200
    working_memory_limit: int = 20
    
    # ============================================================================
    # TOOL CONFIGURATION
    # ============================================================================
    tool_execution_timeout: int = 30
    tool_max_retries: int = 2
    tool_sandbox_enabled: bool = True
    tool_resource_limit_cpu: float = 1.0
    tool_resource_limit_memory: int = 256
    
    # Calculator Tool
    calculator_max_expression_length: int = 1000
    calculator_precision: int = 10
    
    # Database Query Tool
    db_query_timeout: int = 30
    db_query_max_rows: int = 1000
    db_query_allowed_operations: List[str] = ["SELECT", "INSERT", "UPDATE"]
    
    # ============================================================================
    # KNOWLEDGE BASE CONFIGURATION
    # ============================================================================
    knowledge_base_path: str = "./docs/"
    document_chunk_size: int = 1000
    document_chunk_overlap: int = 200
    document_max_size_mb: int = 10
    supported_file_types: List[str] = ["txt", "pdf", "docx", "md"]
    
    # Vector Search
    vector_search_top_k: int = 10
    vector_search_similarity_threshold: float = 0.7
    vector_index_type: str = "IVF_FLAT"
    vector_index_nlist: int = 128
    
    # Graph Search
    graph_search_max_depth: int = 3
    graph_search_max_results: int = 20
    graph_relationship_types: List[str] = ["CONTAINS", "RELATES_TO", "PART_OF"]
    
    # ============================================================================
    # MONITORING & LOGGING
    # ============================================================================
    # Logging
    log_format: str = "json"
    log_file_path: str = "./logs/"
    log_max_size_mb: int = 100
    log_backup_count: int = 5
    log_rotation: str = "daily"
    
    # Performance Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    metrics_path: str = "/metrics"
    performance_tracking: bool = True
    
    # Health Checks
    health_check_interval: int = 30
    health_check_timeout: int = 10
    health_check_retries: int = 3
    
    # Tracing
    enable_tracing: bool = True
    trace_sampling_rate: float = 0.1
    jaeger_endpoint: str = "http://localhost:14268/api/traces"
    
    # ============================================================================
    # COMMUNICATION & MESSAGING
    # ============================================================================
    # gRPC Configuration
    grpc_port: int = 50051
    grpc_max_message_size: int = 4194304
    grpc_keepalive_time: int = 30
    grpc_keepalive_timeout: int = 5
    
    # Message Broker
    message_broker_type: str = "redis"
    message_queue_size: int = 1000
    message_ttl_seconds: int = 3600
    message_retry_attempts: int = 3
    
    # WebSocket
    websocket_enabled: bool = True
    websocket_port: int = 8080
    websocket_max_connections: int = 100
    
    # ============================================================================
    # FRONTEND CONFIGURATION
    # ============================================================================
    frontend_url: str = "http://localhost:3000"
    static_files_path: str = "./frontend/dist/"
    upload_max_size_mb: int = 50
    upload_allowed_types: List[str] = ["txt", "pdf", "docx", "csv", "json"]
    
    # ============================================================================
    # RATE LIMITING & CACHING
    # ============================================================================
    rate_limit_enabled: bool = True
    rate_limit_requests_per_minute: int = 60
    rate_limit_burst_size: int = 10
    rate_limit_storage: str = "redis"
    
    cache_enabled: bool = True
    cache_ttl_seconds: int = 3600
    cache_max_size_mb: int = 512
    cache_compression: bool = True
    
    # ============================================================================
    # FEATURE FLAGS
    # ============================================================================
    feature_advanced_reasoning: bool = True
    feature_multi_agent_coordination: bool = True
    feature_tree_of_thoughts: bool = True
    feature_self_reflection: bool = True
    feature_tool_integration: bool = True
    feature_knowledge_fusion: bool = True
    feature_real_time_learning: bool = True
    feature_voice_interface: bool = False
    
    # ============================================================================
    # ORGANIZATION SPECIFIC
    # ============================================================================
    default_organization: str = "NUVO AI"
    supported_organizations: List[str] = ["NUVO AI", "Meril Life Sciences", "Meril Healthcare", "Meril Diagnostics", "Meril Endo Surgery"]
    organization_data_path: str = "./docs/"
    organization_isolation: bool = True
    
    # ============================================================================
    # SELF-IMPROVEMENT
    # ============================================================================
    learning_enabled: bool = True
    feedback_collection_enabled: bool = True
    performance_optimization_enabled: bool = True
    continuous_improvement_enabled: bool = True
    learning_rate: float = 0.01
    feedback_threshold: float = 0.7
    improvement_interval_hours: int = 24
    
    # ============================================================================
    # RESOURCE LIMITS
    # ============================================================================
    max_memory_usage_mb: int = 2048
    max_cpu_usage_percent: int = 80
    max_disk_usage_percent: int = 85
    max_concurrent_requests: int = 100
    request_timeout_seconds: int = 30
    
    # ============================================================================
    # BACKUP & RECOVERY
    # ============================================================================
    backup_enabled: bool = True
    backup_schedule: str = "0 2 * * *"
    backup_retention_days: int = 30
    backup_storage_path: str = "./backups/"
    backup_compression: str = "gzip"
    
    # ============================================================================
    # COMPLIANCE & SECURITY
    # ============================================================================
    gdpr_compliance: bool = True
    data_retention_days: int = 365
    audit_logging: bool = True
    audit_log_path: str = "./logs/audit/"
    security_headers: bool = True
    content_security_policy: bool = True
    
    # ============================================================================
    # DEVELOPMENT & TESTING
    # ============================================================================
    testing_mode: bool = False
    mock_external_services: bool = False
    enable_debug_endpoints: bool = False
    profiling_enabled: bool = False
    test_database_url: str = "postgresql://test_user:test_pass@localhost:5432/test_chabot_db"
    
    # ============================================================================
    # MILESTONE COMPLETION STATUS
    # ============================================================================
    milestone_1_infrastructure: bool = True
    milestone_2_ai_foundation: bool = True
    milestone_3_agent_framework: bool = True
    milestone_4_advanced_reasoning: bool = True
    milestone_5_user_interfaces: bool = True
    milestone_6_system_integration: bool = True
    milestone_7_production_system: bool = True
    milestone_8_self_improvement: bool = True
    
    # ============================================================================
    # EXTERNAL SERVICES (Optional)
    # ============================================================================
    # Email Service
    smtp_host: str = "smtp.gmail.com"
    smtp_port: int = 587
    smtp_user: str = "<EMAIL>"
    smtp_password: str = "your-app-password"
    smtp_tls: bool = True
    
    # Notification Services
    slack_webhook_url: str = ""
    discord_webhook_url: str = ""
    
    # External APIs
    openai_api_key: str = ""
    huggingface_api_key: str = ""
    anthropic_api_key: str = ""
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from .env

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get application settings."""
    return settings