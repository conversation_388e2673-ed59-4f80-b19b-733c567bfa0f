#!/usr/bin/env python3
"""CHaBot runner script with dynamic configuration."""

import os
import sys
import asyncio
import signal
import subprocess
from pathlib import Path
import time

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_settings

class CHaBotRunner:
    def __init__(self):
        self.settings = get_settings()
        self.processes = []
        self.running = False
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self.stop_all_services()
        sys.exit(0)
    
    def start_service(self, name, command, port):
        """Start a service process."""
        print(f"🚀 Starting {name} on port {port}...")
        try:
            env = os.environ.copy()
            env['PYTHONPATH'] = str(project_root)
            
            proc = subprocess.Popen(
                command,
                shell=True,
                cwd=project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes.append({
                'name': name,
                'process': proc,
                'port': port,
                'command': command
            })
            
            time.sleep(1)  # Give service time to start
            
            if proc.poll() is None:
                print(f"   ✅ {name} started successfully")
                return True
            else:
                print(f"   ❌ {name} failed to start")
                return False
                
        except Exception as e:
            print(f"   ❌ Failed to start {name}: {e}")
            return False
    
    def stop_all_services(self):
        """Stop all running services."""
        print("🛑 Stopping all services...")
        for proc_info in self.processes:
            try:
                proc_info['process'].terminate()
                proc_info['process'].wait(timeout=5)
                print(f"   ✅ {proc_info['name']} stopped")
            except subprocess.TimeoutExpired:
                proc_info['process'].kill()
                print(f"   ⚠️ {proc_info['name']} force killed")
            except Exception as e:
                print(f"   ❌ Error stopping {proc_info['name']}: {e}")
        
        self.processes.clear()
        self.running = False
    
    def check_services(self):
        """Check if services are running."""
        import requests
        
        print("🔍 Checking service health...")
        for proc_info in self.processes:
            try:
                response = requests.get(
                    f"http://localhost:{proc_info['port']}/health",
                    timeout=2
                )
                if response.status_code == 200:
                    print(f"   ✅ {proc_info['name']} - Healthy")
                else:
                    print(f"   ⚠️ {proc_info['name']} - Status: {response.status_code}")
            except requests.exceptions.RequestException:
                print(f"   ❌ {proc_info['name']} - Not responding")
    
    def run_demo(self):
        """Run a quick demo."""
        print("🎯 Running CHaBot demo...")
        
        demo_script = f'''
import sys
sys.path.append("{project_root}")
import requests
import json

try:
    # Test main API
    response = requests.get("http://localhost:{self.settings.api_port}/health")
    print(f"Health check: {{response.status_code}}")
    
    # Test chat endpoint
    chat_data = {{
        "query": "What is the company leave policy?",
        "context": {{"organization": "NuvoAI"}}
    }}
    
    response = requests.post(
        "http://localhost:{self.settings.api_port}/chat",
        params={{"query": chat_data["query"]}},
        json=chat_data["context"]
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Chat response: {{result.get('response', 'No response')}}")
        print(f"Tasks created: {{result.get('tasks_created', 0)}}")
        print("✅ Demo successful!")
    else:
        print(f"❌ Chat failed: {{response.status_code}}")
        
except Exception as e:
    print(f"❌ Demo failed: {{e}}")
'''
        
        try:
            result = subprocess.run(
                f'python -c "{demo_script}"',
                shell=True,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(f"Demo errors: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("❌ Demo timed out")
        except Exception as e:
            print(f"❌ Demo error: {e}")
    
    def run(self):
        """Run CHaBot system."""
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🤖 CHaBot Dynamic Runner")
        print("=" * 40)
        print(f"📊 Configuration:")
        print(f"   App: {self.settings.app_name} v{self.settings.app_version}")
        print(f"   Debug: {self.settings.debug}")
        print(f"   Dev Mode: {self.settings.dev_mode}")
        print(f"   API Port: {self.settings.api_port}")
        
        # Start main application
        success = self.start_service(
            "CHaBot API",
            "python app.py",
            self.settings.api_port
        )
        
        if not success:
            print("❌ Failed to start main application")
            return False
        
        self.running = True
        
        # Wait for services to initialize
        print("⏳ Waiting for services to initialize...")
        time.sleep(5)
        
        # Check service health
        self.check_services()
        
        # Run demo
        self.run_demo()
        
        # Show system status
        print(f"\n📊 CHaBot System Status:")
        print(f"   🔧 Services Running: {len(self.processes)}")
        print(f"   🌐 Main API: http://localhost:{self.settings.api_port}")
        print(f"   📚 API Docs: http://localhost:{self.settings.api_port}/docs")
        print(f"   🧪 Health Check: http://localhost:{self.settings.api_port}/health")
        
        print(f"\n🎯 CHaBot is now running!")
        print(f"   Press Ctrl+C to stop")
        
        try:
            # Keep running
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all_services()
        
        return True

def main():
    """Main entry point."""
    runner = CHaBotRunner()
    success = runner.run()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)