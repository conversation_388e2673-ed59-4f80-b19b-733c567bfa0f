stages:
  - test
  - build
  - deploy

variables:
  PYTHON_VERSION: "3.10"
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

cache:
  paths:
    - .cache/pip
    - venv/

before_script:
  - python -m venv venv
  - source venv/bin/activate
  - pip install --upgrade pip
  - pip install -r requirements.txt

# Testing Stage
test:unit:
  stage: test
  script:
    - source venv/bin/activate
    - pytest tests/ -v --cov=agents --cov=backend
    - flake8 agents/ backend/ --max-line-length=88
    - mypy agents/ backend/ --ignore-missing-imports
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml

test:integration:
  stage: test
  services:
    - postgres:15
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_chatbot_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_pass
    REDIS_URL: redis://redis:6379
  script:
    - source venv/bin/activate
    - pytest tests/integration/ -v
  only:
    - main
    - develop

# Build Stage
build:docker:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t chatbot-app .
    - docker tag chatbot-app $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker tag chatbot-app $CI_REGISTRY_IMAGE:latest
  only:
    - main

# Deploy Stage
deploy:staging:
  stage: deploy
  script:
    - echo "Deploying to staging environment"
    - docker-compose -f docker-compose.staging.yml up -d
  environment:
    name: staging
    url: https://staging.chatbot.example.com
  only:
    - develop

deploy:production:
  stage: deploy
  script:
    - echo "Deploying to production environment"
    - docker-compose -f docker-compose.prod.yml up -d
  environment:
    name: production
    url: https://chatbot.example.com
  when: manual
  only:
    - main