#!/usr/bin/env python3
"""
Simple Database Testing Script for CHaBot
Tests available database connections.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_postgresql():
    """Test PostgreSQL connection."""
    try:
        import asyncpg
        
        # Connection string
        conn_str = "postgresql://chabot_user:chabot_password@localhost:5432/chabot_db"
        
        # Test connection
        conn = await asyncpg.connect(conn_str)
        
        # Test query
        result = await conn.fetchval("SELECT 1")
        
        # Create test table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS test_table (
                id SERIAL PRIMARY KEY,
                name VARCHA<PERSON>(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Insert test data
        await conn.execute("INSERT INTO test_table (name) VALUES ($1)", "test_entry")
        
        # Query test data
        rows = await conn.fetch("SELECT * FROM test_table LIMIT 5")
        
        await conn.close()
        
        logger.info(f"✅ PostgreSQL: Connected, {len(rows)} test records")
        return True
        
    except Exception as e:
        logger.error(f"❌ PostgreSQL: {e}")
        return False

async def test_neo4j():
    """Test Neo4j connection."""
    try:
        from neo4j import GraphDatabase
        
        # Connection details
        uri = "bolt://10.10.110.153:32355"
        user = "neo4j"
        password = "admin@123"
        
        # Test connection
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            # Test query
            result = session.run("RETURN 1 as test")
            record = result.single()
            
            # Create test node
            session.run("CREATE (t:TestNode {name: 'test', timestamp: timestamp()})")
            
            # Query test nodes
            result = session.run("MATCH (t:TestNode) RETURN count(t) as count")
            count = result.single()["count"]
            
            # Cleanup
            session.run("MATCH (t:TestNode {name: 'test'}) DELETE t")
        
        driver.close()
        
        logger.info(f"✅ Neo4j: Connected, {count} test nodes found")
        return True
        
    except Exception as e:
        logger.error(f"❌ Neo4j: {e}")
        return False

async def test_milvus():
    """Test Milvus connection."""
    try:
        from pymilvus import connections, utility, Collection, FieldSchema, CollectionSchema, DataType
        
        # Connect to Milvus
        connections.connect(
            alias="default",
            host="localhost",
            port="19530"
        )
        
        # Test connection
        if not connections.has_connection("default"):
            raise Exception("Failed to connect to Milvus")
        
        # List collections
        collections = utility.list_collections()
        
        logger.info(f"✅ Milvus: Connected, {len(collections)} collections found")
        return True
        
    except Exception as e:
        logger.error(f"❌ Milvus: {e}")
        return False

async def test_chroma():
    """Test Chroma connection."""
    try:
        import chromadb
        import requests

        # Test HTTP connection first
        response = requests.get("http://localhost:8001/api/v1/heartbeat", timeout=5)
        if response.status_code != 200:
            raise Exception(f"HTTP heartbeat failed: {response.status_code}")

        # Try to connect to Chroma server with proper configuration
        client = chromadb.HttpClient(host="localhost", port=8001)

        # Test connection with heartbeat
        heartbeat = client.heartbeat()

        # Try to get or create a test collection
        try:
            collection = client.get_or_create_collection("test_collection")
            collections = client.list_collections()
            logger.info(f"✅ Chroma: Connected, {len(collections)} collections found")
            return True
        except Exception as e:
            # If collection operations fail, at least heartbeat worked
            logger.info(f"✅ Chroma: Connected (heartbeat OK), collection ops failed: {e}")
            return True

    except Exception as e:
        logger.error(f"❌ Chroma: {e}")
        return False

async def test_memgraph():
    """Test Memgraph connection."""
    try:
        from neo4j import GraphDatabase
        
        # Connection details
        uri = "bolt://localhost:7687"
        
        # Test connection
        driver = GraphDatabase.driver(uri, auth=None)
        
        with driver.session() as session:
            # Test query
            result = session.run("RETURN 1 as test")
            record = result.single()
        
        driver.close()
        
        logger.info(f"✅ Memgraph: Connected")
        return True
        
    except Exception as e:
        logger.error(f"❌ Memgraph: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting database connectivity tests...")
    
    tests = {
        "PostgreSQL": test_postgresql(),
        "Neo4j": test_neo4j(),
        "Milvus": test_milvus(),
        "Chroma": test_chroma(),
        "Memgraph": test_memgraph()
    }
    
    results = {}
    
    for db_name, test_coro in tests.items():
        logger.info(f"Testing {db_name}...")
        try:
            results[db_name] = await test_coro
        except Exception as e:
            logger.error(f"Test failed for {db_name}: {e}")
            results[db_name] = False
    
    # Summary
    connected = sum(results.values())
    total = len(results)
    
    logger.info("="*60)
    logger.info("🎯 DATABASE TEST SUMMARY")
    logger.info("="*60)
    
    for db_name, status in results.items():
        status_icon = "✅" if status else "❌"
        logger.info(f"{status_icon} {db_name}: {'Connected' if status else 'Failed'}")
    
    logger.info(f"📊 Total: {connected}/{total} databases connected")
    logger.info(f"📈 Success Rate: {(connected/total)*100:.1f}%")
    
    if connected >= 2:
        logger.info("🎉 Sufficient databases available for CHaBot!")
    else:
        logger.warning("⚠️ Need more database connections for full functionality")
    
    logger.info("="*60)
    
    # Save results
    report = {
        "timestamp": datetime.now().isoformat(),
        "results": results,
        "summary": {
            "connected": connected,
            "total": total,
            "success_rate": f"{(connected/total)*100:.1f}%"
        }
    }
    
    with open("database_test_results.json", "w") as f:
        json.dump(report, f, indent=2)
    
    logger.info("📄 Results saved to database_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())
