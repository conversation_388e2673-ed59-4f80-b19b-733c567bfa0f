#!/usr/bin/env python3
"""Validate Environment Configuration - CHaBot System."""

import os
from config import get_settings
from dotenv import load_dotenv

def validate_environment():
    """Validate all environment variables are properly loaded."""
    print("🔍 CHaBot Environment Configuration Validation")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    settings = get_settings()
    
    # Critical Configuration Checks
    critical_checks = [
        ("Application Name", settings.app_name, "CHaBot"),
        ("Environment", settings.environment, "production"),
        ("API Port", settings.api_port, 8000),
        ("Secret Key Length", len(settings.secret_key) >= 32, True),
        ("Database URL", "postgresql://" in settings.database_url, True),
        ("Neo4j Connection", settings.neo4j_host, "*************"),
        ("Ollama URL", settings.ollama_base_url, "http://localhost:11434"),
        ("LLM Model", settings.llm_model_name, "deepseek-r1:latest"),
        ("Embedding Model", settings.embedding_model, "all-MiniLM-L6-v2"),
    ]
    
    print("\\n📋 Critical Configuration Checks:")
    print("-" * 40)
    
    all_passed = True
    for check_name, actual, expected in critical_checks:
        status = "✅" if actual == expected else "❌"
        if actual != expected:
            all_passed = False
        print(f"{status} {check_name}: {actual}")
    
    # Database Configuration
    print("\\n🗄️  Database Configuration:")
    print("-" * 40)
    print(f"✅ PostgreSQL: {settings.postgres_host}:{settings.postgres_port}/{settings.postgres_db}")
    print(f"✅ Neo4j: {settings.neo4j_host}:{settings.neo4j_port}")
    print(f"✅ Milvus: {settings.milvus_host}:{settings.milvus_port}")
    print(f"✅ Redis: {settings.redis_host}:{settings.redis_port}")
    
    # AI Configuration
    print("\\n🧠 AI Configuration:")
    print("-" * 40)
    print(f"✅ Ollama Base URL: {settings.ollama_base_url}")
    print(f"✅ LLM Model: {settings.llm_model_name}")
    print(f"✅ Fallback Model: {settings.llm_fallback_model}")
    print(f"✅ Embedding Model: {settings.embedding_model}")
    print(f"✅ Embedding Dimension: {settings.embedding_dimension}")
    
    # Agent Configuration
    print("\\n🤖 Agent Configuration:")
    print("-" * 40)
    print(f"✅ Max Agents per Type: {settings.max_agents_per_type}")
    print(f"✅ Agent Timeout: {settings.agent_timeout_seconds}s")
    print(f"✅ Task Queue Size: {settings.task_queue_size}")
    print(f"✅ Max Concurrent Tasks: {settings.max_concurrent_tasks}")
    
    # Feature Flags
    print("\\n🚀 Feature Flags:")
    print("-" * 40)
    features = [
        ("Advanced Reasoning", settings.feature_advanced_reasoning),
        ("Multi-Agent Coordination", settings.feature_multi_agent_coordination),
        ("Tree of Thoughts", settings.feature_tree_of_thoughts),
        ("Self-Reflection", settings.feature_self_reflection),
        ("Tool Integration", settings.feature_tool_integration),
        ("Knowledge Fusion", settings.feature_knowledge_fusion),
        ("Real-time Learning", settings.feature_real_time_learning),
    ]
    
    for feature_name, enabled in features:
        status = "✅" if enabled else "❌"
        print(f"{status} {feature_name}: {'Enabled' if enabled else 'Disabled'}")
    
    # Milestone Status
    print("\\n🎯 Milestone Completion Status:")
    print("-" * 40)
    milestones = [
        ("Milestone 1: Infrastructure Ready", settings.milestone_1_infrastructure),
        ("Milestone 2: AI Foundation Complete", settings.milestone_2_ai_foundation),
        ("Milestone 3: Agent Framework Operational", settings.milestone_3_agent_framework),
        ("Milestone 4: Advanced Reasoning Implemented", settings.milestone_4_advanced_reasoning),
        ("Milestone 5: User Interfaces Complete", settings.milestone_5_user_interfaces),
        ("Milestone 6: System Integration Complete", settings.milestone_6_system_integration),
        ("Milestone 7: Production System Live", settings.milestone_7_production_system),
        ("Milestone 8: Self-Improvement Active", settings.milestone_8_self_improvement),
    ]
    
    completed_milestones = 0
    for milestone_name, completed in milestones:
        status = "✅" if completed else "❌"
        if completed:
            completed_milestones += 1
        print(f"{status} {milestone_name}")
    
    # Organization Configuration
    print("\\n🏢 Organization Configuration:")
    print("-" * 40)
    print(f"✅ Default Organization: {settings.default_organization}")
    print(f"✅ Supported Organizations: {len(settings.supported_organizations)} organizations")
    for org in settings.supported_organizations:
        print(f"   - {org}")
    
    # Security Configuration
    print("\\n🔒 Security Configuration:")
    print("-" * 40)
    print(f"✅ Secret Key Length: {len(settings.secret_key)} chars")
    print(f"✅ JWT Algorithm: {settings.jwt_algorithm}")
    print(f"✅ JWT Expire Minutes: {settings.jwt_expire_minutes}")
    print(f"✅ CORS Enabled: {settings.enable_cors}")
    print(f"✅ Security Headers: {settings.security_headers}")
    
    # Summary
    print("\\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    if all_passed:
        print("✅ All critical configuration checks PASSED")
    else:
        print("❌ Some critical configuration checks FAILED")
    
    print(f"✅ Milestones Completed: {completed_milestones}/8 ({completed_milestones/8*100:.0f}%)")
    print(f"✅ Environment: {settings.environment}")
    print(f"✅ Debug Mode: {'Enabled' if settings.debug else 'Disabled'}")
    print(f"✅ Mock Databases: {'Enabled' if settings.mock_databases else 'Disabled'}")
    
    # Environment Variables Check
    env_vars_count = len([key for key in os.environ.keys() if key.startswith(('APP_', 'API_', 'DATABASE_', 'NEO4J_', 'MILVUS_', 'OLLAMA_', 'LLM_', 'EMBEDDING_', 'AGENT_', 'FEATURE_', 'MILESTONE_'))])
    print(f"✅ Environment Variables Loaded: {env_vars_count}")
    
    print("\\n🎉 CHaBot Environment Configuration Validation Complete!")
    
    return all_passed and completed_milestones == 8

if __name__ == "__main__":
    success = validate_environment()
    exit(0 if success else 1)