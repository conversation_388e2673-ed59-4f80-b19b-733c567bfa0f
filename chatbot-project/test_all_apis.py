#!/usr/bin/env python3
"""
Comprehensive API Test Suite for CHaBot Integrated System
Tests all available API endpoints
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime
from typing import Dict, List, Any

# Test configuration
BASE_URL = "http://localhost:8002"
TIMEOUT = 30

class APITestSuite:
    """Comprehensive API test suite."""
    
    def __init__(self, base_url: str = BASE_URL):
        """Initialize the test suite."""
        self.base_url = base_url
        self.session = None
        self.test_results = {}
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=TIMEOUT))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request and return response."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    data = await response.json()
                else:
                    data = await response.text()
                
                return {
                    "status": response.status,
                    "data": data,
                    "headers": dict(response.headers),
                    "success": 200 <= response.status < 300
                }
        except Exception as e:
            return {
                "status": 0,
                "data": {"error": str(e)},
                "headers": {},
                "success": False
            }
    
    async def test_basic_endpoints(self) -> bool:
        """Test basic system endpoints."""
        print("🔍 Testing Basic Endpoints...")
        
        tests = [
            ("GET", "/", "Root endpoint"),
            ("GET", "/health", "Health check"),
            ("GET", "/system/status", "System status"),
            ("GET", "/system/statistics", "System statistics")
        ]
        
        all_passed = True
        
        for method, endpoint, description in tests:
            result = await self.make_request(method, endpoint)
            
            if result["success"]:
                print(f"  ✅ {description}: {result['status']}")
                if endpoint == "/":
                    print(f"     Message: {result['data'].get('message', 'N/A')}")
                elif endpoint == "/health":
                    print(f"     Status: {result['data'].get('status', 'N/A')}")
            else:
                print(f"  ❌ {description}: {result['status']} - {result['data']}")
                all_passed = False
        
        self.test_results["basic_endpoints"] = all_passed
        return all_passed
    
    async def test_chat_endpoints(self) -> bool:
        """Test chat-related endpoints."""
        print("\n💬 Testing Chat Endpoints...")
        
        all_passed = True
        
        # Test chat message
        chat_data = {
            "message": "Hello, this is a test message",
            "user_id": "test_user",
            "session_id": "test_session_123"
        }
        
        result = await self.make_request("POST", "/chat", json=chat_data)
        
        if result["success"]:
            print(f"  ✅ Chat message: {result['status']}")
            response_data = result["data"]
            if response_data.get("success"):
                print(f"     Response: {response_data['response']['message'][:100]}...")
                print(f"     Session ID: {response_data.get('session_id')}")
            else:
                print(f"     Error: {response_data.get('error')}")
        else:
            print(f"  ❌ Chat message: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test chat sessions list
        result = await self.make_request("GET", "/chat/sessions")
        
        if result["success"]:
            sessions = result["data"]
            print(f"  ✅ Chat sessions list: {result['status']} ({len(sessions)} sessions)")
        else:
            print(f"  ❌ Chat sessions list: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test specific session info
        result = await self.make_request("GET", "/chat/sessions/test_session_123")
        
        if result["success"]:
            print(f"  ✅ Session info: {result['status']}")
            session_data = result["data"]
            print(f"     Messages: {session_data.get('message_count', 0)}")
        else:
            print(f"  ❌ Session info: {result['status']} - {result['data']}")
            # This might fail if session doesn't exist, which is okay
        
        self.test_results["chat_endpoints"] = all_passed
        return all_passed
    
    async def test_agent_endpoints(self) -> bool:
        """Test agent-related endpoints."""
        print("\n🤖 Testing Agent Endpoints...")
        
        all_passed = True
        
        # Test list agents
        result = await self.make_request("GET", "/agents")
        
        if result["success"]:
            agents = result["data"]
            print(f"  ✅ List agents: {result['status']} ({len(agents)} agents)")
            
            # Test specific agent if available
            if agents:
                agent_id = agents[0].get("id")
                result = await self.make_request("GET", f"/agents/{agent_id}")
                
                if result["success"]:
                    print(f"  ✅ Agent info: {result['status']}")
                    agent_data = result["data"]
                    print(f"     Agent: {agent_data.get('name')} ({agent_data.get('type')})")
                else:
                    print(f"  ❌ Agent info: {result['status']} - {result['data']}")
                    all_passed = False
                
                # Test task assignment
                task_data = {
                    "description": "Test task for agent",
                    "task_type": "analysis",
                    "priority": "medium"
                }
                
                result = await self.make_request("POST", f"/agents/{agent_id}/task", json=task_data)
                
                if result["success"]:
                    print(f"  ✅ Task assignment: {result['status']}")
                    task_result = result["data"]
                    print(f"     Task ID: {task_result.get('task_id')}")
                else:
                    print(f"  ❌ Task assignment: {result['status']} - {result['data']}")
                    all_passed = False
        else:
            print(f"  ❌ List agents: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test agent coordination
        coord_data = {
            "description": "Coordinate multiple agents for complex analysis",
            "task_type": "coordination",
            "priority": "high"
        }
        
        result = await self.make_request("POST", "/agents/coordinate", json=coord_data)
        
        if result["success"]:
            print(f"  ✅ Agent coordination: {result['status']}")
            coord_result = result["data"]
            print(f"     Coordination ID: {coord_result.get('coordination_id')}")
        else:
            print(f"  ❌ Agent coordination: {result['status']} - {result['data']}")
            all_passed = False
        
        self.test_results["agent_endpoints"] = all_passed
        return all_passed
    
    async def test_memory_endpoints(self) -> bool:
        """Test memory-related endpoints."""
        print("\n🧠 Testing Memory Endpoints...")
        
        all_passed = True
        
        # Test memory query
        result = await self.make_request("GET", "/memory/query", params={
            "query": "artificial intelligence",
            "limit": 5
        })
        
        if result["success"]:
            print(f"  ✅ Memory query: {result['status']}")
            query_result = result["data"]
            if query_result.get("success"):
                results = query_result.get("results", {})
                print(f"     Episodic: {len(results.get('episodic_memories', []))}")
                print(f"     Semantic: {len(results.get('semantic_concepts', []))}")
                print(f"     Working: {len(results.get('working_memory_items', []))}")
        else:
            print(f"  ❌ Memory query: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test memory storage
        memory_data = {
            "memory_type": "episodic",
            "content": {
                "title": "Test Memory",
                "description": "This is a test memory entry",
                "content": {"test": "data"},
                "importance": 0.7,
                "tags": ["test", "api"]
            }
        }
        
        result = await self.make_request("POST", "/memory/store", json=memory_data)
        
        if result["success"]:
            print(f"  ✅ Memory storage: {result['status']}")
            store_result = result["data"]
            if store_result.get("success"):
                print(f"     Memory ID: {store_result.get('memory_id')}")
        else:
            print(f"  ❌ Memory storage: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test memory statistics
        result = await self.make_request("GET", "/memory/statistics")
        
        if result["success"]:
            print(f"  ✅ Memory statistics: {result['status']}")
            stats = result["data"]
            if stats.get("success"):
                print(f"     Statistics available: {len(stats.get('statistics', {}))}")
        else:
            print(f"  ❌ Memory statistics: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test episode search
        result = await self.make_request("GET", "/memory/episodes", params={
            "query": "test",
            "limit": 5
        })
        
        if result["success"]:
            print(f"  ✅ Episode search: {result['status']}")
            episodes = result["data"]
            if episodes.get("success"):
                print(f"     Episodes found: {episodes.get('count', 0)}")
        else:
            print(f"  ❌ Episode search: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test concept search
        result = await self.make_request("GET", "/memory/concepts", params={
            "query": "intelligence",
            "limit": 5
        })
        
        if result["success"]:
            print(f"  ✅ Concept search: {result['status']}")
            concepts = result["data"]
            if concepts.get("success"):
                print(f"     Concepts found: {concepts.get('count', 0)}")
        else:
            print(f"  ❌ Concept search: {result['status']} - {result['data']}")
            all_passed = False
        
        self.test_results["memory_endpoints"] = all_passed
        return all_passed
    
    async def test_tool_endpoints(self) -> bool:
        """Test tool-related endpoints."""
        print("\n🛠️ Testing Tool Endpoints...")
        
        all_passed = True
        
        # Test list tools
        result = await self.make_request("GET", "/tools")
        
        if result["success"]:
            tools = result["data"]
            print(f"  ✅ List tools: {result['status']} ({len(tools)} tools)")
            
            # Test specific tool info if available
            if tools:
                tool_name = tools[0].get("name")
                result = await self.make_request("GET", f"/tools/{tool_name}")
                
                if result["success"]:
                    print(f"  ✅ Tool info: {result['status']}")
                    tool_data = result["data"]
                    print(f"     Tool: {tool_data.get('name')} - {tool_data.get('description')}")
                else:
                    print(f"  ❌ Tool info: {result['status']} - {result['data']}")
                    all_passed = False
        else:
            print(f"  ❌ List tools: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test tool execution
        exec_data = {
            "tool_name": "calculate",
            "parameters": {"expression": "2 + 2 * 3"}
        }
        
        result = await self.make_request("POST", "/tools/execute", json=exec_data)
        
        if result["success"]:
            print(f"  ✅ Tool execution: {result['status']}")
            exec_result = result["data"]
            if exec_result.get("success"):
                print(f"     Result: {exec_result.get('result')}")
        else:
            print(f"  ❌ Tool execution: {result['status']} - {result['data']}")
            all_passed = False
        
        # Test tool categories
        result = await self.make_request("GET", "/tools/categories")
        
        if result["success"]:
            categories = result["data"]
            print(f"  ✅ Tool categories: {result['status']} ({len(categories)} categories)")
        else:
            print(f"  ❌ Tool categories: {result['status']} - {result['data']}")
            all_passed = False
        
        self.test_results["tool_endpoints"] = all_passed
        return all_passed
    
    async def run_all_tests(self) -> bool:
        """Run all API tests."""
        print("🧪 CHaBot API Test Suite")
        print("=" * 80)
        
        test_functions = [
            self.test_basic_endpoints,
            self.test_chat_endpoints,
            self.test_agent_endpoints,
            self.test_memory_endpoints,
            self.test_tool_endpoints
        ]
        
        all_passed = True
        
        for test_func in test_functions:
            try:
                result = await test_func()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
                all_passed = False
        
        # Print summary
        print("\n" + "=" * 80)
        print("📊 API TEST SUMMARY")
        print("=" * 80)
        
        passed_tests = sum(self.test_results.values())
        total_tests = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}: {test_name.replace('_', ' ').title()}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n📈 Results: {passed_tests}/{total_tests} test suites passed ({success_rate:.1f}%)")
        
        if all_passed:
            print("🎉 All API tests passed! The system is working correctly.")
        else:
            print("⚠️ Some tests failed. Please check the issues above.")
        
        return all_passed

async def main():
    """Main test execution."""
    async with APITestSuite() as test_suite:
        success = await test_suite.run_all_tests()
        return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        sys.exit(1)
