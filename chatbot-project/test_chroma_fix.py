#!/usr/bin/env python3
"""
Fix Chroma connection issues
"""

import chromadb
import requests
import time

def test_chroma_connection():
    """Test and fix Chroma connection."""
    try:
        # Test basic HTTP connection first
        response = requests.get("http://localhost:8001/api/v1/heartbeat", timeout=5)
        print(f"✅ Chroma HTTP heartbeat: {response.status_code}")
        
        # Try different client configurations
        print("Testing different Chroma client configurations...")
        
        # Method 1: Basic HttpClient
        try:
            client = chromadb.HttpClient(host="localhost", port=8001)
            heartbeat = client.heartbeat()
            print(f"✅ Method 1 - Basic HttpClient: {heartbeat}")
            
            # List collections
            collections = client.list_collections()
            print(f"✅ Collections found: {len(collections)}")
            
            # Create a test collection
            collection = client.get_or_create_collection("test_collection")
            print(f"✅ Test collection created/retrieved: {collection.name}")
            
            return True
            
        except Exception as e:
            print(f"❌ Method 1 failed: {e}")
        
        # Method 2: Try with settings
        try:
            import chromadb.config
            settings = chromadb.config.Settings()
            settings.chroma_server_host = "localhost"
            settings.chroma_server_http_port = 8001
            
            client = chromadb.HttpClient(settings=settings)
            heartbeat = client.heartbeat()
            print(f"✅ Method 2 - With settings: {heartbeat}")
            return True
            
        except Exception as e:
            print(f"❌ Method 2 failed: {e}")
        
        # Method 3: Direct API calls
        try:
            # Test collections endpoint
            response = requests.get("http://localhost:8001/api/v1/collections", timeout=5)
            print(f"✅ Method 3 - Direct API: {response.status_code}, {response.json()}")
            return True
            
        except Exception as e:
            print(f"❌ Method 3 failed: {e}")
            
        return False
        
    except Exception as e:
        print(f"❌ Chroma connection test failed: {e}")
        return False

def test_milvus_connection():
    """Test Milvus connection with different approaches."""
    try:
        from pymilvus import connections, utility
        
        # Method 1: Default connection
        try:
            connections.connect(
                alias="default",
                host="localhost",
                port="19530",
                timeout=10
            )
            
            if connections.has_connection("default"):
                collections = utility.list_collections()
                print(f"✅ Milvus connected: {len(collections)} collections")
                return True
            else:
                print("❌ Milvus connection not established")
                
        except Exception as e:
            print(f"❌ Milvus connection failed: {e}")
            
        # Method 2: Check if Milvus is actually running
        import requests
        try:
            response = requests.get("http://localhost:9091/healthz", timeout=5)
            print(f"✅ Milvus health check: {response.status_code}")
        except Exception as e:
            print(f"❌ Milvus health check failed: {e}")
            
        return False
        
    except Exception as e:
        print(f"❌ Milvus test failed: {e}")
        return False

def test_neo4j_connection():
    """Test Neo4j connection with retry."""
    try:
        from neo4j import GraphDatabase
        
        # Connection details
        uri = "bolt://*************:32355"
        user = "neo4j"
        password = "admin@123"
        
        # Test with timeout and retry
        driver = GraphDatabase.driver(uri, auth=(user, password), connection_timeout=10)
        
        with driver.session() as session:
            # Simple test query
            result = session.run("RETURN 1 as test")
            record = result.single()
            print(f"✅ Neo4j connected: test result = {record['test']}")
            
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 FIXING DATABASE CONNECTIONS")
    print("=" * 50)
    
    print("\n1. Testing Chroma...")
    chroma_ok = test_chroma_connection()
    
    print("\n2. Testing Milvus...")
    milvus_ok = test_milvus_connection()
    
    print("\n3. Testing Neo4j...")
    neo4j_ok = test_neo4j_connection()
    
    print("\n" + "=" * 50)
    print("🎯 RESULTS:")
    print(f"✅ Chroma: {'Working' if chroma_ok else 'Failed'}")
    print(f"✅ Milvus: {'Working' if milvus_ok else 'Failed'}")
    print(f"✅ Neo4j: {'Working' if neo4j_ok else 'Failed'}")
    
    working = sum([chroma_ok, milvus_ok, neo4j_ok])
    print(f"📊 Total: {working}/3 additional databases working")
