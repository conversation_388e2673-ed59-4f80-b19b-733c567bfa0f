#!/usr/bin/env python3
"""CHaBot setup and installation script."""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(cmd, check=True):
    """Run a command and return the result."""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check Python version."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    # Core dependencies
    core_deps = [
        "fastapi>=0.100.0",
        "uvicorn[standard]>=0.20.0",
        "pydantic>=2.0.0",
        "python-multipart",
        "python-dotenv",
        "httpx",
        "aiofiles",
        "requests",
        "PyJWT",
        "cryptography",
        "bcrypt",
        "numpy",
        "scikit-learn"
    ]
    
    for dep in core_deps:
        if not run_command(f"pip install {dep}"):
            print(f"❌ Failed to install {dep}")
            return False
    
    print("✅ Dependencies installed")
    return True

def setup_environment():
    """Setup environment files."""
    print("🔧 Setting up environment...")
    
    # Create .env if it doesn't exist
    env_file = Path(".env")
    if not env_file.exists():
        print("Creating .env file...")
        with open(env_file, "w") as f:
            f.write("""# CHaBot Environment Configuration
DEBUG=true
LOG_LEVEL=INFO
API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=dev-secret-key-change-in-production
DEV_MODE=true
MOCK_DATABASES=true
ENABLE_CORS=true
""")
    
    print("✅ Environment configured")
    return True

def test_installation():
    """Test the installation."""
    print("🧪 Testing installation...")
    
    # Test imports
    test_script = '''
import sys
sys.path.append(".")

try:
    from config import get_settings
    from ai.reasoning.engine.reasoning_engine import ReasoningEngine
    from agents.coordinator.task_planning import TaskPlanner
    from agents.tools.registry import ToolRegistry
    print("✅ All imports successful")
    
    # Quick functionality test
    settings = get_settings()
    print(f"✅ Settings loaded: {settings.app_name}")
    
    engine = ReasoningEngine()
    planner = TaskPlanner()
    registry = ToolRegistry()
    print("✅ Core components initialized")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    sys.exit(1)
'''
    
    return run_command(f'python -c "{test_script}"')

def main():
    """Main setup function."""
    print("🚀 CHaBot Setup")
    print("=" * 30)
    
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        sys.exit(1)
    
    print("\n🎉 CHaBot setup completed successfully!")
    print("\n🚀 To start CHaBot:")
    print("   python app.py")
    print("\n🌐 API will be available at:")
    print("   http://localhost:8000")
    print("   http://localhost:8000/docs (API documentation)")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)