class AgentSimulator {
    constructor() {
        this.agents = new Map();
        this.scenarios = [];
        this.results = [];
        this.isRunning = false;
    }

    // Register agent for testing
    registerAgent(id, config) {
        this.agents.set(id, {
            id,
            type: config.type || 'general',
            capabilities: config.capabilities || [],
            status: 'idle',
            memory: [],
            performance: { accuracy: 0, speed: 0, interactions: 0 }
        });
    }

    // Create test scenario
    createScenario(name, config) {
        const scenario = {
            id: Date.now(),
            name,
            agents: config.agents || [],
            tasks: config.tasks || [],
            expectedOutcomes: config.expectedOutcomes || [],
            timeout: config.timeout || 30000,
            status: 'pending'
        };
        this.scenarios.push(scenario);
        return scenario.id;
    }

    // Run single scenario
    async runScenario(scenarioId) {
        const scenario = this.scenarios.find(s => s.id === scenarioId);
        if (!scenario) throw new Error('Scenario not found');

        scenario.status = 'running';
        const startTime = Date.now();
        const results = [];

        try {
            for (const task of scenario.tasks) {
                const result = await this.executeTask(task, scenario.agents);
                results.push(result);
            }

            scenario.status = 'completed';
            const duration = Date.now() - startTime;

            return {
                scenarioId,
                status: 'success',
                duration,
                results,
                verification: this.verifyResults(results, scenario.expectedOutcomes)
            };
        } catch (error) {
            scenario.status = 'failed';
            return {
                scenarioId,
                status: 'error',
                error: error.message,
                results
            };
        }
    }

    // Execute individual task
    async executeTask(task, agentIds) {
        const startTime = Date.now();
        const responses = {};

        for (const agentId of agentIds) {
            const agent = this.agents.get(agentId);
            if (!agent) continue;

            agent.status = 'processing';
            const response = await this.simulateAgentResponse(agent, task);
            responses[agentId] = response;
            agent.performance.interactions++;
            agent.status = 'idle';
        }

        return {
            task: task.description,
            duration: Date.now() - startTime,
            responses,
            coordination: this.analyzeCoordination(responses)
        };
    }

    // Simulate agent response
    async simulateAgentResponse(agent, task) {
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));

        const response = {
            agentId: agent.id,
            confidence: Math.random() * 0.4 + 0.6, // 0.6-1.0
            reasoning: [`Analyzed task: ${task.description}`, 'Applied domain knowledge', 'Generated response'],
            result: this.generateMockResponse(agent, task),
            timestamp: Date.now()
        };

        agent.memory.push({ task: task.description, response: response.result });
        return response;
    }

    // Generate mock response based on agent type
    generateMockResponse(agent, task) {
        const responses = {
            hr: ['HR policy information provided', 'Leave request processed', 'Benefits details shared'],
            general: ['General assistance provided', 'Query routed appropriately', 'Information retrieved'],
            policy: ['Policy analysis completed', 'Compliance check performed', 'Legal guidance provided']
        };

        const typeResponses = responses[agent.type] || responses.general;
        return typeResponses[Math.floor(Math.random() * typeResponses.length)];
    }

    // Analyze agent coordination
    analyzeCoordination(responses) {
        const agentIds = Object.keys(responses);
        const coordination = {
            participantCount: agentIds.length,
            avgConfidence: 0,
            conflicts: 0,
            consensus: false
        };

        if (agentIds.length === 0) return coordination;

        // Calculate average confidence
        const confidences = Object.values(responses).map(r => r.confidence);
        coordination.avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length;

        // Check for conflicts (low confidence spread)
        const confidenceSpread = Math.max(...confidences) - Math.min(...confidences);
        coordination.conflicts = confidenceSpread > 0.3 ? 1 : 0;
        coordination.consensus = confidenceSpread < 0.2;

        return coordination;
    }

    // Verify results against expected outcomes
    verifyResults(results, expectedOutcomes) {
        const verification = {
            passed: 0,
            failed: 0,
            total: expectedOutcomes.length,
            details: []
        };

        expectedOutcomes.forEach(expected => {
            const actual = results.find(r => r.task === expected.task);
            const passed = actual && this.matchesExpectation(actual, expected);
            
            if (passed) {
                verification.passed++;
            } else {
                verification.failed++;
            }

            verification.details.push({
                task: expected.task,
                expected: expected.criteria,
                actual: actual ? actual.coordination : null,
                passed
            });
        });

        return verification;
    }

    // Check if result matches expectation
    matchesExpectation(actual, expected) {
        if (expected.criteria.minParticipants && actual.coordination.participantCount < expected.criteria.minParticipants) {
            return false;
        }
        if (expected.criteria.minConfidence && actual.coordination.avgConfidence < expected.criteria.minConfidence) {
            return false;
        }
        if (expected.criteria.maxConflicts && actual.coordination.conflicts > expected.criteria.maxConflicts) {
            return false;
        }
        return true;
    }

    // Run all scenarios
    async runAllScenarios() {
        this.isRunning = true;
        const results = [];

        for (const scenario of this.scenarios) {
            if (scenario.status === 'pending') {
                const result = await this.runScenario(scenario.id);
                results.push(result);
            }
        }

        this.isRunning = false;
        this.results = results;
        return results;
    }

    // Get test summary
    getSummary() {
        const summary = {
            totalScenarios: this.scenarios.length,
            completed: this.scenarios.filter(s => s.status === 'completed').length,
            failed: this.scenarios.filter(s => s.status === 'failed').length,
            agents: this.agents.size,
            avgPerformance: this.calculateAvgPerformance()
        };

        return summary;
    }

    // Calculate average performance across agents
    calculateAvgPerformance() {
        if (this.agents.size === 0) return { accuracy: 0, speed: 0, interactions: 0 };

        const totals = Array.from(this.agents.values()).reduce((acc, agent) => {
            acc.interactions += agent.performance.interactions;
            return acc;
        }, { interactions: 0 });

        return {
            interactions: totals.interactions / this.agents.size,
            avgConfidence: 0.85 // Mock value
        };
    }

    // Reset simulator
    reset() {
        this.agents.clear();
        this.scenarios = [];
        this.results = [];
        this.isRunning = false;
    }
}

export default AgentSimulator;