import AgentSimulator from './AgentSimulator.js';
import ScenarioGenerator from '../scenarios/ScenarioGenerator.js';
import AutomatedVerifier from '../verification/AutomatedVerifier.js';
import InteractionTester from '../agents/InteractionTester.js';
import SecurityTester from '../security/SecurityTester.js';

class TestRunner {
    constructor() {
        this.simulator = new AgentSimulator();
        this.scenarioGenerator = new ScenarioGenerator();
        this.verifier = new AutomatedVerifier();
        this.interactionTester = new InteractionTester();
        this.securityTester = new SecurityTester();
        this.testSessions = [];
        this.isRunning = false;
    }

    // Initialize test environment
    async initializeTestEnvironment() {
        // Register test agents
        this.simulator.registerAgent('hr_agent', {
            type: 'hr',
            capabilities: ['policy_lookup', 'leave_management', 'benefits_info']
        });

        this.simulator.registerAgent('general_agent', {
            type: 'general',
            capabilities: ['general_qa', 'routing', 'basic_info']
        });

        this.simulator.registerAgent('policy_agent', {
            type: 'policy',
            capabilities: ['policy_analysis', 'compliance_check', 'legal_guidance']
        });

        console.log('Test environment initialized with 3 agents');
    }

    // Run comprehensive test suite
    async runComprehensiveTests() {
        if (this.isRunning) {
            throw new Error('Test suite is already running');
        }

        this.isRunning = true;
        const session = {
            id: Date.now(),
            startTime: new Date().toISOString(),
            status: 'running',
            results: {}
        };

        try {
            console.log('Starting comprehensive test suite...');

            // Phase 1: Multi-Agent Testing
            console.log('Phase 1: Running multi-agent simulation tests...');
            session.results.multiAgent = await this.runMultiAgentTests();

            // Phase 2: Agent Interaction Testing
            console.log('Phase 2: Running agent interaction tests...');
            session.results.interactions = await this.interactionTester.runAllTests();

            // Phase 3: Security Testing
            console.log('Phase 3: Running security tests...');
            session.results.security = await this.securityTester.runComprehensiveSecurityTest();

            // Phase 4: Verification
            console.log('Phase 4: Running automated verification...');
            session.results.verification = await this.runVerificationTests(session.results);

            session.status = 'completed';
            session.endTime = new Date().toISOString();
            session.duration = new Date(session.endTime) - new Date(session.startTime);

            console.log('Comprehensive test suite completed successfully');

        } catch (error) {
            session.status = 'failed';
            session.error = error.message;
            session.endTime = new Date().toISOString();
            console.error('Test suite failed:', error.message);
        } finally {
            this.isRunning = false;
            this.testSessions.push(session);
        }

        return session;
    }

    // Run multi-agent tests
    async runMultiAgentTests() {
        await this.initializeTestEnvironment();

        // Generate test scenarios
        const scenarios = [
            this.scenarioGenerator.generateFromTemplate('hr_basic'),
            this.scenarioGenerator.generateFromTemplate('multi_agent_coordination'),
            this.scenarioGenerator.generateFromTemplate('conflict_resolution'),
            this.scenarioGenerator.generateFromTemplate('reasoning_verification')
        ];

        const results = [];

        // Run each scenario
        for (const scenario of scenarios) {
            const scenarioId = this.simulator.createScenario(scenario.name, {
                agents: scenario.agents,
                tasks: scenario.tasks,
                expectedOutcomes: scenario.expectedOutcomes
            });

            const result = await this.simulator.runScenario(scenarioId);
            results.push(result);
        }

        return {
            totalScenarios: scenarios.length,
            results,
            summary: this.simulator.getSummary()
        };
    }

    // Run verification tests
    async runVerificationTests(testResults) {
        const verificationResults = {};

        // Verify multi-agent results
        if (testResults.multiAgent) {
            verificationResults.multiAgent = await this.verifier.verifyResults(
                testResults.multiAgent.results, 
                'coordination'
            );
        }

        // Verify interaction results
        if (testResults.interactions) {
            const interactionData = [
                ...testResults.interactions.communication.results,
                ...testResults.interactions.coordination.results
            ];
            verificationResults.interactions = await this.verifier.verifyResults(
                interactionData, 
                'communication'
            );
        }

        // Verify security results
        if (testResults.security) {
            verificationResults.security = await this.verifier.verifyResults(
                [testResults.security], 
                'security'
            );
        }

        return verificationResults;
    }

    // Run quick test suite
    async runQuickTests() {
        const session = {
            id: Date.now(),
            type: 'quick',
            startTime: new Date().toISOString(),
            status: 'running',
            results: {}
        };

        try {
            // Quick multi-agent test
            await this.initializeTestEnvironment();
            const quickScenario = this.scenarioGenerator.generateFromTemplate('hr_basic');
            const scenarioId = this.simulator.createScenario(quickScenario.name, quickScenario);
            session.results.multiAgent = await this.simulator.runScenario(scenarioId);

            // Quick interaction test
            session.results.communication = await this.interactionTester.runCommunicationTests();

            session.status = 'completed';
            session.endTime = new Date().toISOString();

        } catch (error) {
            session.status = 'failed';
            session.error = error.message;
            session.endTime = new Date().toISOString();
        }

        this.testSessions.push(session);
        return session;
    }

    // Run stress tests
    async runStressTests() {
        const session = {
            id: Date.now(),
            type: 'stress',
            startTime: new Date().toISOString(),
            status: 'running',
            results: {}
        };

        try {
            await this.initializeTestEnvironment();

            // Generate stress test scenarios
            const stressScenarios = this.scenarioGenerator.generateStressTest();
            const results = [];

            for (const scenario of stressScenarios) {
                const scenarioId = this.simulator.createScenario(scenario.name, scenario);
                const result = await this.simulator.runScenario(scenarioId);
                results.push(result);
            }

            session.results.stress = {
                scenarios: stressScenarios.length,
                results,
                performance: this.analyzeStressTestPerformance(results)
            };

            session.status = 'completed';
            session.endTime = new Date().toISOString();

        } catch (error) {
            session.status = 'failed';
            session.error = error.message;
            session.endTime = new Date().toISOString();
        }

        this.testSessions.push(session);
        return session;
    }

    // Analyze stress test performance
    analyzeStressTestPerformance(results) {
        const performance = {
            avgDuration: 0,
            maxDuration: 0,
            minDuration: Infinity,
            successRate: 0,
            throughput: 0
        };

        if (results.length === 0) return performance;

        const durations = results.map(r => r.duration);
        const successful = results.filter(r => r.status === 'success');

        performance.avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
        performance.maxDuration = Math.max(...durations);
        performance.minDuration = Math.min(...durations);
        performance.successRate = (successful.length / results.length) * 100;
        performance.throughput = results.length / (performance.avgDuration / 1000); // requests per second

        return performance;
    }

    // Generate test report
    generateTestReport(sessionId) {
        const session = this.testSessions.find(s => s.id === sessionId);
        if (!session) {
            throw new Error('Test session not found');
        }

        const report = {
            sessionId: session.id,
            type: session.type || 'comprehensive',
            timestamp: session.startTime,
            duration: session.duration,
            status: session.status,
            summary: this.generateTestSummary(session.results),
            details: session.results,
            recommendations: this.generateRecommendations(session.results)
        };

        return report;
    }

    // Generate test summary
    generateTestSummary(results) {
        const summary = {
            totalTests: 0,
            passed: 0,
            failed: 0,
            securityScore: 0,
            overallScore: 0
        };

        // Multi-agent tests
        if (results.multiAgent) {
            summary.totalTests += results.multiAgent.totalScenarios || 0;
            summary.passed += results.multiAgent.results?.filter(r => r.status === 'success').length || 0;
        }

        // Interaction tests
        if (results.interactions) {
            summary.totalTests += results.interactions.summary?.totalTests || 0;
            summary.passed += results.interactions.summary?.totalPassed || 0;
        }

        // Security tests
        if (results.security) {
            summary.totalTests += results.security.summary?.totalTests || 0;
            summary.passed += results.security.summary?.totalPassed || 0;
            summary.securityScore = results.security.summary?.securityScore || 0;
        }

        summary.failed = summary.totalTests - summary.passed;
        summary.overallScore = summary.totalTests > 0 ? (summary.passed / summary.totalTests) * 100 : 0;

        return summary;
    }

    // Generate recommendations
    generateRecommendations(results) {
        const recommendations = [];

        // Multi-agent recommendations
        if (results.multiAgent && results.multiAgent.summary) {
            const summary = results.multiAgent.summary;
            if (summary.avgPerformance?.interactions < 5) {
                recommendations.push({
                    category: 'performance',
                    priority: 'medium',
                    recommendation: 'Increase agent interaction frequency for better coordination'
                });
            }
        }

        // Interaction recommendations
        if (results.interactions) {
            const commSuccess = results.interactions.communication?.passed || 0;
            const commTotal = results.interactions.communication?.totalTests || 1;
            if ((commSuccess / commTotal) < 0.8) {
                recommendations.push({
                    category: 'communication',
                    priority: 'high',
                    recommendation: 'Improve agent communication protocols and error handling'
                });
            }
        }

        // Security recommendations
        if (results.security && results.security.summary?.securityScore < 80) {
            recommendations.push({
                category: 'security',
                priority: 'high',
                recommendation: 'Address security vulnerabilities and strengthen access controls'
            });
        }

        return recommendations;
    }

    // Get test session history
    getTestHistory() {
        return this.testSessions.map(session => ({
            id: session.id,
            type: session.type || 'comprehensive',
            startTime: session.startTime,
            endTime: session.endTime,
            status: session.status,
            duration: session.duration
        }));
    }

    // Get latest test results
    getLatestResults() {
        if (this.testSessions.length === 0) return null;
        return this.testSessions[this.testSessions.length - 1];
    }

    // Clear test history
    clearHistory() {
        this.testSessions = [];
        this.simulator.reset();
        this.scenarioGenerator.clear();
        this.verifier.clearResults();
        this.interactionTester.clearResults();
        this.securityTester.clearResults();
    }

    // Check if tests are running
    isTestRunning() {
        return this.isRunning;
    }
}

export default TestRunner;