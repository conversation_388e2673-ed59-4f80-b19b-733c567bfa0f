class ScenarioGenerator {
    constructor() {
        this.templates = new Map();
        this.generatedScenarios = [];
        this.initializeTemplates();
    }

    // Initialize scenario templates
    initializeTemplates() {
        // HR Query Scenarios
        this.templates.set('hr_basic', {
            name: 'Basic HR Query',
            description: 'Single agent handles basic HR question',
            agents: ['hr_agent'],
            tasks: [
                { description: 'What is the leave policy?', type: 'query', complexity: 'low' }
            ],
            expectedOutcomes: [
                { task: 'What is the leave policy?', criteria: { minConfidence: 0.8, maxConflicts: 0 } }
            ]
        });

        this.templates.set('multi_agent_coordination', {
            name: 'Multi-Agent Coordination',
            description: 'Multiple agents coordinate to handle complex query',
            agents: ['hr_agent', 'general_agent', 'policy_agent'],
            tasks: [
                { description: 'Complex policy question requiring multiple expertise', type: 'coordination', complexity: 'high' }
            ],
            expectedOutcomes: [
                { task: 'Complex policy question requiring multiple expertise', criteria: { minParticipants: 2, minConfidence: 0.7, maxConflicts: 1 } }
            ]
        });

        this.templates.set('conflict_resolution', {
            name: 'Agent Conflict Resolution',
            description: 'Test how agents resolve conflicting information',
            agents: ['hr_agent', 'policy_agent'],
            tasks: [
                { description: 'Ambiguous policy interpretation', type: 'conflict', complexity: 'medium' }
            ],
            expectedOutcomes: [
                { task: 'Ambiguous policy interpretation', criteria: { minParticipants: 2, minConfidence: 0.6 } }
            ]
        });

        this.templates.set('reasoning_verification', {
            name: 'Reasoning Path Verification',
            description: 'Verify logical reasoning steps',
            agents: ['policy_agent'],
            tasks: [
                { description: 'Multi-step policy analysis', type: 'reasoning', complexity: 'high' }
            ],
            expectedOutcomes: [
                { task: 'Multi-step policy analysis', criteria: { minConfidence: 0.85, maxConflicts: 0 } }
            ]
        });

        this.templates.set('security_boundary', {
            name: 'Security Boundary Testing',
            description: 'Test information isolation and permissions',
            agents: ['hr_agent', 'general_agent'],
            tasks: [
                { description: 'Attempt to access restricted information', type: 'security', complexity: 'medium' }
            ],
            expectedOutcomes: [
                { task: 'Attempt to access restricted information', criteria: { minConfidence: 0.9, maxConflicts: 0 } }
            ]
        });
    }

    // Generate scenario from template
    generateFromTemplate(templateId, customizations = {}) {
        const template = this.templates.get(templateId);
        if (!template) throw new Error(`Template ${templateId} not found`);

        const scenario = {
            id: Date.now() + Math.random(),
            name: customizations.name || template.name,
            description: customizations.description || template.description,
            agents: customizations.agents || template.agents,
            tasks: customizations.tasks || template.tasks,
            expectedOutcomes: customizations.expectedOutcomes || template.expectedOutcomes,
            timeout: customizations.timeout || 30000,
            metadata: {
                templateId,
                generated: new Date().toISOString(),
                complexity: this.calculateComplexity(template.tasks)
            }
        };

        this.generatedScenarios.push(scenario);
        return scenario;
    }

    // Generate random scenario
    generateRandom() {
        const templates = Array.from(this.templates.keys());
        const randomTemplate = templates[Math.floor(Math.random() * templates.length)];
        
        const variations = this.createVariations(randomTemplate);
        return this.generateFromTemplate(randomTemplate, variations);
    }

    // Create variations for scenario
    createVariations(templateId) {
        const variations = {};
        
        switch (templateId) {
            case 'hr_basic':
                variations.tasks = [
                    { description: this.getRandomHRQuery(), type: 'query', complexity: 'low' }
                ];
                break;
            case 'multi_agent_coordination':
                variations.tasks = [
                    { description: this.getRandomComplexQuery(), type: 'coordination', complexity: 'high' }
                ];
                break;
            case 'conflict_resolution':
                variations.tasks = [
                    { description: this.getRandomConflictQuery(), type: 'conflict', complexity: 'medium' }
                ];
                break;
        }

        return variations;
    }

    // Get random HR query
    getRandomHRQuery() {
        const queries = [
            'What is the sick leave policy?',
            'How do I apply for vacation?',
            'What are the working hours?',
            'How do I request time off?',
            'What benefits are available?'
        ];
        return queries[Math.floor(Math.random() * queries.length)];
    }

    // Get random complex query
    getRandomComplexQuery() {
        const queries = [
            'Complex benefits calculation with multiple variables',
            'Policy interpretation requiring legal and HR expertise',
            'Multi-department coordination for employee transfer',
            'Compliance check across multiple regulations'
        ];
        return queries[Math.floor(Math.random() * queries.length)];
    }

    // Get random conflict query
    getRandomConflictQuery() {
        const queries = [
            'Conflicting policy interpretations',
            'Ambiguous regulation requirements',
            'Overlapping jurisdiction policies',
            'Contradictory guidance from different sources'
        ];
        return queries[Math.floor(Math.random() * queries.length)];
    }

    // Calculate scenario complexity
    calculateComplexity(tasks) {
        const complexityMap = { low: 1, medium: 2, high: 3 };
        const totalComplexity = tasks.reduce((sum, task) => sum + (complexityMap[task.complexity] || 1), 0);
        const avgComplexity = totalComplexity / tasks.length;
        
        if (avgComplexity <= 1.5) return 'low';
        if (avgComplexity <= 2.5) return 'medium';
        return 'high';
    }

    // Generate test suite
    generateTestSuite(count = 10) {
        const suite = [];
        const templates = Array.from(this.templates.keys());

        for (let i = 0; i < count; i++) {
            const templateId = templates[i % templates.length];
            const scenario = this.generateFromTemplate(templateId);
            suite.push(scenario);
        }

        return suite;
    }

    // Generate stress test scenarios
    generateStressTest() {
        return [
            this.generateFromTemplate('multi_agent_coordination', {
                name: 'High Load Coordination Test',
                agents: ['hr_agent', 'general_agent', 'policy_agent'],
                tasks: Array(10).fill().map((_, i) => ({
                    description: `Concurrent query ${i + 1}`,
                    type: 'coordination',
                    complexity: 'high'
                }))
            }),
            this.generateFromTemplate('conflict_resolution', {
                name: 'Multiple Conflict Resolution',
                tasks: Array(5).fill().map((_, i) => ({
                    description: `Conflict scenario ${i + 1}`,
                    type: 'conflict',
                    complexity: 'medium'
                }))
            })
        ];
    }

    // Get all available templates
    getTemplates() {
        return Array.from(this.templates.entries()).map(([id, template]) => ({
            id,
            name: template.name,
            description: template.description,
            complexity: this.calculateComplexity(template.tasks)
        }));
    }

    // Get generated scenarios
    getGeneratedScenarios() {
        return this.generatedScenarios;
    }

    // Clear generated scenarios
    clear() {
        this.generatedScenarios = [];
    }
}

export default ScenarioGenerator;