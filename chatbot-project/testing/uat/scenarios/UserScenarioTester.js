class UserScenarioTester {
    constructor() {
        this.userRoles = new Map();
        this.testScenarios = new Map();
        this.testResults = [];
        this.initializeUserRoles();
        this.initializeScenarios();
    }

    // Initialize user roles
    initializeUserRoles() {
        this.userRoles.set('employee', {
            name: 'Employee',
            permissions: ['basic_queries', 'leave_requests', 'benefits_info'],
            scenarios: ['basic_hr_query', 'leave_application', 'benefits_inquiry']
        });

        this.userRoles.set('manager', {
            name: 'Manager',
            permissions: ['team_queries', 'approval_requests', 'policy_guidance'],
            scenarios: ['team_management', 'policy_clarification', 'approval_workflow']
        });

        this.userRoles.set('hr_admin', {
            name: 'HR Administrator',
            permissions: ['all_queries', 'policy_updates', 'system_management'],
            scenarios: ['complex_policy', 'system_configuration', 'bulk_operations']
        });

        this.userRoles.set('executive', {
            name: 'Executive',
            permissions: ['strategic_queries', 'compliance_reports', 'analytics_access'],
            scenarios: ['strategic_planning', 'compliance_review', 'analytics_dashboard']
        });
    }

    // Initialize test scenarios
    initializeScenarios() {
        // Employee scenarios
        this.testScenarios.set('basic_hr_query', {
            name: 'Basic HR Query',
            role: 'employee',
            description: 'Employee asks about company policies',
            tasks: [
                { action: 'ask_leave_policy', expected: 'clear_policy_explanation' },
                { action: 'request_benefits_info', expected: 'benefits_summary' }
            ],
            successCriteria: { clarity: 4, speed: 3, satisfaction: 4 }
        });

        this.testScenarios.set('leave_application', {
            name: 'Leave Application Process',
            role: 'employee',
            description: 'Employee applies for leave through chatbot',
            tasks: [
                { action: 'initiate_leave_request', expected: 'form_guidance' },
                { action: 'submit_application', expected: 'confirmation_receipt' }
            ],
            successCriteria: { clarity: 4, speed: 4, satisfaction: 4 }
        });

        // Manager scenarios
        this.testScenarios.set('team_management', {
            name: 'Team Management Query',
            role: 'manager',
            description: 'Manager seeks guidance on team policies',
            tasks: [
                { action: 'team_policy_query', expected: 'management_guidance' },
                { action: 'approval_process', expected: 'workflow_explanation' }
            ],
            successCriteria: { clarity: 4, speed: 3, satisfaction: 4 }
        });

        // HR Admin scenarios
        this.testScenarios.set('complex_policy', {
            name: 'Complex Policy Interpretation',
            role: 'hr_admin',
            description: 'HR admin needs detailed policy analysis',
            tasks: [
                { action: 'policy_analysis', expected: 'detailed_breakdown' },
                { action: 'compliance_check', expected: 'compliance_report' }
            ],
            successCriteria: { clarity: 5, speed: 3, satisfaction: 5 }
        });

        // Executive scenarios
        this.testScenarios.set('strategic_planning', {
            name: 'Strategic Planning Support',
            role: 'executive',
            description: 'Executive needs strategic HR insights',
            tasks: [
                { action: 'strategic_query', expected: 'executive_summary' },
                { action: 'analytics_request', expected: 'data_visualization' }
            ],
            successCriteria: { clarity: 5, speed: 4, satisfaction: 5 }
        });
    }

    // Run user acceptance test for specific role
    async runUserAcceptanceTest(roleId) {
        const role = this.userRoles.get(roleId);
        if (!role) throw new Error(`Role ${roleId} not found`);

        const testSession = {
            roleId,
            roleName: role.name,
            startTime: new Date().toISOString(),
            scenarios: [],
            overallScore: 0,
            feedback: {}
        };

        for (const scenarioId of role.scenarios) {
            const scenario = this.testScenarios.get(scenarioId);
            if (scenario) {
                const result = await this.executeScenario(scenario, role);
                testSession.scenarios.push(result);
            }
        }

        testSession.overallScore = this.calculateOverallScore(testSession.scenarios);
        testSession.endTime = new Date().toISOString();
        
        this.testResults.push(testSession);
        return testSession;
    }

    // Execute individual scenario
    async executeScenario(scenario, role) {
        const result = {
            scenarioId: scenario.name,
            role: role.name,
            startTime: Date.now(),
            tasks: [],
            scores: {},
            passed: false
        };

        // Execute each task
        for (const task of scenario.tasks) {
            const taskResult = await this.executeTask(task, role);
            result.tasks.push(taskResult);
        }

        // Simulate user scoring
        result.scores = this.simulateUserScoring(scenario);
        result.passed = this.evaluateScenario(result.scores, scenario.successCriteria);
        result.duration = Date.now() - result.startTime;

        return result;
    }

    // Execute individual task
    async executeTask(task, role) {
        // Simulate task execution delay
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

        const taskResult = {
            action: task.action,
            expected: task.expected,
            completed: Math.random() > 0.1, // 90% success rate
            responseTime: Math.random() * 2000 + 500, // 0.5-2.5s
            userSatisfaction: Math.random() * 2 + 3 // 3-5 scale
        };

        return taskResult;
    }

    // Simulate user scoring
    simulateUserScoring(scenario) {
        return {
            clarity: Math.random() * 2 + 3, // 3-5 scale
            speed: Math.random() * 2 + 3,
            satisfaction: Math.random() * 2 + 3,
            usefulness: Math.random() * 2 + 3,
            trustworthiness: Math.random() * 2 + 3
        };
    }

    // Evaluate scenario against success criteria
    evaluateScenario(scores, criteria) {
        return scores.clarity >= criteria.clarity &&
               scores.speed >= criteria.speed &&
               scores.satisfaction >= criteria.satisfaction;
    }

    // Calculate overall score
    calculateOverallScore(scenarios) {
        if (scenarios.length === 0) return 0;

        const totalScore = scenarios.reduce((sum, scenario) => {
            const avgScore = Object.values(scenario.scores).reduce((a, b) => a + b, 0) / Object.keys(scenario.scores).length;
            return sum + avgScore;
        }, 0);

        return totalScore / scenarios.length;
    }

    // Run comprehensive UAT for all roles
    async runComprehensiveUAT() {
        const results = {
            timestamp: new Date().toISOString(),
            roles: [],
            summary: {}
        };

        for (const [roleId] of this.userRoles) {
            const roleResult = await this.runUserAcceptanceTest(roleId);
            results.roles.push(roleResult);
        }

        results.summary = this.generateUATSummary(results.roles);
        return results;
    }

    // Generate UAT summary
    generateUATSummary(roleResults) {
        const summary = {
            totalRoles: roleResults.length,
            totalScenarios: roleResults.reduce((sum, role) => sum + role.scenarios.length, 0),
            passedScenarios: roleResults.reduce((sum, role) => sum + role.scenarios.filter(s => s.passed).length, 0),
            averageScore: 0,
            roleBreakdown: {}
        };

        summary.failedScenarios = summary.totalScenarios - summary.passedScenarios;
        summary.successRate = summary.totalScenarios > 0 ? (summary.passedScenarios / summary.totalScenarios) * 100 : 0;

        // Calculate average score
        const totalScore = roleResults.reduce((sum, role) => sum + role.overallScore, 0);
        summary.averageScore = roleResults.length > 0 ? totalScore / roleResults.length : 0;

        // Role breakdown
        roleResults.forEach(role => {
            summary.roleBreakdown[role.roleId] = {
                name: role.roleName,
                score: role.overallScore,
                scenarios: role.scenarios.length,
                passed: role.scenarios.filter(s => s.passed).length
            };
        });

        return summary;
    }

    // Get test results
    getTestResults() {
        return this.testResults;
    }

    // Generate UAT report
    generateUATReport() {
        if (this.testResults.length === 0) {
            return { message: 'No UAT results available' };
        }

        const latest = this.testResults[this.testResults.length - 1];
        
        return {
            timestamp: latest.timestamp || new Date().toISOString(),
            summary: this.generateUATSummary(this.testResults),
            roleAnalysis: this.analyzeRolePerformance(),
            recommendations: this.generateUATRecommendations()
        };
    }

    // Analyze role performance
    analyzeRolePerformance() {
        const roleAnalysis = {};

        this.testResults.forEach(result => {
            if (!roleAnalysis[result.roleId]) {
                roleAnalysis[result.roleId] = {
                    name: result.roleName,
                    sessions: 0,
                    avgScore: 0,
                    commonIssues: []
                };
            }

            roleAnalysis[result.roleId].sessions++;
            roleAnalysis[result.roleId].avgScore += result.overallScore;
        });

        // Calculate averages
        Object.keys(roleAnalysis).forEach(roleId => {
            const role = roleAnalysis[roleId];
            role.avgScore = role.avgScore / role.sessions;
        });

        return roleAnalysis;
    }

    // Generate UAT recommendations
    generateUATRecommendations() {
        const recommendations = [];

        const summary = this.generateUATSummary(this.testResults);
        
        if (summary.averageScore < 4.0) {
            recommendations.push({
                priority: 'high',
                category: 'overall',
                recommendation: 'Overall user satisfaction below acceptable threshold (4.0)'
            });
        }

        if (summary.successRate < 80) {
            recommendations.push({
                priority: 'high',
                category: 'scenarios',
                recommendation: 'Scenario success rate below 80% - review failed scenarios'
            });
        }

        // Role-specific recommendations
        Object.entries(summary.roleBreakdown || {}).forEach(([roleId, role]) => {
            if (role.score < 3.5) {
                recommendations.push({
                    priority: 'medium',
                    category: 'role',
                    recommendation: `${role.name} role needs UX improvements (score: ${role.score.toFixed(1)})`
                });
            }
        });

        return recommendations;
    }

    // Clear test results
    clearResults() {
        this.testResults = [];
    }
}

export default UserScenarioTester;