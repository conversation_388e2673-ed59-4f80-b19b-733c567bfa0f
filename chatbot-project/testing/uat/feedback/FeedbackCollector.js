class FeedbackCollector {
    constructor() {
        this.feedbackSessions = [];
        this.feedbackMetrics = new Map();
        this.baselineData = null;
        this.initializeFeedbackMetrics();
    }

    // Initialize feedback metrics
    initializeFeedbackMetrics() {
        this.feedbackMetrics.set('usability', {
            name: 'Usability',
            questions: [
                { id: 'ease_of_use', text: 'How easy was it to use the chatbot?', scale: 5 },
                { id: 'navigation', text: 'How intuitive was the navigation?', scale: 5 },
                { id: 'task_completion', text: 'How easily could you complete your tasks?', scale: 5 }
            ]
        });

        this.feedbackMetrics.set('satisfaction', {
            name: 'User Satisfaction',
            questions: [
                { id: 'overall_satisfaction', text: 'Overall, how satisfied are you?', scale: 5 },
                { id: 'recommendation', text: 'Would you recommend this to colleagues?', scale: 5 },
                { id: 'continued_use', text: 'Would you continue using this system?', scale: 5 }
            ]
        });

        this.feedbackMetrics.set('effectiveness', {
            name: 'Effectiveness',
            questions: [
                { id: 'response_quality', text: 'How helpful were the responses?', scale: 5 },
                { id: 'accuracy', text: 'How accurate was the information?', scale: 5 },
                { id: 'completeness', text: 'How complete were the answers?', scale: 5 }
            ]
        });

        this.feedbackMetrics.set('trust', {
            name: 'Trust & Confidence',
            questions: [
                { id: 'trust_responses', text: 'How much do you trust the responses?', scale: 5 },
                { id: 'confidence_decisions', text: 'How confident are you in making decisions based on responses?', scale: 5 },
                { id: 'transparency', text: 'How transparent were the reasoning explanations?', scale: 5 }
            ]
        });
    }

    // Collect feedback session
    async collectFeedback(userId, userRole, sessionData) {
        const feedbackSession = {
            id: Date.now(),
            userId,
            userRole,
            timestamp: new Date().toISOString(),
            sessionData,
            responses: {},
            qualitativeComments: [],
            overallScore: 0
        };

        // Collect quantitative feedback
        for (const [metricId, metric] of this.feedbackMetrics) {
            feedbackSession.responses[metricId] = await this.collectMetricFeedback(metric);
        }

        // Collect qualitative feedback
        feedbackSession.qualitativeComments = await this.collectQualitativeFeedback();

        // Calculate overall score
        feedbackSession.overallScore = this.calculateOverallScore(feedbackSession.responses);

        this.feedbackSessions.push(feedbackSession);
        return feedbackSession;
    }

    // Collect metric-specific feedback
    async collectMetricFeedback(metric) {
        const responses = {};

        for (const question of metric.questions) {
            // Simulate user response (in real implementation, this would be user input)
            responses[question.id] = {
                question: question.text,
                score: Math.random() * (question.scale - 1) + 1, // 1 to scale
                scale: question.scale
            };
        }

        return responses;
    }

    // Collect qualitative feedback
    async collectQualitativeFeedback() {
        // Simulate qualitative comments (in real implementation, these would be user inputs)
        const sampleComments = [
            { category: 'positive', text: 'The chatbot was very helpful and easy to use' },
            { category: 'improvement', text: 'Could be faster in responding to complex queries' },
            { category: 'feature_request', text: 'Would like to see more visual explanations' },
            { category: 'issue', text: 'Sometimes the reasoning was hard to follow' }
        ];

        // Return random subset of comments
        const numComments = Math.floor(Math.random() * 3) + 1;
        return sampleComments.slice(0, numComments);
    }

    // Calculate overall score
    calculateOverallScore(responses) {
        let totalScore = 0;
        let totalQuestions = 0;

        Object.values(responses).forEach(metricResponses => {
            Object.values(metricResponses).forEach(response => {
                if (typeof response.score === 'number') {
                    totalScore += response.score;
                    totalQuestions++;
                }
            });
        });

        return totalQuestions > 0 ? totalScore / totalQuestions : 0;
    }

    // Set baseline data for comparison
    setBaseline(baselineData) {
        this.baselineData = {
            timestamp: new Date().toISOString(),
            averageScore: baselineData.averageScore || 3.0,
            usabilityScore: baselineData.usabilityScore || 3.0,
            satisfactionScore: baselineData.satisfactionScore || 3.0,
            effectivenessScore: baselineData.effectivenessScore || 3.0,
            trustScore: baselineData.trustScore || 3.0,
            taskCompletionRate: baselineData.taskCompletionRate || 70,
            averageTaskTime: baselineData.averageTaskTime || 120 // seconds
        };
    }

    // Compare with baseline
    compareWithBaseline() {
        if (!this.baselineData || this.feedbackSessions.length === 0) {
            return { error: 'Insufficient data for comparison' };
        }

        const currentMetrics = this.calculateCurrentMetrics();
        
        return {
            timestamp: new Date().toISOString(),
            baseline: this.baselineData,
            current: currentMetrics,
            improvements: {
                overallScore: currentMetrics.averageScore - this.baselineData.averageScore,
                usability: currentMetrics.usabilityScore - this.baselineData.usabilityScore,
                satisfaction: currentMetrics.satisfactionScore - this.baselineData.satisfactionScore,
                effectiveness: currentMetrics.effectivenessScore - this.baselineData.effectivenessScore,
                trust: currentMetrics.trustScore - this.baselineData.trustScore
            },
            percentageImprovement: this.calculatePercentageImprovement(currentMetrics)
        };
    }

    // Calculate current metrics
    calculateCurrentMetrics() {
        if (this.feedbackSessions.length === 0) return {};

        const metrics = {
            averageScore: 0,
            usabilityScore: 0,
            satisfactionScore: 0,
            effectivenessScore: 0,
            trustScore: 0,
            totalSessions: this.feedbackSessions.length
        };

        // Calculate averages
        this.feedbackSessions.forEach(session => {
            metrics.averageScore += session.overallScore;
            metrics.usabilityScore += this.getMetricScore(session.responses.usability);
            metrics.satisfactionScore += this.getMetricScore(session.responses.satisfaction);
            metrics.effectivenessScore += this.getMetricScore(session.responses.effectiveness);
            metrics.trustScore += this.getMetricScore(session.responses.trust);
        });

        // Average out
        Object.keys(metrics).forEach(key => {
            if (key !== 'totalSessions') {
                metrics[key] = metrics[key] / metrics.totalSessions;
            }
        });

        return metrics;
    }

    // Get metric score from responses
    getMetricScore(metricResponses) {
        if (!metricResponses) return 0;

        const scores = Object.values(metricResponses).map(r => r.score || 0);
        return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
    }

    // Calculate percentage improvement
    calculatePercentageImprovement(currentMetrics) {
        const improvements = {};

        Object.keys(currentMetrics).forEach(key => {
            if (key !== 'totalSessions' && this.baselineData[key]) {
                const improvement = ((currentMetrics[key] - this.baselineData[key]) / this.baselineData[key]) * 100;
                improvements[key] = improvement;
            }
        });

        return improvements;
    }

    // Analyze feedback patterns
    analyzeFeedbackPatterns() {
        if (this.feedbackSessions.length === 0) return {};

        const patterns = {
            roleAnalysis: {},
            commonIssues: [],
            positiveAspects: [],
            improvementAreas: []
        };

        // Role-based analysis
        const roleGroups = {};
        this.feedbackSessions.forEach(session => {
            if (!roleGroups[session.userRole]) {
                roleGroups[session.userRole] = [];
            }
            roleGroups[session.userRole].push(session);
        });

        Object.entries(roleGroups).forEach(([role, sessions]) => {
            const avgScore = sessions.reduce((sum, s) => sum + s.overallScore, 0) / sessions.length;
            patterns.roleAnalysis[role] = {
                sessionCount: sessions.length,
                averageScore: avgScore,
                satisfaction: avgScore >= 4.0 ? 'high' : avgScore >= 3.0 ? 'medium' : 'low'
            };
        });

        // Analyze qualitative comments
        const allComments = this.feedbackSessions.flatMap(s => s.qualitativeComments);
        
        patterns.commonIssues = allComments
            .filter(c => c.category === 'issue' || c.category === 'improvement')
            .map(c => c.text);

        patterns.positiveAspects = allComments
            .filter(c => c.category === 'positive')
            .map(c => c.text);

        patterns.improvementAreas = this.identifyImprovementAreas();

        return patterns;
    }

    // Identify improvement areas
    identifyImprovementAreas() {
        const currentMetrics = this.calculateCurrentMetrics();
        const areas = [];

        if (currentMetrics.usabilityScore < 3.5) {
            areas.push({
                area: 'Usability',
                score: currentMetrics.usabilityScore,
                priority: 'high',
                recommendation: 'Improve interface design and user flow'
            });
        }

        if (currentMetrics.effectivenessScore < 3.5) {
            areas.push({
                area: 'Effectiveness',
                score: currentMetrics.effectivenessScore,
                priority: 'high',
                recommendation: 'Enhance response quality and accuracy'
            });
        }

        if (currentMetrics.trustScore < 3.5) {
            areas.push({
                area: 'Trust',
                score: currentMetrics.trustScore,
                priority: 'medium',
                recommendation: 'Improve transparency and reasoning explanations'
            });
        }

        return areas;
    }

    // Generate feedback report
    generateFeedbackReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalSessions: this.feedbackSessions.length,
                averageScore: 0,
                responseRate: 100 // Assuming all users provide feedback
            },
            metrics: this.calculateCurrentMetrics(),
            patterns: this.analyzeFeedbackPatterns(),
            baseline: this.baselineData ? this.compareWithBaseline() : null,
            recommendations: this.generateRecommendations()
        };

        if (this.feedbackSessions.length > 0) {
            report.summary.averageScore = report.metrics.averageScore;
        }

        return report;
    }

    // Generate recommendations
    generateRecommendations() {
        const recommendations = [];
        const currentMetrics = this.calculateCurrentMetrics();

        if (currentMetrics.averageScore < 3.5) {
            recommendations.push({
                priority: 'critical',
                category: 'overall',
                recommendation: 'Overall user satisfaction is below acceptable levels - immediate action required'
            });
        }

        if (currentMetrics.usabilityScore < 3.5) {
            recommendations.push({
                priority: 'high',
                category: 'usability',
                recommendation: 'Focus on improving user interface and interaction design'
            });
        }

        if (currentMetrics.trustScore < 3.5) {
            recommendations.push({
                priority: 'medium',
                category: 'trust',
                recommendation: 'Enhance reasoning transparency and explanation quality'
            });
        }

        return recommendations;
    }

    // Get feedback sessions
    getFeedbackSessions() {
        return this.feedbackSessions;
    }

    // Clear feedback data
    clearFeedback() {
        this.feedbackSessions = [];
    }
}

export default FeedbackCollector;