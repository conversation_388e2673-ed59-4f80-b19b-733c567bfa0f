class UXAnalyzer {
    constructor() {
        this.interactionLogs = [];
        this.usabilityIssues = [];
        this.uxMetrics = new Map();
        this.improvements = [];
        this.initializeUXMetrics();
    }

    // Initialize UX metrics
    initializeUXMetrics() {
        this.uxMetrics.set('task_completion', {
            name: 'Task Completion Rate',
            threshold: 80, // 80% minimum
            current: 0
        });

        this.uxMetrics.set('time_on_task', {
            name: 'Average Time on Task',
            threshold: 120, // 2 minutes maximum
            current: 0
        });

        this.uxMetrics.set('error_rate', {
            name: 'User Error Rate',
            threshold: 10, // 10% maximum
            current: 0
        });

        this.uxMetrics.set('satisfaction_score', {
            name: 'User Satisfaction Score',
            threshold: 4.0, // 4.0/5.0 minimum
            current: 0
        });

        this.uxMetrics.set('bounce_rate', {
            name: 'Session Bounce Rate',
            threshold: 20, // 20% maximum
            current: 0
        });
    }

    // Log user interaction
    logInteraction(userId, sessionId, interaction) {
        const log = {
            id: Date.now() + Math.random(),
            userId,
            sessionId,
            timestamp: new Date().toISOString(),
            ...interaction
        };

        this.interactionLogs.push(log);
        return log.id;
    }

    // Analyze interaction patterns
    analyzeInteractionPatterns() {
        if (this.interactionLogs.length === 0) {
            return { message: 'No interaction data available' };
        }

        const patterns = {
            totalInteractions: this.interactionLogs.length,
            uniqueUsers: new Set(this.interactionLogs.map(log => log.userId)).size,
            uniqueSessions: new Set(this.interactionLogs.map(log => log.sessionId)).size,
            commonActions: this.analyzeCommonActions(),
            userJourneys: this.analyzeUserJourneys(),
            dropOffPoints: this.identifyDropOffPoints(),
            timePatterns: this.analyzeTimePatterns()
        };

        return patterns;
    }

    // Analyze common actions
    analyzeCommonActions() {
        const actionCounts = {};
        
        this.interactionLogs.forEach(log => {
            const action = log.action || 'unknown';
            actionCounts[action] = (actionCounts[action] || 0) + 1;
        });

        return Object.entries(actionCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([action, count]) => ({ action, count, percentage: (count / this.interactionLogs.length) * 100 }));
    }

    // Analyze user journeys
    analyzeUserJourneys() {
        const journeys = {};

        // Group interactions by session
        this.interactionLogs.forEach(log => {
            if (!journeys[log.sessionId]) {
                journeys[log.sessionId] = [];
            }
            journeys[log.sessionId].push(log);
        });

        // Analyze journey patterns
        const journeyAnalysis = {
            averageSteps: 0,
            commonPaths: [],
            completionRate: 0
        };

        const sessionSteps = Object.values(journeys).map(journey => journey.length);
        journeyAnalysis.averageSteps = sessionSteps.reduce((a, b) => a + b, 0) / sessionSteps.length;

        // Calculate completion rate (sessions with successful end action)
        const completedSessions = Object.values(journeys).filter(journey => 
            journey.some(log => log.action === 'task_completed' || log.success === true)
        ).length;
        
        journeyAnalysis.completionRate = (completedSessions / Object.keys(journeys).length) * 100;

        return journeyAnalysis;
    }

    // Identify drop-off points
    identifyDropOffPoints() {
        const stepCounts = {};
        const sessions = {};

        // Group by session and count steps
        this.interactionLogs.forEach(log => {
            if (!sessions[log.sessionId]) {
                sessions[log.sessionId] = [];
            }
            sessions[log.sessionId].push(log);
        });

        // Count users at each step
        Object.values(sessions).forEach(session => {
            session.forEach((log, index) => {
                const step = index + 1;
                stepCounts[step] = (stepCounts[step] || 0) + 1;
            });
        });

        // Calculate drop-off rates
        const dropOffPoints = [];
        const steps = Object.keys(stepCounts).map(Number).sort((a, b) => a - b);
        
        for (let i = 0; i < steps.length - 1; i++) {
            const currentStep = steps[i];
            const nextStep = steps[i + 1];
            const dropOff = ((stepCounts[currentStep] - stepCounts[nextStep]) / stepCounts[currentStep]) * 100;
            
            if (dropOff > 20) { // Significant drop-off
                dropOffPoints.push({
                    step: currentStep,
                    dropOffRate: dropOff,
                    usersLost: stepCounts[currentStep] - stepCounts[nextStep]
                });
            }
        }

        return dropOffPoints;
    }

    // Analyze time patterns
    analyzeTimePatterns() {
        const timeData = this.interactionLogs.map(log => ({
            timestamp: new Date(log.timestamp),
            duration: log.duration || 0
        }));

        return {
            averageDuration: timeData.reduce((sum, data) => sum + data.duration, 0) / timeData.length,
            peakHours: this.calculatePeakHours(timeData),
            sessionLengths: this.calculateSessionLengths()
        };
    }

    // Calculate peak usage hours
    calculatePeakHours(timeData) {
        const hourCounts = {};
        
        timeData.forEach(data => {
            const hour = data.timestamp.getHours();
            hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        });

        return Object.entries(hourCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([hour, count]) => ({ hour: parseInt(hour), count }));
    }

    // Calculate session lengths
    calculateSessionLengths() {
        const sessions = {};
        
        this.interactionLogs.forEach(log => {
            if (!sessions[log.sessionId]) {
                sessions[log.sessionId] = {
                    start: new Date(log.timestamp),
                    end: new Date(log.timestamp)
                };
            } else {
                const logTime = new Date(log.timestamp);
                if (logTime > sessions[log.sessionId].end) {
                    sessions[log.sessionId].end = logTime;
                }
            }
        });

        const lengths = Object.values(sessions).map(session => 
            (session.end - session.start) / 1000 // Convert to seconds
        );

        return {
            average: lengths.reduce((a, b) => a + b, 0) / lengths.length,
            median: this.calculateMedian(lengths),
            max: Math.max(...lengths),
            min: Math.min(...lengths)
        };
    }

    // Calculate median
    calculateMedian(values) {
        const sorted = values.sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
    }

    // Identify usability issues
    identifyUsabilityIssues() {
        const issues = [];
        const patterns = this.analyzeInteractionPatterns();

        // High error rate
        if (this.uxMetrics.get('error_rate').current > this.uxMetrics.get('error_rate').threshold) {
            issues.push({
                type: 'high_error_rate',
                severity: 'high',
                description: `Error rate (${this.uxMetrics.get('error_rate').current}%) exceeds threshold`,
                recommendation: 'Review error-prone interactions and improve error handling'
            });
        }

        // Low task completion
        if (patterns.userJourneys && patterns.userJourneys.completionRate < 80) {
            issues.push({
                type: 'low_completion_rate',
                severity: 'high',
                description: `Task completion rate (${patterns.userJourneys.completionRate.toFixed(1)}%) is below target`,
                recommendation: 'Analyze user journeys and remove friction points'
            });
        }

        // Significant drop-off points
        if (patterns.dropOffPoints && patterns.dropOffPoints.length > 0) {
            patterns.dropOffPoints.forEach(dropOff => {
                issues.push({
                    type: 'drop_off_point',
                    severity: 'medium',
                    description: `High drop-off rate (${dropOff.dropOffRate.toFixed(1)}%) at step ${dropOff.step}`,
                    recommendation: 'Investigate and optimize the user experience at this step'
                });
            });
        }

        // Long task times
        if (patterns.timePatterns && patterns.timePatterns.averageDuration > 120) {
            issues.push({
                type: 'long_task_time',
                severity: 'medium',
                description: `Average task time (${patterns.timePatterns.averageDuration.toFixed(1)}s) exceeds target`,
                recommendation: 'Streamline user workflows and reduce cognitive load'
            });
        }

        this.usabilityIssues = issues;
        return issues;
    }

    // Generate UX improvements
    generateUXImprovements() {
        const improvements = [];
        const issues = this.identifyUsabilityIssues();
        const patterns = this.analyzeInteractionPatterns();

        // Based on identified issues
        issues.forEach(issue => {
            switch (issue.type) {
                case 'high_error_rate':
                    improvements.push({
                        category: 'error_handling',
                        priority: 'high',
                        improvement: 'Implement better error prevention and recovery mechanisms',
                        expectedImpact: 'Reduce error rate by 50%'
                    });
                    break;
                case 'low_completion_rate':
                    improvements.push({
                        category: 'user_flow',
                        priority: 'high',
                        improvement: 'Redesign user flow to reduce friction and improve guidance',
                        expectedImpact: 'Increase completion rate to 85%+'
                    });
                    break;
                case 'drop_off_point':
                    improvements.push({
                        category: 'user_experience',
                        priority: 'medium',
                        improvement: 'Optimize specific interaction points with high drop-off rates',
                        expectedImpact: 'Reduce drop-off by 30%'
                    });
                    break;
                case 'long_task_time':
                    improvements.push({
                        category: 'efficiency',
                        priority: 'medium',
                        improvement: 'Streamline workflows and reduce steps required',
                        expectedImpact: 'Reduce average task time by 25%'
                    });
                    break;
            }
        });

        // Based on common actions
        if (patterns.commonActions && patterns.commonActions.length > 0) {
            const topAction = patterns.commonActions[0];
            if (topAction.percentage > 30) {
                improvements.push({
                    category: 'optimization',
                    priority: 'medium',
                    improvement: `Optimize the most common action: ${topAction.action}`,
                    expectedImpact: 'Improve experience for majority of users'
                });
            }
        }

        this.improvements = improvements;
        return improvements;
    }

    // Update UX metrics
    updateUXMetrics(metrics) {
        Object.entries(metrics).forEach(([key, value]) => {
            if (this.uxMetrics.has(key)) {
                this.uxMetrics.get(key).current = value;
            }
        });
    }

    // Generate UX analysis report
    generateUXReport() {
        const report = {
            timestamp: new Date().toISOString(),
            interactionSummary: {
                totalInteractions: this.interactionLogs.length,
                analysisComplete: this.interactionLogs.length > 0
            },
            patterns: this.analyzeInteractionPatterns(),
            usabilityIssues: this.identifyUsabilityIssues(),
            improvements: this.generateUXImprovements(),
            metrics: Object.fromEntries(this.uxMetrics),
            recommendations: this.generateUXRecommendations()
        };

        return report;
    }

    // Generate UX recommendations
    generateUXRecommendations() {
        const recommendations = [];

        // High priority recommendations
        if (this.usabilityIssues.some(issue => issue.severity === 'high')) {
            recommendations.push({
                priority: 'critical',
                category: 'usability',
                recommendation: 'Address high-severity usability issues immediately'
            });
        }

        // Based on metrics
        const taskCompletion = this.uxMetrics.get('task_completion');
        if (taskCompletion.current < taskCompletion.threshold) {
            recommendations.push({
                priority: 'high',
                category: 'completion',
                recommendation: 'Focus on improving task completion rates through UX optimization'
            });
        }

        const satisfaction = this.uxMetrics.get('satisfaction_score');
        if (satisfaction.current < satisfaction.threshold) {
            recommendations.push({
                priority: 'medium',
                category: 'satisfaction',
                recommendation: 'Enhance user satisfaction through improved interface design'
            });
        }

        return recommendations;
    }

    // Clear interaction logs
    clearLogs() {
        this.interactionLogs = [];
        this.usabilityIssues = [];
        this.improvements = [];
    }

    // Get interaction logs
    getInteractionLogs() {
        return this.interactionLogs;
    }
}

export default UXAnalyzer;