#!/usr/bin/env python3
"""
CHaBot System Startup Script
Comprehensive system initialization and startup management
"""

import asyncio
import sys
import os
import logging
import signal
import time
from pathlib import Path
from typing import Dict, Any, Optional
import argparse

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import configuration
from integrated_config import Config

# Set up logging
def setup_logging():
    """Set up comprehensive logging."""
    log_dir = Path(Config.LOG_FILE_PATH).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL.upper()),
        format=Config.LOG_FORMAT,
        handlers=[
            logging.FileHandler(Config.LOG_FILE_PATH),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    
    return logging.getLogger(__name__)

logger = setup_logging()

class CHaBotSystemManager:
    """Manages the complete CHaBot system lifecycle."""
    
    def __init__(self):
        """Initialize the system manager."""
        self.config = Config
        self.system = None
        self.is_running = False
        self.startup_time = None
        
        logger.info("CHaBot System Manager initialized")
    
    async def check_dependencies(self) -> Dict[str, bool]:
        """Check system dependencies and requirements."""
        logger.info("🔍 Checking system dependencies...")
        
        dependencies = {
            "python_version": sys.version_info >= (3, 8),
            "data_directory": Path(self.config.DATABASE_PATH).exists(),
            "logs_directory": Path(self.config.LOG_FILE_PATH).parent.exists(),
            "knowledge_base": Path(self.config.KNOWLEDGE_BASE_PATH).exists(),
        }
        
        # Check optional dependencies
        try:
            import torch
            dependencies["pytorch"] = True
        except ImportError:
            dependencies["pytorch"] = False
            logger.warning("PyTorch not available - some features may be limited")
        
        try:
            import transformers
            dependencies["transformers"] = True
        except ImportError:
            dependencies["transformers"] = False
            logger.warning("Transformers not available - some features may be limited")
        
        # Check database connectivity (if not mocked)
        if not self.config.MOCK_DATABASES:
            dependencies.update(await self._check_database_connectivity())
        else:
            logger.info("Using mock databases - skipping connectivity checks")
            dependencies.update({
                "postgresql": True,
                "redis": True,
                "neo4j": True
            })
        
        # Report dependency status
        failed_deps = [dep for dep, status in dependencies.items() if not status]
        if failed_deps:
            logger.warning(f"Failed dependencies: {failed_deps}")
        else:
            logger.info("✅ All dependencies satisfied")
        
        return dependencies
    
    async def _check_database_connectivity(self) -> Dict[str, bool]:
        """Check database connectivity."""
        connectivity = {}
        
        # Check PostgreSQL
        try:
            import asyncpg
            conn = await asyncpg.connect(self.config.DATABASE_URL)
            await conn.close()
            connectivity["postgresql"] = True
            logger.info("✅ PostgreSQL connection successful")
        except Exception as e:
            connectivity["postgresql"] = False
            logger.error(f"❌ PostgreSQL connection failed: {e}")
        
        # Check Redis
        try:
            import redis
            r = redis.from_url(self.config.REDIS_URL)
            r.ping()
            connectivity["redis"] = True
            logger.info("✅ Redis connection successful")
        except Exception as e:
            connectivity["redis"] = False
            logger.error(f"❌ Redis connection failed: {e}")
        
        # Check Neo4j
        try:
            from neo4j import GraphDatabase
            driver = GraphDatabase.driver(
                self.config.NEO4J_URI,
                auth=(self.config.NEO4J_USER, self.config.NEO4J_PASSWORD)
            )
            with driver.session() as session:
                session.run("RETURN 1")
            driver.close()
            connectivity["neo4j"] = True
            logger.info("✅ Neo4j connection successful")
        except Exception as e:
            connectivity["neo4j"] = False
            logger.error(f"❌ Neo4j connection failed: {e}")
        
        return connectivity
    
    async def initialize_system(self) -> bool:
        """Initialize the complete CHaBot system."""
        logger.info("🚀 Initializing CHaBot System...")
        
        try:
            # Check dependencies first
            deps = await self.check_dependencies()
            critical_deps = ["python_version", "data_directory", "logs_directory"]
            
            if not all(deps.get(dep, False) for dep in critical_deps):
                logger.error("❌ Critical dependencies not satisfied")
                return False
            
            # Import and initialize the integrated system
            from integrated_chabot_system import IntegratedCHaBotSystem
            
            self.system = IntegratedCHaBotSystem()
            await self.system.initialize_components()
            
            self.is_running = True
            self.startup_time = time.time()
            
            logger.info("✅ CHaBot System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    async def start_server(self, host: Optional[str] = None, port: Optional[int] = None):
        """Start the CHaBot server."""
        if not self.is_running:
            success = await self.initialize_system()
            if not success:
                logger.error("❌ Cannot start server - initialization failed")
                return False
        
        host = host or self.config.API_HOST
        port = port or self.config.API_PORT
        
        logger.info(f"🌐 Starting CHaBot Server on {host}:{port}")
        
        try:
            await self.system.run_server(host=host, port=port)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Gracefully shutdown the system."""
        logger.info("🛑 Shutting down CHaBot System...")
        
        if self.system:
            await self.system.shutdown()
        
        self.is_running = False
        logger.info("✅ CHaBot System shutdown complete")
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        uptime = time.time() - self.startup_time if self.startup_time else 0
        
        return {
            "name": self.config.APP_NAME,
            "version": self.config.APP_VERSION,
            "environment": self.config.ENVIRONMENT,
            "debug": self.config.DEBUG,
            "running": self.is_running,
            "uptime_seconds": uptime,
            "config": {
                "api_host": self.config.API_HOST,
                "api_port": self.config.API_PORT,
                "database_path": self.config.DATABASE_PATH,
                "log_level": self.config.LOG_LEVEL,
                "features": {
                    "advanced_reasoning": self.config.FEATURE_ADVANCED_REASONING,
                    "multi_agent_coordination": self.config.FEATURE_MULTI_AGENT_COORDINATION,
                    "tool_integration": self.config.FEATURE_TOOL_INTEGRATION,
                    "knowledge_fusion": self.config.FEATURE_KNOWLEDGE_FUSION
                }
            }
        }

# Global system manager
system_manager = CHaBotSystemManager()

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, initiating shutdown...")
    asyncio.create_task(system_manager.shutdown())
    sys.exit(0)

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="CHaBot Integrated System")
    parser.add_argument("--host", default=None, help="Host to bind to")
    parser.add_argument("--port", type=int, default=None, help="Port to bind to")
    parser.add_argument("--check-deps", action="store_true", help="Check dependencies and exit")
    parser.add_argument("--info", action="store_true", help="Show system info and exit")
    parser.add_argument("--config", action="store_true", help="Show configuration and exit")
    
    args = parser.parse_args()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.check_deps:
            # Check dependencies only
            deps = await system_manager.check_dependencies()
            print("\n📋 Dependency Check Results:")
            for dep, status in deps.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {dep}: {'OK' if status else 'FAILED'}")
            
            failed_count = sum(1 for status in deps.values() if not status)
            if failed_count > 0:
                print(f"\n⚠️  {failed_count} dependencies failed")
                sys.exit(1)
            else:
                print("\n🎉 All dependencies satisfied!")
                sys.exit(0)
        
        elif args.info:
            # Show system info
            info = system_manager.get_system_info()
            print("\n📊 CHaBot System Information:")
            print(f"  Name: {info['name']}")
            print(f"  Version: {info['version']}")
            print(f"  Environment: {info['environment']}")
            print(f"  Debug Mode: {info['debug']}")
            print(f"  API Endpoint: http://{info['config']['api_host']}:{info['config']['api_port']}")
            print(f"  Database Path: {info['config']['database_path']}")
            print(f"  Log Level: {info['config']['log_level']}")
            print("\n🎛️  Feature Flags:")
            for feature, enabled in info['config']['features'].items():
                status_icon = "✅" if enabled else "❌"
                print(f"  {status_icon} {feature.replace('_', ' ').title()}")
            sys.exit(0)
        
        elif args.config:
            # Show configuration
            config_dict = system_manager.config.to_dict()
            print("\n⚙️  CHaBot Configuration:")
            for section in ["APP", "API", "DATABASE", "LLM", "AGENT", "MEMORY"]:
                section_items = {k: v for k, v in config_dict.items() if k.startswith(section)}
                if section_items:
                    print(f"\n  {section} Settings:")
                    for key, value in section_items.items():
                        # Hide sensitive values
                        if any(sensitive in key.lower() for sensitive in ["password", "key", "secret"]):
                            value = "***HIDDEN***"
                        print(f"    {key}: {value}")
            sys.exit(0)
        
        else:
            # Start the server
            logger.info("🎯 Starting CHaBot Integrated System")
            logger.info("=" * 80)
            
            # Show startup banner
            print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                            CHaBot System v{system_manager.config.APP_VERSION}                            ║
║                     Comprehensive AI Assistant Platform                      ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  Environment: {system_manager.config.ENVIRONMENT:<20} Debug: {str(system_manager.config.DEBUG):<20}        ║
║  API Endpoint: http://{system_manager.config.API_HOST}:{system_manager.config.API_PORT:<40}        ║
║  Log Level: {system_manager.config.LOG_LEVEL:<25} Workers: {system_manager.config.API_WORKERS:<20}        ║
╚══════════════════════════════════════════════════════════════════════════════╝
            """)
            
            await system_manager.start_server(host=args.host, port=args.port)
    
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"System error: {e}")
        sys.exit(1)
    finally:
        await system_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
