"""
Integrated Configuration Management for CHaBot System
Comprehensive environment-based configuration with validation
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path

logger = logging.getLogger(__name__)

def get_bool_env(key: str, default: bool = False) -> bool:
    """Get boolean environment variable."""
    return os.getenv(key, str(default)).lower() in ('true', '1', 'yes', 'on')

def get_int_env(key: str, default: int = 0) -> int:
    """Get integer environment variable."""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        logger.warning(f"Invalid integer value for {key}, using default: {default}")
        return default

def get_float_env(key: str, default: float = 0.0) -> float:
    """Get float environment variable."""
    try:
        return float(os.getenv(key, str(default)))
    except ValueError:
        logger.warning(f"Invalid float value for {key}, using default: {default}")
        return default

def get_list_env(key: str, default: List[str] = None) -> List[str]:
    """Get list environment variable (JSON format)."""
    if default is None:
        default = []
    try:
        value = os.getenv(key)
        if value:
            return json.loads(value)
        return default
    except json.JSONDecodeError:
        logger.warning(f"Invalid JSON value for {key}, using default: {default}")
        return default

@dataclass
class Config:
    """Comprehensive configuration for CHaBot Integrated System."""
    
    # =============================================================================
    # APPLICATION SETTINGS
    # =============================================================================
    APP_NAME: str = os.getenv("APP_NAME", "CHaBot")
    APP_VERSION: str = os.getenv("APP_VERSION", "1.0.0")
    DEBUG: bool = get_bool_env("DEBUG", False)
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    DEV_MODE: bool = get_bool_env("DEV_MODE", True)
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    
    # =============================================================================
    # API CONFIGURATION
    # =============================================================================
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = get_int_env("API_PORT", 8000)
    API_WORKERS: int = get_int_env("API_WORKERS", 1)
    ENABLE_CORS: bool = get_bool_env("ENABLE_CORS", True)
    CORS_ORIGINS: List[str] = field(default_factory=lambda: get_list_env("CORS_ORIGINS", ["http://localhost:3000", "http://localhost:8080"]))
    
    # =============================================================================
    # SECURITY CONFIGURATION
    # =============================================================================
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-super-secret-key-change-in-production-min-32-chars")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_EXPIRE_MINUTES: int = get_int_env("JWT_EXPIRE_MINUTES", 60)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = get_int_env("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
    ENCRYPTION_KEY: str = os.getenv("ENCRYPTION_KEY", "your-encryption-key-32-bytes-long")
    
    # =============================================================================
    # DATABASE CONFIGURATION
    # =============================================================================
    # PostgreSQL
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://chabot_user:chabot_password@localhost:5432/chabot_db")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST", "localhost")
    POSTGRES_PORT: int = get_int_env("POSTGRES_PORT", 5432)
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "chabot_db")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "chabot_user")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "chabot_password")
    
    # Redis
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = get_int_env("REDIS_PORT", 6379)
    REDIS_DB: int = get_int_env("REDIS_DB", 0)
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "")
    
    # Neo4j
    NEO4J_URI: str = os.getenv("NEO4J_URI", "bolt://*************:32355")
    NEO4J_HOST: str = os.getenv("NEO4J_HOST", "*************")
    NEO4J_PORT: int = get_int_env("NEO4J_PORT", 32355)
    NEO4J_USER: str = os.getenv("NEO4J_USER", "neo4j")
    NEO4J_PASSWORD: str = os.getenv("NEO4J_PASSWORD", "admin@123")
    NEO4J_DATABASE: str = os.getenv("NEO4J_DATABASE", "neo4j")
    
    # Database Options
    MOCK_DATABASES: bool = get_bool_env("MOCK_DATABASES", True)
    DATABASE_PATH: str = os.getenv("DATABASE_PATH", "./data")
    
    # =============================================================================
    # AI & ML CONFIGURATION
    # =============================================================================
    # LLM Configuration
    OLLAMA_BASE_URL: str = os.getenv("OLLAMA_BASE_URL", "https://developer.nuvoai.io/ollama")
    LLM_MODEL_NAME: str = os.getenv("LLM_MODEL_NAME", "llama3.1:8b")
    LLM_FALLBACK_MODEL: str = os.getenv("LLM_FALLBACK_MODEL", "llama3.1:7b")
    LLM_MAX_LENGTH: int = get_int_env("LLM_MAX_LENGTH", 2048)
    LLM_TEMPERATURE: float = get_float_env("LLM_TEMPERATURE", 0.7)
    LLM_TOP_P: float = get_float_env("LLM_TOP_P", 0.9)
    LLM_TOP_K: int = get_int_env("LLM_TOP_K", 50)
    
    # Embedding Configuration
    EMBEDDING_MODEL: str = os.getenv("EMBEDDING_MODEL", "nomic-embed-text")
    EMBEDDING_DIMENSION: int = get_int_env("EMBEDDING_DIMENSION", 768)
    EMBEDDING_BATCH_SIZE: int = get_int_env("EMBEDDING_BATCH_SIZE", 32)
    EMBEDDING_MAX_LENGTH: int = get_int_env("EMBEDDING_MAX_LENGTH", 512)
    
    # Context & Reasoning
    MAX_CONTEXT_LENGTH: int = get_int_env("MAX_CONTEXT_LENGTH", 4000)
    REASONING_CONFIDENCE_THRESHOLD: float = get_float_env("REASONING_CONFIDENCE_THRESHOLD", 0.7)
    MAX_REASONING_DEPTH: int = get_int_env("MAX_REASONING_DEPTH", 5)
    REASONING_TIMEOUT_SECONDS: int = get_int_env("REASONING_TIMEOUT_SECONDS", 60)
    
    # =============================================================================
    # AGENT CONFIGURATION
    # =============================================================================
    MAX_AGENTS_PER_TYPE: int = get_int_env("MAX_AGENTS_PER_TYPE", 3)
    AGENT_TIMEOUT_SECONDS: int = get_int_env("AGENT_TIMEOUT_SECONDS", 30)
    AGENT_COMMUNICATION_TIMEOUT: int = get_int_env("AGENT_COMMUNICATION_TIMEOUT", 30)
    AGENT_HEARTBEAT_INTERVAL: int = get_int_env("AGENT_HEARTBEAT_INTERVAL", 10)
    AGENT_MAX_RETRIES: int = get_int_env("AGENT_MAX_RETRIES", 3)
    
    # Task Management
    TASK_QUEUE_SIZE: int = get_int_env("TASK_QUEUE_SIZE", 100)
    TASK_TIMEOUT_SECONDS: int = get_int_env("TASK_TIMEOUT_SECONDS", 300)
    MAX_CONCURRENT_TASKS: int = get_int_env("MAX_CONCURRENT_TASKS", 10)
    TASK_RETRY_ATTEMPTS: int = get_int_env("TASK_RETRY_ATTEMPTS", 3)
    
    # Agent Memory
    CONVERSATION_MEMORY_LIMIT: int = get_int_env("CONVERSATION_MEMORY_LIMIT", 100)
    EPISODIC_MEMORY_LIMIT: int = get_int_env("EPISODIC_MEMORY_LIMIT", 50)
    SEMANTIC_MEMORY_LIMIT: int = get_int_env("SEMANTIC_MEMORY_LIMIT", 200)
    WORKING_MEMORY_LIMIT: int = get_int_env("WORKING_MEMORY_LIMIT", 20)
    
    # Specialized Agents
    ENABLE_RESEARCH_AGENT: bool = get_bool_env("ENABLE_RESEARCH_AGENT", True)
    ENABLE_ANALYSIS_AGENT: bool = get_bool_env("ENABLE_ANALYSIS_AGENT", True)
    ENABLE_CREATIVE_AGENT: bool = get_bool_env("ENABLE_CREATIVE_AGENT", True)
    ENABLE_TECHNICAL_AGENT: bool = get_bool_env("ENABLE_TECHNICAL_AGENT", True)
    
    # =============================================================================
    # TOOL CONFIGURATION
    # =============================================================================
    TOOL_EXECUTION_TIMEOUT: int = get_int_env("TOOL_EXECUTION_TIMEOUT", 30)
    TOOL_MAX_RETRIES: int = get_int_env("TOOL_MAX_RETRIES", 2)
    TOOL_SANDBOX_ENABLED: bool = get_bool_env("TOOL_SANDBOX_ENABLED", True)
    
    # Calculator Tool
    CALCULATOR_PRECISION: int = get_int_env("CALCULATOR_PRECISION", 10)
    CALCULATOR_MAX_EXPRESSION_LENGTH: int = get_int_env("CALCULATOR_MAX_EXPRESSION_LENGTH", 1000)
    
    # Database Query Tool
    DB_QUERY_TIMEOUT: int = get_int_env("DB_QUERY_TIMEOUT", 30)
    DB_QUERY_MAX_ROWS: int = get_int_env("DB_QUERY_MAX_ROWS", 1000)
    
    # =============================================================================
    # MEMORY SYSTEM CONFIGURATION
    # =============================================================================
    # Episodic Memory
    EPISODIC_MEMORY_DB_PATH: str = os.getenv("EPISODIC_MEMORY_DB_PATH", "./data/episodic_memory.db")
    EPISODIC_MEMORY_MAX_EPISODES: int = get_int_env("EPISODIC_MEMORY_MAX_EPISODES", 10000)
    
    # Semantic Memory
    SEMANTIC_MEMORY_DB_PATH: str = os.getenv("SEMANTIC_MEMORY_DB_PATH", "./data/semantic_memory.db")
    SEMANTIC_MEMORY_MAX_CONCEPTS: int = get_int_env("SEMANTIC_MEMORY_MAX_CONCEPTS", 50000)
    
    # Working Memory
    WORKING_MEMORY_CAPACITY: int = get_int_env("WORKING_MEMORY_CAPACITY", 100)
    WORKING_MEMORY_DEFAULT_TTL: int = get_int_env("WORKING_MEMORY_DEFAULT_TTL", 3600)
    WORKING_MEMORY_DECAY_RATE: float = get_float_env("WORKING_MEMORY_DECAY_RATE", 0.1)
    
    # Memory Consolidation
    AUTO_CONSOLIDATION: bool = get_bool_env("AUTO_CONSOLIDATION", True)
    CONSOLIDATION_INTERVAL: int = get_int_env("CONSOLIDATION_INTERVAL", 1800)
    CONSOLIDATION_MIN_IMPORTANCE: float = get_float_env("CONSOLIDATION_MIN_IMPORTANCE", 0.4)
    
    # =============================================================================
    # KNOWLEDGE BASE CONFIGURATION
    # =============================================================================
    KNOWLEDGE_BASE_PATH: str = os.getenv("KNOWLEDGE_BASE_PATH", "./docs/")
    DOCUMENT_CHUNK_SIZE: int = get_int_env("DOCUMENT_CHUNK_SIZE", 1000)
    DOCUMENT_CHUNK_OVERLAP: int = get_int_env("DOCUMENT_CHUNK_OVERLAP", 200)
    DOCUMENT_MAX_SIZE_MB: int = get_int_env("DOCUMENT_MAX_SIZE_MB", 10)
    SUPPORTED_FILE_TYPES: List[str] = field(default_factory=lambda: get_list_env("SUPPORTED_FILE_TYPES", ["txt", "pdf", "docx", "md"]))
    
    # Vector Search
    VECTOR_SEARCH_TOP_K: int = get_int_env("VECTOR_SEARCH_TOP_K", 10)
    VECTOR_SEARCH_SIMILARITY_THRESHOLD: float = get_float_env("VECTOR_SEARCH_SIMILARITY_THRESHOLD", 0.7)
    
    # =============================================================================
    # MONITORING & LOGGING
    # =============================================================================
    LOG_FORMAT: str = os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    LOG_FILE_PATH: str = os.getenv("LOG_FILE_PATH", "./logs/chabot.log")
    LOG_MAX_SIZE: str = os.getenv("LOG_MAX_SIZE", "10MB")
    LOG_BACKUP_COUNT: int = get_int_env("LOG_BACKUP_COUNT", 5)
    
    # Performance Monitoring
    MONITORING_ENABLED: bool = get_bool_env("MONITORING_ENABLED", True)
    METRICS_PORT: int = get_int_env("METRICS_PORT", 9090)
    HEALTH_CHECK_INTERVAL: int = get_int_env("HEALTH_CHECK_INTERVAL", 60)
    PERFORMANCE_MONITORING: bool = get_bool_env("PERFORMANCE_MONITORING", True)
    
    # =============================================================================
    # EXTERNAL INTEGRATIONS
    # =============================================================================
    # Email Configuration
    SMTP_HOST: str = os.getenv("SMTP_HOST", "smtp.gmail.com")
    SMTP_PORT: int = get_int_env("SMTP_PORT", 587)
    SMTP_USER: str = os.getenv("SMTP_USER", "<EMAIL>")
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "your_email_password")
    SMTP_TLS: bool = get_bool_env("SMTP_TLS", True)
    
    # External APIs
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    ANTHROPIC_API_KEY: str = os.getenv("ANTHROPIC_API_KEY", "")
    HUGGINGFACE_API_KEY: str = os.getenv("HUGGINGFACE_API_KEY", "")
    
    # =============================================================================
    # FEATURE FLAGS
    # =============================================================================
    FEATURE_ADVANCED_REASONING: bool = get_bool_env("FEATURE_ADVANCED_REASONING", True)
    FEATURE_MULTI_AGENT_COORDINATION: bool = get_bool_env("FEATURE_MULTI_AGENT_COORDINATION", True)
    FEATURE_TREE_OF_THOUGHTS: bool = get_bool_env("FEATURE_TREE_OF_THOUGHTS", True)
    FEATURE_SELF_REFLECTION: bool = get_bool_env("FEATURE_SELF_REFLECTION", True)
    FEATURE_TOOL_INTEGRATION: bool = get_bool_env("FEATURE_TOOL_INTEGRATION", True)
    FEATURE_KNOWLEDGE_FUSION: bool = get_bool_env("FEATURE_KNOWLEDGE_FUSION", True)
    
    # =============================================================================
    # ORGANIZATION CONFIGURATION
    # =============================================================================
    DEFAULT_ORGANIZATION: str = os.getenv("DEFAULT_ORGANIZATION", "NuvoAI")
    SUPPORTED_ORGANIZATIONS: List[str] = field(default_factory=lambda: get_list_env("SUPPORTED_ORGANIZATIONS", ["NuvoAI", "Meril_Life_Sciences"]))
    ORGANIZATION_DATA_PATH: str = os.getenv("ORGANIZATION_DATA_PATH", "./docs/")
    ORGANIZATION_ISOLATION: bool = get_bool_env("ORGANIZATION_ISOLATION", True)
    
    # =============================================================================
    # RESOURCE LIMITS
    # =============================================================================
    MAX_MEMORY_USAGE_MB: int = get_int_env("MAX_MEMORY_USAGE_MB", 2048)
    MAX_CPU_USAGE_PERCENT: int = get_int_env("MAX_CPU_USAGE_PERCENT", 80)
    MAX_CONCURRENT_REQUESTS: int = get_int_env("MAX_CONCURRENT_REQUESTS", 100)
    REQUEST_TIMEOUT_SECONDS: int = get_int_env("REQUEST_TIMEOUT_SECONDS", 30)
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        # Create necessary directories
        Path(self.DATABASE_PATH).mkdir(parents=True, exist_ok=True)
        Path(self.KNOWLEDGE_BASE_PATH).mkdir(parents=True, exist_ok=True)
        Path(os.path.dirname(self.LOG_FILE_PATH)).mkdir(parents=True, exist_ok=True)
        
        # Validate critical settings
        if not self.SECRET_KEY or self.SECRET_KEY == "your-super-secret-key-change-in-production-min-32-chars":
            if self.ENVIRONMENT == "production":
                raise ValueError("SECRET_KEY must be set in production")
        
        if self.API_PORT < 1 or self.API_PORT > 65535:
            raise ValueError("API_PORT must be between 1 and 65535")
        
        # Log configuration status
        logger.info(f"Configuration loaded for environment: {self.ENVIRONMENT}")
        logger.info(f"Debug mode: {self.DEBUG}")
        logger.info(f"API will run on {self.API_HOST}:{self.API_PORT}")
    
    @classmethod
    def from_env_file(cls, env_file: str = ".env"):
        """Load configuration from environment file."""
        if os.path.exists(env_file):
            try:
                from dotenv import load_dotenv
                load_dotenv(env_file)
                logger.info(f"Loaded environment from {env_file}")
            except ImportError:
                logger.warning("python-dotenv not installed, skipping .env file loading")
        else:
            logger.info(f"Environment file {env_file} not found, using system environment")
        
        return cls()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    def get_database_urls(self) -> Dict[str, str]:
        """Get all database connection URLs."""
        return {
            "postgresql": self.DATABASE_URL,
            "redis": self.REDIS_URL,
            "neo4j": self.NEO4J_URI
        }
    
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration."""
        return {
            "base_url": self.OLLAMA_BASE_URL,
            "model_name": self.LLM_MODEL_NAME,
            "fallback_model": self.LLM_FALLBACK_MODEL,
            "max_length": self.LLM_MAX_LENGTH,
            "temperature": self.LLM_TEMPERATURE,
            "top_p": self.LLM_TOP_P,
            "top_k": self.LLM_TOP_K
        }
    
    def get_memory_config(self) -> Dict[str, Any]:
        """Get memory system configuration."""
        return {
            "episodic_db_path": self.EPISODIC_MEMORY_DB_PATH,
            "semantic_db_path": self.SEMANTIC_MEMORY_DB_PATH,
            "working_memory_capacity": self.WORKING_MEMORY_CAPACITY,
            "auto_consolidation": self.AUTO_CONSOLIDATION,
            "consolidation_interval": self.CONSOLIDATION_INTERVAL
        }

# Global configuration instance
Config = Config.from_env_file()
