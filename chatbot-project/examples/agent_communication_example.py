import sys
import os
import asyncio
import time
import signal
import threading

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from agents.communication.communication_service import CommunicationService
from agents.core.agent_registry import AgentRegistry
from agents.core.base_agent import BaseAgent, AgentStatus

# Define example agents
class SenderAgent(BaseAgent):
    def __init__(self):
        super().__init__(agent_id="sender", name="SenderAgent")
        self.capabilities = ["send_messages"]
        self.register_message_handler("response", self.handle_response)
        self.responses = []
    
    async def process_task(self, task):
        message = task.get("message", "Default message")
        recipient = task.get("recipient", "receiver")
        
        # Send message
        await self.send_message(
            recipient,
            "request",
            {"content": message, "timestamp": time.time()}
        )
        
        return {"status": "Message sent", "recipient": recipient}
    
    async def handle_response(self, message):
        print(f"SenderAgent received response: {message.payload}")
        self.responses.append(message)
    
    def get_capabilities(self):
        return self.capabilities

class ReceiverAgent(BaseAgent):
    def __init__(self):
        super().__init__(agent_id="receiver", name="ReceiverAgent")
        self.capabilities = ["receive_messages"]
        self.register_message_handler("request", self.handle_request)
        self.messages = []
    
    async def process_task(self, task):
        return {"status": "Ready to receive messages"}
    
    async def handle_request(self, message):
        print(f"ReceiverAgent received message: {message.payload}")
        self.messages.append(message)
        
        # Send response
        await self.send_message(
            message.sender_id,
            "response",
            {"content": f"Message received at {time.time()}", "original_id": message.message_id, "correlation_id": message.message_id}
        )
    
    def get_capabilities(self):
        return self.capabilities

# Main function
async def main():
    # Start Redis if not running
    os.system("redis-server --daemonize yes")
    time.sleep(1)  # Wait for Redis to start
    
    # Create communication service
    comm_service = CommunicationService(redis_host='localhost', redis_port=6379, grpc_port=50054)
    agent_registry = AgentRegistry()
    
    # Start the service
    comm_service.start(agent_registry)
    print("Communication service started")
    
    # Create agents
    sender = SenderAgent()
    receiver = ReceiverAgent()
    
    # Register agents
    comm_service.register_agent(sender)
    comm_service.register_agent(receiver)
    agent_registry.register_agent(sender)
    agent_registry.register_agent(receiver)
    
    # Initialize agents
    await sender.initialize()
    await receiver.initialize()
    print("Agents initialized and registered")
    
    # Send messages
    for i in range(3):
        message = f"Test message {i+1}"
        print(f"\nSending message: {message}")
        await sender.process_task({"message": message})
        await asyncio.sleep(1)  # Wait for message processing
    
    # Wait for responses
    await asyncio.sleep(2)
    
    # Print statistics
    print("\nStatistics:")
    print(f"Messages sent: 3")
    print(f"Messages received by receiver: {len(receiver.messages)}")
    print(f"Responses received by sender: {len(sender.responses)}")
    
    # Shutdown
    await sender.shutdown()
    await receiver.shutdown()
    comm_service.stop()
    
    # Stop Redis
    os.system("redis-cli shutdown")
    print("\nExample completed")

if __name__ == "__main__":
    # Run the example
    asyncio.run(main())