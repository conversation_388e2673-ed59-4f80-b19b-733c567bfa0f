# ============================================================================
# CHaBot Integrated System - Production Docker Image
# ============================================================================

FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# ============================================================================
# SYSTEM DEPENDENCIES STAGE
# ============================================================================
FROM base as system-deps

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Build tools
    build-essential \
    gcc \
    g++ \
    make \
    cmake \
    # System libraries
    curl \
    wget \
    git \
    # Database clients
    postgresql-client \
    redis-tools \
    # SSL and crypto
    ca-certificates \
    openssl \
    libssl-dev \
    # Graphics and ML dependencies
    libffi-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    # Graph processing
    graphviz \
    libgraphviz-dev \
    # Cleanup
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# ============================================================================
# PYTHON DEPENDENCIES STAGE
# ============================================================================
FROM system-deps as python-deps

# Create application user
RUN groupadd -r chabot && useradd -r -g chabot chabot

# Set working directory
WORKDIR /app

# Copy requirements files
COPY requirements_integrated.txt requirements.txt ./

# Install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements_integrated.txt && \
    pip install --no-cache-dir -r requirements.txt

# ============================================================================
# APPLICATION STAGE
# ============================================================================
FROM python-deps as application

# Copy application code
COPY --chown=chabot:chabot . .

# Create necessary directories
RUN mkdir -p \
    /app/data \
    /app/logs \
    /app/models \
    /app/docs \
    /app/backups \
    && chown -R chabot:chabot /app

# Create data directories for different components
RUN mkdir -p \
    /app/data/episodic_memory \
    /app/data/semantic_memory \
    /app/data/vector_db \
    /app/data/graph_db \
    /app/models/embeddings \
    /app/models/llm \
    /app/models/custom_ner_model \
    && chown -R chabot:chabot /app/data /app/models

# Set permissions
RUN chmod +x start_chabot.py integrated_chabot_system.py

# Switch to non-root user
USER chabot

# ============================================================================
# RUNTIME CONFIGURATION
# ============================================================================

# Expose ports
EXPOSE 8000 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Set default environment
ENV ENVIRONMENT=production \
    DEBUG=false \
    LOG_LEVEL=INFO \
    API_HOST=0.0.0.0 \
    API_PORT=8000

# Volume mounts for persistent data
VOLUME ["/app/data", "/app/logs", "/app/models", "/app/docs"]

# Default command
CMD ["python", "start_chabot.py", "--host", "0.0.0.0", "--port", "8000"]

# ============================================================================
# DEVELOPMENT STAGE (Optional)
# ============================================================================
FROM application as development

USER root

# Install development dependencies
RUN pip install --no-cache-dir \
    jupyter \
    ipython \
    pytest-xdist \
    pytest-benchmark \
    memory-profiler \
    line-profiler

# Install additional development tools
RUN apt-get update && apt-get install -y \
    vim \
    htop \
    tree \
    && rm -rf /var/lib/apt/lists/*

USER chabot

# Override for development
ENV ENVIRONMENT=development \
    DEBUG=true \
    LOG_LEVEL=DEBUG \
    DEV_MODE=true

CMD ["python", "start_chabot.py", "--host", "0.0.0.0", "--port", "8000"]
