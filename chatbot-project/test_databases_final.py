#!/usr/bin/env python3
"""
Final Database Testing Script for CHaBot
Tests all database connections with proper API versions and configurations.
"""

import asyncio
import logging
import sys
import os
import time
from datetime import datetime
import json
import requests

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_postgresql():
    """Test PostgreSQL connection."""
    try:
        import asyncpg
        
        # Connection string
        conn_str = "postgresql://chabot_user:chabot_password@localhost:5432/chabot_db"
        
        # Test connection
        conn = await asyncpg.connect(conn_str)
        
        # Test query
        result = await conn.fetchval("SELECT 1")
        
        # Create test table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS test_table (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Insert test data
        await conn.execute("INSERT INTO test_table (name) VALUES ($1)", "test_entry_final")
        
        # Query test data
        rows = await conn.fetch("SELECT * FROM test_table LIMIT 5")
        
        await conn.close()
        
        logger.info(f"✅ PostgreSQL: Connected, {len(rows)} test records")
        return True
        
    except Exception as e:
        logger.error(f"❌ PostgreSQL: {e}")
        return False

async def test_neo4j():
    """Test Neo4j connection with retry."""
    try:
        from neo4j import GraphDatabase
        
        # Connection details
        uri = "bolt://10.10.110.153:32355"
        user = "neo4j"
        password = "admin@123"
        
        # Test with timeout and retry
        driver = GraphDatabase.driver(uri, auth=(user, password), connection_timeout=15)
        
        with driver.session() as session:
            # Simple test query
            result = session.run("RETURN 1 as test")
            record = result.single()
            
            # Create test node
            session.run("CREATE (t:TestNodeFinal {name: 'test_final', timestamp: timestamp()})")
            
            # Query test nodes
            result = session.run("MATCH (t:TestNodeFinal) RETURN count(t) as count")
            count = result.single()["count"]
            
            # Cleanup
            session.run("MATCH (t:TestNodeFinal {name: 'test_final'}) DELETE t")
        
        driver.close()
        
        logger.info(f"✅ Neo4j: Connected, {count} test nodes found")
        return True
        
    except Exception as e:
        logger.error(f"❌ Neo4j: {e}")
        return False

async def test_milvus():
    """Test Milvus connection with proper initialization wait."""
    try:
        # Wait for Milvus to fully initialize
        logger.info("Waiting for Milvus to initialize...")
        time.sleep(10)
        
        from pymilvus import connections, utility, Collection, FieldSchema, CollectionSchema, DataType
        
        # Connect to Milvus with timeout
        connections.connect(
            alias="default",
            host="localhost",
            port="19530",
            timeout=30
        )
        
        # Test connection
        if not connections.has_connection("default"):
            raise Exception("Failed to connect to Milvus")
        
        # List collections
        collections = utility.list_collections()
        
        # Test creating a simple collection
        try:
            collection_name = "test_collection_final"
            if utility.has_collection(collection_name):
                utility.drop_collection(collection_name)
            
            # Define schema
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=128)
            ]
            schema = CollectionSchema(fields, "Test collection")
            
            # Create collection
            collection = Collection(collection_name, schema)
            logger.info(f"✅ Milvus: Connected, {len(collections)} collections, test collection created")
            
            # Cleanup
            utility.drop_collection(collection_name)
            
        except Exception as e:
            logger.info(f"✅ Milvus: Connected, {len(collections)} collections (collection ops failed: {e})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Milvus: {e}")
        return False

async def test_chroma():
    """Test Chroma connection with v2 API."""
    try:
        import chromadb
        
        # Test HTTP connection with v2 API first
        response = requests.get("http://localhost:8001/api/v2/heartbeat", timeout=10)
        if response.status_code != 200:
            raise Exception(f"HTTP v2 heartbeat failed: {response.status_code}")
        
        # Connect to Chroma server
        client = chromadb.HttpClient(host="localhost", port=8001)
        
        # Test heartbeat
        heartbeat = client.heartbeat()
        
        # Try to get or create a test collection
        try:
            collection = client.get_or_create_collection("test_collection_final")
            collections = client.list_collections()
            
            # Test basic operations
            collection.add(
                documents=["This is a test document"],
                metadatas=[{"source": "test"}],
                ids=["test_id_final"]
            )
            
            # Query
            results = collection.query(
                query_texts=["test"],
                n_results=1
            )
            
            logger.info(f"✅ Chroma: Connected, {len(collections)} collections, operations working")
            return True
            
        except Exception as e:
            logger.info(f"✅ Chroma: Connected (heartbeat OK), collection ops failed: {e}")
            return True
        
    except Exception as e:
        logger.error(f"❌ Chroma: {e}")
        return False

async def test_memgraph():
    """Test Memgraph connection (should fail as it's disabled)."""
    try:
        from neo4j import GraphDatabase
        
        # Connection details
        uri = "bolt://localhost:7687"
        
        # Test connection
        driver = GraphDatabase.driver(uri, auth=None, connection_timeout=5)
        
        with driver.session() as session:
            # Test query
            result = session.run("RETURN 1 as test")
            record = result.single()
        
        driver.close()
        
        logger.info(f"✅ Memgraph: Connected")
        return True
        
    except Exception as e:
        logger.info(f"ℹ️ Memgraph: Disabled (as expected): {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting FINAL database connectivity tests...")
    
    tests = {
        "PostgreSQL": test_postgresql(),
        "Neo4j": test_neo4j(),
        "Milvus": test_milvus(),
        "Chroma": test_chroma(),
        "Memgraph": test_memgraph()
    }
    
    results = {}
    
    for db_name, test_coro in tests.items():
        logger.info(f"Testing {db_name}...")
        try:
            results[db_name] = await test_coro
        except Exception as e:
            logger.error(f"Test failed for {db_name}: {e}")
            results[db_name] = False
    
    # Summary
    connected = sum(results.values())
    total = len(results)
    
    logger.info("="*60)
    logger.info("🎯 FINAL DATABASE TEST SUMMARY")
    logger.info("="*60)
    
    for db_name, status in results.items():
        status_icon = "✅" if status else "❌"
        logger.info(f"{status_icon} {db_name}: {'Connected' if status else 'Failed'}")
    
    logger.info(f"📊 Total: {connected}/{total} databases connected")
    logger.info(f"📈 Success Rate: {(connected/total)*100:.1f}%")
    
    if connected >= 3:
        logger.info("🎉 Excellent! Multiple databases available for CHaBot!")
    elif connected >= 2:
        logger.info("🎉 Good! Sufficient databases available for CHaBot!")
    else:
        logger.warning("⚠️ Need more database connections for full functionality")
    
    logger.info("="*60)
    
    # Save results
    report = {
        "timestamp": datetime.now().isoformat(),
        "results": results,
        "summary": {
            "connected": connected,
            "total": total,
            "success_rate": f"{(connected/total)*100:.1f}%"
        }
    }
    
    with open("database_test_results_final.json", "w") as f:
        json.dump(report, f, indent=2)
    
    logger.info("📄 Results saved to database_test_results_final.json")
    
    # Recommendations
    logger.info("\n🔧 RECOMMENDATIONS:")
    if not results.get("Neo4j", False):
        logger.info("- Neo4j: Check external server status and restart if needed")
    if not results.get("Milvus", False):
        logger.info("- Milvus: Wait longer for initialization or check etcd connectivity")
    if not results.get("Chroma", False):
        logger.info("- Chroma: Check container status and API version compatibility")
    
    return connected, total

if __name__ == "__main__":
    connected, total = asyncio.run(main())
    sys.exit(0 if connected >= 2 else 1)
