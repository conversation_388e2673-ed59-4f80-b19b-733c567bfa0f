# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# Linting
.mypy_cache/
.flake8

# Environment Variables
.env
.env.local
.env.production

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Docker
.dockerignore

# OS
.DS_Store
Thumbs.db

# Project Specific
ai/llm/models/
data/
temp/