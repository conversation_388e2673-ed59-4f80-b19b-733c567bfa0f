# Contribution Guidelines

Thank you for considering contributing to this project! Your help is greatly appreciated. Below are some guidelines to
follow to make the contribution process smooth and effective for everyone involved.

## How to Contribute

1. **Fork the Repository**:
    - Navigate to the repository you want to contribute to.
    - Click the "Fork" button at the top right corner of the page.

2. **Clone Your Fork**:
    - Open your terminal and run:
      ```sh
      git clone https://github.com/RanitManik/Gemini-Clone.git
      ```

3. **Create a Branch**:
    - Move into the cloned directory:
      ```sh
      cd Gemini-Clone
      ```
    - Create a new branch for your changes:
      ```sh
      git checkout -b feature-branch-name
      ```

4. **Make Your Changes**:
    - Implement your changes in your local repository.

5. **Commit Your Changes**:
    - Add your changes:
      ```sh
      git add .
      ```
    - Commit your changes with a meaningful message:
      ```sh
      git commit -m "Description of changes"
      ```

6. **Push to Your Fork**:
    - Push your changes to your forked repository:
      ```sh
      git push origin feature-branch-name
      ```

7. **Create a Pull Request**:
    - Go to the original repository.
    - Click the "New Pull Request" button.
    - Select the branch you created and click "Create Pull Request".
    - Provide a detailed description of your changes in the pull request.

## Code Style

- Follow the coding standards and style guides of the project.
- Write clean, readable, and maintainable code.
- Add comments where necessary to explain complex logic or decisions.

## Testing

- Ensure that your changes do not break any existing functionality.
- Write tests to cover new or changed functionality if applicable.
- Run all tests to confirm that everything works as expected.

## Documentation

- Update the documentation if your changes include new features or modifications.
- Ensure that your documentation is clear, concise, and easy to understand.

## Commit Messages

- Use clear and concise commit messages.
- Follow the format:
  ```
  [type]: [subject]
  
  [body]
  
  [footer]
  ```

  Example:
  ```
  feat: add new feature for user authentication
  
  Implemented user login and registration functionality.
  Added tests for the new feature.
  
  Closes #123
  ```

## Issue Reporting

- If you find a bug, create an issue before submitting a pull request.
- Provide a detailed description of the bug, including steps to reproduce it.
- If possible, include screenshots or code snippets to help illustrate the issue.

## Pull Request Review

- Be patient and respectful while waiting for your pull request to be reviewed.
- Address any feedback or requested changes promptly and thoughtfully.
- Engage in discussions and provide clarifications if needed.

## Code of Conduct

- Follow the project's code of conduct.
- Be respectful, inclusive, and considerate in your interactions.
- Help create a welcoming and positive environment for all contributors.

---

We appreciate your contribution and look forward to collaborating with you!
