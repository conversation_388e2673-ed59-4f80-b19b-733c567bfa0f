# CHaBot API Quick Reference

## 🌐 Base URL: `http://localhost:8002`

---

## 👤 **USER APIs** (16 endpoints)

### 💬 **Chat (3 endpoints)**
```bash
# Send message
POST /chat
{"message": "Hello!", "user_id": "user123", "session_id": "optional"}

# Get session info
GET /chat/sessions/{session_id}

# List sessions (Admin)
GET /chat/sessions
```

### 🤖 **Agents (4 endpoints)**
```bash
# List agents
GET /agents

# Get agent info
GET /agents/{agent_id}

# Assign task
POST /agents/{agent_id}/task
{"description": "Task description", "task_type": "research", "priority": "high"}

# Coordinate agents
POST /agents/coordinate
{"description": "Complex task", "task_type": "analysis", "priority": "high"}
```

### 🧠 **Memory (6 endpoints)**
```bash
# Query memory
GET /memory/query?query=AI&limit=5&memory_types=episodic,semantic

# Search episodes
GET /memory/episodes?query=conversation&limit=10&episode_type=conversation

# Search concepts
GET /memory/concepts?query=AI&limit=10&concept_type=concept

# Store memory (Admin)
POST /memory/store
{"memory_type": "episodic", "content": {...}}

# Get statistics (Admin)
GET /memory/statistics

# Trigger consolidation (Admin)
POST /memory/consolidate
```

### 🛠️ **Tools (5 endpoints)**
```bash
# List tools
GET /tools

# Get tool info
GET /tools/{tool_name}

# Execute tool
POST /tools/execute
{"tool_name": "calculate", "parameters": {"expression": "2+2"}}

# Get categories
GET /tools/categories

# Get tools by category
GET /tools/categories/{category}
```

### ℹ️ **System (4 endpoints)**
```bash
# Basic info
GET /

# Health check
GET /health

# System status (Admin)
GET /system/status

# System statistics (Admin)
GET /system/statistics
```

---

## 🔐 **ADMIN APIs** (6 endpoints)

### 📊 **System Management**
```bash
# Detailed system status
GET /system/status

# Comprehensive statistics
GET /system/statistics
```

### 💬 **Chat Administration**
```bash
# List all chat sessions
GET /chat/sessions
```

### 🧠 **Memory Administration**
```bash
# Store memory
POST /memory/store

# Get memory statistics
GET /memory/statistics

# Trigger consolidation
POST /memory/consolidate
```

---

## 🚀 **Quick Test Commands**

### **Basic System Check**
```bash
curl http://localhost:8002/health
```

### **Send Chat Message**
```bash
curl -X POST http://localhost:8002/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello CHaBot!", "user_id": "test_user"}'
```

### **List Agents**
```bash
curl http://localhost:8002/agents
```

### **Execute Calculator**
```bash
curl -X POST http://localhost:8002/tools/execute \
  -H "Content-Type: application/json" \
  -d '{"tool_name": "calculate", "parameters": {"expression": "2+2*3"}}'
```

### **Query Memory**
```bash
curl "http://localhost:8002/memory/query?query=AI&limit=5"
```

### **Get System Statistics (Admin)**
```bash
curl http://localhost:8002/system/statistics
```

---

## 📊 **API Status Summary**

| **Category** | **User APIs** | **Admin APIs** | **Total** | **Status** |
|--------------|---------------|----------------|-----------|------------|
| Chat         | 2             | 1              | 3         | ✅ Active  |
| Agents       | 4             | 0              | 4         | ✅ Active  |
| Memory       | 3             | 3              | 6         | ✅ Active  |
| Tools        | 5             | 0              | 5         | ✅ Active  |
| System       | 2             | 2              | 4         | ✅ Active  |
| **TOTAL**    | **16**        | **6**          | **22**    | ✅ **All Active** |

---

## 🎯 **Response Formats**

### **Success Response**
```json
{
  "success": true,
  "data": {...},
  "timestamp": "2025-01-17T17:00:00Z"
}
```

### **Error Response**
```json
{
  "success": false,
  "error": "Error description",
  "timestamp": "2025-01-17T17:00:00Z"
}
```

### **Chat Response**
```json
{
  "success": true,
  "response": {
    "message": "AI response",
    "type": "text",
    "processing_time": 0.15,
    "context_used": true
  },
  "session_id": "session-123",
  "message_count": 1
}
```

### **Agent Task Response**
```json
{
  "success": true,
  "task_id": "task-uuid",
  "agent_id": "agent-001",
  "status": "completed",
  "result": "Task result",
  "assigned_at": "2025-01-17T17:00:00Z",
  "completed_at": "2025-01-17T17:01:00Z"
}
```

### **Tool Execution Response**
```json
{
  "success": true,
  "execution_id": "exec-123",
  "tool_name": "calculate",
  "result": {
    "status": "success",
    "result": 8,
    "execution_time": 0.002
  },
  "timestamp": "2025-01-17T17:00:00Z"
}
```

---

## 🔧 **Testing & Development**

### **Run API Tests**
```bash
python test_all_apis.py
```

### **Run API Demo**
```bash
python demo_api_usage.py
```

### **Start Server**
```bash
python start_chabot.py --port 8002
```

### **Check Server Status**
```bash
curl http://localhost:8002/health | jq
```

---

## 📚 **Documentation Files**

- **`API_DOCUMENTATION.md`** - Complete API documentation
- **`API_QUICK_REFERENCE.md`** - This quick reference
- **`test_all_apis.py`** - Comprehensive API test suite
- **`demo_api_usage.py`** - Interactive API demonstration
- **`README_INTEGRATED.md`** - System overview and setup

---

## 🎉 **System Status: PRODUCTION READY**

✅ **22 API endpoints** fully functional  
✅ **Memory system** with episodic, semantic, and working memory  
✅ **Agent coordination** with 4 specialized agents  
✅ **Tool execution** with safe sandbox environment  
✅ **Real-time chat** with session management  
✅ **Admin controls** for system management  
✅ **Comprehensive testing** with automated test suite  

**The CHaBot system is ready for production deployment!** 🚀
