# ============================================================================
# CHaBot Integrated System - Docker Compose Configuration
# ============================================================================

version: '3.8'

services:
  # ============================================================================
  # DATABASE SERVICES
  # ============================================================================
  
  # PostgreSQL - Primary Database
  postgres:
    image: postgres:15-alpine
    container_name: chabot_postgres
    environment:
      POSTGRES_DB: chabot_db
      POSTGRES_USER: chabot_user
      POSTGRES_PASSWORD: chabot_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - chabot_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chabot_user -d chabot_db"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis - Caching and Message Broker
  redis:
    image: redis:7-alpine
    container_name: chabot_redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - chabot_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Neo4j - Graph Database
  neo4j:
    image: neo4j:5.15-community
    container_name: chabot_neo4j
    environment:
      NEO4J_AUTH: neo4j/admin@123
      NEO4J_PLUGINS: '["apoc", "graph-data-science"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*,gds.*
      NEO4J_dbms_memory_heap_initial__size: 512m
      NEO4J_dbms_memory_heap_max__size: 2G
      NEO4J_dbms_memory_pagecache_size: 1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    networks:
      - chabot_network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "admin@123", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Milvus Vector Database
  milvus:
    image: milvusdb/milvus:v2.3.4
    container_name: chabot_milvus
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    ports:
      - "19530:19530"
    networks:
      - chabot_network
    depends_on:
      - etcd
      - minio
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Etcd for Milvus
  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: chabot_etcd
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - chabot_network
    restart: unless-stopped

  # MinIO for Milvus
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: chabot_minio
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    volumes:
      - minio_data:/data
    command: minio server /data
    ports:
      - "9001:9000"
      - "9002:9001"
    networks:
      - chabot_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped

  # ============================================================================
  # MONITORING SERVICES
  # ============================================================================
  
  # Prometheus - Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: chabot_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - chabot_network
    restart: unless-stopped

  # Grafana - Metrics Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: chabot_grafana
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3000:3000"
    networks:
      - chabot_network
    depends_on:
      - prometheus
    restart: unless-stopped

  # ============================================================================
  # CHABOT APPLICATION SERVICES
  # ============================================================================
  
  # Main CHaBot Application
  chabot_app:
    build:
      context: .
      dockerfile: Dockerfile.integrated
      target: application
    container_name: chabot_main
    environment:
      # Application Settings
      ENVIRONMENT: production
      DEBUG: false
      LOG_LEVEL: INFO
      API_HOST: 0.0.0.0
      API_PORT: 8000
      
      # Database Configuration
      DATABASE_URL: ******************************************************/chabot_db
      REDIS_URL: redis://:redis_password@redis:6379/0
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: admin@123
      
      # Milvus Configuration
      MILVUS_HOST: milvus
      MILVUS_PORT: 19530
      
      # LLM Configuration
      OLLAMA_BASE_URL: https://developer.nuvoai.io/ollama
      LLM_MODEL_NAME: llama3.1:8b
      EMBEDDING_MODEL: nomic-embed-text
      
      # Memory Configuration
      DATABASE_PATH: /app/data
      EPISODIC_MEMORY_DB_PATH: /app/data/episodic_memory.db
      SEMANTIC_MEMORY_DB_PATH: /app/data/semantic_memory.db
      
      # Feature Flags
      FEATURE_ADVANCED_REASONING: true
      FEATURE_MULTI_AGENT_COORDINATION: true
      FEATURE_TOOL_INTEGRATION: true
      FEATURE_KNOWLEDGE_FUSION: true
      
      # Agent Configuration
      ENABLE_RESEARCH_AGENT: true
      ENABLE_ANALYSIS_AGENT: true
      ENABLE_CREATIVE_AGENT: true
      ENABLE_TECHNICAL_AGENT: true
      
      # Monitoring
      MONITORING_ENABLED: true
      METRICS_PORT: 9090
      
    volumes:
      - chabot_data:/app/data
      - chabot_logs:/app/logs
      - chabot_models:/app/models
      - ./docs:/app/docs:ro
    ports:
      - "8000:8000"
      - "8080:8080"
      - "9091:9090"  # Metrics
    networks:
      - chabot_network
    depends_on:
      - postgres
      - redis
      - neo4j
      - milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # ============================================================================
  # UTILITY SERVICES
  # ============================================================================
  
  # Nginx - Reverse Proxy and Load Balancer
  nginx:
    image: nginx:alpine
    container_name: chabot_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - chabot_network
    depends_on:
      - chabot_app
    restart: unless-stopped

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  chabot_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ============================================================================
# VOLUMES
# ============================================================================
volumes:
  # Database volumes
  postgres_data:
    driver: local
  redis_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
  milvus_data:
    driver: local
  etcd_data:
    driver: local
  minio_data:
    driver: local
  
  # Monitoring volumes
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  
  # Application volumes
  chabot_data:
    driver: local
  chabot_logs:
    driver: local
  chabot_models:
    driver: local
