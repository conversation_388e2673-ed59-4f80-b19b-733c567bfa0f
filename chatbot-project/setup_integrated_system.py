#!/usr/bin/env python3
"""
CHaBot Integrated System Setup Script
Comprehensive system setup, validation, and initialization
"""

import os
import sys
import subprocess
import shutil
import logging
import asyncio
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CHaBotSystemSetup:
    """Comprehensive setup manager for CHaBot Integrated System."""
    
    def __init__(self):
        """Initialize the setup manager."""
        self.project_root = Path(__file__).parent
        self.setup_status = {
            "environment": False,
            "dependencies": False,
            "directories": False,
            "databases": False,
            "models": False,
            "configuration": False,
            "tests": False
        }
        
        logger.info("CHaBot System Setup Manager initialized")
    
    def check_system_requirements(self) -> Dict[str, bool]:
        """Check system requirements and dependencies."""
        logger.info("🔍 Checking system requirements...")
        
        requirements = {
            "python_version": sys.version_info >= (3, 8),
            "pip": shutil.which("pip") is not None,
            "git": shutil.which("git") is not None,
            "docker": shutil.which("docker") is not None,
            "docker_compose": shutil.which("docker-compose") is not None or shutil.which("docker") is not None,
        }
        
        # Check Python version details
        if requirements["python_version"]:
            logger.info(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        else:
            logger.error(f"❌ Python version {sys.version_info.major}.{sys.version_info.minor} < 3.8")
        
        # Check available tools
        for tool, available in requirements.items():
            if tool != "python_version":
                status = "✅" if available else "❌"
                logger.info(f"{status} {tool}: {'Available' if available else 'Not found'}")
        
        return requirements
    
    def setup_environment(self) -> bool:
        """Set up environment configuration."""
        logger.info("🌍 Setting up environment configuration...")
        
        try:
            # Create .env file from template if it doesn't exist
            env_file = self.project_root / ".env"
            env_example = self.project_root / ".env.example"
            
            if not env_file.exists() and env_example.exists():
                shutil.copy(env_example, env_file)
                logger.info("✅ Created .env file from template")
            elif env_file.exists():
                logger.info("✅ .env file already exists")
            else:
                logger.warning("⚠️ No .env.example template found")
            
            # Validate environment variables
            required_vars = [
                "APP_NAME", "API_HOST", "API_PORT", "DATABASE_URL",
                "REDIS_URL", "NEO4J_URI", "OLLAMA_BASE_URL"
            ]
            
            missing_vars = []
            for var in required_vars:
                if not os.getenv(var):
                    missing_vars.append(var)
            
            if missing_vars:
                logger.warning(f"⚠️ Missing environment variables: {missing_vars}")
                logger.info("Please configure these in your .env file")
            else:
                logger.info("✅ All required environment variables present")
            
            self.setup_status["environment"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            return False
    
    def create_directories(self) -> bool:
        """Create necessary directories."""
        logger.info("📁 Creating directory structure...")
        
        try:
            directories = [
                "data",
                "data/episodic_memory",
                "data/semantic_memory",
                "data/vector_db",
                "data/graph_db",
                "logs",
                "models",
                "models/embeddings",
                "models/llm",
                "models/custom_ner_model",
                "docs",
                "backups",
                "monitoring",
                "nginx",
                "database/init"
            ]
            
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ Created directory: {directory}")
            
            # Create placeholder files
            placeholders = [
                "data/.gitkeep",
                "logs/.gitkeep",
                "models/.gitkeep",
                "backups/.gitkeep"
            ]
            
            for placeholder in placeholders:
                placeholder_path = self.project_root / placeholder
                placeholder_path.touch(exist_ok=True)
            
            self.setup_status["directories"] = True
            logger.info("✅ Directory structure created successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Directory creation failed: {e}")
            return False
    
    def install_dependencies(self, use_integrated: bool = True) -> bool:
        """Install Python dependencies."""
        logger.info("📦 Installing Python dependencies...")
        
        try:
            # Choose requirements file
            requirements_file = "requirements_integrated.txt" if use_integrated else "requirements.txt"
            requirements_path = self.project_root / requirements_file
            
            if not requirements_path.exists():
                logger.error(f"❌ Requirements file not found: {requirements_file}")
                return False
            
            # Install dependencies
            cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_path)]
            
            logger.info(f"Installing from {requirements_file}...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Dependencies installed successfully")
                self.setup_status["dependencies"] = True
                return True
            else:
                logger.error(f"❌ Dependency installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Dependency installation failed: {e}")
            return False
    
    def setup_databases(self, use_docker: bool = True) -> bool:
        """Set up database services."""
        logger.info("🗄️ Setting up database services...")
        
        try:
            if use_docker:
                # Check if Docker is available
                if not shutil.which("docker"):
                    logger.error("❌ Docker not found. Please install Docker first.")
                    return False
                
                # Start database services using docker-compose
                compose_file = self.project_root / "docker-compose.integrated.yml"
                
                if not compose_file.exists():
                    logger.error("❌ Docker compose file not found")
                    return False
                
                # Start only database services
                services = ["postgres", "redis", "neo4j", "milvus", "etcd", "minio"]
                
                for service in services:
                    cmd = ["docker-compose", "-f", str(compose_file), "up", "-d", service]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        logger.info(f"✅ Started {service} service")
                    else:
                        logger.warning(f"⚠️ Failed to start {service}: {result.stderr}")
                
                # Wait for services to be ready
                logger.info("⏳ Waiting for database services to be ready...")
                import time
                time.sleep(30)  # Give services time to start
                
            else:
                logger.info("📝 Database setup skipped (using external/mock databases)")
            
            self.setup_status["databases"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            return False
    
    def download_models(self) -> bool:
        """Download and set up AI models."""
        logger.info("🤖 Setting up AI models...")
        
        try:
            # Create model configuration
            model_config = {
                "llm": {
                    "model_name": "llama3.1:8b",
                    "fallback_model": "llama3.1:7b",
                    "base_url": "https://developer.nuvoai.io/ollama"
                },
                "embedding": {
                    "model_name": "nomic-embed-text",
                    "dimension": 768
                },
                "nlp": {
                    "spacy_model": "en_core_web_sm"
                }
            }
            
            # Save model configuration
            config_path = self.project_root / "models" / "model_config.json"
            with open(config_path, 'w') as f:
                json.dump(model_config, f, indent=2)
            
            logger.info("✅ Model configuration saved")
            
            # Download spaCy model if needed
            try:
                import spacy
                try:
                    spacy.load("en_core_web_sm")
                    logger.info("✅ spaCy model already available")
                except OSError:
                    logger.info("📥 Downloading spaCy model...")
                    subprocess.run([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
                    logger.info("✅ spaCy model downloaded")
            except ImportError:
                logger.warning("⚠️ spaCy not installed, skipping model download")
            
            self.setup_status["models"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Model setup failed: {e}")
            return False
    
    def create_configuration_files(self) -> bool:
        """Create additional configuration files."""
        logger.info("⚙️ Creating configuration files...")
        
        try:
            # Create Nginx configuration
            nginx_config = """
events {
    worker_connections 1024;
}

http {
    upstream chabot_backend {
        server chabot_app:8000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            proxy_pass http://chabot_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /health {
            proxy_pass http://chabot_backend/health;
        }
    }
}
"""
            
            nginx_dir = self.project_root / "nginx"
            nginx_dir.mkdir(exist_ok=True)
            
            with open(nginx_dir / "nginx.conf", 'w') as f:
                f.write(nginx_config)
            
            logger.info("✅ Nginx configuration created")
            
            # Create Prometheus configuration
            prometheus_config = """
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'chabot'
    static_configs:
      - targets: ['chabot_app:9090']
    scrape_interval: 5s
    metrics_path: /metrics
"""
            
            monitoring_dir = self.project_root / "monitoring"
            monitoring_dir.mkdir(exist_ok=True)
            
            with open(monitoring_dir / "prometheus.yml", 'w') as f:
                f.write(prometheus_config)
            
            logger.info("✅ Prometheus configuration created")
            
            self.setup_status["configuration"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration creation failed: {e}")
            return False
    
    async def run_tests(self) -> bool:
        """Run system tests to validate setup."""
        logger.info("🧪 Running system tests...")
        
        try:
            # Run the memory system test
            test_file = self.project_root / "test_memory_system.py"
            
            if test_file.exists():
                cmd = [sys.executable, str(test_file)]
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("✅ Memory system tests passed")
                else:
                    logger.warning(f"⚠️ Memory system tests failed: {result.stderr}")
            
            # Run basic import tests
            try:
                from integrated_config import Config
                logger.info("✅ Configuration import successful")
                
                # Test basic functionality
                config = Config
                logger.info(f"✅ Configuration loaded: {config.APP_NAME}")
                
            except Exception as e:
                logger.error(f"❌ Configuration test failed: {e}")
                return False
            
            self.setup_status["tests"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Testing failed: {e}")
            return False
    
    def generate_setup_report(self) -> Dict[str, Any]:
        """Generate a comprehensive setup report."""
        passed_steps = sum(self.setup_status.values())
        total_steps = len(self.setup_status)
        
        report = {
            "setup_status": self.setup_status,
            "summary": {
                "passed_steps": passed_steps,
                "total_steps": total_steps,
                "success_rate": (passed_steps / total_steps) * 100,
                "overall_success": passed_steps == total_steps
            },
            "next_steps": []
        }
        
        # Add next steps based on failures
        if not self.setup_status["environment"]:
            report["next_steps"].append("Configure environment variables in .env file")
        
        if not self.setup_status["dependencies"]:
            report["next_steps"].append("Install Python dependencies")
        
        if not self.setup_status["databases"]:
            report["next_steps"].append("Set up database services")
        
        if report["summary"]["overall_success"]:
            report["next_steps"].append("Run: python start_chabot.py")
        
        return report
    
    async def run_full_setup(self, use_docker: bool = True, run_tests: bool = True) -> bool:
        """Run the complete setup process."""
        logger.info("🚀 Starting CHaBot Integrated System Setup")
        logger.info("=" * 80)
        
        setup_steps = [
            ("System Requirements", self.check_system_requirements),
            ("Environment Setup", self.setup_environment),
            ("Directory Creation", self.create_directories),
            ("Dependency Installation", lambda: self.install_dependencies(use_integrated=True)),
            ("Database Setup", lambda: self.setup_databases(use_docker=use_docker)),
            ("Model Setup", self.download_models),
            ("Configuration Files", self.create_configuration_files),
        ]
        
        if run_tests:
            setup_steps.append(("System Tests", self.run_tests))
        
        success = True
        
        for step_name, step_func in setup_steps:
            logger.info(f"\n🔧 {step_name}...")
            try:
                if asyncio.iscoroutinefunction(step_func):
                    result = await step_func()
                else:
                    result = step_func()
                
                if isinstance(result, dict):
                    # Handle requirement checks
                    failed = [k for k, v in result.items() if not v]
                    if failed:
                        logger.warning(f"⚠️ {step_name} issues: {failed}")
                        success = False
                    else:
                        logger.info(f"✅ {step_name} completed")
                elif result:
                    logger.info(f"✅ {step_name} completed")
                else:
                    logger.error(f"❌ {step_name} failed")
                    success = False
                    
            except Exception as e:
                logger.error(f"❌ {step_name} failed: {e}")
                success = False
        
        # Generate and display report
        report = self.generate_setup_report()
        
        logger.info("\n" + "=" * 80)
        logger.info("📊 SETUP REPORT")
        logger.info("=" * 80)
        
        for step, status in report["setup_status"].items():
            status_icon = "✅" if status else "❌"
            logger.info(f"{status_icon} {step.replace('_', ' ').title()}")
        
        logger.info(f"\n📈 Success Rate: {report['summary']['success_rate']:.1f}% ({report['summary']['passed_steps']}/{report['summary']['total_steps']})")
        
        if report["next_steps"]:
            logger.info("\n📋 Next Steps:")
            for step in report["next_steps"]:
                logger.info(f"  • {step}")
        
        if report["summary"]["overall_success"]:
            logger.info("\n🎉 CHaBot Integrated System setup completed successfully!")
            logger.info("🚀 You can now start the system with: python start_chabot.py")
        else:
            logger.warning("\n⚠️ Setup completed with issues. Please address the failed steps above.")
        
        return success

async def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="CHaBot Integrated System Setup")
    parser.add_argument("--no-docker", action="store_true", help="Skip Docker database setup")
    parser.add_argument("--no-tests", action="store_true", help="Skip running tests")
    parser.add_argument("--check-only", action="store_true", help="Only check requirements")
    
    args = parser.parse_args()
    
    setup_manager = CHaBotSystemSetup()
    
    if args.check_only:
        requirements = setup_manager.check_system_requirements()
        failed = [req for req, status in requirements.items() if not status]
        
        if failed:
            logger.error(f"❌ Failed requirements: {failed}")
            sys.exit(1)
        else:
            logger.info("✅ All requirements satisfied")
            sys.exit(0)
    
    success = await setup_manager.run_full_setup(
        use_docker=not args.no_docker,
        run_tests=not args.no_tests
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
