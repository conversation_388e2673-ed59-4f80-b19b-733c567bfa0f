"""
Enhanced Dynamic Tool Creation Framework for the Tool Integration Framework.
Enables runtime creation and management of tools from code or functions.
"""

import inspect
import sys
import os
import json
import logging
import time
import re
import ast
from typing import Dict, List, Any, Callable, Optional, Union, Type
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import traceback

# Import sandbox for secure execution
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))
from ai.tools.execution_sandbox import ToolExecutionSandbox, SandboxConfig, ExecutionResult

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ToolCreationMethod(Enum):
    """Methods for creating dynamic tools."""
    CODE = "code"
    FUNCTION = "function"
    MODULE = "module"
    TEMPLATE = "template"
    COMPOSITION = "composition"

@dataclass
class CreatedTool:
    """Information about a dynamically created tool."""
    name: str
    description: str
    function: Callable
    creation_method: ToolCreationMethod
    signature: str
    created_at: datetime = field(default_factory=datetime.now)
    code: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Usage statistics
    usage_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    total_execution_time: float = 0.0
    last_used: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "name": self.name,
            "description": self.description,
            "signature": self.signature,
            "creation_method": self.creation_method.value,
            "created_at": self.created_at.isoformat(),
            "usage_count": self.usage_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": self.success_count / max(self.usage_count, 1),
            "average_execution_time": self.total_execution_time / max(self.usage_count, 1),
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "metadata": self.metadata
        }

class CodeValidator:
    """Validates code for security and correctness."""
    
    def __init__(self):
        """Initialize the code validator."""
        # Dangerous patterns to detect
        self.dangerous_patterns = [
            r'__import__\s*\(',
            r'eval\s*\(',
            r'exec\s*\(',
            r'compile\s*\(',
            r'globals\s*\(',
            r'locals\s*\(',
            r'subprocess',
            r'os\.system',
            r'os\.popen',
            r'importlib',
            r'open\s*\(',
            r'file\s*\('
        ]
        
        # Blocked imports
        self.blocked_imports = [
            'os', 'subprocess', 'sys', 'importlib', 'pickle', 'marshal',
            'socket', 'multiprocessing', 'threading', 'ctypes'
        ]
    
    def validate_code(self, code: str) -> tuple[bool, List[str]]:
        """
        Validate code for security issues.
        
        Args:
            code: Python code to validate
            
        Returns:
            Tuple of (is_safe, list_of_issues)
        """
        issues = []
        
        # Check for dangerous patterns
        for pattern in self.dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                issues.append(f"Dangerous pattern detected: {pattern}")
        
        # Check for blocked imports
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        if name.name in self.blocked_imports:
                            issues.append(f"Blocked import detected: {name.name}")
                elif isinstance(node, ast.ImportFrom):
                    if node.module in self.blocked_imports:
                        issues.append(f"Blocked import detected: {node.module}")
        except SyntaxError:
            issues.append("Code contains syntax errors")
        
        return len(issues) == 0, issues

class EnhancedDynamicToolCreator:
    """
    Enhanced Dynamic Tool Creator for runtime creation and management of tools.
    """
    
    def __init__(self, sandbox_config: Optional[SandboxConfig] = None):
        """Initialize the dynamic tool creator."""
        self.created_tools: Dict[str, CreatedTool] = {}
        
        # Initialize code validator and sandbox
        self.code_validator = CodeValidator()
        self.sandbox = ToolExecutionSandbox(sandbox_config)
        
        # Creation statistics
        self.total_creations = 0
        self.successful_creations = 0
        self.failed_creations = 0
        
        logger.info("Enhanced Dynamic Tool Creator initialized")
    
    def create_tool_from_code(self, 
                             code: str, 
                             name: str, 
                             description: str,
                             validate_security: bool = True) -> Dict[str, Any]:
        """
        Create a tool from Python code.
        
        Args:
            code: Python code defining the tool function
            name: Name for the tool
            description: Description of what the tool does
            validate_security: Whether to validate code security
            
        Returns:
            Dictionary with creation result
        """
        self.total_creations += 1
        
        try:
            # Security validation
            if validate_security:
                is_safe, issues = self.code_validator.validate_code(code)
                if not is_safe:
                    self.failed_creations += 1
                    return {
                        "success": False,
                        "error": f"Security validation failed: {'; '.join(issues)}",
                        "name": name
                    }
            
            # Create a local namespace for execution
            local_namespace = {}
            
            # Safe globals for code execution
            safe_globals = {
                "__builtins__": {
                    "abs": abs, "max": max, "min": min, "round": round,
                    "sum": sum, "len": len, "range": range, "enumerate": enumerate,
                    "zip": zip, "map": map, "filter": filter, "sorted": sorted,
                    "str": str, "int": int, "float": float, "bool": bool,
                    "list": list, "dict": dict, "set": set, "tuple": tuple,
                    "__import__": __import__
                },
                "math": __import__("math"),
                "json": __import__("json"),
                "re": __import__("re"),
                "datetime": __import__("datetime"),
                "statistics": __import__("statistics")
            }
            
            # Execute the code in the safe namespace
            exec(code, safe_globals, local_namespace)
            
            # Find the main function in the code
            main_function = None
            for item_name, item in local_namespace.items():
                if callable(item) and not item_name.startswith('_'):
                    main_function = item
                    break
            
            if not main_function:
                self.failed_creations += 1
                return {
                    "success": False,
                    "error": "No callable function found in code",
                    "name": name
                }
            
            # Create tool info
            created_tool = CreatedTool(
                name=name,
                description=description,
                function=main_function,
                creation_method=ToolCreationMethod.CODE,
                signature=str(inspect.signature(main_function)),
                code=code,
                metadata={
                    "code_lines": len(code.split('\n')),
                    "function_name": main_function.__name__
                }
            )
            
            # Store the created tool
            self.created_tools[name] = created_tool
            self.successful_creations += 1
            
            logger.info(f"Tool '{name}' created successfully from code")
            
            return {
                "success": True,
                "name": name,
                "description": description,
                "signature": created_tool.signature,
                "creation_method": ToolCreationMethod.CODE.value
            }
            
        except Exception as e:
            self.failed_creations += 1
            logger.error(f"Failed to create tool '{name}' from code: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "name": name
            }
    
    def create_tool_from_function(self, 
                                 function: Callable, 
                                 name: str, 
                                 description: str) -> Dict[str, Any]:
        """
        Create a tool from an existing function.
        
        Args:
            function: Callable function
            name: Name for the tool
            description: Description of what the tool does
            
        Returns:
            Dictionary with creation result
        """
        self.total_creations += 1
        
        try:
            # Validate function
            if not callable(function):
                self.failed_creations += 1
                return {
                    "success": False,
                    "error": "Provided object is not callable",
                    "name": name
                }
            
            # Create tool info
            created_tool = CreatedTool(
                name=name,
                description=description,
                function=function,
                creation_method=ToolCreationMethod.FUNCTION,
                signature=str(inspect.signature(function)),
                metadata={
                    "function_name": function.__name__,
                    "module": getattr(function, '__module__', 'unknown')
                }
            )
            
            # Store the created tool
            self.created_tools[name] = created_tool
            self.successful_creations += 1
            
            logger.info(f"Tool '{name}' created successfully from function")
            
            return {
                "success": True,
                "name": name,
                "description": description,
                "signature": created_tool.signature,
                "creation_method": ToolCreationMethod.FUNCTION.value
            }
            
        except Exception as e:
            self.failed_creations += 1
            logger.error(f"Failed to create tool '{name}' from function: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "name": name
            }
    
    async def execute_tool(self, 
                          name: str, 
                          use_sandbox: bool = True,
                          *args, 
                          **kwargs) -> ExecutionResult:
        """
        Execute a dynamically created tool.
        
        Args:
            name: Name of the tool to execute
            use_sandbox: Whether to use sandbox for execution
            *args: Positional arguments for the tool
            **kwargs: Keyword arguments for the tool
            
        Returns:
            ExecutionResult with execution details
        """
        if name not in self.created_tools:
            return ExecutionResult(
                status="failure",
                error=f"Tool '{name}' not found"
            )
        
        tool = self.created_tools[name]
        start_time = time.time()
        
        try:
            # Update usage statistics
            tool.usage_count += 1
            tool.last_used = datetime.now()
            
            # Execute with or without sandbox
            if use_sandbox:
                result = await self.sandbox.execute_tool(tool.function, args, kwargs)
            else:
                # Direct execution
                try:
                    tool_result = tool.function(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    result = ExecutionResult(
                        status="success",
                        result=tool_result,
                        execution_time=execution_time
                    )
                except Exception as e:
                    execution_time = time.time() - start_time
                    result = ExecutionResult(
                        status="failure",
                        error=str(e),
                        execution_time=execution_time
                    )
            
            # Update tool statistics
            tool.total_execution_time += result.execution_time
            
            if result.status == "success":
                tool.success_count += 1
            else:
                tool.failure_count += 1
            
            logger.info(f"Tool '{name}' executed: {result.status} in {result.execution_time:.3f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            tool.failure_count += 1
            tool.total_execution_time += execution_time
            
            logger.error(f"Tool execution failed: {e}")
            
            return ExecutionResult(
                status="failure",
                error=str(e),
                execution_time=execution_time
            )
    
    def list_created_tools(self) -> List[Dict[str, Any]]:
        """
        List all dynamically created tools.
        
        Returns:
            List of tool information dictionaries
        """
        return [tool.to_dict() for tool in self.created_tools.values()]
    
    def get_tool(self, name: str) -> Optional[CreatedTool]:
        """
        Get a created tool by name.
        
        Args:
            name: Name of the tool
            
        Returns:
            CreatedTool object or None if not found
        """
        return self.created_tools.get(name)
    
    def delete_tool(self, name: str) -> bool:
        """
        Delete a dynamically created tool.
        
        Args:
            name: Name of the tool to delete
            
        Returns:
            True if successful, False if tool not found
        """
        if name in self.created_tools:
            del self.created_tools[name]
            logger.info(f"Tool '{name}' deleted successfully")
            return True
        
        logger.warning(f"Tool '{name}' not found for deletion")
        return False
    
    def get_creation_statistics(self) -> Dict[str, Any]:
        """Get tool creation statistics."""
        success_rate = (
            self.successful_creations / max(self.total_creations, 1)
        )
        
        # Method distribution
        method_counts = {}
        for tool in self.created_tools.values():
            method = tool.creation_method.value
            method_counts[method] = method_counts.get(method, 0) + 1
        
        return {
            "total_creations": self.total_creations,
            "successful_creations": self.successful_creations,
            "failed_creations": self.failed_creations,
            "success_rate": success_rate,
            "active_tools": len(self.created_tools),
            "creation_methods": method_counts
        }

# Tool functions for registration
def create_tool_from_code(code: str, name: str, description: str) -> Dict[str, Any]:
    """Tool function: Create a tool from Python code."""
    creator = EnhancedDynamicToolCreator()
    try:
        result = creator.create_tool_from_code(code, name, description)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "name": name
        }

def create_simple_calculator() -> Dict[str, Any]:
    """Tool function: Create a simple calculator tool."""
    code = """
def calculate(expression: str) -> float:
    import math
    
    # Safe evaluation with limited scope
    allowed_names = {
        "__builtins__": {},
        "abs": abs, "round": round, "min": min, "max": max, "sum": sum,
        "math": math, "pi": math.pi, "e": math.e, "sqrt": math.sqrt,
        "sin": math.sin, "cos": math.cos, "tan": math.tan, "log": math.log
    }
    
    try:
        result = eval(expression, allowed_names)
        return float(result)
    except Exception as e:
        raise ValueError(f"Calculation error: {str(e)}")
"""
    
    creator = EnhancedDynamicToolCreator()
    return creator.create_tool_from_code(
        code=code,
        name="simple_calculator",
        description="Simple calculator for mathematical expressions"
    )
