"""
Enhanced Search Tools for the Tool Integration Framework.
Provides comprehensive search capabilities across multiple sources and types.
"""

import asyncio
import json
import logging
import time
import re
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import requests
from urllib.parse import quote_plus, urljoin
import sqlite3
from contextlib import contextmanager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SearchType(Enum):
    """Types of search operations."""
    TEXT = "text"
    SEMANTIC = "semantic"
    FUZZY = "fuzzy"
    REGEX = "regex"
    BOOLEAN = "boolean"

class SearchSource(Enum):
    """Sources for search operations."""
    WEB = "web"
    DATABASE = "database"
    DOCUMENTS = "documents"
    MEMORY = "memory"
    API = "api"

@dataclass
class SearchResult:
    """Result from a search operation."""
    title: str
    content: str
    url: Optional[str] = None
    score: float = 0.0
    source: SearchSource = SearchSource.WEB
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "title": self.title,
            "content": self.content,
            "url": self.url,
            "score": self.score,
            "source": self.source.value,
            "metadata": self.metadata
        }

@dataclass
class SearchQuery:
    """Search query configuration."""
    query: str
    search_type: SearchType = SearchType.TEXT
    sources: List[SearchSource] = field(default_factory=lambda: [SearchSource.WEB])
    max_results: int = 10
    filters: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "query": self.query,
            "search_type": self.search_type.value,
            "sources": [source.value for source in self.sources],
            "max_results": self.max_results,
            "filters": self.filters
        }

class EnhancedSearchTool:
    """
    Enhanced search tool with multiple search capabilities.
    """
    
    def __init__(self, 
                 web_search_api_key: Optional[str] = None,
                 database_path: Optional[str] = None):
        """Initialize the search tool."""
        self.web_search_api_key = web_search_api_key
        self.database_path = database_path or ":memory:"
        self.connection = None
        
        # Search statistics
        self.search_count = 0
        self.successful_searches = 0
        self.failed_searches = 0
        self.total_search_time = 0.0
        
        # Initialize search index database
        self._init_search_index()
        
        logger.info("Enhanced Search Tool initialized")
    
    def _init_search_index(self):
        """Initialize the search index database."""
        try:
            self.connection = sqlite3.connect(self.database_path)
            self.connection.row_factory = sqlite3.Row
            
            # Create search index table
            self.connection.execute("""
                CREATE TABLE IF NOT EXISTS search_index (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    url TEXT,
                    source TEXT NOT NULL,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(title, url, source)
                )
            """)
            
            # Create full-text search virtual table
            self.connection.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS search_fts USING fts5(
                    title, content, url, source,
                    content='search_index',
                    content_rowid='id'
                )
            """)
            
            self.connection.commit()
            logger.info("Search index initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize search index: {e}")
    
    async def search(self, search_query: SearchQuery) -> List[SearchResult]:
        """
        Perform a comprehensive search across specified sources.
        
        Args:
            search_query: SearchQuery configuration
            
        Returns:
            List of SearchResult objects
        """
        start_time = time.time()
        self.search_count += 1
        
        logger.info(f"Performing search: {search_query.query}")
        
        try:
            all_results = []
            
            # Search across all specified sources
            for source in search_query.sources:
                if source == SearchSource.WEB:
                    web_results = await self._search_web(search_query)
                    all_results.extend(web_results)
                elif source == SearchSource.DATABASE:
                    db_results = await self._search_database(search_query)
                    all_results.extend(db_results)
                elif source == SearchSource.DOCUMENTS:
                    doc_results = await self._search_documents(search_query)
                    all_results.extend(doc_results)
                elif source == SearchSource.MEMORY:
                    memory_results = await self._search_memory(search_query)
                    all_results.extend(memory_results)
            
            # Sort by score and limit results
            all_results.sort(key=lambda x: x.score, reverse=True)
            final_results = all_results[:search_query.max_results]
            
            # Update statistics
            search_time = time.time() - start_time
            self.successful_searches += 1
            self.total_search_time += search_time
            
            logger.info(f"Search completed: {len(final_results)} results in {search_time:.3f}s")
            return final_results
            
        except Exception as e:
            search_time = time.time() - start_time
            self.failed_searches += 1
            self.total_search_time += search_time
            
            logger.error(f"Search failed: {e}")
            return []
    
    async def _search_web(self, search_query: SearchQuery) -> List[SearchResult]:
        """Search the web using various APIs."""
        results = []
        
        try:
            # Mock web search results (in real implementation, use actual APIs)
            mock_results = [
                {
                    "title": f"Web result 1 for '{search_query.query}'",
                    "content": f"This is a web search result containing information about {search_query.query}. It provides comprehensive details and relevant information.",
                    "url": f"https://example.com/result1?q={quote_plus(search_query.query)}",
                    "score": 0.9
                },
                {
                    "title": f"Web result 2 for '{search_query.query}'",
                    "content": f"Another web search result related to {search_query.query}. This result offers additional insights and perspectives.",
                    "url": f"https://example.com/result2?q={quote_plus(search_query.query)}",
                    "score": 0.8
                },
                {
                    "title": f"Web result 3 for '{search_query.query}'",
                    "content": f"Third web search result about {search_query.query}. Contains supplementary information and references.",
                    "url": f"https://example.com/result3?q={quote_plus(search_query.query)}",
                    "score": 0.7
                }
            ]
            
            for result_data in mock_results:
                result = SearchResult(
                    title=result_data["title"],
                    content=result_data["content"],
                    url=result_data["url"],
                    score=result_data["score"],
                    source=SearchSource.WEB,
                    metadata={
                        "search_type": search_query.search_type.value,
                        "timestamp": datetime.now().isoformat()
                    }
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"Web search failed: {e}")
        
        return results
    
    async def _search_database(self, search_query: SearchQuery) -> List[SearchResult]:
        """Search the local database/index."""
        results = []
        
        try:
            if not self.connection:
                return results
            
            # Use FTS for text search
            if search_query.search_type == SearchType.TEXT:
                cursor = self.connection.execute("""
                    SELECT title, content, url, source, metadata
                    FROM search_fts
                    WHERE search_fts MATCH ?
                    ORDER BY rank
                    LIMIT ?
                """, (search_query.query, search_query.max_results))
            else:
                # Use LIKE for other search types
                cursor = self.connection.execute("""
                    SELECT title, content, url, source, metadata
                    FROM search_index
                    WHERE title LIKE ? OR content LIKE ?
                    ORDER BY title
                    LIMIT ?
                """, (f"%{search_query.query}%", f"%{search_query.query}%", search_query.max_results))
            
            for row in cursor.fetchall():
                metadata = json.loads(row["metadata"]) if row["metadata"] else {}
                
                result = SearchResult(
                    title=row["title"],
                    content=row["content"],
                    url=row["url"],
                    score=0.8,  # Default score for database results
                    source=SearchSource(row["source"]),
                    metadata=metadata
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"Database search failed: {e}")
        
        return results
    
    async def _search_documents(self, search_query: SearchQuery) -> List[SearchResult]:
        """Search through documents."""
        results = []
        
        try:
            # Mock document search results
            mock_docs = [
                {
                    "title": f"Document about {search_query.query}",
                    "content": f"This document contains detailed information about {search_query.query}. It includes analysis, examples, and best practices.",
                    "url": f"/documents/doc1_{search_query.query.replace(' ', '_')}.pdf",
                    "score": 0.85
                },
                {
                    "title": f"Research paper on {search_query.query}",
                    "content": f"Academic research paper discussing {search_query.query} with methodology, results, and conclusions.",
                    "url": f"/documents/research_{search_query.query.replace(' ', '_')}.pdf",
                    "score": 0.75
                }
            ]
            
            for doc_data in mock_docs:
                result = SearchResult(
                    title=doc_data["title"],
                    content=doc_data["content"],
                    url=doc_data["url"],
                    score=doc_data["score"],
                    source=SearchSource.DOCUMENTS,
                    metadata={
                        "document_type": "pdf",
                        "search_type": search_query.search_type.value
                    }
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"Document search failed: {e}")
        
        return results
    
    async def _search_memory(self, search_query: SearchQuery) -> List[SearchResult]:
        """Search through memory/cache."""
        results = []
        
        try:
            # Mock memory search results
            mock_memory = [
                {
                    "title": f"Cached result for {search_query.query}",
                    "content": f"Previously cached information about {search_query.query} from recent searches and interactions.",
                    "score": 0.6
                }
            ]
            
            for memory_data in mock_memory:
                result = SearchResult(
                    title=memory_data["title"],
                    content=memory_data["content"],
                    score=memory_data["score"],
                    source=SearchSource.MEMORY,
                    metadata={
                        "cached_at": datetime.now().isoformat(),
                        "search_type": search_query.search_type.value
                    }
                )
                results.append(result)
            
        except Exception as e:
            logger.error(f"Memory search failed: {e}")
        
        return results
    
    def add_to_index(self, 
                    title: str,
                    content: str,
                    url: Optional[str] = None,
                    source: SearchSource = SearchSource.DOCUMENTS,
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Add content to the search index.
        
        Args:
            title: Title of the content
            content: Content text
            url: Optional URL
            source: Source of the content
            metadata: Optional metadata
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.connection:
                return False
            
            metadata_json = json.dumps(metadata or {})
            
            # Insert into main table
            self.connection.execute("""
                INSERT OR REPLACE INTO search_index 
                (title, content, url, source, metadata)
                VALUES (?, ?, ?, ?, ?)
            """, (title, content, url, source.value, metadata_json))
            
            # Update FTS index
            self.connection.execute("""
                INSERT OR REPLACE INTO search_fts 
                (title, content, url, source)
                VALUES (?, ?, ?, ?)
            """, (title, content, url, source.value))
            
            self.connection.commit()
            logger.info(f"Added to search index: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add to search index: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get search tool statistics."""
        success_rate = (
            self.successful_searches / max(self.search_count, 1)
        )
        
        avg_search_time = (
            self.total_search_time / max(self.successful_searches, 1)
        )
        
        # Get index statistics
        index_count = 0
        if self.connection:
            try:
                cursor = self.connection.execute("SELECT COUNT(*) FROM search_index")
                index_count = cursor.fetchone()[0]
            except Exception:
                pass
        
        return {
            "total_searches": self.search_count,
            "successful_searches": self.successful_searches,
            "failed_searches": self.failed_searches,
            "success_rate": success_rate,
            "total_search_time": self.total_search_time,
            "average_search_time": avg_search_time,
            "indexed_items": index_count
        }
    
    def close(self):
        """Close the search tool and database connection."""
        if self.connection:
            self.connection.close()
            self.connection = None
        logger.info("Search tool closed")

# Tool functions for registration
async def search_web(query: str, max_results: int = 5) -> Dict[str, Any]:
    """Tool function: Search the web."""
    search_tool = EnhancedSearchTool()
    try:
        search_query = SearchQuery(
            query=query,
            sources=[SearchSource.WEB],
            max_results=max_results
        )
        
        results = await search_tool.search(search_query)
        
        return {
            "success": True,
            "query": query,
            "results": [result.to_dict() for result in results],
            "result_count": len(results)
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "query": query
        }
    finally:
        search_tool.close()

async def search_documents(query: str, max_results: int = 5) -> Dict[str, Any]:
    """Tool function: Search documents."""
    search_tool = EnhancedSearchTool()
    try:
        search_query = SearchQuery(
            query=query,
            sources=[SearchSource.DOCUMENTS],
            max_results=max_results
        )
        
        results = await search_tool.search(search_query)
        
        return {
            "success": True,
            "query": query,
            "results": [result.to_dict() for result in results],
            "result_count": len(results)
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "query": query
        }
    finally:
        search_tool.close()

async def comprehensive_search(query: str, max_results: int = 10) -> Dict[str, Any]:
    """Tool function: Comprehensive search across all sources."""
    search_tool = EnhancedSearchTool()
    try:
        search_query = SearchQuery(
            query=query,
            sources=[SearchSource.WEB, SearchSource.DOCUMENTS, SearchSource.DATABASE, SearchSource.MEMORY],
            max_results=max_results
        )
        
        results = await search_tool.search(search_query)
        
        return {
            "success": True,
            "query": query,
            "results": [result.to_dict() for result in results],
            "result_count": len(results),
            "sources_searched": [source.value for source in search_query.sources]
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "query": query
        }
    finally:
        search_tool.close()
