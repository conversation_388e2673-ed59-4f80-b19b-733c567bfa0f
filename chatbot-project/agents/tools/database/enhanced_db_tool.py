"""
Enhanced Database Query Tools for the Tool Integration Framework.
Provides comprehensive database operations with security and performance monitoring.
"""

import sqlite3
import json
import logging
import time
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from contextlib import contextmanager
import re

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseType(Enum):
    """Supported database types."""
    SQLITE = "sqlite"
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"

class QueryType(Enum):
    """Types of database queries."""
    SELECT = "select"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    CREATE = "create"
    DROP = "drop"
    ALTER = "alter"

@dataclass
class QueryResult:
    """Result of a database query operation."""
    success: bool
    query_type: QueryType
    query: str
    results: Optional[List[Dict[str, Any]]] = None
    affected_rows: int = 0
    execution_time: float = 0.0
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "success": self.success,
            "query_type": self.query_type.value,
            "query": self.query,
            "results": self.results,
            "affected_rows": self.affected_rows,
            "execution_time": self.execution_time,
            "error": self.error,
            "metadata": self.metadata
        }

class EnhancedDatabaseTool:
    """
    Enhanced database tool with comprehensive query capabilities.
    """
    
    def __init__(self, database_path: str = ":memory:"):
        """Initialize the database tool."""
        self.database_path = database_path
        self.connection = None
        
        # Query statistics
        self.query_count = 0
        self.successful_queries = 0
        self.failed_queries = 0
        self.total_execution_time = 0.0
        
        # Security settings
        self.allowed_operations = {
            QueryType.SELECT, QueryType.INSERT, QueryType.UPDATE
        }
        self.blocked_keywords = [
            'DROP DATABASE', 'DROP SCHEMA', 'TRUNCATE', 'ALTER SYSTEM',
            'SHUTDOWN', 'GRANT', 'REVOKE', 'CREATE USER', 'DROP USER'
        ]
        
        logger.info(f"Enhanced Database Tool initialized with database: {database_path}")
    
    def connect(self) -> bool:
        """
        Establish database connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.connection = sqlite3.connect(self.database_path)
            self.connection.row_factory = sqlite3.Row
            
            logger.info("Database connection established successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def disconnect(self):
        """Close database connection."""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Database connection closed")
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections."""
        if not self.connection:
            if not self.connect():
                raise RuntimeError("Failed to establish database connection")
        
        try:
            yield self.connection
        except Exception as e:
            if self.connection:
                self.connection.rollback()
            raise e
    
    def _validate_query_security(self, query: str) -> Tuple[bool, Optional[str]]:
        """
        Validate query for security issues.
        
        Args:
            query: SQL query to validate
            
        Returns:
            Tuple of (is_safe, error_message)
        """
        query_upper = query.upper().strip()
        
        # Check for blocked keywords
        for blocked in self.blocked_keywords:
            if blocked in query_upper:
                return False, f"Blocked keyword detected: {blocked}"
        
        # Determine query type
        query_type = None
        for qtype in QueryType:
            if query_upper.startswith(qtype.value.upper()):
                query_type = qtype
                break
        
        if not query_type:
            return False, "Unknown query type"
        
        # Check if operation is allowed
        if query_type not in self.allowed_operations:
            return False, f"Operation not allowed: {query_type.value}"
        
        return True, None
    
    def _determine_query_type(self, query: str) -> QueryType:
        """Determine the type of SQL query."""
        query_upper = query.upper().strip()
        
        for qtype in QueryType:
            if query_upper.startswith(qtype.value.upper()):
                return qtype
        
        return QueryType.SELECT  # Default
    
    def execute_query(self, 
                     query: str, 
                     params: Optional[Union[Tuple, List]] = None,
                     validate_security: bool = True) -> QueryResult:
        """
        Execute a database query.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            validate_security: Whether to validate query security
            
        Returns:
            QueryResult with execution details
        """
        start_time = time.time()
        query_type = self._determine_query_type(query)
        
        # Update statistics
        self.query_count += 1
        
        try:
            # Security validation
            if validate_security:
                is_safe, error_msg = self._validate_query_security(query)
                if not is_safe:
                    self.failed_queries += 1
                    return QueryResult(
                        success=False,
                        query_type=query_type,
                        query=query,
                        error=f"Security validation failed: {error_msg}",
                        execution_time=time.time() - start_time
                    )
            
            # Execute query
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # Handle different query types
                if query_type == QueryType.SELECT:
                    rows = cursor.fetchall()
                    results = [dict(row) for row in rows]
                    affected_rows = len(results)
                else:
                    results = None
                    affected_rows = cursor.rowcount
                    conn.commit()
                
                execution_time = time.time() - start_time
                self.successful_queries += 1
                self.total_execution_time += execution_time
                
                return QueryResult(
                    success=True,
                    query_type=query_type,
                    query=query,
                    results=results,
                    affected_rows=affected_rows,
                    execution_time=execution_time,
                    metadata={
                        "params": params,
                        "row_count": affected_rows
                    }
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.failed_queries += 1
            
            logger.error(f"Query execution failed: {e}")
            
            return QueryResult(
                success=False,
                query_type=query_type,
                query=query,
                error=str(e),
                execution_time=execution_time,
                metadata={"params": params}
            )
    
    def create_table(self, 
                    table_name: str, 
                    columns: Dict[str, str],
                    if_not_exists: bool = True) -> QueryResult:
        """
        Create a table with specified columns.
        
        Args:
            table_name: Name of the table to create
            columns: Dictionary of column_name: data_type
            if_not_exists: Whether to use IF NOT EXISTS clause
            
        Returns:
            QueryResult with creation details
        """
        column_defs = [f"{name} {data_type}" for name, data_type in columns.items()]
        
        if if_not_exists:
            query = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(column_defs)})"
        else:
            query = f"CREATE TABLE {table_name} ({', '.join(column_defs)})"
        
        return self.execute_query(query, validate_security=False)
    
    def insert_data(self, 
                   table_name: str, 
                   data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> QueryResult:
        """
        Insert data into a table.
        
        Args:
            table_name: Name of the table
            data: Data to insert (single dict or list of dicts)
            
        Returns:
            QueryResult with insertion details
        """
        if isinstance(data, dict):
            data = [data]
        
        if not data:
            return QueryResult(
                success=False,
                query_type=QueryType.INSERT,
                query="",
                error="No data provided for insertion"
            )
        
        # Use the first row to determine columns
        columns = list(data[0].keys())
        placeholders = ', '.join(['?' for _ in columns])
        
        query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
        
        # For multiple rows, execute multiple times
        total_affected = 0
        for row in data:
            values = [row.get(col) for col in columns]
            result = self.execute_query(query, values)
            
            if not result.success:
                return result
            
            total_affected += result.affected_rows
        
        return QueryResult(
            success=True,
            query_type=QueryType.INSERT,
            query=query,
            affected_rows=total_affected,
            metadata={"rows_inserted": len(data)}
        )
    
    def select_data(self, 
                   table_name: str,
                   columns: Optional[List[str]] = None,
                   conditions: Optional[Dict[str, Any]] = None,
                   order_by: Optional[str] = None,
                   limit: Optional[int] = None) -> QueryResult:
        """
        Select data from a table.
        
        Args:
            table_name: Name of the table
            columns: Columns to select (None for all)
            conditions: WHERE conditions
            order_by: ORDER BY clause
            limit: LIMIT clause
            
        Returns:
            QueryResult with selected data
        """
        # Build SELECT clause
        if columns:
            select_clause = ', '.join(columns)
        else:
            select_clause = '*'
        
        query = f"SELECT {select_clause} FROM {table_name}"
        params = []
        
        # Add WHERE clause
        if conditions:
            where_clauses = []
            for key, value in conditions.items():
                where_clauses.append(f"{key} = ?")
                params.append(value)
            query += f" WHERE {' AND '.join(where_clauses)}"
        
        # Add ORDER BY clause
        if order_by:
            query += f" ORDER BY {order_by}"
        
        # Add LIMIT clause
        if limit:
            query += f" LIMIT {limit}"
        
        return self.execute_query(query, params)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database tool statistics."""
        success_rate = (
            self.successful_queries / max(self.query_count, 1)
        )
        
        avg_execution_time = (
            self.total_execution_time / max(self.successful_queries, 1)
        )
        
        return {
            "total_queries": self.query_count,
            "successful_queries": self.successful_queries,
            "failed_queries": self.failed_queries,
            "success_rate": success_rate,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": avg_execution_time
        }

# Tool functions for registration
def execute_database_query(query: str, params: Optional[List] = None) -> Dict[str, Any]:
    """Tool function: Execute a database query."""
    db_tool = EnhancedDatabaseTool()
    try:
        result = db_tool.execute_query(query, params)
        return result.to_dict()
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "query": query
        }
    finally:
        db_tool.disconnect()

def create_database_table(table_name: str, columns: Dict[str, str]) -> Dict[str, Any]:
    """Tool function: Create a database table."""
    db_tool = EnhancedDatabaseTool()
    try:
        result = db_tool.create_table(table_name, columns)
        return result.to_dict()
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "table_name": table_name
        }
    finally:
        db_tool.disconnect()

def insert_database_data(table_name: str, data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> Dict[str, Any]:
    """Tool function: Insert data into a database table."""
    db_tool = EnhancedDatabaseTool()
    try:
        result = db_tool.insert_data(table_name, data)
        return result.to_dict()
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "table_name": table_name
        }
    finally:
        db_tool.disconnect()
