from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import uuid
import asyncio
from datetime import datetime
from enum import Enum

# Import communication components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))
from communication.message_protocols import MessageType, Priority, AgentMessage

class AgentStatus(Enum):
    INITIALIZING = "initializing"
    IDLE = "idle"
    BUSY = "busy"
    ACTIVE = "active"
    ERROR = "error"
    OFFLINE = "offline"

class BaseAgent(ABC):
    def __init__(self, agent_id: str = None, name: str = None):
        self.agent_id = agent_id or str(uuid.uuid4())
        self.name = name or self.__class__.__name__
        self.status = AgentStatus.INITIALIZING
        self.capabilities = []
        self.memory = {}
        self.running = False
        self.message_queue = asyncio.Queue()
        self.message_handlers = {}
        self.communication_client = None
        
    @abstractmethod
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process a task and return results"""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """Return list of agent capabilities"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status.value,
            "capabilities": self.capabilities,
            "timestamp": datetime.now().isoformat()
        }
    
    def update_status(self, status: AgentStatus):
        """Update agent status"""
        self.status = status
    
    def store_memory(self, key: str, value: Any):
        """Store information in agent memory"""
        self.memory[key] = value
    
    def retrieve_memory(self, key: str) -> Any:
        """Retrieve information from agent memory"""
        return self.memory.get(key)
    
    async def initialize(self) -> bool:
        """Initialize the agent"""
        try:
            await self._setup()
            self.status = AgentStatus.ACTIVE
            self.running = True
            return True
        except Exception as e:
            self.status = AgentStatus.ERROR
            return False
    
    async def _setup(self):
        """Setup method to be overridden by subclasses"""
        pass
    
    async def shutdown(self):
        """Shutdown the agent"""
        self.status = AgentStatus.OFFLINE
        self.running = False
        if self.communication_client:
            self.communication_client.close()
    
    def receive_message(self, message: AgentMessage):
        """Receive a message from another agent"""
        try:
            # Add to queue for async processing
            asyncio.create_task(self.message_queue.put(message))
            
            # Process message type-specific handlers
            message_type = message.message_type.value
            if message_type in self.message_handlers:
                for handler in self.message_handlers[message_type]:
                    asyncio.create_task(handler(message))
        except Exception as e:
            print(f"Error receiving message in agent {self.agent_id}: {str(e)}")
    
    def register_message_handler(self, message_type: str, handler):
        """Register a handler for a specific message type"""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)
    
    async def send_message(self, recipient_id: str, message_type: str, payload: Dict[str, Any]):
        """Send a message to another agent"""
        correlation_id = payload.get("correlation_id")
        reply_to = payload.get("reply_to")
        """Send a message to another agent"""
        if not self.communication_client:
            return False
        
        message = {
            "message_id": str(uuid.uuid4()),
            "sender_id": self.agent_id,
            "recipient_id": recipient_id,
            "message_type": message_type,
            "priority": 2,  # Medium priority
            "payload": payload,
            "timestamp": datetime.now().isoformat(),
            "correlation_id": correlation_id,
            "reply_to": reply_to
        }
        
        return self.communication_client.send_message(message)
    
    async def broadcast_message(self, message_type: str, payload: Dict[str, Any]):
        """Broadcast a message to all agents"""
        if not self.communication_client:
            return 0
        
        message = {
            "message_id": str(uuid.uuid4()),
            "sender_id": self.agent_id,
            "recipient_id": "*",  # Broadcast
            "message_type": message_type,
            "priority": 2,  # Medium priority
            "payload": payload,
            "timestamp": datetime.now().isoformat()
        }
        
        return self.communication_client.broadcast_message(message)
    
    def set_communication_client(self, client):
        """Set the communication client for this agent"""
        self.communication_client = client
        if client:
            client.start_message_stream(self.agent_id)