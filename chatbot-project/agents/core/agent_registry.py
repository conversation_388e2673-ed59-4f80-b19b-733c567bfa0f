from typing import Dict, List, Any, Optional
from .base_agent import BaseAgent

class AgentRegistry:
    def __init__(self):
        self.agents = {}
        self.agent_capabilities = {}
    
    def register_agent(self, agent: BaseAgent) -> bool:
        """Register an agent with the registry"""
        if agent.agent_id in self.agents:
            return False
        
        self.agents[agent.agent_id] = agent
        
        # Register capabilities
        capabilities = agent.get_capabilities()
        for capability in capabilities:
            if capability not in self.agent_capabilities:
                self.agent_capabilities[capability] = []
            self.agent_capabilities[capability].append(agent.agent_id)
        
        return True
    
    def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent from the registry"""
        if agent_id not in self.agents:
            return False
        
        agent = self.agents[agent_id]
        
        # Remove from capabilities
        capabilities = agent.get_capabilities()
        for capability in capabilities:
            if capability in self.agent_capabilities and agent_id in self.agent_capabilities[capability]:
                self.agent_capabilities[capability].remove(agent_id)
                if not self.agent_capabilities[capability]:
                    del self.agent_capabilities[capability]
        
        # Remove agent
        del self.agents[agent_id]
        
        return True
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """Get an agent by ID"""
        return self.agents.get(agent_id)
    
    def get_all_agents(self) -> List[BaseAgent]:
        """Get all registered agents"""
        return list(self.agents.values())
    
    def find_agents_by_capability(self, capability: str) -> List[BaseAgent]:
        """Find agents with a specific capability"""
        if capability not in self.agent_capabilities:
            return []
        
        return [self.agents[agent_id] for agent_id in self.agent_capabilities[capability] 
                if agent_id in self.agents]
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        return {
            agent_id: agent.get_status() 
            for agent_id, agent in self.agents.items()
        }