import asyncio
import threading
import time
from typing import Dict, Any, Optional

from .message_broker import Message<PERSON>roker
from .grpc_server import serve as start_grpc_server
from .grpc_client import AgentCommunicationClient

class CommunicationService:
    def __init__(self, redis_host='localhost', redis_port=6379, grpc_port=50051):
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.grpc_port = grpc_port
        self.message_broker = MessageBroker(redis_host=redis_host, redis_port=redis_port)
        self.grpc_server = None
        self.running = False
        self.agent_registry = None
    
    def start(self, agent_registry=None):
        """Start the communication service"""
        self.agent_registry = agent_registry
        
        # Initialize message broker
        self.message_broker.initialize(agent_registry)
        
        # Start gRPC server
        self.grpc_server = start_grpc_server(self.message_broker, self.grpc_port)
        
        self.running = True
        print(f"Communication service started on port {self.grpc_port}")
        return True
    
    def stop(self):
        """Stop the communication service"""
        self.running = False
        
        # Stop gRPC server
        if self.grpc_server:
            self.grpc_server.stop(grace=1.0)
            self.grpc_server.wait_for_termination(timeout=2.0)
        
        # Close message broker
        self.message_broker.close()
        
        print("Communication service stopped")
    
    def register_agent(self, agent):
        """Register an agent with the communication service"""
        # Register with message broker
        self.message_broker.register_agent(agent.agent_id, agent)
        
        # Create client for the agent
        client = AgentCommunicationClient(f"localhost:{self.grpc_port}")
        
        # Set client on agent
        agent.set_communication_client(client)
        
        return True
    
    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the communication service"""
        self.message_broker.unregister_agent(agent_id)
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of the communication service"""
        return {
            "running": self.running,
            "grpc_port": self.grpc_port,
            "redis_host": self.redis_host,
            "redis_port": self.redis_port,
            "registered_agents": len(self.message_broker.active_agents) if self.message_broker else 0
        }