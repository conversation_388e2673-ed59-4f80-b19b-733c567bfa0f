import grpc
import time
import json
from concurrent import futures
from typing import Dict, List, Any

# Import the generated gRPC code
from . import agent_communication_pb2
from . import agent_communication_pb2_grpc

class AgentCommunicationServicer(agent_communication_pb2_grpc.AgentCommunicationServicer):
    def __init__(self, message_broker):
        self.message_broker = message_broker
        self.active_streams = {}
    
    def SendMessage(self, request, context):
        try:
            # Convert protobuf message to dict
            message = {
                "message_id": request.message_id,
                "sender_id": request.sender_id,
                "recipient_id": request.recipient_id,
                "message_type": request.message_type,
                "priority": request.priority,
                "payload": json.loads(request.payload),
                "timestamp": request.timestamp,
                "correlation_id": request.correlation_id,
                "reply_to": request.reply_to,
                "ttl": request.ttl
            }
            
            # Send message through broker
            success = self.message_broker.send_message(message)
            
            if success:
                return agent_communication_pb2.MessageResponse(
                    status="success",
                    message_id=request.message_id,
                    error=""
                )
            else:
                return agent_communication_pb2.MessageResponse(
                    status="error",
                    message_id=request.message_id,
                    error="Failed to deliver message"
                )
        except Exception as e:
            return agent_communication_pb2.MessageResponse(
                status="error",
                message_id=request.message_id,
                error=str(e)
            )
    
    def StreamMessages(self, request, context):
        agent_id = request.agent_id
        message_types = list(request.message_types) if request.message_types else None
        
        # Register this stream
        if agent_id not in self.active_streams:
            self.active_streams[agent_id] = []
        self.active_streams[agent_id].append(context)
        
        try:
            # Subscribe to messages for this agent
            self.message_broker.subscribe(agent_id, message_types)
            
            # Keep stream open and yield messages as they arrive
            while context.is_active():
                messages = self.message_broker.get_messages_for_agent(agent_id)
                
                for msg in messages:
                    # Convert dict to protobuf message
                    proto_msg = agent_communication_pb2.Message(
                        message_id=msg["message_id"],
                        sender_id=msg["sender_id"],
                        recipient_id=msg["recipient_id"],
                        message_type=msg["message_type"],
                        priority=msg["priority"],
                        payload=json.dumps(msg["payload"]),
                        timestamp=msg["timestamp"],
                        correlation_id=msg.get("correlation_id", ""),
                        reply_to=msg.get("reply_to", ""),
                        ttl=msg.get("ttl", 0)
                    )
                    yield proto_msg
                
                time.sleep(0.1)  # Avoid busy waiting
        except Exception as e:
            print(f"Stream error for agent {agent_id}: {str(e)}")
        finally:
            # Clean up when stream ends
            if agent_id in self.active_streams:
                self.active_streams[agent_id].remove(context)
                if not self.active_streams[agent_id]:
                    del self.active_streams[agent_id]
            self.message_broker.unsubscribe(agent_id)
    
    def BroadcastMessage(self, request, context):
        try:
            # Convert protobuf message to dict
            message = {
                "message_id": request.message_id,
                "sender_id": request.sender_id,
                "recipient_id": "*",  # Broadcast
                "message_type": request.message_type,
                "priority": request.priority,
                "payload": json.loads(request.payload),
                "timestamp": request.timestamp,
                "correlation_id": request.correlation_id,
                "reply_to": request.reply_to,
                "ttl": request.ttl
            }
            
            # Broadcast through broker
            recipient_count = self.message_broker.broadcast_message(message)
            
            return agent_communication_pb2.BroadcastResponse(
                status="success",
                recipient_count=recipient_count,
                error=""
            )
        except Exception as e:
            return agent_communication_pb2.BroadcastResponse(
                status="error",
                recipient_count=0,
                error=str(e)
            )

def serve(message_broker, port=50051):
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    agent_communication_pb2_grpc.add_AgentCommunicationServicer_to_server(
        AgentCommunicationServicer(message_broker), server
    )
    server.add_insecure_port(f'[::]:{port}')
    server.start()
    print(f"Agent Communication gRPC server started on port {port}")
    return server