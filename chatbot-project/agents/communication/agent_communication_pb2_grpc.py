# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import agent_communication_pb2 as agent__communication__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in agent_communication_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AgentCommunicationStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SendMessage = channel.unary_unary(
                '/agent_communication.AgentCommunication/SendMessage',
                request_serializer=agent__communication__pb2.Message.SerializeToString,
                response_deserializer=agent__communication__pb2.MessageResponse.FromString,
                _registered_method=True)
        self.StreamMessages = channel.unary_stream(
                '/agent_communication.AgentCommunication/StreamMessages',
                request_serializer=agent__communication__pb2.StreamRequest.SerializeToString,
                response_deserializer=agent__communication__pb2.Message.FromString,
                _registered_method=True)
        self.BroadcastMessage = channel.unary_unary(
                '/agent_communication.AgentCommunication/BroadcastMessage',
                request_serializer=agent__communication__pb2.Message.SerializeToString,
                response_deserializer=agent__communication__pb2.BroadcastResponse.FromString,
                _registered_method=True)


class AgentCommunicationServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SendMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamMessages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BroadcastMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AgentCommunicationServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SendMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.SendMessage,
                    request_deserializer=agent__communication__pb2.Message.FromString,
                    response_serializer=agent__communication__pb2.MessageResponse.SerializeToString,
            ),
            'StreamMessages': grpc.unary_stream_rpc_method_handler(
                    servicer.StreamMessages,
                    request_deserializer=agent__communication__pb2.StreamRequest.FromString,
                    response_serializer=agent__communication__pb2.Message.SerializeToString,
            ),
            'BroadcastMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.BroadcastMessage,
                    request_deserializer=agent__communication__pb2.Message.FromString,
                    response_serializer=agent__communication__pb2.BroadcastResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'agent_communication.AgentCommunication', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('agent_communication.AgentCommunication', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AgentCommunication(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SendMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_communication.AgentCommunication/SendMessage',
            agent__communication__pb2.Message.SerializeToString,
            agent__communication__pb2.MessageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StreamMessages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/agent_communication.AgentCommunication/StreamMessages',
            agent__communication__pb2.StreamRequest.SerializeToString,
            agent__communication__pb2.Message.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BroadcastMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/agent_communication.AgentCommunication/BroadcastMessage',
            agent__communication__pb2.Message.SerializeToString,
            agent__communication__pb2.BroadcastResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
