"""
Temporal Workflow Orchestration for Agent Communication Infrastructure.
Provides workflow management, task orchestration, and distributed coordination.
"""

import asyncio
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import json
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class WorkflowStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class TaskStatus(Enum):
    WAITING = "waiting"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"

class TaskType(Enum):
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    LOOP = "loop"
    AGENT_TASK = "agent_task"
    MULTI_STEP = "multi_step"

@dataclass
class WorkflowTask:
    task_id: str
    name: str
    task_type: TaskType
    agent_id: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    timeout_seconds: int = 300
    retry_count: int = 0
    max_retries: int = 3
    status: TaskStatus = TaskStatus.WAITING
    result: Any = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Workflow:
    workflow_id: str
    name: str
    description: str
    tasks: List[WorkflowTask]
    status: WorkflowStatus = WorkflowStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

class TemporalOrchestrator:
    """
    Temporal Workflow Orchestrator for managing complex agent workflows.
    Provides workflow execution, task coordination, and state management.
    """
    
    def __init__(self, redis_manager=None, message_broker=None):
        self.redis_manager = redis_manager
        self.message_broker = message_broker
        
        # Workflow management
        self.active_workflows: Dict[str, Workflow] = {}
        self.workflow_history: List[Workflow] = []
        self.task_executors: Dict[TaskType, Callable] = {}
        
        # Execution state
        self.running = False
        self.execution_tasks: Dict[str, asyncio.Task] = {}
        
        # Performance metrics
        self.metrics = {
            "workflows_executed": 0,
            "workflows_completed": 0,
            "workflows_failed": 0,
            "average_execution_time": 0.0,
            "tasks_executed": 0,
            "tasks_completed": 0,
            "tasks_failed": 0
        }
        
        # Register default task executors
        self._register_default_executors()
        
        logger.info("Temporal Orchestrator initialized")
    
    def _register_default_executors(self):
        """Register default task executors."""
        self.task_executors[TaskType.SEQUENTIAL] = self._execute_sequential_task
        self.task_executors[TaskType.PARALLEL] = self._execute_parallel_task
        self.task_executors[TaskType.CONDITIONAL] = self._execute_conditional_task
        self.task_executors[TaskType.LOOP] = self._execute_loop_task
        self.task_executors[TaskType.AGENT_TASK] = self._execute_agent_task
        self.task_executors[TaskType.MULTI_STEP] = self._execute_multi_step_task
    
    async def start(self):
        """Start the temporal orchestrator."""
        self.running = True
        logger.info("Temporal Orchestrator started")
    
    async def stop(self):
        """Stop the temporal orchestrator."""
        self.running = False
        
        # Cancel all running workflows
        for workflow_id in list(self.execution_tasks.keys()):
            await self.cancel_workflow(workflow_id)
        
        logger.info("Temporal Orchestrator stopped")
    
    async def create_workflow(self, name: str, description: str, tasks: List[Dict[str, Any]], 
                            context: Dict[str, Any] = None) -> str:
        """Create a new workflow."""
        workflow_id = str(uuid.uuid4())
        
        # Convert task dictionaries to WorkflowTask objects
        workflow_tasks = []
        for task_data in tasks:
            task = WorkflowTask(
                task_id=task_data.get("task_id", str(uuid.uuid4())),
                name=task_data["name"],
                task_type=TaskType(task_data.get("task_type", "agent_task")),
                agent_id=task_data.get("agent_id"),
                dependencies=task_data.get("dependencies", []),
                parameters=task_data.get("parameters", {}),
                timeout_seconds=task_data.get("timeout_seconds", 300),
                max_retries=task_data.get("max_retries", 3),
                metadata=task_data.get("metadata", {})
            )
            workflow_tasks.append(task)
        
        # Create workflow
        workflow = Workflow(
            workflow_id=workflow_id,
            name=name,
            description=description,
            tasks=workflow_tasks,
            context=context or {},
            metadata={"created_by": "temporal_orchestrator"}
        )
        
        self.active_workflows[workflow_id] = workflow
        
        logger.info(f"Created workflow {workflow_id}: {name}")
        return workflow_id
    
    async def execute_workflow(self, workflow_id: str) -> bool:
        """Execute a workflow."""
        if workflow_id not in self.active_workflows:
            logger.error(f"Workflow {workflow_id} not found")
            return False
        
        workflow = self.active_workflows[workflow_id]
        
        if workflow.status != WorkflowStatus.PENDING:
            logger.error(f"Workflow {workflow_id} is not in pending state")
            return False
        
        # Start workflow execution
        workflow.status = WorkflowStatus.RUNNING
        workflow.started_at = datetime.now()
        
        # Create execution task
        execution_task = asyncio.create_task(self._execute_workflow_internal(workflow))
        self.execution_tasks[workflow_id] = execution_task
        
        logger.info(f"Started execution of workflow {workflow_id}")
        return True
    
    async def _execute_workflow_internal(self, workflow: Workflow):
        """Internal workflow execution logic."""
        try:
            self.metrics["workflows_executed"] += 1
            
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(workflow.tasks)
            
            # Execute tasks based on dependencies
            completed_tasks = set()
            
            while len(completed_tasks) < len(workflow.tasks):
                # Find tasks ready to execute
                ready_tasks = []
                for task in workflow.tasks:
                    if (task.status == TaskStatus.WAITING and 
                        all(dep in completed_tasks for dep in task.dependencies)):
                        ready_tasks.append(task)
                
                if not ready_tasks:
                    # Check if we're stuck
                    remaining_tasks = [t for t in workflow.tasks if t.task_id not in completed_tasks]
                    if remaining_tasks:
                        logger.error(f"Workflow {workflow.workflow_id} stuck - no ready tasks")
                        workflow.status = WorkflowStatus.FAILED
                        break
                    else:
                        break
                
                # Execute ready tasks
                task_results = await asyncio.gather(
                    *[self._execute_task(task, workflow) for task in ready_tasks],
                    return_exceptions=True
                )
                
                # Process results
                for task, result in zip(ready_tasks, task_results):
                    if isinstance(result, Exception):
                        task.status = TaskStatus.FAILED
                        task.error = str(result)
                        logger.error(f"Task {task.task_id} failed: {result}")
                    else:
                        task.status = TaskStatus.COMPLETED
                        task.result = result
                        task.completed_at = datetime.now()
                        completed_tasks.add(task.task_id)
                        logger.info(f"Task {task.task_id} completed successfully")
            
            # Determine final workflow status
            failed_tasks = [t for t in workflow.tasks if t.status == TaskStatus.FAILED]
            if failed_tasks:
                workflow.status = WorkflowStatus.FAILED
                self.metrics["workflows_failed"] += 1
                logger.error(f"Workflow {workflow.workflow_id} failed with {len(failed_tasks)} failed tasks")
            else:
                workflow.status = WorkflowStatus.COMPLETED
                self.metrics["workflows_completed"] += 1
                logger.info(f"Workflow {workflow.workflow_id} completed successfully")
            
            workflow.completed_at = datetime.now()
            
            # Update metrics
            execution_time = (workflow.completed_at - workflow.started_at).total_seconds()
            self._update_execution_metrics(execution_time)
            
        except Exception as e:
            workflow.status = WorkflowStatus.FAILED
            workflow.completed_at = datetime.now()
            self.metrics["workflows_failed"] += 1
            logger.error(f"Workflow {workflow.workflow_id} execution failed: {e}")
        
        finally:
            # Move to history and cleanup
            self.workflow_history.append(workflow)
            if workflow.workflow_id in self.active_workflows:
                del self.active_workflows[workflow.workflow_id]
            if workflow.workflow_id in self.execution_tasks:
                del self.execution_tasks[workflow.workflow_id]
    
    def _build_dependency_graph(self, tasks: List[WorkflowTask]) -> Dict[str, List[str]]:
        """Build a dependency graph from tasks."""
        graph = {}
        for task in tasks:
            graph[task.task_id] = task.dependencies.copy()
        return graph
    
    async def _execute_task(self, task: WorkflowTask, workflow: Workflow) -> Any:
        """Execute a single task."""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        self.metrics["tasks_executed"] += 1
        
        try:
            # Get task executor
            executor = self.task_executors.get(task.task_type)
            if not executor:
                raise ValueError(f"No executor found for task type {task.task_type}")
            
            # Execute with timeout
            result = await asyncio.wait_for(
                executor(task, workflow),
                timeout=task.timeout_seconds
            )
            
            self.metrics["tasks_completed"] += 1
            return result
            
        except asyncio.TimeoutError:
            self.metrics["tasks_failed"] += 1
            raise Exception(f"Task {task.task_id} timed out after {task.timeout_seconds} seconds")
        except Exception as e:
            self.metrics["tasks_failed"] += 1
            # Handle retries
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                logger.warning(f"Task {task.task_id} failed, retrying ({task.retry_count}/{task.max_retries})")
                await asyncio.sleep(2 ** task.retry_count)  # Exponential backoff
                return await self._execute_task(task, workflow)
            else:
                raise e
    
    async def _execute_sequential_task(self, task: WorkflowTask, workflow: Workflow) -> Any:
        """Execute a sequential task."""
        results = []
        subtasks = task.parameters.get("subtasks", [])
        
        for subtask_data in subtasks:
            subtask = WorkflowTask(**subtask_data)
            result = await self._execute_task(subtask, workflow)
            results.append(result)
        
        return results
    
    async def _execute_parallel_task(self, task: WorkflowTask, workflow: Workflow) -> Any:
        """Execute a parallel task."""
        subtasks = task.parameters.get("subtasks", [])
        
        # Create subtask objects
        subtask_objects = [WorkflowTask(**subtask_data) for subtask_data in subtasks]
        
        # Execute in parallel
        results = await asyncio.gather(
            *[self._execute_task(subtask, workflow) for subtask in subtask_objects],
            return_exceptions=True
        )
        
        return results
    
    async def _execute_conditional_task(self, task: WorkflowTask, workflow: Workflow) -> Any:
        """Execute a conditional task."""
        condition = task.parameters.get("condition")
        true_task = task.parameters.get("true_task")
        false_task = task.parameters.get("false_task")
        
        # Evaluate condition (simplified)
        condition_result = self._evaluate_condition(condition, workflow.context)
        
        if condition_result and true_task:
            subtask = WorkflowTask(**true_task)
            return await self._execute_task(subtask, workflow)
        elif not condition_result and false_task:
            subtask = WorkflowTask(**false_task)
            return await self._execute_task(subtask, workflow)
        
        return None
    
    async def _execute_loop_task(self, task: WorkflowTask, workflow: Workflow) -> Any:
        """Execute a loop task."""
        loop_task = task.parameters.get("loop_task")
        max_iterations = task.parameters.get("max_iterations", 10)
        condition = task.parameters.get("condition")
        
        results = []
        iteration = 0
        
        while iteration < max_iterations:
            # Check condition if provided
            if condition and not self._evaluate_condition(condition, workflow.context):
                break
            
            subtask = WorkflowTask(**loop_task)
            result = await self._execute_task(subtask, workflow)
            results.append(result)
            
            iteration += 1
        
        return results
    
    async def _execute_agent_task(self, task: WorkflowTask, workflow: Workflow) -> Any:
        """Execute an agent task."""
        if not task.agent_id:
            raise ValueError("Agent task requires agent_id")

        if not self.message_broker:
            raise ValueError("Message broker not available for agent communication")

        # Send task to agent
        message = {
            "message_id": str(uuid.uuid4()),
            "sender_id": "temporal_orchestrator",
            "recipient_id": task.agent_id,
            "message_type": "task_execution",
            "priority": 1,
            "payload": {
                "task_id": task.task_id,
                "task_name": task.name,
                "parameters": task.parameters,
                "workflow_id": workflow.workflow_id
            },
            "timestamp": datetime.now().isoformat(),
            "correlation_id": f"workflow_{workflow.workflow_id}_task_{task.task_id}",
            "ttl": task.timeout_seconds
        }

        # Send message and wait for response
        success = self.message_broker.send_message(message)
        if not success:
            raise Exception(f"Failed to send task to agent {task.agent_id}")

        # Wait for response (simplified - in real implementation, use proper response handling)
        await asyncio.sleep(1)  # Placeholder for actual response waiting

        return {"status": "completed", "agent_id": task.agent_id}

    async def _execute_multi_step_task(self, task: WorkflowTask, workflow: Workflow) -> Any:
        """Execute a multi-step task."""
        # Multi-step tasks are similar to sequential tasks but with more complex logic
        steps = task.parameters.get("steps", [])
        data_source = task.parameters.get("data_source", "")
        results = []

        # Process each step in sequence
        for i, step in enumerate(steps or []):
            step_result = {"step": i + 1, "name": step.get("name", f"Step {i+1}"), "status": "completed"}
            results.append(step_result)

        # If no steps defined, treat as a simple processing task
        if not steps and data_source:
            # Simulate processing
            await asyncio.sleep(0.5)
            results.append({
                "data_source": data_source,
                "processed": True,
                "timestamp": datetime.now().isoformat()
            })

        return {
            "status": "completed",
            "steps_executed": len(results),
            "results": results
        }
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """Evaluate a condition string against workflow context."""
        # Simplified condition evaluation
        # In production, use a proper expression evaluator
        try:
            # Replace context variables
            for key, value in context.items():
                condition = condition.replace(f"${key}", str(value))
            
            # Basic evaluation
            return eval(condition)
        except:
            return False
    
    def _update_execution_metrics(self, execution_time: float):
        """Update execution metrics."""
        total_workflows = self.metrics["workflows_executed"]
        current_avg = self.metrics["average_execution_time"]
        
        self.metrics["average_execution_time"] = (
            (current_avg * (total_workflows - 1) + execution_time) / total_workflows
        )
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow."""
        if workflow_id in self.execution_tasks:
            self.execution_tasks[workflow_id].cancel()
            del self.execution_tasks[workflow_id]
        
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            workflow.status = WorkflowStatus.CANCELLED
            workflow.completed_at = datetime.now()
            
            self.workflow_history.append(workflow)
            del self.active_workflows[workflow_id]
            
            logger.info(f"Cancelled workflow {workflow_id}")
            return True
        
        return False
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a workflow."""
        # Check active workflows
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
        else:
            # Check history
            workflow = next((w for w in self.workflow_history if w.workflow_id == workflow_id), None)
        
        if not workflow:
            return None
        
        return {
            "workflow_id": workflow.workflow_id,
            "name": workflow.name,
            "status": workflow.status.value,
            "created_at": workflow.created_at.isoformat(),
            "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
            "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None,
            "tasks": [
                {
                    "task_id": task.task_id,
                    "name": task.name,
                    "status": task.status.value,
                    "retry_count": task.retry_count,
                    "error": task.error
                }
                for task in workflow.tasks
            ]
        }
    
    def get_orchestrator_metrics(self) -> Dict[str, Any]:
        """Get orchestrator performance metrics."""
        return {
            "metrics": self.metrics.copy(),
            "active_workflows": len(self.active_workflows),
            "workflow_history_count": len(self.workflow_history),
            "running_tasks": len(self.execution_tasks),
            "orchestrator_status": "running" if self.running else "stopped"
        }
