syntax = "proto3";

package agent_communication;

service AgentCommunication {
  rpc SendMessage (Message) returns (MessageResponse);
  rpc StreamMessages (StreamRequest) returns (stream Message);
  rpc BroadcastMessage (Message) returns (BroadcastResponse);
}

message Message {
  string message_id = 1;
  string sender_id = 2;
  string recipient_id = 3;
  string message_type = 4;
  int32 priority = 5;
  string payload = 6;
  string timestamp = 7;
  string correlation_id = 8;
  string reply_to = 9;
  int32 ttl = 10;
}

message MessageResponse {
  string status = 1;
  string message_id = 2;
  string error = 3;
}

message StreamRequest {
  string agent_id = 1;
  repeated string message_types = 2;
}

message BroadcastResponse {
  string status = 1;
  int32 recipient_count = 2;
  string error = 3;
}