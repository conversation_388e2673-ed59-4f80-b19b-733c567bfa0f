import sys
import os
import json
import uuid
import threading
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import defaultdict, deque

# Import the message protocols
from .message_protocols import MessageType, Priority, AgentMessage, MessageProtocols
from .redis_stream_manager import EnhancedRedisStreamManager, StreamType
from .temporal_orchestrator import TemporalOrchestrator

logger = logging.getLogger(__name__)

class BrokerStatus(Enum):
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"

@dataclass
class MessageRoute:
    sender_id: str
    recipient_id: str
    message_type: str
    route_priority: int = 1
    load_balancing: bool = False
    retry_policy: Dict[str, Any] = field(default_factory=dict)
    transformation_rules: List[str] = field(default_factory=list)

@dataclass
class AgentConnection:
    agent_id: str
    connection_type: str  # "direct", "redis", "grpc"
    last_heartbeat: datetime
    message_count: int = 0
    error_count: int = 0
    status: str = "active"
    capabilities: Set[str] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)

class EnhancedMessageBroker:
    def __init__(self, redis_host='localhost', redis_port=6379, enable_temporal=True):
        self.message_protocols = MessageProtocols()
        self.redis_manager = EnhancedRedisStreamManager(host=redis_host, port=redis_port)
        self.agent_registry = None

        # Enhanced features
        self.message_handlers = {}
        self.active_agents: Dict[str, AgentConnection] = {}
        self.message_routes: Dict[str, MessageRoute] = {}
        self.message_queue = deque()
        self.priority_queues = {priority: deque() for priority in Priority}

        # Temporal orchestration
        self.temporal_orchestrator = TemporalOrchestrator(
            redis_manager=self.redis_manager,
            message_broker=self
        ) if enable_temporal else None

        # State management
        self.status = BrokerStatus.INITIALIZING
        self.running = False
        self.processing_threads = []

        # Performance metrics
        self.metrics = {
            "messages_processed": 0,
            "messages_failed": 0,
            "messages_routed": 0,
            "agents_connected": 0,
            "workflows_executed": 0,
            "average_latency": 0.0,
            "error_rate": 0.0
        }

        # Message filtering and transformation
        self.message_filters: Dict[str, Callable] = {}
        self.message_transformers: Dict[str, Callable] = {}

        # Load balancing
        self.load_balancer_groups: Dict[str, List[str]] = defaultdict(list)
        self.round_robin_counters: Dict[str, int] = defaultdict(int)
    
    async def initialize(self, agent_registry=None):
        """Initialize the enhanced message broker"""
        self.agent_registry = agent_registry
        self.status = BrokerStatus.INITIALIZING

        try:
            # Initialize Redis streams
            success = self.redis_manager.initialize()
            if not success:
                logger.error("Failed to initialize Redis stream manager")
                return False

            # Initialize temporal orchestrator if enabled
            if self.temporal_orchestrator:
                await self.temporal_orchestrator.start()
                logger.info("Temporal orchestrator started")

            # Start processing threads
            self.running = True
            self._start_processing_threads()

            # Register coordination handlers
            self._register_coordination_handlers()

            self.status = BrokerStatus.RUNNING
            logger.info("Enhanced Message Broker initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize message broker: {e}")
            self.status = BrokerStatus.STOPPED
            return False

    def _start_processing_threads(self):
        """Start multiple processing threads for better performance"""
        # Main message processing thread
        main_thread = threading.Thread(
            target=self._process_messages_loop,
            daemon=True,
            name="MessageProcessor-Main"
        )
        main_thread.start()
        self.processing_threads.append(main_thread)

        # Priority message processing thread
        priority_thread = threading.Thread(
            target=self._process_priority_messages,
            daemon=True,
            name="MessageProcessor-Priority"
        )
        priority_thread.start()
        self.processing_threads.append(priority_thread)

        # Heartbeat monitoring thread
        heartbeat_thread = threading.Thread(
            target=self._monitor_agent_heartbeats,
            daemon=True,
            name="HeartbeatMonitor"
        )
        heartbeat_thread.start()
        self.processing_threads.append(heartbeat_thread)

    def _register_coordination_handlers(self):
        """Register handlers for coordination events"""
        self.redis_manager.register_coordination_handler(
            "agent_join", self._handle_agent_join
        )
        self.redis_manager.register_coordination_handler(
            "agent_leave", self._handle_agent_leave
        )
        self.redis_manager.register_coordination_handler(
            "load_balance_request", self._handle_load_balance_request
        )
    
    def register_agent(self, agent_id: str, agent_instance, capabilities: Set[str] = None,
                      connection_type: str = "redis", metadata: Dict[str, Any] = None):
        """Register an agent with the broker"""
        connection = AgentConnection(
            agent_id=agent_id,
            connection_type=connection_type,
            last_heartbeat=datetime.now(),
            capabilities=capabilities or set(),
            metadata=metadata or {}
        )

        self.active_agents[agent_id] = connection

        # Subscribe to Redis streams for this agent
        stream_types = [StreamType.AGENT_MESSAGES, StreamType.COORDINATION]
        if "workflow" in connection.capabilities:
            stream_types.append(StreamType.WORKFLOW)

        self.redis_manager.subscribe(agent_id, stream_types)

        # Send coordination event
        self.redis_manager.send_coordination_event(
            "agent_join", agent_id, {
                "capabilities": list(capabilities or []),
                "connection_type": connection_type,
                "metadata": metadata or {}
            }
        )

        self.metrics["agents_connected"] += 1
        logger.info(f"Registered agent {agent_id} with capabilities: {capabilities}")

    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the broker"""
        if agent_id in self.active_agents:
            connection = self.active_agents[agent_id]
            del self.active_agents[agent_id]

            # Send coordination event
            self.redis_manager.send_coordination_event(
                "agent_leave", agent_id, {
                    "reason": "unregistered",
                    "last_heartbeat": connection.last_heartbeat.isoformat()
                }
            )

            self.metrics["agents_connected"] = max(0, self.metrics["agents_connected"] - 1)

        # Unsubscribe from Redis stream
        self.redis_manager.unsubscribe(agent_id)
        logger.info(f"Unregistered agent {agent_id}")

    def create_message_route(self, route_id: str, sender_pattern: str, recipient_pattern: str,
                           message_type: str, priority: int = 1, load_balancing: bool = False,
                           retry_policy: Dict[str, Any] = None):
        """Create a message routing rule"""
        route = MessageRoute(
            sender_id=sender_pattern,
            recipient_id=recipient_pattern,
            message_type=message_type,
            route_priority=priority,
            load_balancing=load_balancing,
            retry_policy=retry_policy or {}
        )

        self.message_routes[route_id] = route
        logger.info(f"Created message route {route_id}: {sender_pattern} -> {recipient_pattern}")

    def add_to_load_balancer_group(self, group_name: str, agent_ids: List[str]):
        """Add agents to a load balancer group"""
        self.load_balancer_groups[group_name].extend(agent_ids)
        logger.info(f"Added {len(agent_ids)} agents to load balancer group {group_name}")

    def register_message_filter(self, filter_name: str, filter_func: Callable[[Dict[str, Any]], bool]):
        """Register a message filter function"""
        self.message_filters[filter_name] = filter_func
        logger.info(f"Registered message filter: {filter_name}")

    def register_message_transformer(self, transformer_name: str,
                                   transformer_func: Callable[[Dict[str, Any]], Dict[str, Any]]):
        """Register a message transformer function"""
        self.message_transformers[transformer_name] = transformer_func
        logger.info(f"Registered message transformer: {transformer_name}")
    
    def send_message(self, message: Dict[str, Any], stream_type: StreamType = StreamType.AGENT_MESSAGES) -> bool:
        """Send a message through the enhanced broker with routing and filtering"""
        try:
            start_time = time.time()

            # Validate message format
            if not self.message_protocols.validate_message(message):
                logger.error(f"Invalid message format: {message}")
                self.metrics["messages_failed"] += 1
                return False

            # Apply message filters
            if not self._apply_message_filters(message):
                logger.debug(f"Message filtered out: {message.get('message_id', 'unknown')}")
                return False

            # Apply message transformations
            transformed_message = self._apply_message_transformations(message)

            # Route message based on routing rules
            routed_successfully = self._route_message(transformed_message, stream_type)

            # Update metrics
            if routed_successfully:
                self.metrics["messages_processed"] += 1
                self.metrics["messages_routed"] += 1

                # Update latency
                latency = time.time() - start_time
                self._update_latency_metric(latency)
            else:
                self.metrics["messages_failed"] += 1

            return routed_successfully

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            self.metrics["messages_failed"] += 1
            return False

    def broadcast_message(self, message: Dict[str, Any], stream_type: StreamType = StreamType.AGENT_MESSAGES) -> int:
        """Broadcast a message to all agents with enhanced features"""
        try:
            # Validate message
            if not self.message_protocols.validate_message(message):
                logger.error(f"Invalid broadcast message format: {message}")
                return 0

            # Apply filters and transformations
            if not self._apply_message_filters(message):
                return 0

            transformed_message = self._apply_message_transformations(message)

            # Broadcast via Redis
            count = self.redis_manager.broadcast_message(transformed_message, stream_type)

            if count > 0:
                self.metrics["messages_processed"] += 1
                self.metrics["messages_routed"] += count

            return count

        except Exception as e:
            logger.error(f"Error broadcasting message: {e}")
            return 0
    
    def register_message_handler(self, message_type: str, handler: Callable[[Dict[str, Any]], None]):
        """Register a handler for a specific message type"""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)

    # Helper methods for enhanced functionality

    def _route_message(self, message: Dict[str, Any], stream_type: StreamType) -> bool:
        """Route message based on routing rules and load balancing"""
        recipient_id = message.get('recipient_id')
        message_type = message.get('message_type', '')
        sender_id = message.get('sender_id', '')

        # Check for specific routing rules
        applicable_routes = []
        for route_id, route in self.message_routes.items():
            if (self._matches_pattern(sender_id, route.sender_id) and
                self._matches_pattern(recipient_id, route.recipient_id) and
                (not route.message_type or route.message_type == message_type)):
                applicable_routes.append(route)

        # Sort by priority
        applicable_routes.sort(key=lambda r: r.route_priority, reverse=True)

        if applicable_routes:
            # Use the highest priority route
            route = applicable_routes[0]
            return self._execute_route(message, route, stream_type)

        # Default routing
        if recipient_id == '*':
            # Broadcast message
            return self.redis_manager.broadcast_message(message, stream_type) > 0
        elif recipient_id in self.active_agents:
            # Direct message to active agent
            return self.redis_manager.send_message(message, stream_type)
        else:
            # Check load balancer groups
            for group_name, agent_ids in self.load_balancer_groups.items():
                if recipient_id == group_name:
                    selected_agent = self._select_agent_from_group(group_name, agent_ids)
                    if selected_agent:
                        message['recipient_id'] = selected_agent
                        return self.redis_manager.send_message(message, stream_type)

            logger.warning(f"No route found for message to {recipient_id}")
            return False

    def _execute_route(self, message: Dict[str, Any], route: MessageRoute, stream_type: StreamType) -> bool:
        """Execute a specific routing rule"""
        if route.load_balancing:
            # Use load balancing
            group_agents = self.load_balancer_groups.get(route.recipient_id, [])
            if group_agents:
                selected_agent = self._select_agent_from_group(route.recipient_id, group_agents)
                if selected_agent:
                    message['recipient_id'] = selected_agent
                    return self.redis_manager.send_message(message, stream_type)

        # Standard routing
        return self.redis_manager.send_message(message, stream_type)

    def _select_agent_from_group(self, group_name: str, agent_ids: List[str]) -> Optional[str]:
        """Select an agent from a load balancer group using round-robin"""
        active_agents = [aid for aid in agent_ids if aid in self.active_agents]

        if not active_agents:
            return None

        # Round-robin selection
        counter = self.round_robin_counters[group_name]
        selected_agent = active_agents[counter % len(active_agents)]
        self.round_robin_counters[group_name] = (counter + 1) % len(active_agents)

        return selected_agent

    def _matches_pattern(self, value: str, pattern: str) -> bool:
        """Check if a value matches a pattern (supports wildcards)"""
        if pattern == '*':
            return True
        if pattern == value:
            return True
        # Add more pattern matching logic as needed
        return False

    def _apply_message_filters(self, message: Dict[str, Any]) -> bool:
        """Apply registered message filters"""
        for filter_name, filter_func in self.message_filters.items():
            try:
                if not filter_func(message):
                    logger.debug(f"Message filtered by {filter_name}")
                    return False
            except Exception as e:
                logger.error(f"Error in message filter {filter_name}: {e}")

        return True

    def _apply_message_transformations(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Apply registered message transformations"""
        transformed_message = message.copy()

        for transformer_name, transformer_func in self.message_transformers.items():
            try:
                transformed_message = transformer_func(transformed_message)
            except Exception as e:
                logger.error(f"Error in message transformer {transformer_name}: {e}")

        return transformed_message

    def _update_latency_metric(self, latency: float):
        """Update average latency metric"""
        current_avg = self.metrics["average_latency"]
        total_messages = self.metrics["messages_processed"]

        if total_messages > 0:
            self.metrics["average_latency"] = (
                (current_avg * (total_messages - 1) + latency) / total_messages
            )

    # Workflow integration methods

    async def create_workflow(self, name: str, description: str, tasks: List[Dict[str, Any]],
                            context: Dict[str, Any] = None) -> Optional[str]:
        """Create a workflow using the temporal orchestrator"""
        if not self.temporal_orchestrator:
            logger.error("Temporal orchestrator not available")
            return None

        try:
            workflow_id = await self.temporal_orchestrator.create_workflow(
                name, description, tasks, context
            )

            # Send workflow event
            self.redis_manager.send_workflow_event(
                workflow_id, "workflow_created", data={"name": name, "description": description}
            )

            self.metrics["workflows_executed"] += 1
            return workflow_id

        except Exception as e:
            logger.error(f"Failed to create workflow: {e}")
            return None

    async def execute_workflow(self, workflow_id: str) -> bool:
        """Execute a workflow"""
        if not self.temporal_orchestrator:
            return False

        try:
            success = await self.temporal_orchestrator.execute_workflow(workflow_id)

            if success:
                self.redis_manager.send_workflow_event(
                    workflow_id, "workflow_started"
                )

            return success

        except Exception as e:
            logger.error(f"Failed to execute workflow {workflow_id}: {e}")
            return False

    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow status"""
        if not self.temporal_orchestrator:
            return None

        return self.temporal_orchestrator.get_workflow_status(workflow_id)

    # Coordination event handlers

    def _handle_agent_join(self, agent_id: str, data: Dict[str, Any]):
        """Handle agent join coordination event"""
        logger.info(f"Agent {agent_id} joined with capabilities: {data.get('capabilities', [])}")

        # Update load balancer groups if needed
        capabilities = data.get('capabilities', [])
        for capability in capabilities:
            if capability in self.load_balancer_groups:
                if agent_id not in self.load_balancer_groups[capability]:
                    self.load_balancer_groups[capability].append(agent_id)

    def _handle_agent_leave(self, agent_id: str, data: Dict[str, Any]):
        """Handle agent leave coordination event"""
        logger.info(f"Agent {agent_id} left: {data.get('reason', 'unknown')}")

        # Remove from load balancer groups
        for group_name, agent_ids in self.load_balancer_groups.items():
            if agent_id in agent_ids:
                agent_ids.remove(agent_id)

        # Remove from active agents if still there
        if agent_id in self.active_agents:
            del self.active_agents[agent_id]

    def _handle_load_balance_request(self, agent_id: str, data: Dict[str, Any]):
        """Handle load balance request"""
        group_name = data.get('group_name')
        if group_name and group_name in self.load_balancer_groups:
            selected_agent = self._select_agent_from_group(
                group_name, self.load_balancer_groups[group_name]
            )

            # Send response back to requesting agent
            response_message = {
                "message_id": str(uuid.uuid4()),
                "sender_id": "message_broker",
                "recipient_id": agent_id,
                "message_type": "load_balance_response",
                "payload": {
                    "selected_agent": selected_agent,
                    "group_name": group_name
                },
                "timestamp": datetime.now().isoformat()
            }

            self.send_message(response_message)

    # Enhanced processing methods

    def _process_priority_messages(self):
        """Process high-priority messages in a separate thread"""
        while self.running:
            try:
                # Process messages from priority queues
                for priority in [Priority.CRITICAL, Priority.HIGH]:
                    queue = self.priority_queues[priority]
                    if queue:
                        message = queue.popleft()
                        self._process_single_message(message)

                time.sleep(0.1)  # Small delay to prevent busy waiting

            except Exception as e:
                logger.error(f"Error in priority message processing: {e}")
                time.sleep(1)

    def _monitor_agent_heartbeats(self):
        """Monitor agent heartbeats and update connection status"""
        while self.running:
            try:
                current_time = datetime.now()
                timeout_threshold = timedelta(minutes=2)

                for agent_id, connection in list(self.active_agents.items()):
                    if (current_time - connection.last_heartbeat) > timeout_threshold:
                        logger.warning(f"Agent {agent_id} heartbeat timeout")
                        connection.status = "timeout"

                        # Send coordination event
                        self.redis_manager.send_coordination_event(
                            "agent_timeout", agent_id, {
                                "last_heartbeat": connection.last_heartbeat.isoformat(),
                                "timeout_duration": str(timeout_threshold)
                            }
                        )

                # Update error rate metric
                total_agents = len(self.active_agents)
                error_agents = len([c for c in self.active_agents.values() if c.status != "active"])
                self.metrics["error_rate"] = error_agents / max(total_agents, 1)

                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Error in heartbeat monitoring: {e}")
                time.sleep(30)

    def update_agent_heartbeat(self, agent_id: str):
        """Update agent heartbeat timestamp"""
        if agent_id in self.active_agents:
            self.active_agents[agent_id].last_heartbeat = datetime.now()
            self.active_agents[agent_id].status = "active"

            # Send heartbeat to Redis
            self.redis_manager.send_heartbeat(agent_id, "active")

    def get_broker_metrics(self) -> Dict[str, Any]:
        """Get comprehensive broker metrics"""
        return {
            "status": self.status.value,
            "metrics": self.metrics.copy(),
            "active_agents": len(self.active_agents),
            "message_routes": len(self.message_routes),
            "load_balancer_groups": {
                group: len(agents) for group, agents in self.load_balancer_groups.items()
            },
            "processing_threads": len(self.processing_threads),
            "redis_metrics": self.redis_manager.get_stream_metrics(),
            "temporal_metrics": (
                self.temporal_orchestrator.get_orchestrator_metrics()
                if self.temporal_orchestrator else None
            )
        }

    async def shutdown(self):
        """Gracefully shutdown the message broker"""
        logger.info("Shutting down Enhanced Message Broker...")

        self.status = BrokerStatus.STOPPING
        self.running = False

        # Stop temporal orchestrator
        if self.temporal_orchestrator:
            await self.temporal_orchestrator.stop()

        # Wait for processing threads to finish
        for thread in self.processing_threads:
            if thread.is_alive():
                thread.join(timeout=5.0)

        # Close Redis connections
        self.redis_manager.close()

        self.status = BrokerStatus.STOPPED
        logger.info("Enhanced Message Broker shutdown complete")

    def _process_single_message(self, message: Dict[str, Any]):
        """Process a single message (used by priority processing)"""
        try:
            # Apply filters and transformations
            if not self._apply_message_filters(message):
                return

            transformed_message = self._apply_message_transformations(message)

            # Route the message
            self._route_message(transformed_message, StreamType.AGENT_MESSAGES)

        except Exception as e:
            logger.error(f"Error processing single message: {e}")

# For backward compatibility
MessageBroker = EnhancedMessageBroker