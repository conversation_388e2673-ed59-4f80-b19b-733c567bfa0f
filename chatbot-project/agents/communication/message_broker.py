import sys
import os
import json
import uuid
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

# Import the message protocols
from .message_protocols import MessageType, Priority, AgentMessage, MessageProtocols
from .redis_stream_manager import RedisStreamManager

class MessageBroker:
    def __init__(self, redis_host='localhost', redis_port=6379):
        self.message_protocols = MessageProtocols()
        self.redis_manager = RedisStreamManager(host=redis_host, port=redis_port)
        self.agent_registry = None
        self.message_handlers = {}
        self.active_agents = {}
        self.running = False
        self.processing_thread = None
    
    def initialize(self, agent_registry=None):
        """Initialize the message broker"""
        self.agent_registry = agent_registry
        success = self.redis_manager.initialize()
        
        if success:
            self.running = True
            self.processing_thread = threading.Thread(
                target=self._process_messages_loop,
                daemon=True
            )
            self.processing_thread.start()
        
        return success
    
    def register_agent(self, agent_id: str, agent_instance):
        """Register an agent with the broker"""
        self.active_agents[agent_id] = agent_instance
        # Subscribe to Redis stream for this agent
        self.redis_manager.subscribe(agent_id)
    
    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the broker"""
        if agent_id in self.active_agents:
            del self.active_agents[agent_id]
        # Unsubscribe from Redis stream
        self.redis_manager.unsubscribe(agent_id)
    
    def send_message(self, message: Dict[str, Any]) -> bool:
        """Send a message to another agent"""
        # Validate message
        if not self._validate_message(message):
            return False
        
        # Add to Redis stream
        return self.redis_manager.send_message(message)
    
    def broadcast_message(self, message: Dict[str, Any]) -> int:
        """Broadcast a message to all agents"""
        # Validate message
        if not self._validate_message(message):
            return 0
        
        # Broadcast via Redis
        return self.redis_manager.broadcast_message(message)
    
    def register_message_handler(self, message_type: str, handler: Callable[[Dict[str, Any]], None]):
        """Register a handler for a specific message type"""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)
    
    def subscribe(self, agent_id: str, message_types: Optional[List[str]] = None):
        """Subscribe an agent to receive messages"""
        self.redis_manager.subscribe(agent_id, message_types)
    
    def unsubscribe(self, agent_id: str):
        """Unsubscribe an agent from receiving messages"""
        self.redis_manager.unsubscribe(agent_id)
    
    def get_messages_for_agent(self, agent_id: str) -> List[Dict[str, Any]]:
        """Get all pending messages for an agent"""
        return self.redis_manager.get_messages_for_agent(agent_id)
    
    def _process_messages_loop(self):
        """Background thread to process messages"""
        while self.running:
            try:
                # Process messages for each active agent
                for agent_id, agent in list(self.active_agents.items()):
                    messages = self.get_messages_for_agent(agent_id)
                    
                    for message in messages:
                        self._deliver_message_to_agent(message, agent)
                
                # Sleep to avoid busy waiting
                time.sleep(0.1)
            except Exception as e:
                print(f"Error in message processing loop: {str(e)}")
                time.sleep(1)  # Longer sleep on error
    
    def _deliver_message_to_agent(self, message: Dict[str, Any], agent):
        """Deliver a message to an agent"""
        try:
            # Convert dict to AgentMessage
            agent_message = AgentMessage(
                message_id=message.get("message_id", str(uuid.uuid4())),
                sender_id=message.get("sender_id", "system"),
                recipient_id=message.get("recipient_id", agent.agent_id),
                message_type=MessageType(message.get("message_type", "request")),
                priority=Priority(message.get("priority", 2)),
                payload=message.get("payload", {}),
                timestamp=message.get("timestamp", datetime.now().isoformat()),
                correlation_id=message.get("correlation_id"),
                reply_to=message.get("reply_to"),
                ttl=message.get("ttl")
            )
            
            # Deliver to agent
            if hasattr(agent, 'receive_message'):
                agent.receive_message(agent_message)
            elif hasattr(agent, 'process_message'):
                agent.process_message(agent_message)
            
            # Call registered handlers
            message_type = message.get("message_type")
            if message_type in self.message_handlers:
                for handler in self.message_handlers[message_type]:
                    handler(message)
        except Exception as e:
            print(f"Error delivering message to agent: {str(e)}")
    
    def _validate_message(self, message: Dict[str, Any]) -> bool:
        """Validate message structure"""
        required_fields = ["sender_id", "recipient_id", "message_type", "payload"]
        
        for field in required_fields:
            if field not in message:
                return False
        
        # Add message_id if not present
        if "message_id" not in message:
            message["message_id"] = str(uuid.uuid4())
        
        # Add timestamp if not present
        if "timestamp" not in message:
            message["timestamp"] = datetime.now().isoformat()
        
        return True
    
    def close(self):
        """Close the message broker"""
        self.running = False
        
        # Wait for processing thread to terminate
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)
        
        # Close Redis manager
        self.redis_manager.close()