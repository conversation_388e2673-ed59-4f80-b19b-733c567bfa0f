import grpc
import json
import threading
import time
from typing import Dict, List, Any, Callable, Optional

# Import the generated gRPC code
import agent_communication_pb2
import agent_communication_pb2_grpc

class AgentCommunicationClient:
    def __init__(self, server_address='localhost:50051'):
        self.server_address = server_address
        self.channel = grpc.insecure_channel(server_address)
        self.stub = agent_communication_pb2_grpc.AgentCommunicationStub(self.channel)
        self.message_handlers = {}
        self.stream_thread = None
        self.running = False
    
    def send_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Send a message to another agent"""
        try:
            # Convert dict to protobuf message
            proto_msg = agent_communication_pb2.Message(
                message_id=message["message_id"],
                sender_id=message["sender_id"],
                recipient_id=message["recipient_id"],
                message_type=message["message_type"],
                priority=message["priority"],
                payload=json.dumps(message["payload"]),
                timestamp=message["timestamp"],
                correlation_id=message.get("correlation_id", ""),
                reply_to=message.get("reply_to", ""),
                ttl=message.get("ttl", 0)
            )
            
            # Send message via gRPC
            response = self.stub.SendMessage(proto_msg)
            
            return {
                "status": response.status,
                "message_id": response.message_id,
                "error": response.error
            }
        except Exception as e:
            return {
                "status": "error",
                "message_id": message.get("message_id", ""),
                "error": str(e)
            }
    
    def broadcast_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Broadcast a message to all agents"""
        try:
            # Convert dict to protobuf message
            proto_msg = agent_communication_pb2.Message(
                message_id=message["message_id"],
                sender_id=message["sender_id"],
                recipient_id="*",  # Broadcast
                message_type=message["message_type"],
                priority=message["priority"],
                payload=json.dumps(message["payload"]),
                timestamp=message["timestamp"],
                correlation_id=message.get("correlation_id", ""),
                reply_to=message.get("reply_to", ""),
                ttl=message.get("ttl", 0)
            )
            
            # Send broadcast via gRPC
            response = self.stub.BroadcastMessage(proto_msg)
            
            return {
                "status": response.status,
                "recipient_count": response.recipient_count,
                "error": response.error
            }
        except Exception as e:
            return {
                "status": "error",
                "recipient_count": 0,
                "error": str(e)
            }
    
    def register_message_handler(self, message_type: str, handler: Callable[[Dict[str, Any]], None]):
        """Register a handler for a specific message type"""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)
    
    def start_message_stream(self, agent_id: str, message_types: Optional[List[str]] = None):
        """Start streaming messages for this agent"""
        if self.stream_thread and self.stream_thread.is_alive():
            return False  # Already running
        
        self.running = True
        self.stream_thread = threading.Thread(
            target=self._stream_messages,
            args=(agent_id, message_types),
            daemon=True
        )
        self.stream_thread.start()
        return True
    
    def stop_message_stream(self):
        """Stop streaming messages"""
        self.running = False
        if self.stream_thread:
            self.stream_thread.join(timeout=2.0)
    
    def _stream_messages(self, agent_id: str, message_types: Optional[List[str]]):
        """Stream messages from the server and process them"""
        try:
            # Create stream request
            request = agent_communication_pb2.StreamRequest(
                agent_id=agent_id,
                message_types=message_types or []
            )
            
            # Start streaming
            for message in self.stub.StreamMessages(request):
                if not self.running:
                    break
                
                # Convert protobuf message to dict
                msg_dict = {
                    "message_id": message.message_id,
                    "sender_id": message.sender_id,
                    "recipient_id": message.recipient_id,
                    "message_type": message.message_type,
                    "priority": message.priority,
                    "payload": json.loads(message.payload),
                    "timestamp": message.timestamp,
                    "correlation_id": message.correlation_id,
                    "reply_to": message.reply_to,
                    "ttl": message.ttl
                }
                
                # Process message with registered handlers
                self._process_message(msg_dict)
        except Exception as e:
            print(f"Stream error: {str(e)}")
            # Try to reconnect after a delay if still running
            if self.running:
                time.sleep(5)
                self._stream_messages(agent_id, message_types)
    
    def _process_message(self, message: Dict[str, Any]):
        """Process a received message with registered handlers"""
        message_type = message.get("message_type")
        
        # Call handlers for this message type
        if message_type in self.message_handlers:
            for handler in self.message_handlers[message_type]:
                try:
                    handler(message)
                except Exception as e:
                    print(f"Error in message handler: {str(e)}")
        
        # Call handlers for all messages
        if "*" in self.message_handlers:
            for handler in self.message_handlers["*"]:
                try:
                    handler(message)
                except Exception as e:
                    print(f"Error in wildcard message handler: {str(e)}")
    
    def close(self):
        """Close the gRPC channel"""
        self.stop_message_stream()
        self.channel.close()