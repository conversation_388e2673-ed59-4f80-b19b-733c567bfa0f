"""
Enhanced Agent-to-Agent Communication Protocols.
Provides advanced communication patterns, negotiation, and coordination protocols.
"""

import asyncio
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class CommunicationPattern(Enum):
    REQUEST_RESPONSE = "request_response"
    PUBLISH_SUBSCRIBE = "publish_subscribe"
    PEER_TO_PEER = "peer_to_peer"
    BROADCAST = "broadcast"
    MULTICAST = "multicast"
    PIPELINE = "pipeline"
    SCATTER_GATHER = "scatter_gather"
    CONSENSUS = "consensus"
    NEGOTIATION = "negotiation"
    AUCTION = "auction"

class ProtocolState(Enum):
    INITIALIZING = "initializing"
    ACTIVE = "active"
    NEGOTIATING = "negotiating"
    WAITING = "waiting"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

class MessageSemantic(Enum):
    COMMAND = "command"
    QUERY = "query"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    PROPOSAL = "proposal"
    ACCEPTANCE = "acceptance"
    REJECTION = "rejection"
    NEGOTIATION = "negotiation"
    HEARTBEAT = "heartbeat"
    STATUS = "status"

@dataclass
class CommunicationSession:
    session_id: str
    pattern: CommunicationPattern
    participants: Set[str]
    initiator: str
    state: ProtocolState = ProtocolState.INITIALIZING
    created_at: datetime = field(default_factory=datetime.now)
    timeout_seconds: int = 300
    context: Dict[str, Any] = field(default_factory=dict)
    messages: List[Dict[str, Any]] = field(default_factory=list)
    results: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NegotiationRound:
    round_id: str
    session_id: str
    proposals: Dict[str, Any] = field(default_factory=dict)
    responses: Dict[str, Any] = field(default_factory=dict)
    consensus_reached: bool = False
    round_number: int = 1

class AgentCommunicationProtocols:
    """
    Enhanced Agent-to-Agent Communication Protocols.
    Implements various communication patterns and coordination mechanisms.
    """
    
    def __init__(self, message_broker=None, agent_id: str = None):
        self.message_broker = message_broker
        self.agent_id = agent_id or "protocol_manager"
        
        # Session management
        self.active_sessions: Dict[str, CommunicationSession] = {}
        self.session_handlers: Dict[CommunicationPattern, Callable] = {}
        self.negotiation_rounds: Dict[str, List[NegotiationRound]] = {}
        
        # Protocol configuration
        self.protocol_configs: Dict[CommunicationPattern, Dict[str, Any]] = {}
        self.message_handlers: Dict[MessageSemantic, Callable] = {}
        
        # Performance metrics
        self.metrics = {
            "sessions_created": 0,
            "sessions_completed": 0,
            "sessions_failed": 0,
            "negotiations_successful": 0,
            "negotiations_failed": 0,
            "average_session_duration": 0.0,
            "message_throughput": 0.0
        }
        
        # Register default handlers
        self._register_default_handlers()
        
        logger.info(f"Agent Communication Protocols initialized for {self.agent_id}")
    
    def _register_default_handlers(self):
        """Register default protocol handlers."""
        self.session_handlers[CommunicationPattern.REQUEST_RESPONSE] = self._handle_request_response
        self.session_handlers[CommunicationPattern.PUBLISH_SUBSCRIBE] = self._handle_publish_subscribe
        self.session_handlers[CommunicationPattern.PEER_TO_PEER] = self._handle_peer_to_peer
        self.session_handlers[CommunicationPattern.BROADCAST] = self._handle_broadcast
        self.session_handlers[CommunicationPattern.SCATTER_GATHER] = self._handle_scatter_gather
        self.session_handlers[CommunicationPattern.CONSENSUS] = self._handle_consensus
        self.session_handlers[CommunicationPattern.NEGOTIATION] = self._handle_negotiation
        self.session_handlers[CommunicationPattern.AUCTION] = self._handle_auction
        
        # Message semantic handlers
        self.message_handlers[MessageSemantic.COMMAND] = self._handle_command_message
        self.message_handlers[MessageSemantic.QUERY] = self._handle_query_message
        self.message_handlers[MessageSemantic.RESPONSE] = self._handle_response_message
        self.message_handlers[MessageSemantic.PROPOSAL] = self._handle_proposal_message
        self.message_handlers[MessageSemantic.NEGOTIATION] = self._handle_negotiation_message
    
    async def create_session(self, pattern: CommunicationPattern, participants: List[str],
                           context: Dict[str, Any] = None, timeout_seconds: int = 300) -> str:
        """Create a new communication session."""
        session_id = str(uuid.uuid4())
        
        session = CommunicationSession(
            session_id=session_id,
            pattern=pattern,
            participants=set(participants),
            initiator=self.agent_id,
            timeout_seconds=timeout_seconds,
            context=context or {}
        )
        
        self.active_sessions[session_id] = session
        self.metrics["sessions_created"] += 1
        
        logger.info(f"Created {pattern.value} session {session_id} with {len(participants)} participants")
        
        # Start session handler
        asyncio.create_task(self._execute_session(session))
        
        return session_id
    
    async def _execute_session(self, session: CommunicationSession):
        """Execute a communication session."""
        try:
            session.state = ProtocolState.ACTIVE
            
            # Get session handler
            handler = self.session_handlers.get(session.pattern)
            if not handler:
                raise ValueError(f"No handler for pattern {session.pattern}")
            
            # Execute with timeout
            start_time = time.time()
            
            result = await asyncio.wait_for(
                handler(session),
                timeout=session.timeout_seconds
            )
            
            # Update session
            session.state = ProtocolState.COMPLETED
            session.results = result or {}
            
            # Update metrics
            duration = time.time() - start_time
            self._update_session_metrics(duration, True)
            
            logger.info(f"Session {session.session_id} completed successfully in {duration:.2f}s")
            
        except asyncio.TimeoutError:
            session.state = ProtocolState.TIMEOUT
            self.metrics["sessions_failed"] += 1
            logger.error(f"Session {session.session_id} timed out")
            
        except Exception as e:
            session.state = ProtocolState.FAILED
            self.metrics["sessions_failed"] += 1
            logger.error(f"Session {session.session_id} failed: {e}")
        
        finally:
            # Cleanup session after delay
            await asyncio.sleep(60)  # Keep for 1 minute for debugging
            if session.session_id in self.active_sessions:
                del self.active_sessions[session.session_id]
    
    async def send_message(self, session_id: str, recipient: str, semantic: MessageSemantic,
                         payload: Dict[str, Any], correlation_id: str = None) -> bool:
        """Send a message within a communication session."""
        if session_id not in self.active_sessions:
            logger.error(f"Session {session_id} not found")
            return False
        
        session = self.active_sessions[session_id]
        
        message = {
            "message_id": str(uuid.uuid4()),
            "session_id": session_id,
            "sender_id": self.agent_id,
            "recipient_id": recipient,
            "message_type": "protocol_message",
            "semantic": semantic.value,
            "payload": payload,
            "timestamp": datetime.now().isoformat(),
            "correlation_id": correlation_id or session_id
        }
        
        # Add to session messages
        session.messages.append(message)
        
        # Send via message broker
        if self.message_broker:
            return self.message_broker.send_message(message)
        
        return True
    
    async def broadcast_to_session(self, session_id: str, semantic: MessageSemantic,
                                 payload: Dict[str, Any]) -> int:
        """Broadcast a message to all participants in a session."""
        if session_id not in self.active_sessions:
            return 0
        
        session = self.active_sessions[session_id]
        sent_count = 0
        
        for participant in session.participants:
            if participant != self.agent_id:  # Don't send to self
                success = await self.send_message(
                    session_id, participant, semantic, payload
                )
                if success:
                    sent_count += 1
        
        return sent_count
    
    # Protocol Pattern Handlers
    
    async def _handle_request_response(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle request-response communication pattern."""
        if len(session.participants) != 2:
            raise ValueError("Request-response requires exactly 2 participants")
        
        # Find the other participant
        other_participant = next(p for p in session.participants if p != self.agent_id)
        
        # Send request
        request_payload = session.context.get("request", {})
        await self.send_message(
            session.session_id, other_participant, 
            MessageSemantic.QUERY, request_payload
        )
        
        # Wait for response
        response = await self._wait_for_response(session.session_id, timeout=30)
        
        return {"response": response}
    
    async def _handle_publish_subscribe(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle publish-subscribe communication pattern."""
        topic = session.context.get("topic", "default")
        message_payload = session.context.get("message", {})
        
        # Broadcast to all subscribers
        sent_count = await self.broadcast_to_session(
            session.session_id, MessageSemantic.NOTIFICATION, {
                "topic": topic,
                "message": message_payload
            }
        )
        
        return {"topic": topic, "subscribers_notified": sent_count}
    
    async def _handle_peer_to_peer(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle peer-to-peer communication pattern."""
        # Enable direct communication between all participants
        connections_established = 0
        
        for participant in session.participants:
            if participant != self.agent_id:
                await self.send_message(
                    session.session_id, participant,
                    MessageSemantic.NOTIFICATION, {
                        "action": "establish_p2p_connection",
                        "peers": list(session.participants - {participant})
                    }
                )
                connections_established += 1
        
        return {"connections_established": connections_established}
    
    async def _handle_broadcast(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle broadcast communication pattern."""
        message_payload = session.context.get("message", {})
        
        sent_count = await self.broadcast_to_session(
            session.session_id, MessageSemantic.NOTIFICATION, message_payload
        )
        
        return {"recipients": sent_count}
    
    async def _handle_scatter_gather(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle scatter-gather communication pattern."""
        task_payload = session.context.get("task", {})
        
        # Scatter: Send task to all participants
        await self.broadcast_to_session(
            session.session_id, MessageSemantic.COMMAND, {
                "action": "execute_task",
                "task": task_payload
            }
        )
        
        # Gather: Wait for responses from all participants
        responses = await self._gather_responses(
            session.session_id, 
            expected_count=len(session.participants) - 1,  # Exclude self
            timeout=60
        )
        
        return {"task": task_payload, "responses": responses}
    
    async def _handle_consensus(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle consensus communication pattern."""
        proposal = session.context.get("proposal", {})
        consensus_threshold = session.context.get("threshold", 0.67)  # 67% agreement
        
        # Send proposal to all participants
        await self.broadcast_to_session(
            session.session_id, MessageSemantic.PROPOSAL, proposal
        )
        
        # Collect votes
        votes = await self._collect_votes(
            session.session_id,
            expected_count=len(session.participants) - 1,
            timeout=120
        )
        
        # Calculate consensus
        total_votes = len(votes)
        positive_votes = sum(1 for vote in votes.values() if vote.get("decision") == "accept")
        consensus_ratio = positive_votes / max(total_votes, 1)
        consensus_reached = consensus_ratio >= consensus_threshold
        
        if consensus_reached:
            self.metrics["negotiations_successful"] += 1
        else:
            self.metrics["negotiations_failed"] += 1
        
        return {
            "proposal": proposal,
            "votes": votes,
            "consensus_reached": consensus_reached,
            "consensus_ratio": consensus_ratio
        }
    
    async def _handle_negotiation(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle negotiation communication pattern."""
        max_rounds = session.context.get("max_rounds", 5)
        initial_proposal = session.context.get("initial_proposal", {})
        
        self.negotiation_rounds[session.session_id] = []
        
        current_proposal = initial_proposal
        
        for round_num in range(1, max_rounds + 1):
            round_id = f"{session.session_id}_round_{round_num}"
            
            negotiation_round = NegotiationRound(
                round_id=round_id,
                session_id=session.session_id,
                round_number=round_num
            )
            
            # Send current proposal
            await self.broadcast_to_session(
                session.session_id, MessageSemantic.PROPOSAL, {
                    "round": round_num,
                    "proposal": current_proposal
                }
            )
            
            # Collect responses
            responses = await self._gather_responses(
                session.session_id,
                expected_count=len(session.participants) - 1,
                timeout=60
            )
            
            negotiation_round.responses = responses
            
            # Check for consensus
            acceptances = [r for r in responses.values() if r.get("decision") == "accept"]
            
            if len(acceptances) == len(responses):
                # Full consensus reached
                negotiation_round.consensus_reached = True
                self.negotiation_rounds[session.session_id].append(negotiation_round)
                self.metrics["negotiations_successful"] += 1
                
                return {
                    "final_proposal": current_proposal,
                    "rounds": round_num,
                    "consensus_reached": True,
                    "negotiation_history": self.negotiation_rounds[session.session_id]
                }
            
            # Modify proposal based on feedback
            current_proposal = self._modify_proposal(current_proposal, responses)
            self.negotiation_rounds[session.session_id].append(negotiation_round)
        
        # No consensus reached within max rounds
        self.metrics["negotiations_failed"] += 1
        
        return {
            "final_proposal": current_proposal,
            "rounds": max_rounds,
            "consensus_reached": False,
            "negotiation_history": self.negotiation_rounds[session.session_id]
        }
    
    async def _handle_auction(self, session: CommunicationSession) -> Dict[str, Any]:
        """Handle auction communication pattern."""
        auction_item = session.context.get("item", {})
        auction_duration = session.context.get("duration", 60)  # seconds
        
        # Start auction
        await self.broadcast_to_session(
            session.session_id, MessageSemantic.NOTIFICATION, {
                "action": "auction_start",
                "item": auction_item,
                "duration": auction_duration
            }
        )
        
        # Collect bids during auction period
        bids = await self._collect_bids(session.session_id, auction_duration)
        
        # Determine winner
        if bids:
            winning_bid = max(bids.values(), key=lambda b: b.get("amount", 0))
            winner = next(agent for agent, bid in bids.items() if bid == winning_bid)
            
            # Notify winner
            await self.send_message(
                session.session_id, winner,
                MessageSemantic.NOTIFICATION, {
                    "action": "auction_won",
                    "item": auction_item,
                    "winning_bid": winning_bid
                }
            )
            
            return {
                "item": auction_item,
                "winner": winner,
                "winning_bid": winning_bid,
                "all_bids": bids
            }
        
        return {"item": auction_item, "winner": None, "bids": bids}

    # Helper Methods

    async def _wait_for_response(self, session_id: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """Wait for a single response in a session."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            session = self.active_sessions.get(session_id)
            if not session:
                return None

            # Check for response messages
            for message in session.messages:
                if (message.get("semantic") == MessageSemantic.RESPONSE.value and
                    message.get("sender_id") != self.agent_id):
                    return message.get("payload")

            await asyncio.sleep(0.1)

        return None

    async def _gather_responses(self, session_id: str, expected_count: int,
                              timeout: int = 60) -> Dict[str, Any]:
        """Gather responses from multiple participants."""
        responses = {}
        start_time = time.time()

        while len(responses) < expected_count and time.time() - start_time < timeout:
            session = self.active_sessions.get(session_id)
            if not session:
                break

            # Check for new response messages
            for message in session.messages:
                sender = message.get("sender_id")
                if (message.get("semantic") == MessageSemantic.RESPONSE.value and
                    sender != self.agent_id and sender not in responses):
                    responses[sender] = message.get("payload", {})

            await asyncio.sleep(0.1)

        return responses

    async def _collect_votes(self, session_id: str, expected_count: int,
                           timeout: int = 120) -> Dict[str, Any]:
        """Collect votes for consensus."""
        votes = {}
        start_time = time.time()

        while len(votes) < expected_count and time.time() - start_time < timeout:
            session = self.active_sessions.get(session_id)
            if not session:
                break

            # Check for vote messages
            for message in session.messages:
                sender = message.get("sender_id")
                if (message.get("semantic") in [MessageSemantic.ACCEPTANCE.value, MessageSemantic.REJECTION.value] and
                    sender != self.agent_id and sender not in votes):
                    votes[sender] = message.get("payload", {})

            await asyncio.sleep(0.1)

        return votes

    async def _collect_bids(self, session_id: str, duration: int) -> Dict[str, Any]:
        """Collect bids during auction period."""
        bids = {}
        start_time = time.time()

        while time.time() - start_time < duration:
            session = self.active_sessions.get(session_id)
            if not session:
                break

            # Check for bid messages
            for message in session.messages:
                sender = message.get("sender_id")
                payload = message.get("payload", {})

                if (message.get("semantic") == MessageSemantic.PROPOSAL.value and
                    sender != self.agent_id and "bid_amount" in payload):
                    # Update bid if higher than previous
                    current_bid = bids.get(sender, {}).get("amount", 0)
                    new_bid = payload.get("bid_amount", 0)

                    if new_bid > current_bid:
                        bids[sender] = {
                            "amount": new_bid,
                            "timestamp": message.get("timestamp"),
                            "details": payload
                        }

            await asyncio.sleep(0.1)

        return bids

    def _modify_proposal(self, proposal: Dict[str, Any], responses: Dict[str, Any]) -> Dict[str, Any]:
        """Modify proposal based on negotiation responses."""
        modified_proposal = proposal.copy()

        # Simple modification strategy - average numerical values
        for agent_id, response in responses.items():
            if response.get("decision") == "reject" and "counter_proposal" in response:
                counter = response["counter_proposal"]

                # Average numerical values
                for key, value in counter.items():
                    if isinstance(value, (int, float)) and key in modified_proposal:
                        current_value = modified_proposal[key]
                        if isinstance(current_value, (int, float)):
                            modified_proposal[key] = (current_value + value) / 2

        return modified_proposal

    # Message Semantic Handlers

    async def _handle_command_message(self, message: Dict[str, Any]):
        """Handle command semantic messages."""
        logger.info(f"Received command: {message.get('payload', {})}")

    async def _handle_query_message(self, message: Dict[str, Any]):
        """Handle query semantic messages."""
        logger.info(f"Received query: {message.get('payload', {})}")

    async def _handle_response_message(self, message: Dict[str, Any]):
        """Handle response semantic messages."""
        logger.info(f"Received response: {message.get('payload', {})}")

    async def _handle_proposal_message(self, message: Dict[str, Any]):
        """Handle proposal semantic messages."""
        logger.info(f"Received proposal: {message.get('payload', {})}")

    async def _handle_negotiation_message(self, message: Dict[str, Any]):
        """Handle negotiation semantic messages."""
        logger.info(f"Received negotiation: {message.get('payload', {})}")

    def _update_session_metrics(self, duration: float, success: bool):
        """Update session performance metrics."""
        if success:
            self.metrics["sessions_completed"] += 1

        # Update average duration
        total_sessions = self.metrics["sessions_completed"] + self.metrics["sessions_failed"]
        current_avg = self.metrics["average_session_duration"]

        if total_sessions > 0:
            self.metrics["average_session_duration"] = (
                (current_avg * (total_sessions - 1) + duration) / total_sessions
            )

    # Public API Methods

    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a communication session."""
        session = self.active_sessions.get(session_id)
        if not session:
            return None

        return {
            "session_id": session.session_id,
            "pattern": session.pattern.value,
            "state": session.state.value,
            "participants": list(session.participants),
            "initiator": session.initiator,
            "created_at": session.created_at.isoformat(),
            "message_count": len(session.messages),
            "context": session.context,
            "results": session.results
        }

    def get_protocol_metrics(self) -> Dict[str, Any]:
        """Get protocol performance metrics."""
        return {
            "metrics": self.metrics.copy(),
            "active_sessions": len(self.active_sessions),
            "supported_patterns": [pattern.value for pattern in CommunicationPattern],
            "supported_semantics": [semantic.value for semantic in MessageSemantic]
        }

    def configure_protocol(self, pattern: CommunicationPattern, config: Dict[str, Any]):
        """Configure a specific communication protocol."""
        self.protocol_configs[pattern] = config
        logger.info(f"Configured protocol {pattern.value} with config: {config}")

    async def process_incoming_message(self, message: Dict[str, Any]):
        """Process an incoming protocol message."""
        try:
            semantic = MessageSemantic(message.get("semantic", "notification"))
            handler = self.message_handlers.get(semantic)

            if handler:
                await handler(message)
            else:
                logger.warning(f"No handler for message semantic: {semantic}")

        except Exception as e:
            logger.error(f"Error processing incoming message: {e}")

    async def shutdown(self):
        """Shutdown the communication protocols."""
        logger.info("Shutting down Agent Communication Protocols...")

        # Cancel all active sessions
        for session_id in list(self.active_sessions.keys()):
            session = self.active_sessions[session_id]
            session.state = ProtocolState.FAILED
            del self.active_sessions[session_id]

        logger.info("Agent Communication Protocols shutdown complete")
