# Agent Communication Framework

This module implements a robust communication framework for the multi-agent system, enabling agents to exchange messages, coordinate tasks, and collaborate effectively.

## Components

### 1. Message Protocols
Defines structured message formats and validation rules for agent communication.

### 2. gRPC Communication
Implements a gRPC server and client for efficient, bidirectional communication between agents.

### 3. Redis Streams Integration
Provides event-driven coordination using Redis Streams for reliable message delivery and persistence.

### 4. Message Broker
Connects the gRPC and Redis components to the agent system, handling message routing and delivery.

## Usage

### Starting the Communication Service

```python
from agents.communication.communication_service import CommunicationService
from agents.core.agent_registry import AgentRegistry

# Create agent registry
agent_registry = AgentRegistry()

# Create and start communication service
comm_service = CommunicationService(
    redis_host='localhost',
    redis_port=6379,
    grpc_port=50051
)
comm_service.start(agent_registry)
```

### Registering Agents

```python
# Create an agent
agent = MyAgent(agent_id="agent1", name="MyA<PERSON>")

# Register with communication service
comm_service.register_agent(agent)

# Register with agent registry
agent_registry.register_agent(agent)

# Initialize agent
await agent.initialize()
```

### Sending Messages

```python
# Send a direct message
await agent.send_message(
    recipient_id="agent2",
    message_type="request",
    payload={"content": "Hello from Agent1"},
    priority=2
)

# Broadcast a message
await agent.broadcast_message(
    message_type="announcement",
    payload={"content": "Important announcement"}
)
```

### Handling Messages

```python
# In your agent class
def __init__(self):
    super().__init__(agent_id="my_agent", name="MyAgent")
    # Register message handlers
    self.register_message_handler("request", self.handle_request)
    self.register_message_handler("announcement", self.handle_announcement)

async def handle_request(self, message):
    # Process request message
    print(f"Received request: {message.payload}")
    
    # Send a response
    await self.send_message(
        message.sender_id,
        "response",
        {"content": "Request processed"},
        correlation_id=message.message_id
    )

async def handle_announcement(self, message):
    # Process announcement
    print(f"Received announcement: {message.payload}")
```

## Running the Example

```bash
# Make sure Redis is installed
python -m pip install redis grpcio grpcio-tools

# Run the example
python examples/agent_communication_example.py
```

## Testing

```bash
# Run the tests
python tests/communication/test_agent_communication.py
```