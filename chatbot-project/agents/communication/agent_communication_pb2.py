# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: agent_communication.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'agent_communication.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19\x61gent_communication.proto\x12\x13\x61gent_communication\"\xc9\x01\n\x07Message\x12\x12\n\nmessage_id\x18\x01 \x01(\t\x12\x11\n\tsender_id\x18\x02 \x01(\t\x12\x14\n\x0crecipient_id\x18\x03 \x01(\t\x12\x14\n\x0cmessage_type\x18\x04 \x01(\t\x12\x10\n\x08priority\x18\x05 \x01(\x05\x12\x0f\n\x07payload\x18\x06 \x01(\t\x12\x11\n\ttimestamp\x18\x07 \x01(\t\x12\x16\n\x0e\x63orrelation_id\x18\x08 \x01(\t\x12\x10\n\x08reply_to\x18\t \x01(\t\x12\x0b\n\x03ttl\x18\n \x01(\x05\"D\n\x0fMessageResponse\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x12\n\nmessage_id\x18\x02 \x01(\t\x12\r\n\x05\x65rror\x18\x03 \x01(\t\"8\n\rStreamRequest\x12\x10\n\x08\x61gent_id\x18\x01 \x01(\t\x12\x15\n\rmessage_types\x18\x02 \x03(\t\"K\n\x11\x42roadcastResponse\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x17\n\x0frecipient_count\x18\x02 \x01(\x05\x12\r\n\x05\x65rror\x18\x03 \x01(\t2\x97\x02\n\x12\x41gentCommunication\x12Q\n\x0bSendMessage\x12\x1c.agent_communication.Message\x1a$.agent_communication.MessageResponse\x12T\n\x0eStreamMessages\x12\".agent_communication.StreamRequest\x1a\x1c.agent_communication.Message0\x01\x12X\n\x10\x42roadcastMessage\x12\x1c.agent_communication.Message\x1a&.agent_communication.BroadcastResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'agent_communication_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_MESSAGE']._serialized_start=51
  _globals['_MESSAGE']._serialized_end=252
  _globals['_MESSAGERESPONSE']._serialized_start=254
  _globals['_MESSAGERESPONSE']._serialized_end=322
  _globals['_STREAMREQUEST']._serialized_start=324
  _globals['_STREAMREQUEST']._serialized_end=380
  _globals['_BROADCASTRESPONSE']._serialized_start=382
  _globals['_BROADCASTRESPONSE']._serialized_end=457
  _globals['_AGENTCOMMUNICATION']._serialized_start=460
  _globals['_AGENTCOMMUNICATION']._serialized_end=739
# @@protoc_insertion_point(module_scope)
