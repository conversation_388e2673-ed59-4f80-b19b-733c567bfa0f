import redis
import json
import threading
import time
import uuid
import asyncio
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class StreamType(Enum):
    AGENT_MESSAGES = "agent_messages"
    COORDINATION = "agent_coordination"
    WORKFLOW = "workflow_events"
    HEARTBEAT = "agent_heartbeat"
    METRICS = "agent_metrics"

@dataclass
class StreamSubscription:
    agent_id: str
    stream_types: Set[StreamType]
    message_types: Set[str] = field(default_factory=set)
    last_processed_id: str = "0"
    consumer_group: str = "default"

class EnhancedRedisStreamManager:
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        self.redis_client = redis.Redis(host=host, port=port, db=db, password=password, decode_responses=True)
        self.async_redis_client = None

        # Stream management
        self.consumer_groups = {}
        self.stream_threads = {}
        self.running = False
        self.agent_subscriptions: Dict[str, StreamSubscription] = {}

        # Coordination features
        self.coordination_handlers: Dict[str, Callable] = {}
        self.workflow_handlers: Dict[str, Callable] = {}
        self.heartbeat_tracking: Dict[str, datetime] = {}

        # Performance metrics
        self.metrics = {
            "messages_sent": 0,
            "messages_received": 0,
            "broadcasts_sent": 0,
            "coordination_events": 0,
            "workflow_events": 0,
            "active_agents": 0,
            "stream_errors": 0
        }
    
    def initialize(self):
        """Initialize Redis streams and consumer groups for all stream types"""
        try:
            # Initialize all stream types
            for stream_type in StreamType:
                stream_name = stream_type.value
                try:
                    self.redis_client.xgroup_create(stream_name, 'agent_group', id='0', mkstream=True)
                    logger.info(f"Created stream and consumer group for {stream_name}")
                except redis.exceptions.ResponseError as e:
                    if 'BUSYGROUP' in str(e):
                        logger.info(f"Consumer group already exists for {stream_name}")
                    else:
                        logger.error(f"Error creating stream {stream_name}: {e}")
                        return False

            # Start coordination monitoring
            self.running = True
            self._start_coordination_monitoring()

            logger.info("Enhanced Redis Stream Manager initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Redis initialization error: {e}")
            return False

    def _start_coordination_monitoring(self):
        """Start background monitoring for coordination events."""
        coordination_thread = threading.Thread(
            target=self._monitor_coordination_events,
            daemon=True
        )
        coordination_thread.start()

        heartbeat_thread = threading.Thread(
            target=self._monitor_heartbeats,
            daemon=True
        )
        heartbeat_thread.start()
    
    def send_message(self, message: Dict[str, Any], stream_type: StreamType = StreamType.AGENT_MESSAGES) -> bool:
        """Send a message to the specified Redis stream"""
        try:
            # Convert complex objects to JSON strings
            message_copy = message.copy()
            if isinstance(message_copy.get('payload'), dict):
                message_copy['payload'] = json.dumps(message_copy['payload'])

            # Add timestamp if not present
            if 'timestamp' not in message_copy:
                message_copy['timestamp'] = datetime.now().isoformat()

            # Add to specified stream
            stream_name = stream_type.value
            self.redis_client.xadd(stream_name, message_copy)

            # Update metrics
            self.metrics["messages_sent"] += 1

            logger.debug(f"Sent message to {stream_name}: {message_copy.get('message_id', 'unknown')}")
            return True

        except Exception as e:
            self.metrics["stream_errors"] += 1
            logger.error(f"Error sending message to Redis stream {stream_type.value}: {e}")
            return False

    def send_coordination_event(self, event_type: str, agent_id: str, data: Dict[str, Any]) -> bool:
        """Send a coordination event to the coordination stream."""
        coordination_message = {
            "message_id": str(uuid.uuid4()),
            "event_type": event_type,
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "data": json.dumps(data)
        }

        success = self.send_message(coordination_message, StreamType.COORDINATION)
        if success:
            self.metrics["coordination_events"] += 1

        return success

    def send_workflow_event(self, workflow_id: str, event_type: str, task_id: str = None,
                          data: Dict[str, Any] = None) -> bool:
        """Send a workflow event to the workflow stream."""
        workflow_message = {
            "message_id": str(uuid.uuid4()),
            "workflow_id": workflow_id,
            "event_type": event_type,
            "task_id": task_id or "",
            "timestamp": datetime.now().isoformat(),
            "data": json.dumps(data or {})
        }

        success = self.send_message(workflow_message, StreamType.WORKFLOW)
        if success:
            self.metrics["workflow_events"] += 1

        return success

    def send_heartbeat(self, agent_id: str, status: str = "active", metadata: Dict[str, Any] = None) -> bool:
        """Send a heartbeat message for an agent."""
        heartbeat_message = {
            "agent_id": agent_id,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "metadata": json.dumps(metadata or {})
        }

        # Update local tracking
        self.heartbeat_tracking[agent_id] = datetime.now()

        return self.send_message(heartbeat_message, StreamType.HEARTBEAT)
    
    def broadcast_message(self, message: Dict[str, Any], stream_type: StreamType = StreamType.AGENT_MESSAGES) -> int:
        """Broadcast a message to all agents"""
        try:
            # Set recipient to broadcast marker
            message_copy = message.copy()
            message_copy['recipient_id'] = '*'

            # Convert complex objects to JSON strings
            if isinstance(message_copy.get('payload'), dict):
                message_copy['payload'] = json.dumps(message_copy['payload'])

            # Add timestamp if not present
            if 'timestamp' not in message_copy:
                message_copy['timestamp'] = datetime.now().isoformat()

            # Add to specified stream
            stream_name = stream_type.value
            self.redis_client.xadd(stream_name, message_copy)

            # Update metrics
            self.metrics["broadcasts_sent"] += 1

            # Return approximate number of recipients (all subscribed agents)
            return len(self.agent_subscriptions)

        except Exception as e:
            self.metrics["stream_errors"] += 1
            logger.error(f"Error broadcasting message to Redis stream {stream_type.value}: {e}")
            return 0

    def _monitor_coordination_events(self):
        """Monitor coordination events in background thread."""
        last_id = '0'

        while self.running:
            try:
                streams = {StreamType.COORDINATION.value: last_id}
                messages = self.redis_client.xread(streams, count=10, block=1000)

                for stream_name, stream_messages in messages:
                    for message_id, message_data in stream_messages:
                        self._handle_coordination_event(message_data)
                        last_id = message_id

            except Exception as e:
                logger.error(f"Error monitoring coordination events: {e}")
                time.sleep(1)

    def _monitor_heartbeats(self):
        """Monitor agent heartbeats and detect inactive agents."""
        while self.running:
            try:
                current_time = datetime.now()
                inactive_agents = []

                for agent_id, last_heartbeat in self.heartbeat_tracking.items():
                    if (current_time - last_heartbeat).total_seconds() > 60:  # 60 seconds timeout
                        inactive_agents.append(agent_id)

                # Handle inactive agents
                for agent_id in inactive_agents:
                    self._handle_agent_timeout(agent_id)
                    del self.heartbeat_tracking[agent_id]

                # Update active agents metric
                self.metrics["active_agents"] = len(self.heartbeat_tracking)

                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Error monitoring heartbeats: {e}")
                time.sleep(30)

    def _handle_coordination_event(self, event_data: Dict[str, Any]):
        """Handle a coordination event."""
        try:
            event_type = event_data.get('event_type')
            agent_id = event_data.get('agent_id')
            data = json.loads(event_data.get('data', '{}'))

            # Call registered handler if available
            if event_type in self.coordination_handlers:
                handler = self.coordination_handlers[event_type]
                handler(agent_id, data)

            logger.debug(f"Processed coordination event {event_type} from {agent_id}")

        except Exception as e:
            logger.error(f"Error handling coordination event: {e}")

    def _handle_agent_timeout(self, agent_id: str):
        """Handle agent timeout."""
        logger.warning(f"Agent {agent_id} timed out - no heartbeat received")

        # Send coordination event about agent timeout
        self.send_coordination_event("agent_timeout", agent_id, {
            "reason": "heartbeat_timeout",
            "last_seen": self.heartbeat_tracking.get(agent_id, datetime.now()).isoformat()
        })

    def register_coordination_handler(self, event_type: str, handler: Callable[[str, Dict[str, Any]], None]):
        """Register a handler for coordination events."""
        self.coordination_handlers[event_type] = handler
        logger.info(f"Registered coordination handler for {event_type}")

    def register_workflow_handler(self, event_type: str, handler: Callable[[Dict[str, Any]], None]):
        """Register a handler for workflow events."""
        self.workflow_handlers[event_type] = handler
        logger.info(f"Registered workflow handler for {event_type}")
    
    def subscribe(self, agent_id: str, stream_types: List[StreamType] = None,
                 message_types: Optional[List[str]] = None, consumer_group: str = "default"):
        """Subscribe an agent to receive messages from specified streams"""
        if stream_types is None:
            stream_types = [StreamType.AGENT_MESSAGES]

        # Create subscription
        subscription = StreamSubscription(
            agent_id=agent_id,
            stream_types=set(stream_types),
            message_types=set(message_types or []),
            consumer_group=consumer_group
        )

        self.agent_subscriptions[agent_id] = subscription

        # Start consumer thread for this agent
        consumer_name = f"consumer_{agent_id}_{int(time.time())}"
        thread = threading.Thread(
            target=self._consume_messages_enhanced,
            args=(agent_id, consumer_name),
            daemon=True
        )
        thread.start()
        self.stream_threads[agent_id] = thread

        logger.info(f"Agent {agent_id} subscribed to streams: {[st.value for st in stream_types]}")
    
    def unsubscribe(self, agent_id: str):
        """Unsubscribe an agent from receiving messages"""
        if agent_id in self.agent_subscriptions:
            del self.agent_subscriptions[agent_id]

        # Stop consumer thread
        if agent_id in self.stream_threads and self.stream_threads[agent_id].is_alive():
            # Thread will terminate on next iteration when it checks agent_subscriptions
            pass

    def _consume_messages_enhanced(self, agent_id: str, consumer_name: str):
        """Enhanced message consumption for multiple stream types"""
        while agent_id in self.agent_subscriptions:
            try:
                subscription = self.agent_subscriptions[agent_id]

                # Build streams dict for all subscribed stream types
                streams = {}
                for stream_type in subscription.stream_types:
                    streams[stream_type.value] = subscription.last_processed_id

                # Read from all subscribed streams
                messages = self.redis_client.xread(streams, count=10, block=1000)

                for stream_name, stream_messages in messages:
                    for message_id, message_data in stream_messages:
                        # Process message if it's for this agent
                        if self._is_message_for_agent_enhanced(message_data, agent_id, subscription):
                            self._deliver_message_to_agent(agent_id, message_data)
                            subscription.last_processed_id = message_id
                            self.metrics["messages_received"] += 1

            except Exception as e:
                logger.error(f"Error consuming messages for agent {agent_id}: {e}")
                time.sleep(1)

    def _is_message_for_agent_enhanced(self, message_data: Dict[str, Any], agent_id: str,
                                     subscription: StreamSubscription) -> bool:
        """Enhanced message filtering for agent subscriptions"""
        # Check if message is for this agent or broadcast
        recipient_id = message_data.get('recipient_id', '')
        if recipient_id != agent_id and recipient_id != '*':
            return False

        # Check message type filter if specified
        if subscription.message_types:
            message_type = message_data.get('message_type', '')
            if message_type not in subscription.message_types:
                return False

        return True
    
    def _consume_messages(self, agent_id: str, consumer_name: str):
        """Consume messages from Redis stream for a specific agent"""
        last_id = '0'  # Start from beginning
        
        while agent_id in self.agent_subscriptions:
            try:
                # Read new messages
                streams = {'agent_messages': last_id}
                messages = self.redis_client.xread(streams, count=10, block=1000)
                
                for stream_name, stream_messages in messages:
                    for message_id, message_data in stream_messages:
                        # Process message if it's for this agent
                        if self._is_message_for_agent(message_data, agent_id):
                            # Parse payload if it's JSON
                            if 'payload' in message_data and message_data['payload']:
                                try:
                                    message_data['payload'] = json.loads(message_data['payload'])
                                except:
                                    pass  # Keep as string if not valid JSON
                            
                            # Store message for agent to retrieve
                            self._store_message_for_agent(message_data, agent_id)
                        
                        # Update last ID
                        last_id = message_id
                
                # Acknowledge messages
                if messages:
                    self.redis_client.xack('agent_messages', 'agent_group', last_id)
            
            except Exception as e:
                print(f"Error consuming messages for agent {agent_id}: {str(e)}")
                time.sleep(1)  # Avoid tight loop on error
    
    def _is_message_for_agent(self, message: Dict[str, Any], agent_id: str) -> bool:
        """Check if a message is intended for a specific agent"""
        # Direct message to this agent
        if message.get('recipient_id') == agent_id:
            return True
        
        # Broadcast message
        if message.get('recipient_id') == '*':
            # Check if sender is not the same as recipient to avoid echo
            if message.get('sender_id') != agent_id:
                return True
        
        return False

    def get_stream_metrics(self) -> Dict[str, Any]:
        """Get comprehensive stream metrics"""
        return {
            "metrics": self.metrics.copy(),
            "active_subscriptions": len(self.agent_subscriptions),
            "active_threads": len(self.stream_threads),
            "heartbeat_tracking": len(self.heartbeat_tracking),
            "coordination_handlers": len(self.coordination_handlers),
            "workflow_handlers": len(self.workflow_handlers),
            "stream_status": {
                stream_type.value: self._get_stream_info(stream_type.value)
                for stream_type in StreamType
            }
        }

    def _get_stream_info(self, stream_name: str) -> Dict[str, Any]:
        """Get information about a specific stream"""
        try:
            info = self.redis_client.xinfo_stream(stream_name)
            return {
                "length": info.get("length", 0),
                "groups": info.get("groups", 0),
                "last_generated_id": info.get("last-generated-id", "0")
            }
        except Exception as e:
            return {"error": str(e)}

    def cleanup_old_messages(self, stream_type: StreamType, max_age_hours: int = 24):
        """Clean up old messages from a stream"""
        try:
            stream_name = stream_type.value

            # Use XTRIM to remove old messages
            self.redis_client.xtrim(stream_name, maxlen=10000, approximate=True)

            logger.info(f"Cleaned up old messages from {stream_name}")

        except Exception as e:
            logger.error(f"Error cleaning up stream {stream_type.value}: {e}")

    def get_agent_message_history(self, agent_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get message history for an agent"""
        try:
            messages = []

            # Read from agent messages stream
            stream_messages = self.redis_client.xrevrange(
                StreamType.AGENT_MESSAGES.value,
                count=limit
            )

            for message_id, message_data in stream_messages:
                if message_data.get('recipient_id') == agent_id or message_data.get('sender_id') == agent_id:
                    message_data['message_id'] = message_id
                    messages.append(message_data)

            return messages

        except Exception as e:
            logger.error(f"Error getting message history for {agent_id}: {e}")
            return []

    def close(self):
        """Close Redis connections and stop all threads."""
        self.running = False

        # Wait for threads to terminate
        for thread in self.stream_threads.values():
            if thread.is_alive():
                thread.join(timeout=1.0)

        # Close Redis connection
        try:
            self.redis_client.close()
        except:
            pass  # Ignore errors during cleanup

# For backward compatibility, create an alias
RedisStreamManager = EnhancedRedisStreamManager