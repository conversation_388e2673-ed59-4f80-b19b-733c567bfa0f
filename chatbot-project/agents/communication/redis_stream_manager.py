import redis
import json
import threading
import time
import uuid
from typing import Dict, List, Any, Optional, Callable

class RedisStreamManager:
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        self.redis_client = redis.Redis(host=host, port=port, db=db, password=password, decode_responses=True)
        self.consumer_groups = {}
        self.stream_threads = {}
        self.running = False
        self.agent_subscriptions = {}  # agent_id -> list of message types
    
    def initialize(self):
        """Initialize Redis streams and consumer groups"""
        try:
            # Create main message stream if it doesn't exist
            self.redis_client.xgroup_create('agent_messages', 'agent_group', id='0', mkstream=True)
            print("Redis stream initialized successfully")
            return True
        except redis.exceptions.ResponseError as e:
            if 'BUSYGROUP' in str(e):  # Group already exists
                print("Redis consumer group already exists")
                return True
            print(f"Redis initialization error: {str(e)}")
            return False
    
    def send_message(self, message: Dict[str, Any]) -> bool:
        """Send a message to the Redis stream"""
        try:
            # Convert complex objects to JSON strings
            message_copy = message.copy()
            if isinstance(message_copy.get('payload'), dict):
                message_copy['payload'] = json.dumps(message_copy['payload'])
            
            # Add to stream
            self.redis_client.xadd('agent_messages', message_copy)
            return True
        except Exception as e:
            print(f"Error sending message to Redis stream: {str(e)}")
            return False
    
    def broadcast_message(self, message: Dict[str, Any]) -> int:
        """Broadcast a message to all agents"""
        try:
            # Set recipient to broadcast marker
            message_copy = message.copy()
            message_copy['recipient_id'] = '*'
            
            # Convert complex objects to JSON strings
            if isinstance(message_copy.get('payload'), dict):
                message_copy['payload'] = json.dumps(message_copy['payload'])
            
            # Add to stream
            self.redis_client.xadd('agent_messages', message_copy)
            
            # Return approximate number of recipients (all subscribed agents)
            return len(self.agent_subscriptions)
        except Exception as e:
            print(f"Error broadcasting message to Redis stream: {str(e)}")
            return 0
    
    def subscribe(self, agent_id: str, message_types: Optional[List[str]] = None):
        """Subscribe an agent to receive messages"""
        consumer_name = f"agent:{agent_id}"
        
        # Register subscription
        self.agent_subscriptions[agent_id] = message_types or ["*"]
        
        # Create consumer if needed
        if agent_id not in self.consumer_groups:
            try:
                # Create a unique consumer for this agent
                self.consumer_groups[agent_id] = consumer_name
                
                # Start consumer thread if not already running
                if agent_id not in self.stream_threads or not self.stream_threads[agent_id].is_alive():
                    self.stream_threads[agent_id] = threading.Thread(
                        target=self._consume_messages,
                        args=(agent_id, consumer_name),
                        daemon=True
                    )
                    self.stream_threads[agent_id].start()
            except Exception as e:
                print(f"Error subscribing agent {agent_id}: {str(e)}")
    
    def unsubscribe(self, agent_id: str):
        """Unsubscribe an agent from receiving messages"""
        if agent_id in self.agent_subscriptions:
            del self.agent_subscriptions[agent_id]
        
        # Stop consumer thread
        if agent_id in self.stream_threads and self.stream_threads[agent_id].is_alive():
            # Thread will terminate on next iteration when it checks agent_subscriptions
            pass
    
    def _consume_messages(self, agent_id: str, consumer_name: str):
        """Consume messages from Redis stream for a specific agent"""
        last_id = '0'  # Start from beginning
        
        while agent_id in self.agent_subscriptions:
            try:
                # Read new messages
                streams = {'agent_messages': last_id}
                messages = self.redis_client.xread(streams, count=10, block=1000)
                
                for stream_name, stream_messages in messages:
                    for message_id, message_data in stream_messages:
                        # Process message if it's for this agent
                        if self._is_message_for_agent(message_data, agent_id):
                            # Parse payload if it's JSON
                            if 'payload' in message_data and message_data['payload']:
                                try:
                                    message_data['payload'] = json.loads(message_data['payload'])
                                except:
                                    pass  # Keep as string if not valid JSON
                            
                            # Store message for agent to retrieve
                            self._store_message_for_agent(message_data, agent_id)
                        
                        # Update last ID
                        last_id = message_id
                
                # Acknowledge messages
                if messages:
                    self.redis_client.xack('agent_messages', 'agent_group', last_id)
            
            except Exception as e:
                print(f"Error consuming messages for agent {agent_id}: {str(e)}")
                time.sleep(1)  # Avoid tight loop on error
    
    def _is_message_for_agent(self, message: Dict[str, Any], agent_id: str) -> bool:
        """Check if a message is intended for a specific agent"""
        # Direct message to this agent
        if message.get('recipient_id') == agent_id:
            return True
        
        # Broadcast message
        if message.get('recipient_id') == '*':
            # Check if sender is not the same as recipient to avoid echo
            if message.get('sender_id') != agent_id:
                return True
        
        return False
    
    def _store_message_for_agent(self, message: Dict[str, Any], agent_id: str):
        """Store a message for an agent to retrieve later"""
        # In a real implementation, this would store in a per-agent queue
        # For simplicity, we'll use Redis lists
        message_key = f"agent:{agent_id}:messages"
        self.redis_client.lpush(message_key, json.dumps(message))
        # Trim to avoid unbounded growth
        self.redis_client.ltrim(message_key, 0, 99)  # Keep last 100 messages
    
    def get_messages_for_agent(self, agent_id: str) -> List[Dict[str, Any]]:
        """Get all pending messages for an agent"""
        message_key = f"agent:{agent_id}:messages"
        messages = []
        
        # Get all messages and clear the list
        while True:
            message_data = self.redis_client.rpop(message_key)
            if not message_data:
                break
            
            try:
                message = json.loads(message_data)
                messages.append(message)
            except:
                continue
        
        return messages
    
    def close(self):
        """Close Redis connection and stop all threads"""
        self.running = False
        
        # Wait for threads to terminate
        for thread in self.stream_threads.values():
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        # Close Redis connection
        self.redis_client.close()