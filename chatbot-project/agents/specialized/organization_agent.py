import sys
import os
import asyncio
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.base_agent import BaseAgent, AgentStatus

class OrganizationAgent(BaseAgent):
    def __init__(self, agent_id=None, name=None):
        super().__init__(agent_id, name or "OrganizationAgent")
        self.capabilities = ["organization_knowledge", "policy_lookup"]
        self.register_message_handler("knowledge_request", self.handle_knowledge_request)
        self.organization_data = {
            "NuvoAI": {
                "policies": {
                    "vacation": "NuvoAI offers 20 days of paid vacation per year, accrued monthly.",
                    "remote_work": "NuvoAI allows 2 days of remote work per week.",
                    "sick_leave": "NuvoAI provides 10 days of sick leave per year."
                },
                "departments": ["AI", "Product", "Engineering", "HR", "Finance"],
                "locations": ["Mumbai", "Bangalore", "Delhi"]
            },
            "Meril": {
                "policies": {
                    "vacation": "<PERSON><PERSON> offers 15 days of paid vacation per year.",
                    "remote_work": "<PERSON><PERSON> has a hybrid work model with 3 days in office.",
                    "sick_leave": "<PERSON><PERSON> provides 12 days of sick leave per year."
                },
                "departments": ["Life Sciences", "Healthcare", "Diagnostics", "Endo-Surgery", "HR", "Finance"],
                "locations": ["Mumbai", "Vapi", "Bengaluru"]
            }
        }
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process a task related to organization knowledge"""
        query = task.get("query", "")
        organization = task.get("organization") or task.get("user_context", {}).get("organization")
        
        if not organization:
            return {"error": "Organization not specified", "status": "failed"}
        
        if organization not in self.organization_data:
            return {"error": f"Unknown organization: {organization}", "status": "failed"}
        
        # Extract policy information if query is about policies
        if "policy" in query.lower() or "policies" in query.lower():
            policy_info = self._get_policy_info(organization, query)
            return {
                "organization": organization,
                "policy_info": policy_info,
                "status": "success"
            }
        
        # General organization information
        return {
            "organization": organization,
            "organization_info": self.organization_data[organization],
            "status": "success"
        }
    
    def _get_policy_info(self, organization: str, query: str) -> Dict[str, Any]:
        """Extract policy information based on the query"""
        policies = self.organization_data[organization]["policies"]
        
        # Check for specific policies mentioned in the query
        for policy_name, policy_text in policies.items():
            if policy_name in query.lower():
                return {policy_name: policy_text}
        
        # Return all policies if no specific one is mentioned
        return policies
    
    async def handle_knowledge_request(self, message):
        """Handle knowledge request messages"""
        try:
            # Extract query and organization from message
            payload = message.payload
            query = payload.get("query", "")
            organization = payload.get("organization")
            
            # Process the request
            result = await self.process_task({
                "query": query,
                "organization": organization
            })
            
            # Send response
            await self.send_message(
                message.sender_id,
                "knowledge_response",
                {
                    "result": result,
                    "correlation_id": message.message_id,
                    "query": query,
                    "organization": organization
                }
            )
        except Exception as e:
            # Send error response
            await self.send_message(
                message.sender_id,
                "error_response",
                {
                    "error": str(e),
                    "correlation_id": message.message_id
                }
            )
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities