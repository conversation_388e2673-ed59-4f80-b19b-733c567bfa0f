.PHONY: help install test lint format clean docker-build docker-up docker-down

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install dependencies
	python -m venv venv
	./venv/bin/pip install --upgrade pip
	./venv/bin/pip install -r requirements.txt

test: ## Run tests
	python -m pytest tests/ -v --cov=agents --cov=backend

test-watch: ## Run tests in watch mode
	python -m pytest-watch tests/

lint: ## Run linting
	python -m flake8 agents/ backend/
	python -m mypy agents/ backend/ --ignore-missing-imports

format: ## Format code
	python -m black agents/ backend/ tests/
	python -m isort agents/ backend/ tests/

format-check: ## Check code formatting
	python -m black --check agents/ backend/ tests/
	python -m isort --check-only agents/ backend/ tests/

clean: ## Clean up cache files
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +

docker-build: ## Build Docker image
	docker build -t chatbot-app .

docker-up: ## Start development services
	docker-compose up -d

docker-down: ## Stop development services
	docker-compose down

docker-logs: ## View service logs
	docker-compose logs -f

dev-setup: install docker-up ## Complete development setup
	@echo "✅ Development environment ready!"
	@echo "Run 'make test' to verify installation"

ci: lint test ## Run CI checks locally
	@echo "✅ All CI checks passed!"