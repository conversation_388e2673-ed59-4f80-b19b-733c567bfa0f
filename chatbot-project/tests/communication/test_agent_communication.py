import sys
import os
import asyncio
import time
import unittest
from concurrent.futures import ThreadPoolExecutor

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.communication.communication_service import CommunicationService
from agents.communication.message_protocols import MessageType, Priority
from agents.core.base_agent import BaseAgent, AgentStatus

class TestAgent(BaseAgent):
    def __init__(self, agent_id=None, name=None):
        super().__init__(agent_id, name)
        self.received_messages = []
        self.capabilities = ["test"]
        self.register_message_handler("request", self.handle_request)
        self.register_message_handler("broadcast", self.handle_broadcast)
    
    async def process_task(self, task):
        return {"result": "Processed task", "agent": self.name}
    
    def get_capabilities(self):
        return self.capabilities
    
    async def handle_request(self, message):
        self.received_messages.append(message)
        
        # Send a response if reply_to is set
        if message.reply_to:
            await self.send_message(
                message.sender_id,
                "response",
                {"response": f"Handled by {self.name}"},
                correlation_id=message.message_id,
                reply_to=message.message_id
            )
    
    async def handle_broadcast(self, message):
        self.received_messages.append(message)

class TestAgentCommunication(unittest.TestCase):
    def setUp(self):
        # Start Redis in a separate process for testing
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.executor.submit(self.start_redis)
        time.sleep(2)  # Wait for Redis to start
        
        # Create communication service
        self.comm_service = CommunicationService(redis_host='localhost', redis_port=6379, grpc_port=50052)
        self.comm_service.start()
        
        # Create test agents
        self.agent1 = TestAgent(agent_id="agent1", name="Agent1")
        self.agent2 = TestAgent(agent_id="agent2", name="Agent2")
        
        # Register agents
        self.comm_service.register_agent(self.agent1)
        self.comm_service.register_agent(self.agent2)
        
        # Initialize agents
        asyncio.run(self.agent1.initialize())
        asyncio.run(self.agent2.initialize())
    
    def tearDown(self):
        # Shutdown agents
        asyncio.run(self.agent1.shutdown())
        asyncio.run(self.agent2.shutdown())
        
        # Stop communication service
        self.comm_service.stop()
        
        # Stop Redis
        os.system("redis-cli shutdown")
        self.executor.shutdown()
    
    def start_redis(self):
        os.system("redis-server --daemonize yes")
    
    def test_direct_message(self):
        """Test sending a direct message between agents"""
        # Send message from agent1 to agent2
        asyncio.run(self.agent1.send_message(
            "agent2",
            "request",
            {"content": "Hello from Agent1"}
        ))
        
        # Wait for message processing
        time.sleep(1)
        
        # Check if agent2 received the message
        self.assertTrue(len(self.agent2.received_messages) > 0)
        last_message = self.agent2.received_messages[-1]
        self.assertEqual(last_message.sender_id, "agent1")
        self.assertEqual(last_message.message_type, MessageType.REQUEST)
        self.assertEqual(last_message.payload["content"], "Hello from Agent1")
    
    def test_broadcast_message(self):
        """Test broadcasting a message to all agents"""
        # Broadcast message from agent1
        asyncio.run(self.agent1.broadcast_message(
            "broadcast",
            {"content": "Broadcast from Agent1"}
        ))
        
        # Wait for message processing
        time.sleep(1)
        
        # Check if agent2 received the broadcast
        self.assertTrue(len(self.agent2.received_messages) > 0)
        broadcast_messages = [m for m in self.agent2.received_messages 
                             if m.message_type == MessageType.BROADCAST]
        self.assertTrue(len(broadcast_messages) > 0)
        
        # Agent1 should not receive its own broadcast
        broadcast_to_self = [m for m in self.agent1.received_messages 
                            if m.message_type == MessageType.BROADCAST]
        self.assertEqual(len(broadcast_to_self), 0)
    
    def test_request_response(self):
        """Test request-response pattern"""
        # Send request with reply_to
        message_id = "test_request_1"
        asyncio.run(self.agent1.send_message(
            "agent2",
            "request",
            {"content": "Request needing response"},
            correlation_id=message_id,
            reply_to=message_id
        ))
        
        # Wait for message processing and response
        time.sleep(2)
        
        # Check if agent1 received a response
        responses = [m for m in self.agent1.received_messages 
                    if m.message_type == MessageType.RESPONSE]
        self.assertTrue(len(responses) > 0)
        self.assertEqual(responses[-1].correlation_id, message_id)

if __name__ == "__main__":
    unittest.main()