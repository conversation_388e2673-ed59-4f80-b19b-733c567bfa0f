"""
Comprehensive system integration tests for CHaBot Advanced Agentic RAG system.
"""

import pytest
import asyncio
import httpx
import json
from datetime import datetime
from typing import Dict, List, Any
import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.core.agent_registry import AgentRegistry
from agents.orchestrator.orchestrator_agent import OrchestratorAgent
from agents.specialized.organization_agent import OrganizationAgent
from agents.specialized.reasoning_agent import ReasoningAgent
from agents.communication.agent_coordinator import AgentCoordinator
from ai.reasoning.tree_of_thoughts.tree_of_thoughts import TreeOfThoughts
from backend.services.agentic_chat_service import AgenticChatService


class TestSystemIntegration:
    """Comprehensive system integration tests."""
    
    @pytest.fixture(autouse=True)
    async def setup_system(self):
        """Setup complete system for testing."""
        self.agent_registry = AgentRegistry()
        self.coordinator = AgentCoordinator()
        self.chat_service = AgenticChatService()
        
        # Initialize agents
        self.orchestrator = OrchestratorAgent()
        self.org_agent_nuvo = OrganizationAgent("NUVO AI")
        self.org_agent_meril = OrganizationAgent("Meril Life Sciences")
        self.reasoning_agent = ReasoningAgent()
        
        # Register agents
        await self.agent_registry.register_agent(self.orchestrator)
        await self.agent_registry.register_agent(self.org_agent_nuvo)
        await self.agent_registry.register_agent(self.org_agent_meril)
        await self.agent_registry.register_agent(self.reasoning_agent)
        
        # Setup coordinator
        self.coordinator.register_agent("orchestrator", self.orchestrator)
        self.coordinator.register_agent("org_nuvo", self.org_agent_nuvo)
        self.coordinator.register_agent("org_meril", self.org_agent_meril)
        self.coordinator.register_agent("reasoning", self.reasoning_agent)
        
        yield
        
        # Cleanup
        await self.cleanup_system()
    
    async def cleanup_system(self):
        """Cleanup system after tests."""
        # Reset agent states
        for agent_id in list(self.agent_registry._agents.keys()):
            await self.agent_registry.unregister_agent(agent_id)
    
    @pytest.mark.asyncio
    async def test_end_to_end_query_processing(self):
        """Test complete end-to-end query processing."""
        test_query = "What is the vacation policy for NUVO AI employees?"
        
        # Test query processing through complete system
        task = {
            "query": test_query,
            "user_context": {
                "organization": "NUVO AI",
                "department": "Engineering",
                "user_role": "employee"
            },
            "user_id": "test_user_001"
        }
        
        # Process through coordinator
        result = await self.coordinator.coordinate_multi_agent_task(task)
        
        # Verify result structure
        assert "agents_used" in result
        assert "combined_results" in result
        assert "overall_confidence" in result
        assert len(result["agents_used"]) > 0
        assert result["overall_confidence"] > 0
        
        # Verify organization agent was used
        assert any("nuvo" in agent.lower() for agent in result["agents_used"])
        
        # Verify results contain relevant information
        combined_results = result["combined_results"]
        assert len(combined_results) > 0
        assert any("vacation" in str(res).lower() or "leave" in str(res).lower() 
                  for res in combined_results)
    
    @pytest.mark.asyncio
    async def test_multi_agent_collaboration(self):
        """Test multi-agent collaboration patterns."""
        complex_query = "Compare vacation policies between NUVO AI and Meril Life Sciences"
        
        task = {
            "query": complex_query,
            "user_context": {
                "organizations": ["NUVO AI", "Meril Life Sciences"],
                "comparison_type": "policy_comparison"
            },
            "user_id": "test_user_002"
        }
        
        result = await self.coordinator.coordinate_multi_agent_task(task)
        
        # Verify multiple organization agents were involved
        agents_used = result["agents_used"]
        assert len(agents_used) >= 2
        
        # Verify both organizations are represented
        org_agents = [agent for agent in agents_used if "org" in agent.lower()]
        assert len(org_agents) >= 2
        
        # Verify reasoning agent was involved for comparison
        assert any("reasoning" in agent.lower() for agent in agents_used)
        
        # Verify results contain information from both organizations
        combined_results = result["combined_results"]
        nuvo_mentioned = any("nuvo" in str(res).lower() for res in combined_results)
        meril_mentioned = any("meril" in str(res).lower() for res in combined_results)
        assert nuvo_mentioned and meril_mentioned
    
    @pytest.mark.asyncio
    async def test_reasoning_integration(self):
        """Test advanced reasoning integration."""
        reasoning_query = "Why might an employee's leave request be denied?"
        
        task = {
            "query": reasoning_query,
            "user_context": {
                "organization": "NUVO AI",
                "query_type": "reasoning"
            },
            "user_id": "test_user_003"
        }
        
        result = await self.coordinator.coordinate_multi_agent_task(task)
        
        # Verify reasoning agent was used
        assert any("reasoning" in agent.lower() for agent in result["agents_used"])
        
        # Verify reasoning results
        combined_results = result["combined_results"]
        reasoning_results = [res for res in combined_results 
                           if isinstance(res, dict) and res.get("type") == "reasoning"]
        
        if reasoning_results:
            reasoning_result = reasoning_results[0]
            assert "reasoning_steps" in reasoning_result or "analysis" in reasoning_result
    
    @pytest.mark.asyncio
    async def test_tree_of_thoughts_integration(self):
        """Test Tree of Thoughts reasoning integration."""
        tot = TreeOfThoughts()
        
        complex_query = "What are the implications of changing the vacation policy?"
        context = {
            "organization": "NUVO AI",
            "policy_type": "vacation",
            "change_type": "modification"
        }
        
        # Generate reasoning paths
        reasoning_paths = tot.generate_thoughts(complex_query, context, max_depth=3)
        
        # Verify reasoning paths were generated
        assert len(reasoning_paths) > 0
        
        # Get best reasoning path
        best_reasoning = tot.get_best_reasoning_path(complex_query, context)
        
        # Verify reasoning structure
        assert "path" in best_reasoning
        assert "confidence" in best_reasoning
        assert "reasoning" in best_reasoning
        assert best_reasoning["confidence"] > 0
        assert len(best_reasoning["reasoning"]) > 0
        
        # Verify reasoning steps have required fields
        for step in best_reasoning["reasoning"]:
            assert "step" in step
            assert "type" in step
            assert "confidence" in step
    
    @pytest.mark.asyncio
    async def test_agent_registry_operations(self):
        """Test agent registry operations."""
        # Test agent discovery
        org_agents = await self.agent_registry.find_agents_by_capability("organization_knowledge")
        assert len(org_agents) >= 2  # NUVO AI and Meril agents
        
        # Test agent type filtering
        reasoning_agents = await self.agent_registry.find_agents_by_type("reasoning")
        assert len(reasoning_agents) >= 1
        
        # Test health check
        health_status = await self.agent_registry.health_check()
        assert "total_agents" in health_status
        assert health_status["total_agents"] >= 4
        assert "status_breakdown" in health_status
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self):
        """Test system error handling and recovery."""
        # Test with invalid query
        invalid_task = {
            "query": "",  # Empty query
            "user_context": {},
            "user_id": "test_user_004"
        }
        
        result = await self.coordinator.coordinate_multi_agent_task(invalid_task)
        
        # System should handle gracefully
        assert "error" in result or "agents_used" in result
        
        # Test with missing context
        minimal_task = {
            "query": "Test query",
            "user_id": "test_user_005"
        }
        
        result = await self.coordinator.coordinate_multi_agent_task(minimal_task)
        
        # Should still process with minimal context
        assert "agents_used" in result or "error" in result
    
    @pytest.mark.asyncio
    async def test_performance_benchmarks(self):
        """Test system performance benchmarks."""
        queries = [
            "What is the leave policy?",
            "Who is the HR manager?",
            "How do I apply for vacation?",
            "What are the travel guidelines?",
            "Compare policies between organizations"
        ]
        
        response_times = []
        
        for query in queries:
            start_time = datetime.now()
            
            task = {
                "query": query,
                "user_context": {"organization": "NUVO AI"},
                "user_id": f"perf_test_{queries.index(query)}"
            }
            
            result = await self.coordinator.coordinate_multi_agent_task(task)
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            response_times.append(response_time)
            
            # Verify successful processing
            assert "agents_used" in result or "error" in result
        
        # Performance assertions
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # Response time should be reasonable (adjust thresholds as needed)
        assert avg_response_time < 5.0  # Average under 5 seconds
        assert max_response_time < 10.0  # Max under 10 seconds
        
        print(f"Performance Results:")
        print(f"Average response time: {avg_response_time:.2f}s")
        print(f"Max response time: {max_response_time:.2f}s")
        print(f"Min response time: {min(response_times):.2f}s")
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self):
        """Test concurrent query processing."""
        concurrent_queries = [
            {"query": "NUVO AI vacation policy", "org": "NUVO AI"},
            {"query": "Meril travel guidelines", "org": "Meril Life Sciences"},
            {"query": "HR contact information", "org": "NUVO AI"},
            {"query": "Leave application process", "org": "Meril Life Sciences"}
        ]
        
        # Create tasks
        tasks = []
        for i, query_data in enumerate(concurrent_queries):
            task = {
                "query": query_data["query"],
                "user_context": {"organization": query_data["org"]},
                "user_id": f"concurrent_test_{i}"
            }
            tasks.append(self.coordinator.coordinate_multi_agent_task(task))
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all queries processed
        assert len(results) == len(concurrent_queries)
        
        # Verify no exceptions occurred
        for result in results:
            assert not isinstance(result, Exception)
            assert isinstance(result, dict)
            assert "agents_used" in result or "error" in result
    
    @pytest.mark.asyncio
    async def test_memory_and_context_integration(self):
        """Test memory and context integration."""
        user_id = "memory_test_user"
        
        # First query to establish context
        first_task = {
            "query": "What is NUVO AI's vacation policy?",
            "user_context": {"organization": "NUVO AI"},
            "user_id": user_id
        }
        
        first_result = await self.coordinator.coordinate_multi_agent_task(first_task)
        assert "agents_used" in first_result
        
        # Follow-up query that should use context
        followup_task = {
            "query": "How many days do I get?",  # Contextual reference
            "user_context": {"organization": "NUVO AI"},
            "user_id": user_id
        }
        
        followup_result = await self.coordinator.coordinate_multi_agent_task(followup_task)
        assert "agents_used" in followup_result
        
        # Verify context was maintained (implementation dependent)
        # This would require memory system integration
    
    @pytest.mark.asyncio
    async def test_api_integration(self):
        """Test API integration if services are running."""
        try:
            async with httpx.AsyncClient() as client:
                # Test health endpoints
                health_response = await client.get("http://localhost:8000/health")
                if health_response.status_code == 200:
                    # API is running, test integration
                    
                    # Test authentication
                    auth_data = {
                        "username": "test_user",
                        "password": "test_password"
                    }
                    
                    # This would require proper test user setup
                    # auth_response = await client.post("http://localhost:8000/auth/login", json=auth_data)
                    
                    print("API integration test: Services are running")
                else:
                    print("API integration test: Services not available")
        except Exception as e:
            print(f"API integration test: Services not available - {e}")
    
    def test_system_configuration(self):
        """Test system configuration and setup."""
        # Verify agent registry is properly configured
        assert self.agent_registry is not None
        assert len(self.agent_registry._agents) >= 4
        
        # Verify coordinator is properly configured
        assert self.coordinator is not None
        assert len(self.coordinator.agents) >= 4
        
        # Verify agents have proper capabilities
        for agent_id, agent_info in self.coordinator.agents.items():
            assert "capabilities" in agent_info
            assert len(agent_info["capabilities"]) > 0
    
    @pytest.mark.asyncio
    async def test_data_flow_integrity(self):
        """Test data flow integrity through the system."""
        test_query = "Test data flow query"
        
        task = {
            "query": test_query,
            "user_context": {"test": "data"},
            "user_id": "data_flow_test"
        }
        
        # Track data flow
        result = await self.coordinator.coordinate_multi_agent_task(task)
        
        # Verify data integrity
        assert isinstance(result, dict)
        
        if "combined_results" in result:
            for res in result["combined_results"]:
                # Verify result structure
                assert res is not None
                
        # Verify no data corruption
        if "overall_confidence" in result:
            assert 0 <= result["overall_confidence"] <= 1


class TestLoadAndStress:
    """Load and stress testing for the system."""
    
    @pytest.mark.asyncio
    async def test_load_testing(self):
        """Basic load testing."""
        coordinator = AgentCoordinator()
        
        # Setup minimal system for load testing
        from agents.specialized.organization_agent import OrganizationAgent
        org_agent = OrganizationAgent("NUVO AI")
        coordinator.register_agent("org_agent", org_agent)
        
        # Generate load
        num_requests = 50
        tasks = []
        
        for i in range(num_requests):
            task = {
                "query": f"Load test query {i}",
                "user_context": {"organization": "NUVO AI"},
                "user_id": f"load_test_user_{i}"
            }
            tasks.append(coordinator.coordinate_multi_agent_task(task))
        
        # Execute load test
        start_time = datetime.now()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = datetime.now()
        
        # Analyze results
        successful_requests = sum(1 for r in results if not isinstance(r, Exception))
        total_time = (end_time - start_time).total_seconds()
        throughput = successful_requests / total_time
        
        print(f"Load Test Results:")
        print(f"Total requests: {num_requests}")
        print(f"Successful requests: {successful_requests}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Throughput: {throughput:.2f} requests/second")
        
        # Assertions
        assert successful_requests >= num_requests * 0.9  # 90% success rate
        assert throughput > 1.0  # At least 1 request per second


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--asyncio-mode=auto"])