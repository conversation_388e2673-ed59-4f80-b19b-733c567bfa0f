"""Integration tests for API Gateway."""

import pytest
import httpx
from fastapi.testclient import TestClient
from backend.api.gateway.api_gateway import app

class TestAPIGateway:
    def setup_method(self):
        self.client = TestClient(app)
    
    def test_health_check(self):
        response = self.client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "services" in data
    
    def test_auth_proxy_without_token(self):
        response = self.client.post("/auth/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        # Should proxy to auth service (may fail if service not running)
        assert response.status_code in [200, 500, 502]  # Various possible responses
    
    def test_chat_proxy_without_auth(self):
        response = self.client.post("/chat/send", json={
            "message": "Hello"
        })
        assert response.status_code == 403  # Should require authentication
    
    def test_admin_proxy_without_auth(self):
        response = self.client.get("/admin/stats/users")
        assert response.status_code == 403  # Should require authentication