import sys
import os
import asyncio
import time
import threading

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.communication.communication_service import CommunicationService
from agents.core.agent_registry import AgentRegistry
from agents.core.base_agent import BaseAgent

class SimpleAgent(BaseAgent):
    def __init__(self, agent_id, name):
        super().__init__(agent_id, name)
        self.received_messages = []
        self.capabilities = ["test"]
        self.register_message_handler("test_message", self.handle_test_message)
    
    async def process_task(self, task):
        return {"result": f"Task processed by {self.name}", "task": task}
    
    def get_capabilities(self):
        return self.capabilities
    
    async def handle_test_message(self, message):
        print(f"{self.name} received message: {message.payload}")
        self.received_messages.append(message)
        
        # Send a response
        if message.payload.get("needs_response", False):
            await self.send_message(
                message.sender_id,
                "test_response",
                {"response": f"Response from {self.name}", "original_id": message.message_id},
                correlation_id=message.message_id
            )

async def run_test():
    # Start communication service
    comm_service = CommunicationService(redis_host='localhost', redis_port=6379, grpc_port=50053)
    agent_registry = AgentRegistry()
    comm_service.start(agent_registry)
    
    # Create agents
    agent1 = SimpleAgent("agent1", "TestAgent1")
    agent2 = SimpleAgent("agent2", "TestAgent2")
    
    # Register agents
    comm_service.register_agent(agent1)
    comm_service.register_agent(agent2)
    agent_registry.register_agent(agent1)
    agent_registry.register_agent(agent2)
    
    # Initialize agents
    await agent1.initialize()
    await agent2.initialize()
    
    print("Agents initialized and registered")
    
    # Send a test message
    print("Sending test message from Agent1 to Agent2...")
    await agent1.send_message(
        "agent2",
        "test_message",
        {"content": "Hello from Agent1", "needs_response": True}
    )
    
    # Wait for message processing
    await asyncio.sleep(2)
    
    # Check if message was received
    if agent2.received_messages:
        print(f"Agent2 received {len(agent2.received_messages)} messages")
        last_message = agent2.received_messages[-1]
        print(f"Last message content: {last_message.payload}")
    else:
        print("Agent2 did not receive any messages")
    
    # Check if response was received
    if agent1.received_messages:
        print(f"Agent1 received {len(agent1.received_messages)} messages")
        last_message = agent1.received_messages[-1]
        print(f"Response content: {last_message.payload}")
    else:
        print("Agent1 did not receive any responses")
    
    # Broadcast a message
    print("\nBroadcasting message from Agent1...")
    await agent1.broadcast_message(
        "test_message",
        {"content": "Broadcast from Agent1", "broadcast": True}
    )
    
    # Wait for message processing
    await asyncio.sleep(2)
    
    # Check if broadcast was received
    broadcast_messages = [m for m in agent2.received_messages if m.payload.get("broadcast")]
    print(f"Agent2 received {len(broadcast_messages)} broadcast messages")
    
    # Shutdown
    await agent1.shutdown()
    await agent2.shutdown()
    comm_service.stop()
    print("Test completed")

if __name__ == "__main__":
    # Start Redis if not running
    os.system("redis-server --daemonize yes")
    time.sleep(1)  # Wait for Redis to start
    
    # Run the test
    asyncio.run(run_test())
    
    # Stop Redis
    os.system("redis-cli shutdown")