import sys
import os
import asyncio
import time
import json
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.communication.communication_service import CommunicationService
from agents.core.agent_registry import AgentRegistry
from agents.core.base_agent import BaseAgent, AgentStatus
from agents.specialized.organization_agent import OrganizationAgent
from agents.specialized.reasoning_agent import ReasoningAgent

async def run_integration_test():
    """Run an integration test between the agent communication framework and specialized agents"""
    print("Starting agent system integration test...")
    
    # Start Redis if not running
    os.system("redis-server --daemonize yes")
    time.sleep(1)  # Wait for Redis to start
    
    try:
        # Create communication service and agent registry
        comm_service = CommunicationService(redis_host='localhost', redis_port=6379, grpc_port=50055)
        agent_registry = AgentRegistry()
        
        # Start the service
        comm_service.start(agent_registry)
        print("Communication service started")
        
        # Create specialized agents
        org_agent = OrganizationAgent(agent_id="org_agent", name="OrganizationAgent")
        reasoning_agent = ReasoningAgent(agent_id="reasoning_agent", name="ReasoningAgent")
        
        # Register agents
        comm_service.register_agent(org_agent)
        comm_service.register_agent(reasoning_agent)
        agent_registry.register_agent(org_agent)
        agent_registry.register_agent(reasoning_agent)
        
        # Initialize agents
        await org_agent.initialize()
        await reasoning_agent.initialize()
        print("Agents initialized and registered")
        
        # Create a test query
        test_query = {
            "query": "What is the vacation policy at NuvoAI?",
            "user_context": {
                "organization": "NuvoAI",
                "department": "AI",
                "role": "Engineer"
            }
        }
        
        # Process the query with the reasoning agent
        print(f"\nProcessing query: {test_query['query']}")
        result = await reasoning_agent.process_task(test_query)
        
        # Print the result
        print("\nQuery result:")
        print(json.dumps(result, indent=2))
        
        # Wait for any pending messages
        await asyncio.sleep(2)
        
        # Shutdown
        await org_agent.shutdown()
        await reasoning_agent.shutdown()
        comm_service.stop()
        
        print("\nIntegration test completed successfully")
        
    except Exception as e:
        print(f"Error in integration test: {str(e)}")
    finally:
        # Stop Redis
        os.system("redis-cli shutdown")

if __name__ == "__main__":
    # Run the integration test
    asyncio.run(run_integration_test())