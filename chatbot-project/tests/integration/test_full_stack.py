"""Full stack integration tests."""

import pytest
import requests
import asyncio
from fastapi.testclient import TestClient

@pytest.fixture
def client():
    from app import app
    return TestClient(app)

def test_health_endpoint(client):
    """Test health endpoint."""
    response = client.get("/health")
    assert response.status_code == 200

def test_auth_flow(client):
    """Test authentication flow."""
    # Test login
    response = client.post("/auth/login", json={"username": "admin", "password": "admin123"})
    assert response.status_code == 200
    token = response.json()["access_token"]
    
    # Test protected endpoint
    headers = {"Authorization": f"Bearer {token}"}
    response = client.post("/api/chat/message", json={"message": "test"}, headers=headers)
    assert response.status_code == 200

def test_chat_integration(client):
    """Test chat integration."""
    # Login first
    auth_response = client.post("/auth/login", json={"username": "admin", "password": "admin123"})
    token = auth_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test chat
    response = client.post("/api/chat/message", json={"message": "What is AI?"}, headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert "response" in data
    assert "confidence" in data

def test_tools_integration(client):
    """Test tools integration."""
    # Login first
    auth_response = client.post("/auth/login", json={"username": "admin", "password": "admin123"})
    token = auth_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get tools
    response = client.get("/api/chat/tools", headers=headers)
    assert response.status_code == 200
    
    # Execute calculator
    response = client.post("/api/chat/tools/calculator", json={"expression": "2+2"}, headers=headers)
    assert response.status_code == 200