"""Load testing for CHaBot."""

import asyncio
import time
import requests
from concurrent.futures import ThreadPoolExecutor
import statistics

class LoadTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.results = []
    
    def single_request(self, endpoint, data=None):
        """Single request with timing."""
        start_time = time.time()
        try:
            if data:
                response = requests.post(f"{self.base_url}{endpoint}", json=data, timeout=10)
            else:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
            
            duration = time.time() - start_time
            return {"status": response.status_code, "duration": duration, "success": response.status_code == 200}
        except Exception as e:
            duration = time.time() - start_time
            return {"status": 0, "duration": duration, "success": False, "error": str(e)}
    
    def load_test(self, endpoint, concurrent_users=10, requests_per_user=10, data=None):
        """Run load test."""
        print(f"Load testing {endpoint} with {concurrent_users} users, {requests_per_user} requests each")
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []
            for user in range(concurrent_users):
                for req in range(requests_per_user):
                    futures.append(executor.submit(self.single_request, endpoint, data))
            
            results = [future.result() for future in futures]
            self.results.extend(results)
        
        return self.analyze_results()
    
    def analyze_results(self):
        """Analyze test results."""
        successful = [r for r in self.results if r["success"]]
        failed = [r for r in self.results if not r["success"]]
        durations = [r["duration"] for r in successful]
        
        if not durations:
            return {"error": "No successful requests"}
        
        return {
            "total_requests": len(self.results),
            "successful": len(successful),
            "failed": len(failed),
            "success_rate": len(successful) / len(self.results) * 100,
            "avg_response_time": statistics.mean(durations),
            "min_response_time": min(durations),
            "max_response_time": max(durations),
            "requests_per_second": len(successful) / sum(durations) if sum(durations) > 0 else 0
        }

def run_load_tests():
    """Run comprehensive load tests."""
    tester = LoadTester()
    
    # Test health endpoint
    print("Testing /health endpoint...")
    health_results = tester.load_test("/health", 5, 10)
    print(f"Health endpoint results: {health_results}")
    
    return health_results

if __name__ == "__main__":
    run_load_tests()