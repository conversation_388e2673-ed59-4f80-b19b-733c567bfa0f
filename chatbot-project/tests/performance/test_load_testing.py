"""
Performance and load testing for CHaBot system.
"""

import pytest
import asyncio
import time
import statistics
from datetime import datetime, timedelta
from typing import List, Dict, Any
import sys
import os
import concurrent.futures
import threading
import psutil
import json

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from agents.communication.agent_coordinator import AgentCoordinator
from agents.specialized.organization_agent import OrganizationAgent
from agents.specialized.reasoning_agent import ReasoningAgent
from ai.reasoning.tree_of_thoughts.tree_of_thoughts import TreeOfThoughts


class PerformanceMetrics:
    """Class to track performance metrics."""
    
    def __init__(self):
        self.response_times = []
        self.throughput_data = []
        self.error_count = 0
        self.success_count = 0
        self.memory_usage = []
        self.cpu_usage = []
        self.start_time = None
        self.end_time = None
    
    def add_response_time(self, response_time: float):
        """Add response time measurement."""
        self.response_times.append(response_time)
    
    def add_success(self):
        """Record successful request."""
        self.success_count += 1
    
    def add_error(self):
        """Record failed request."""
        self.error_count += 1
    
    def record_system_metrics(self):
        """Record system resource usage."""
        self.memory_usage.append(psutil.virtual_memory().percent)
        self.cpu_usage.append(psutil.cpu_percent())
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get performance statistics."""
        if not self.response_times:
            return {"error": "No response time data"}
        
        total_requests = self.success_count + self.error_count
        duration = (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0
        
        return {
            "total_requests": total_requests,
            "successful_requests": self.success_count,
            "failed_requests": self.error_count,
            "success_rate": (self.success_count / total_requests * 100) if total_requests > 0 else 0,
            "duration_seconds": duration,
            "throughput_rps": total_requests / duration if duration > 0 else 0,
            "response_times": {
                "min": min(self.response_times),
                "max": max(self.response_times),
                "mean": statistics.mean(self.response_times),
                "median": statistics.median(self.response_times),
                "p95": self._percentile(self.response_times, 95),
                "p99": self._percentile(self.response_times, 99)
            },
            "system_resources": {
                "avg_memory_usage": statistics.mean(self.memory_usage) if self.memory_usage else 0,
                "max_memory_usage": max(self.memory_usage) if self.memory_usage else 0,
                "avg_cpu_usage": statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
                "max_cpu_usage": max(self.cpu_usage) if self.cpu_usage else 0
            }
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile."""
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]


class LoadTestRunner:
    """Load test runner for CHaBot system."""
    
    def __init__(self):
        self.coordinator = AgentCoordinator()
        self.metrics = PerformanceMetrics()
        self.setup_agents()
    
    def setup_agents(self):
        """Setup agents for testing."""
        # Create test agents
        self.org_agent_nuvo = OrganizationAgent("NUVO AI")
        self.org_agent_meril = OrganizationAgent("Meril Life Sciences")
        self.reasoning_agent = ReasoningAgent()
        
        # Register agents
        self.coordinator.register_agent("org_nuvo", self.org_agent_nuvo)
        self.coordinator.register_agent("org_meril", self.org_agent_meril)
        self.coordinator.register_agent("reasoning", self.reasoning_agent)
    
    async def execute_single_request(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single request and measure performance."""
        start_time = time.time()
        
        try:
            task = {
                "query": query,
                "user_context": context,
                "user_id": f"load_test_{int(time.time() * 1000)}"
            }
            
            result = await self.coordinator.coordinate_multi_agent_task(task)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            self.metrics.add_response_time(response_time)
            self.metrics.add_success()
            
            return {"success": True, "response_time": response_time, "result": result}
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            
            self.metrics.add_response_time(response_time)
            self.metrics.add_error()
            
            return {"success": False, "response_time": response_time, "error": str(e)}
    
    async def run_concurrent_load_test(self, num_requests: int, concurrency: int, 
                                     queries: List[str], contexts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Run concurrent load test."""
        print(f"Starting load test: {num_requests} requests with {concurrency} concurrent users")
        
        self.metrics.start_time = datetime.now()
        
        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(concurrency)
        
        async def bounded_request(query: str, context: Dict[str, Any]):
            async with semaphore:
                return await self.execute_single_request(query, context)
        
        # Generate tasks
        tasks = []
        for i in range(num_requests):
            query = queries[i % len(queries)]
            context = contexts[i % len(contexts)]
            tasks.append(bounded_request(query, context))
        
        # Monitor system resources
        monitor_task = asyncio.create_task(self.monitor_system_resources())
        
        # Execute all requests
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Stop monitoring
        monitor_task.cancel()
        
        self.metrics.end_time = datetime.now()
        
        # Process results
        successful_results = [r for r in results if isinstance(r, dict) and r.get("success")]
        failed_results = [r for r in results if isinstance(r, dict) and not r.get("success")]
        exceptions = [r for r in results if isinstance(r, Exception)]
        
        print(f"Load test completed:")
        print(f"  Successful: {len(successful_results)}")
        print(f"  Failed: {len(failed_results)}")
        print(f"  Exceptions: {len(exceptions)}")
        
        return self.metrics.get_statistics()
    
    async def monitor_system_resources(self):
        """Monitor system resources during load test."""
        try:
            while True:
                self.metrics.record_system_metrics()
                await asyncio.sleep(1)  # Record every second
        except asyncio.CancelledError:
            pass
    
    async def run_stress_test(self, duration_minutes: int, max_concurrency: int) -> Dict[str, Any]:
        """Run stress test with increasing load."""
        print(f"Starting stress test: {duration_minutes} minutes, max concurrency: {max_concurrency}")
        
        stress_results = []
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        queries = [
            "What is the vacation policy?",
            "Who is the HR manager?",
            "How do I apply for leave?",
            "What are the travel guidelines?",
            "Compare policies between organizations"
        ]
        
        contexts = [
            {"organization": "NUVO AI"},
            {"organization": "Meril Life Sciences"},
            {"organization": "NUVO AI", "department": "Engineering"},
            {"organization": "Meril Life Sciences", "department": "HR"}
        ]
        
        current_concurrency = 1
        
        while datetime.now() < end_time and current_concurrency <= max_concurrency:
            print(f"Testing with concurrency: {current_concurrency}")
            
            # Reset metrics for this iteration
            self.metrics = PerformanceMetrics()
            
            # Run test for 30 seconds with current concurrency
            test_duration = 30  # seconds
            num_requests = current_concurrency * 10  # 10 requests per concurrent user
            
            iteration_result = await self.run_concurrent_load_test(
                num_requests, current_concurrency, queries, contexts
            )
            
            iteration_result["concurrency"] = current_concurrency
            stress_results.append(iteration_result)
            
            # Check if system is still performing well
            if iteration_result["response_times"]["p95"] > 10.0:  # 10 second threshold
                print(f"Performance degraded at concurrency {current_concurrency}")
                break
            
            current_concurrency += 1
            
            # Brief pause between iterations
            await asyncio.sleep(5)
        
        return {
            "stress_test_results": stress_results,
            "max_stable_concurrency": current_concurrency - 1,
            "total_duration_minutes": duration_minutes
        }


class TestPerformance:
    """Performance test cases."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup for performance tests."""
        self.load_runner = LoadTestRunner()
        yield
        # Cleanup if needed
    
    @pytest.mark.asyncio
    async def test_basic_load_test(self):
        """Basic load test with moderate load."""
        queries = [
            "What is the vacation policy for NUVO AI?",
            "Who is the HR manager at Meril?",
            "How do I apply for leave?",
            "What are the travel guidelines?",
            "Compare vacation policies"
        ]
        
        contexts = [
            {"organization": "NUVO AI"},
            {"organization": "Meril Life Sciences"},
            {"organization": "NUVO AI", "department": "Engineering"},
            {"organization": "Meril Life Sciences", "department": "HR"}
        ]
        
        # Test parameters
        num_requests = 50
        concurrency = 5
        
        results = await self.load_runner.run_concurrent_load_test(
            num_requests, concurrency, queries, contexts
        )
        
        # Performance assertions
        assert results["success_rate"] >= 90.0  # 90% success rate
        assert results["response_times"]["p95"] < 5.0  # 95th percentile under 5 seconds
        assert results["throughput_rps"] > 1.0  # At least 1 request per second
        
        print(f"Basic Load Test Results:")
        print(f"  Success Rate: {results['success_rate']:.1f}%")
        print(f"  P95 Response Time: {results['response_times']['p95']:.2f}s")
        print(f"  Throughput: {results['throughput_rps']:.2f} RPS")
    
    @pytest.mark.asyncio
    async def test_high_concurrency_load(self):
        """Test with high concurrency."""
        queries = ["Quick test query"] * 10
        contexts = [{"organization": "NUVO AI"}] * 10
        
        # High concurrency test
        num_requests = 100
        concurrency = 20
        
        results = await self.load_runner.run_concurrent_load_test(
            num_requests, concurrency, queries, contexts
        )
        
        # More lenient assertions for high concurrency
        assert results["success_rate"] >= 80.0  # 80% success rate
        assert results["response_times"]["p95"] < 10.0  # 95th percentile under 10 seconds
        
        print(f"High Concurrency Test Results:")
        print(f"  Success Rate: {results['success_rate']:.1f}%")
        print(f"  P95 Response Time: {results['response_times']['p95']:.2f}s")
        print(f"  Max Memory Usage: {results['system_resources']['max_memory_usage']:.1f}%")
    
    @pytest.mark.asyncio
    async def test_reasoning_performance(self):
        """Test performance of reasoning-heavy queries."""
        reasoning_queries = [
            "Why might a leave request be denied?",
            "What are the implications of changing vacation policy?",
            "How do different policies compare across organizations?",
            "What factors should be considered for policy updates?",
            "Analyze the impact of remote work on leave policies"
        ]
        
        contexts = [
            {"organization": "NUVO AI", "query_type": "reasoning"},
            {"organization": "Meril Life Sciences", "query_type": "reasoning"}
        ]
        
        num_requests = 20
        concurrency = 3  # Lower concurrency for reasoning tasks
        
        results = await self.load_runner.run_concurrent_load_test(
            num_requests, concurrency, reasoning_queries, contexts
        )
        
        # Reasoning tasks may take longer
        assert results["success_rate"] >= 85.0
        assert results["response_times"]["p95"] < 15.0  # 15 seconds for complex reasoning
        
        print(f"Reasoning Performance Test Results:")
        print(f"  Success Rate: {results['success_rate']:.1f}%")
        print(f"  Mean Response Time: {results['response_times']['mean']:.2f}s")
        print(f"  P95 Response Time: {results['response_times']['p95']:.2f}s")
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self):
        """Test memory usage stability over time."""
        queries = ["Memory test query"] * 5
        contexts = [{"organization": "NUVO AI"}] * 5
        
        # Run multiple iterations to check for memory leaks
        iterations = 5
        memory_usage_over_time = []
        
        for i in range(iterations):
            print(f"Memory test iteration {i + 1}/{iterations}")
            
            results = await self.load_runner.run_concurrent_load_test(
                20, 5, queries, contexts
            )
            
            memory_usage_over_time.append(results["system_resources"]["max_memory_usage"])
            
            # Brief pause between iterations
            await asyncio.sleep(2)
        
        # Check for memory leaks (memory usage shouldn't increase significantly)
        initial_memory = memory_usage_over_time[0]
        final_memory = memory_usage_over_time[-1]
        memory_increase = final_memory - initial_memory
        
        print(f"Memory Usage Over Time: {memory_usage_over_time}")
        print(f"Memory Increase: {memory_increase:.1f}%")
        
        # Memory shouldn't increase by more than 20% over iterations
        assert memory_increase < 20.0
    
    @pytest.mark.asyncio
    async def test_tree_of_thoughts_performance(self):
        """Test Tree of Thoughts reasoning performance."""
        tot = TreeOfThoughts()
        
        complex_queries = [
            "What are the long-term implications of policy changes?",
            "How should we balance employee satisfaction with business needs?",
            "What factors influence policy effectiveness?",
            "How do cultural differences affect policy implementation?",
            "What are the trade-offs in different policy approaches?"
        ]
        
        contexts = [
            {"organization": "NUVO AI", "complexity": "high"},
            {"organization": "Meril Life Sciences", "complexity": "high"}
        ]
        
        response_times = []
        
        for query in complex_queries:
            for context in contexts:
                start_time = time.time()
                
                # Generate reasoning with Tree of Thoughts
                reasoning_paths = tot.generate_thoughts(query, context, max_depth=3)
                best_reasoning = tot.get_best_reasoning_path(query, context)
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                
                # Verify reasoning quality
                assert len(reasoning_paths) > 0
                assert best_reasoning["confidence"] > 0
                assert len(best_reasoning["reasoning"]) > 0
        
        # Performance assertions for Tree of Thoughts
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        print(f"Tree of Thoughts Performance:")
        print(f"  Average Response Time: {avg_response_time:.2f}s")
        print(f"  Max Response Time: {max_response_time:.2f}s")
        print(f"  Total Queries: {len(response_times)}")
        
        # Tree of Thoughts should complete within reasonable time
        assert avg_response_time < 3.0  # Average under 3 seconds
        assert max_response_time < 8.0  # Max under 8 seconds
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_stress_test(self):
        """Comprehensive stress test (marked as slow)."""
        # Only run if explicitly requested
        if not os.getenv("RUN_STRESS_TESTS"):
            pytest.skip("Stress tests disabled. Set RUN_STRESS_TESTS=1 to enable.")
        
        duration_minutes = 5  # 5-minute stress test
        max_concurrency = 15
        
        results = await self.load_runner.run_stress_test(duration_minutes, max_concurrency)
        
        print(f"Stress Test Results:")
        print(f"  Max Stable Concurrency: {results['max_stable_concurrency']}")
        print(f"  Total Duration: {results['total_duration_minutes']} minutes")
        
        # Verify system handled some level of stress
        assert results["max_stable_concurrency"] >= 3
        
        # Print detailed results for each concurrency level
        for result in results["stress_test_results"]:
            concurrency = result["concurrency"]
            success_rate = result["success_rate"]
            p95_time = result["response_times"]["p95"]
            throughput = result["throughput_rps"]
            
            print(f"  Concurrency {concurrency}: {success_rate:.1f}% success, "
                  f"P95: {p95_time:.2f}s, Throughput: {throughput:.2f} RPS")


class TestScalability:
    """Scalability testing."""
    
    @pytest.mark.asyncio
    async def test_agent_scaling(self):
        """Test system behavior with varying numbers of agents."""
        coordinator = AgentCoordinator()
        
        # Test with different numbers of agents
        agent_counts = [1, 3, 5, 8]
        results = {}
        
        for count in agent_counts:
            # Setup agents
            agents = []
            for i in range(count):
                if i % 2 == 0:
                    agent = OrganizationAgent(f"TestOrg{i}")
                else:
                    agent = ReasoningAgent()
                agents.append(agent)
                coordinator.register_agent(f"agent_{i}", agent)
            
            # Test performance with this agent count
            start_time = time.time()
            
            task = {
                "query": f"Test query for {count} agents",
                "user_context": {"test": True},
                "user_id": f"scaling_test_{count}"
            }
            
            result = await coordinator.coordinate_multi_agent_task(task)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            results[count] = {
                "response_time": response_time,
                "agents_used": len(result.get("agents_used", [])),
                "success": "error" not in result
            }
            
            # Cleanup for next iteration
            coordinator.agents.clear()
        
        print("Agent Scaling Results:")
        for count, result in results.items():
            print(f"  {count} agents: {result['response_time']:.2f}s, "
                  f"Used: {result['agents_used']}, Success: {result['success']}")
        
        # Verify scaling behavior
        for count in agent_counts:
            assert results[count]["success"]


if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "--asyncio-mode=auto", "-m", "not slow"])