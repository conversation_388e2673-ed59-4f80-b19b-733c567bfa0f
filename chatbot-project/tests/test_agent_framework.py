"""
Tests for the agent framework.
"""

import pytest
import asyncio
from datetime import datetime

from agents.core.base_agent import BaseAgent, AgentStatus
from agents.core.agent_registry import AgentRegistry
from agents.communication.message_protocols import MessageType
from agents.core.lifecycle import AgentLifecycle


class TestAgent(BaseAgent):
    """Test agent implementation."""

    def __init__(self, agent_id: str = None, name: str = None, capabilities: list = None):
        super().__init__(agent_id, name)
        if capabilities:
            self.capabilities = capabilities

    async def process_task(self, task: dict) -> dict:
        """Process a task and return results"""
        return {"result": "test_processed", "status": "completed"}
    
    def get_capabilities(self) -> list:
        """Return list of agent capabilities"""
        return self.capabilities or ["test_capability"]

    async def _setup(self):
        """Setup test agent."""
        pass

    async def process_message(self, message):
        """Process test message."""
        return {
            "message_id": "test_response",
            "sender_id": self.agent_id,
            "response": "test_processed"
        }
    
    async def receive_message(self, message):
        """Receive and process a message."""
        response_data = await self.process_message(message)
        # Create a mock response message object
        class MockMessage:
            def __init__(self, data):
                self.message_id = data["message_id"]
                self.sender_id = data["sender_id"]
                self.recipient_id = message.sender_id
                self.message_type = MessageType.RESPONSE
                self.content = {"response": data["response"]}
        
        return MockMessage(response_data)


@pytest.mark.asyncio
async def test_agent_initialization():
    """Test agent initialization."""
    agent = TestAgent("test_001", "Test Agent", ["test_capability"])

    assert agent.agent_id == "test_001"
    assert agent.name == "Test Agent"
    assert agent.capabilities == ["test_capability"]
    assert agent.status == AgentStatus.INITIALIZING

    # Initialize agent
    success = await agent.initialize()
    assert success is True
    assert agent.status == AgentStatus.ACTIVE
    assert agent.running is True

    # Shutdown agent
    await agent.shutdown()
    assert agent.status == AgentStatus.OFFLINE
    assert agent.running is False


@pytest.mark.asyncio
async def test_agent_registry():
    """Test agent registry functionality."""
    registry = AgentRegistry()
    agent1 = TestAgent("test_001", "Test Agent 1", ["capability_1"])
    agent2 = TestAgent("test_002", "Test Agent 2", ["capability_2"])

    # Register agents
    success1 = await registry.register_agent(agent1)
    success2 = await registry.register_agent(agent2)

    assert success1 is True
    assert success2 is True

    # Test duplicate registration
    duplicate = await registry.register_agent(agent1)
    assert duplicate is False

    # Test retrieval
    retrieved = await registry.get_agent("test_001")
    assert retrieved is not None
    assert retrieved.agent_id == "test_001"

    # Test capability search
    agents_with_cap1 = await registry.find_agents_by_capability("capability_1")
    assert len(agents_with_cap1) == 0  # Agent not active yet

    # Activate agents
    await agent1.initialize()
    await agent2.initialize()

    agents_with_cap1 = await registry.find_agents_by_capability("capability_1")
    assert len(agents_with_cap1) == 1
    assert agents_with_cap1[0].agent_id == "test_001"

    # Test health check
    health = await registry.health_check()
    assert health["total_agents"] == 2
    assert "active" in health["status_breakdown"]

    # Cleanup
    await agent1.shutdown()
    await agent2.shutdown()


@pytest.mark.asyncio
async def test_agent_communication():
    """Test agent message processing."""
    agent = TestAgent("test_001", "Test Agent")
    await agent.initialize()

    # Create test message
    class MockMessage:
        def __init__(self):
            self.message_id = "test_msg_001"
            self.sender_id = "sender_001"
            self.recipient_id = "test_001"
            self.message_type = MessageType.REQUEST
            self.content = {"test": "data"}
            self.timestamp = datetime.now()
    
    message = MockMessage()

    # Process message
    response = await agent.receive_message(message)

    assert response is not None
    assert response.sender_id == "test_001"
    assert response.recipient_id == "sender_001"
    assert response.message_type == MessageType.RESPONSE
    assert response.content["response"] == "test_processed"

    await agent.shutdown()


@pytest.mark.asyncio
async def test_agent_lifecycle():
    """Test agent lifecycle management."""
    registry = AgentRegistry()
    lifecycle = AgentLifecycle(registry)

    agent = TestAgent("test_001", "Test Agent")

    # Start agent
    success = await lifecycle.start_agent(agent)
    assert success is True
    assert agent.status == AgentStatus.ACTIVE

    # Check registry
    retrieved = await registry.get_agent("test_001")
    assert retrieved is not None

    # Stop agent
    success = await lifecycle.stop_agent("test_001")
    assert success is True
    assert agent.status == AgentStatus.OFFLINE

    # Check registry
    retrieved = await registry.get_agent("test_001")
    assert retrieved is None


if __name__ == "__main__":
    pytest.main([__file__])
