"""Tests for agent communication."""

import pytest
import asyncio
from backend.utils.communication import Message<PERSON>roke<PERSON>, AgentCommunicationProtocol

class TestAgentCommunication:
    @pytest.fixture
    async def message_broker(self):
        broker = MessageBroker("redis://localhost:6379")
        yield broker
        # Cleanup
        if broker.redis:
            await broker.redis.close()
    
    @pytest.mark.asyncio
    async def test_message_broker_publish(self, message_broker):
        # Test publishing a message
        test_message = {"type": "test", "content": "Hello World"}
        
        # This will fail if Redis is not available, but that's expected in unit tests
        try:
            await message_broker.publish("test_channel", test_message)
        except Exception:
            pytest.skip("Redis not available for testing")
    
    @pytest.mark.asyncio
    async def test_agent_communication_protocol(self, message_broker):
        protocol = AgentCommunicationProtocol("test_agent", message_broker)
        
        # Test handler registration
        def test_handler(message):
            return {"status": "received"}
        
        protocol.register_handler("test_message", test_handler)
        assert "test_message" in protocol.message_handlers
    
    def test_agent_message_creation(self):
        # Test message structure
        from datetime import datetime
        
        message = {
            'from': 'agent1',
            'to': 'agent2',
            'type': 'request',
            'content': {'query': 'test'},
            'id': f"agent1_agent2_{datetime.utcnow().timestamp()}"
        }
        
        assert message['from'] == 'agent1'
        assert message['to'] == 'agent2'
        assert message['type'] == 'request'
        assert 'id' in message