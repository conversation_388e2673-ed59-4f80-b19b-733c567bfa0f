"""End-to-end tests for complete CHaBot workflow."""

import pytest
import httpx
import asyncio
from fastapi.testclient import TestClient

class TestCompleteWorkflow:
    def setup_method(self):
        # This would typically use a test environment
        self.base_url = "http://localhost:8000"
        self.client = httpx.Client(base_url=self.base_url)
        self.auth_token = None
    
    def test_user_registration_and_login(self):
        """Test user registration and login flow."""
        # Register a new user
        register_data = {
            "username": "test_user_e2e",
            "email": "<EMAIL>",
            "password": "secure_password_123",
            "role": "employee"
        }
        
        try:
            response = self.client.post("/auth/register", json=register_data)
            if response.status_code == 200:
                data = response.json()
                assert "access_token" in data
                self.auth_token = data["access_token"]
            else:
                pytest.skip("Auth service not available")
        except Exception:
            pytest.skip("API Gateway not available")
    
    def test_chat_conversation_flow(self):
        """Test complete chat conversation flow."""
        if not self.auth_token:
            pytest.skip("Authentication required")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Send a chat message
        chat_data = {
            "message": "What is the company leave policy?",
            "conversation_id": None
        }
        
        try:
            response = self.client.post("/chat/send", json=chat_data, headers=headers)
            if response.status_code == 200:
                data = response.json()
                assert "response" in data
                assert "conversation_id" in data
                assert "agent_used" in data
                
                conversation_id = data["conversation_id"]
                
                # Get conversation history
                history_response = self.client.get(
                    f"/chat/conversations/{conversation_id}/messages",
                    headers=headers
                )
                
                if history_response.status_code == 200:
                    messages = history_response.json()
                    assert len(messages) >= 2  # User message + bot response
            else:
                pytest.skip("Chat service not available")
        except Exception:
            pytest.skip("Chat service not available")
    
    def test_agent_status_monitoring(self):
        """Test agent status monitoring."""
        if not self.auth_token:
            pytest.skip("Authentication required")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        try:
            response = self.client.get("/agent/status", headers=headers)
            if response.status_code == 200:
                agents = response.json()
                assert isinstance(agents, list)
                
                for agent in agents:
                    assert "agent_id" in agent
                    assert "status" in agent
                    assert "load" in agent
            else:
                pytest.skip("Agent service not available")
        except Exception:
            pytest.skip("Agent service not available")
    
    def test_admin_functionality(self):
        """Test admin functionality (requires admin role)."""
        # This test would need an admin user
        pytest.skip("Admin functionality requires admin user setup")
    
    def teardown_method(self):
        """Cleanup after tests."""
        if hasattr(self, 'client'):
            self.client.close()