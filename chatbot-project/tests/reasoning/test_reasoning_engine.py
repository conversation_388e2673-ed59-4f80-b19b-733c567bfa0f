"""Tests for reasoning engine."""

import pytest
from ai.reasoning.engine.reasoning_engine import ReasoningEngine, ReasoningStep

class TestReasoningEngine:
    def setup_method(self):
        self.engine = ReasoningEngine()
    
    def test_create_reasoning_chain(self):
        query = "What is the leave policy for sick days?"
        context = {"user_role": "employee", "department": "IT"}
        
        chain_id = self.engine.create_reasoning_chain(query, context)
        
        assert chain_id is not None
        assert chain_id in self.engine.reasoning_chains
        
        chain = self.engine.reasoning_chains[chain_id]
        assert chain["query"] == query
        assert chain["context"] == context
        assert not chain["completed"]
    
    def test_add_reasoning_step(self):
        query = "What is the leave policy?"
        context = {}
        chain_id = self.engine.create_reasoning_chain(query, context)
        
        step_id = self.engine.add_reasoning_step(
            chain_id,
            ReasoningStep.INFER,
            "Based on the policy document, sick leave is available for all employees",
            ["Policy Document Section 3.2"],
            0.9
        )
        
        assert step_id is not None
        chain = self.engine.reasoning_chains[chain_id]
        assert len(chain["root"].children) == 1
        assert chain["root"].children[0].step_type == ReasoningStep.INFER
    
    def test_validate_reasoning_step(self):
        query = "What is the leave policy?"
        context = {}
        chain_id = self.engine.create_reasoning_chain(query, context)
        
        step_id = self.engine.add_reasoning_step(
            chain_id,
            ReasoningStep.CONCLUDE,
            "Employees can take up to 10 sick days per year",
            ["Policy Document", "HR Manual"],
            0.85
        )
        
        is_valid, message = self.engine.validate_reasoning_step(chain_id, step_id)
        assert is_valid
        assert message == "Valid"
    
    def test_complete_reasoning_chain(self):
        query = "What is the leave policy?"
        context = {}
        chain_id = self.engine.create_reasoning_chain(query, context)
        
        # Add some reasoning steps
        self.engine.add_reasoning_step(
            chain_id,
            ReasoningStep.INFER,
            "Analyzing leave policy requirements",
            ["Policy Document"],
            0.8
        )
        
        self.engine.add_reasoning_step(
            chain_id,
            ReasoningStep.CONCLUDE,
            "Sick leave policy allows 10 days per year",
            ["Policy Section 3.2", "HR Guidelines"],
            0.9
        )
        
        result = self.engine.complete_reasoning_chain(chain_id)
        
        assert result["chain_id"] == chain_id
        assert "conclusion" in result
        assert "confidence" in result
        assert "reasoning_trace" in result
        
        chain = self.engine.reasoning_chains[chain_id]
        assert chain["completed"]
    
    def test_get_reasoning_explanation(self):
        query = "What is the leave policy?"
        context = {}
        chain_id = self.engine.create_reasoning_chain(query, context)
        
        self.engine.add_reasoning_step(
            chain_id,
            ReasoningStep.CONCLUDE,
            "Final answer about leave policy",
            ["Evidence"],
            0.9
        )
        
        self.engine.complete_reasoning_chain(chain_id)
        
        explanation = self.engine.get_reasoning_explanation(chain_id)
        
        assert isinstance(explanation, str)
        assert "reasoned through" in explanation.lower()
        assert "conclusion" in explanation.lower()