"""Unit tests for security utilities."""

import pytest
from backend.utils.security import SecurityManager

class TestSecurityManager:
    def setup_method(self):
        self.security = SecurityManager("test-secret-key")
    
    def test_password_hashing(self):
        password = "test_password_123"
        hashed = self.security.hash_password(password)
        
        assert hashed != password
        assert self.security.verify_password(password, hashed)
        assert not self.security.verify_password("wrong_password", hashed)
    
    def test_token_generation_and_verification(self):
        user_id = "test_user_123"
        token = self.security.generate_token(user_id)
        
        assert token is not None
        assert len(token) > 0
        
        payload = self.security.verify_token(token)
        assert payload is not None
        assert payload["user_id"] == user_id
    
    def test_data_encryption(self):
        sensitive_data = "sensitive information"
        encrypted = self.security.encrypt_data(sensitive_data)
        
        assert encrypted != sensitive_data
        
        decrypted = self.security.decrypt_data(encrypted)
        assert decrypted == sensitive_data
    
    def test_api_key_generation(self):
        api_key = self.security.generate_api_key()
        
        assert api_key is not None
        assert len(api_key) == 43  # URL-safe base64 encoding of 32 bytes
    
    def test_input_validation(self):
        assert self.security.validate_input("normal text")
        assert not self.security.validate_input("<script>alert('xss')</script>")
        assert not self.security.validate_input("DROP TABLE users;")
        assert not self.security.validate_input("a" * 1001)  # Too long