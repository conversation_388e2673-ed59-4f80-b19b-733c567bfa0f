#!/usr/bin/env python3
"""
Comprehensive Test Suite for the Knowledge Fusion System.
Tests all components: Multi-source Fusion, Vector-Graph Integration, Conflict Resolution, and Cross-document Coreference.
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import knowledge fusion components
from ai.knowledge.fusion.multi_source_fusion import (
    MultiSourceFusion, KnowledgeItem, SourceType, SourceReliability, FusionMethod
)
from ai.knowledge.fusion.vector_graph_integration import (
    VectorGraphIntegration, SearchMode, IntegratedResult
)
from ai.knowledge.fusion.conflict_resolution import (
    ConflictResolution, ConflictType, ResolutionStrategy
)
from ai.knowledge.fusion.cross_document_coreference import (
    CrossDocumentCoreference, EntityType, EntityMention
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KnowledgeFusionTestSuite:
    """Comprehensive test suite for the Knowledge Fusion System."""
    
    def __init__(self):
        self.test_results = {}
        
        # Initialize components
        self.multi_source_fusion = MultiSourceFusion()
        self.vector_graph_integration = VectorGraphIntegration()
        self.conflict_resolution = ConflictResolution()
        self.cross_document_coreference = CrossDocumentCoreference()
        
        # Test data
        self.sample_documents = self._create_sample_documents()
        self.sample_knowledge_items = self._create_sample_knowledge_items()
    
    async def run_all_tests(self):
        """Run all knowledge fusion system tests."""
        logger.info("🚀 Starting Knowledge Fusion System Tests")
        logger.info("=" * 80)
        
        tests = [
            ("Multi-source Knowledge Fusion", self.test_multi_source_fusion),
            ("Vector-Graph Search Integration", self.test_vector_graph_integration),
            ("Knowledge Conflict Resolution", self.test_conflict_resolution),
            ("Cross-document Coreference", self.test_cross_document_coreference),
            ("Integrated Knowledge Fusion", self.test_integrated_fusion)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running {test_name} test...")
            try:
                result = await test_func()
                if result:
                    logger.info(f"✅ PASSED: {test_name}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ FAILED: {test_name}")
                self.test_results[test_name] = result
            except Exception as e:
                logger.error(f"❌ FAILED: {test_name} - {e}")
                self.test_results[test_name] = False
        
        # Print summary
        logger.info("\n" + "=" * 80)
        logger.info("🎯 KNOWLEDGE FUSION SYSTEM TEST SUMMARY")
        logger.info("=" * 80)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if passed_tests == total_tests:
            logger.info("🎉 All Knowledge Fusion System components are working perfectly!")
            logger.info("🚀 Ready for production deployment!")
        else:
            logger.warning("⚠️ Some tests failed. Please review the issues above.")
        
        return passed_tests == total_tests
    
    async def test_multi_source_fusion(self) -> bool:
        """Test Multi-source Knowledge Fusion functionality."""
        try:
            logger.info("🔄 Testing Multi-source Knowledge Fusion...")
            
            # Test different fusion methods
            fusion_methods = [
                FusionMethod.WEIGHTED_AVERAGE,
                FusionMethod.MAJORITY_VOTING,
                FusionMethod.BAYESIAN_INTEGRATION,
                FusionMethod.EVIDENCE_ACCUMULATION,
                FusionMethod.HIERARCHICAL,
                FusionMethod.ENSEMBLE
            ]
            
            results_by_method = {}
            
            for method in fusion_methods:
                logger.info(f"  Testing {method.value} fusion...")
                
                # Test fusion with sample knowledge items
                result = self.multi_source_fusion.fuse_knowledge(
                    self.sample_knowledge_items,
                    method=method
                )
                
                if not result:
                    logger.error(f"Failed to get fusion result for {method.value}")
                    return False
                
                results_by_method[method.value] = {
                    "content_length": len(result.content),
                    "confidence": result.confidence,
                    "source_count": len(result.source_items),
                    "reasoning_steps": len(result.reasoning)
                }
                
                logger.info(f"    ✅ {method.value}: confidence={result.confidence:.3f}, "
                           f"sources={len(result.source_items)}")
            
            # Test fusion statistics
            stats = self.multi_source_fusion.get_fusion_statistics(self.sample_knowledge_items)
            logger.info(f"✅ Fusion statistics: {stats['total_items']} items, "
                       f"{stats['unique_sources']} unique sources")
            
            # Test weight updates
            new_weights = {"document": 0.8, "database": 0.9}
            self.multi_source_fusion.update_source_weights(new_weights)
            logger.info("✅ Source weights updated successfully")
            
            logger.info("✅ Multi-source Knowledge Fusion test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Multi-source fusion test failed: {e}")
            return False
    
    async def test_vector_graph_integration(self) -> bool:
        """Test Vector-Graph Search Integration functionality."""
        try:
            logger.info("🔍 Testing Vector-Graph Search Integration...")
            
            # Test different search modes
            search_modes = [
                SearchMode.VECTOR_FIRST,
                SearchMode.GRAPH_FIRST,
                SearchMode.PARALLEL,
                SearchMode.ITERATIVE,
                SearchMode.HYBRID
            ]
            
            test_query = "What is the leave policy for employees at NUVO AI?"
            
            for mode in search_modes:
                logger.info(f"  Testing {mode.value} search mode...")
                
                # Perform integrated search
                results = await self.vector_graph_integration.integrated_search(
                    query=test_query,
                    mode=mode,
                    k=5
                )
                
                if not results:
                    logger.warning(f"No results for {mode.value} mode (expected with mock data)")
                    continue
                
                logger.info(f"    ✅ {mode.value}: {len(results)} results, "
                           f"avg_score={sum(r.combined_score for r in results)/len(results):.3f}")
            
            # Test search statistics
            mock_results = [
                IntegratedResult(
                    content="Mock result 1",
                    vector_score=0.8,
                    graph_score=0.7,
                    combined_score=0.75,
                    source_type="both",
                    relationships=[{"type": "semantic", "strength": 0.8}]
                ),
                IntegratedResult(
                    content="Mock result 2",
                    vector_score=0.6,
                    graph_score=0.9,
                    combined_score=0.75,
                    source_type="graph",
                    relationships=[{"type": "hierarchical", "strength": 0.9}]
                )
            ]
            
            stats = self.vector_graph_integration.get_search_statistics(mock_results)
            logger.info(f"✅ Search statistics: {stats['total_results']} results, "
                       f"avg_combined_score={stats['average_combined_score']:.3f}")
            
            # Test configuration updates
            new_config = {"max_vector_results": 100, "similarity_threshold": 0.8}
            self.vector_graph_integration.update_config(new_config)
            logger.info("✅ Search configuration updated successfully")
            
            logger.info("✅ Vector-Graph Search Integration test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Vector-graph integration test failed: {e}")
            return False
    
    async def test_conflict_resolution(self) -> bool:
        """Test Knowledge Conflict Resolution functionality."""
        try:
            logger.info("⚖️ Testing Knowledge Conflict Resolution...")
            
            # Create conflicting knowledge items
            conflicting_items = [
                KnowledgeItem(
                    content="The company has 500 employees",
                    source_id="hr_database",
                    source_type=SourceType.DATABASE,
                    confidence=0.9,
                    reliability=SourceReliability.HIGH
                ),
                KnowledgeItem(
                    content="The company has 450 employees",
                    source_id="annual_report",
                    source_type=SourceType.DOCUMENT,
                    confidence=0.8,
                    reliability=SourceReliability.MEDIUM
                ),
                KnowledgeItem(
                    content="The company has approximately 480 employees",
                    source_id="website",
                    source_type=SourceType.WEB,
                    confidence=0.6,
                    reliability=SourceReliability.LOW
                )
            ]
            
            # Test conflict detection
            conflicts = self.conflict_resolution.detect_conflicts(
                conflicting_items,
                conflict_types=[ConflictType.NUMERICAL, ConflictType.FACTUAL]
            )
            
            logger.info(f"✅ Detected {len(conflicts)} conflicts")
            
            # Test different resolution strategies
            if conflicts:
                conflict = conflicts[0]
                
                resolution_strategies = [
                    ResolutionStrategy.AUTHORITY,
                    ResolutionStrategy.CONFIDENCE,
                    ResolutionStrategy.RECENCY,
                    ResolutionStrategy.MAJORITY,
                    ResolutionStrategy.EVIDENCE,
                    ResolutionStrategy.HYBRID
                ]
                
                for strategy in resolution_strategies:
                    logger.info(f"  Testing {strategy.value} resolution...")
                    
                    resolution = self.conflict_resolution.resolve_conflict(
                        conflict,
                        strategy=strategy
                    )
                    
                    if not resolution:
                        logger.error(f"Failed to resolve conflict with {strategy.value}")
                        return False
                    
                    logger.info(f"    ✅ {strategy.value}: confidence={resolution.confidence:.3f}, "
                               f"reasoning_steps={len(resolution.reasoning)}")
            
            # Test conflict statistics
            conflict_stats = self.conflict_resolution.get_conflict_statistics()
            resolution_stats = self.conflict_resolution.get_resolution_statistics()
            
            logger.info(f"✅ Conflict statistics: {conflict_stats.get('total_conflicts', 0)} conflicts detected")
            logger.info(f"✅ Resolution statistics: {resolution_stats.get('total_resolutions', 0)} resolutions performed")
            
            logger.info("✅ Knowledge Conflict Resolution test completed successfully")
            return True

        except Exception as e:
            logger.error(f"Conflict resolution test failed: {e}")
            return False

    async def test_cross_document_coreference(self) -> bool:
        """Test Cross-document Coreference functionality."""
        try:
            logger.info("🔗 Testing Cross-document Coreference...")

            # Test entity extraction
            mentions = self.cross_document_coreference.extract_entity_mentions(self.sample_documents)

            if not mentions:
                logger.error("Failed to extract entity mentions")
                return False

            logger.info(f"✅ Extracted {len(mentions)} entity mentions")

            # Test coreference resolution
            links = self.cross_document_coreference.resolve_coreferences(mentions)

            logger.info(f"✅ Resolved {len(links)} coreference links")

            # Test entity clustering
            clusters = self.cross_document_coreference.cluster_entities(links)

            logger.info(f"✅ Created {len(clusters)} entity clusters")

            # Test entity statistics
            stats = self.cross_document_coreference.get_entity_statistics()

            logger.info(f"✅ Entity statistics: {stats.get('total_mentions', 0)} mentions, "
                       f"{stats.get('total_clusters', 0)} clusters")

            # Test document-specific mentions
            doc_mentions = self.cross_document_coreference.get_mentions_by_document(
                self.sample_documents[0]["id"]
            )

            logger.info(f"✅ Document-specific mentions: {len(doc_mentions)} mentions in document 1")

            # Test export functionality
            export_data = self.cross_document_coreference.export_coreference_data()

            logger.info(f"✅ Exported coreference data with {len(export_data.get('mentions', []))} mentions, "
                       f"{len(export_data.get('clusters', []))} clusters")

            logger.info("✅ Cross-document Coreference test completed successfully")
            return True

        except Exception as e:
            logger.error(f"Cross-document coreference test failed: {e}")
            return False

    async def test_integrated_fusion(self) -> bool:
        """Test integrated knowledge fusion workflow."""
        try:
            logger.info("🔄 Testing Integrated Knowledge Fusion Workflow...")

            # Step 1: Extract entity mentions from documents
            logger.info("Step 1: Extracting entity mentions...")
            mentions = self.cross_document_coreference.extract_entity_mentions(self.sample_documents)

            if not mentions:
                logger.error("Failed to extract entity mentions")
                return False

            logger.info(f"✅ Extracted {len(mentions)} entity mentions")

            # Step 2: Resolve coreferences and cluster entities
            logger.info("Step 2: Resolving coreferences...")
            links = self.cross_document_coreference.resolve_coreferences(mentions)
            clusters = self.cross_document_coreference.cluster_entities(links)

            logger.info(f"✅ Created {len(clusters)} entity clusters")

            # Step 3: Perform vector-graph search for relevant information
            logger.info("Step 3: Performing vector-graph search...")
            query = "What is the company's leave policy?"
            search_results = await self.vector_graph_integration.integrated_search(
                query=query,
                mode=SearchMode.HYBRID,
                k=5
            )

            logger.info(f"✅ Found {len(search_results)} search results")

            # Step 4: Create knowledge items from search results
            logger.info("Step 4: Creating knowledge items...")
            knowledge_items = []

            # Create from search results (mock data)
            for i, result in enumerate(search_results):
                knowledge_items.append(
                    KnowledgeItem(
                        content=result.content,
                        source_id=f"search_result_{i+1}",
                        source_type=SourceType.VECTOR_SEARCH if result.source_type == "vector" else SourceType.GRAPH_SEARCH,
                        confidence=result.combined_score,
                        reliability=SourceReliability.MEDIUM
                    )
                )

            # Add some conflicting items
            knowledge_items.extend([
                KnowledgeItem(
                    content="Employees are entitled to 20 days of paid leave per year",
                    source_id="hr_policy",
                    source_type=SourceType.DOCUMENT,
                    confidence=0.9,
                    reliability=SourceReliability.HIGH
                ),
                KnowledgeItem(
                    content="Staff members receive 15 days of annual leave plus 5 sick days",
                    source_id="employee_handbook",
                    source_type=SourceType.DOCUMENT,
                    confidence=0.85,
                    reliability=SourceReliability.MEDIUM
                )
            ])

            logger.info(f"✅ Created {len(knowledge_items)} knowledge items")

            # Step 5: Detect and resolve conflicts
            logger.info("Step 5: Detecting and resolving conflicts...")
            conflicts = self.conflict_resolution.detect_conflicts(knowledge_items)

            resolutions = []
            for conflict in conflicts:
                resolution = self.conflict_resolution.resolve_conflict(
                    conflict,
                    strategy=ResolutionStrategy.HYBRID
                )
                resolutions.append(resolution)

            logger.info(f"✅ Resolved {len(resolutions)} conflicts")

            # Step 6: Fuse knowledge
            logger.info("Step 6: Fusing knowledge...")
            fused_result = self.multi_source_fusion.fuse_knowledge(
                knowledge_items,
                method=FusionMethod.ENSEMBLE
            )

            logger.info(f"✅ Fused knowledge with confidence: {fused_result.confidence:.3f}")
            logger.info(f"✅ Final result: {fused_result.content[:100]}...")

            # Step 7: Verify integration
            logger.info("Step 7: Verifying integration...")

            integration_success = (
                len(mentions) > 0 and
                len(clusters) > 0 and
                len(search_results) > 0 and
                len(knowledge_items) > 0 and
                fused_result.confidence > 0.5
            )

            if integration_success:
                logger.info("✅ Integrated Knowledge Fusion workflow completed successfully")
                return True
            else:
                logger.error("❌ Integrated Knowledge Fusion workflow failed verification")
                return False

        except Exception as e:
            logger.error(f"Integrated fusion test failed: {e}")
            return False

    def _create_sample_documents(self) -> List[Dict[str, Any]]:
        """Create sample documents for testing."""
        return [
            {
                "id": "doc1",
                "content": "NUVO AI is a leading technology company with over 500 employees. "
                          "The company was founded in 2015 by Dr. Sarah Johnson and has offices "
                          "in San Francisco, New York, and London. NUVO's leave policy allows "
                          "employees to take 20 days of paid time off per year."
            },
            {
                "id": "doc2",
                "content": "According to the annual report, NUVO Artificial Intelligence Corp. "
                          "currently employs approximately 450 people across its global offices. "
                          "Dr. Johnson, the CEO, announced new benefits including flexible work "
                          "arrangements and competitive leave policies."
            },
            {
                "id": "doc3",
                "content": "The HR handbook at NUVO states that staff members receive 15 days "
                          "of annual leave plus 5 sick days. The company's headquarters is located "
                          "in San Francisco, with satellite offices in NYC and London."
            }
        ]

    def _create_sample_knowledge_items(self) -> List[KnowledgeItem]:
        """Create sample knowledge items for testing."""
        return [
            KnowledgeItem(
                content="NUVO AI has 500 employees worldwide",
                source_id="company_database",
                source_type=SourceType.DATABASE,
                confidence=0.9,
                reliability=SourceReliability.HIGH,
                metadata={"last_updated": "2023-06-15"}
            ),
            KnowledgeItem(
                content="NUVO AI employs approximately 450 people",
                source_id="annual_report",
                source_type=SourceType.DOCUMENT,
                confidence=0.8,
                reliability=SourceReliability.MEDIUM,
                metadata={"document_date": "2023-04-01"}
            ),
            KnowledgeItem(
                content="NUVO Artificial Intelligence Corporation has offices in San Francisco, New York, and London",
                source_id="company_website",
                source_type=SourceType.WEB,
                confidence=0.7,
                reliability=SourceReliability.MEDIUM,
                metadata={"url": "https://nuvoai.example.com/about"}
            ),
            KnowledgeItem(
                content="Employees at NUVO are entitled to 20 days of paid leave annually",
                source_id="hr_policy",
                source_type=SourceType.DOCUMENT,
                confidence=0.85,
                reliability=SourceReliability.HIGH,
                metadata={"policy_id": "HR-2023-05"}
            ),
            KnowledgeItem(
                content="NUVO AI staff receive 15 days of annual leave plus 5 sick days",
                source_id="employee_handbook",
                source_type=SourceType.DOCUMENT,
                confidence=0.75,
                reliability=SourceReliability.MEDIUM,
                metadata={"version": "2023.2"}
            )
        ]

async def main():
    """Main test execution function."""
    test_suite = KnowledgeFusionTestSuite()

    try:
        success = await test_suite.run_all_tests()

        # Save test results
        results_file = "knowledge_fusion_system_test_results.json"
        with open(results_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "test_results": {k: v for k, v in test_suite.test_results.items()},
                "overall_success": success,
                "summary": f"{sum(test_suite.test_results.values())}/{len(test_suite.test_results)} tests passed"
            }, f, indent=2)

        logger.info(f"📄 Detailed results saved to {results_file}")

        return success

    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return False

if __name__ == "__main__":
    # Run the test suite
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
