#!/usr/bin/env python3
"""
CHaBot API Usage Demo
Demonstrates User and Admin API functionality
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime
from typing import Dict, Any

BASE_URL = "http://localhost:8002"

class CHaBotAPIDemo:
    """Demo class for CHaBot API usage."""
    
    def __init__(self, base_url: str = BASE_URL):
        """Initialize the demo."""
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                if 'application/json' in response.headers.get('content-type', ''):
                    data = await response.json()
                else:
                    data = await response.text()
                
                return {
                    "status": response.status,
                    "data": data,
                    "success": 200 <= response.status < 300
                }
        except Exception as e:
            return {
                "status": 0,
                "data": {"error": str(e)},
                "success": False
            }
    
    def print_response(self, title: str, response: Dict[str, Any]):
        """Print formatted response."""
        status_icon = "✅" if response["success"] else "❌"
        print(f"\n{status_icon} {title}")
        print(f"Status: {response['status']}")
        
        if isinstance(response["data"], dict):
            print(f"Response: {json.dumps(response['data'], indent=2)}")
        else:
            print(f"Response: {response['data']}")
        print("-" * 80)
    
    async def demo_user_apis(self):
        """Demonstrate User APIs."""
        print("\n" + "="*80)
        print("👤 USER API DEMONSTRATIONS")
        print("="*80)
        
        # 1. Basic System Info
        response = await self.make_request("GET", "/")
        self.print_response("System Information", response)
        
        response = await self.make_request("GET", "/health")
        self.print_response("Health Check", response)
        
        # 2. Chat APIs
        print("\n💬 CHAT FUNCTIONALITY")
        
        chat_data = {
            "message": "Hello CHaBot! Can you help me understand AI?",
            "user_id": "demo_user",
            "session_id": "demo_session_001"
        }
        response = await self.make_request("POST", "/chat", json=chat_data)
        self.print_response("Send Chat Message", response)
        
        # Get session info
        response = await self.make_request("GET", "/chat/sessions/demo_session_001")
        self.print_response("Get Session Info", response)
        
        # 3. Agent APIs
        print("\n🤖 AGENT FUNCTIONALITY")
        
        # List agents
        response = await self.make_request("GET", "/agents")
        self.print_response("List Available Agents", response)
        
        # Get specific agent
        response = await self.make_request("GET", "/agents/research-001")
        self.print_response("Get Research Agent Info", response)
        
        # Assign task to agent
        task_data = {
            "description": "Research the latest developments in machine learning",
            "task_type": "research",
            "priority": "high"
        }
        response = await self.make_request("POST", "/agents/research-001/task", json=task_data)
        self.print_response("Assign Task to Agent", response)
        
        # Coordinate multiple agents
        coord_data = {
            "description": "Analyze AI market trends and create comprehensive report",
            "task_type": "analysis",
            "priority": "high"
        }
        response = await self.make_request("POST", "/agents/coordinate", json=coord_data)
        self.print_response("Coordinate Multiple Agents", response)
        
        # 4. Memory APIs
        print("\n🧠 MEMORY FUNCTIONALITY")
        
        # Query memory
        response = await self.make_request("GET", "/memory/query", params={
            "query": "machine learning",
            "limit": 5
        })
        self.print_response("Query Memory System", response)
        
        # Search episodes
        response = await self.make_request("GET", "/memory/episodes", params={
            "query": "AI",
            "limit": 3
        })
        self.print_response("Search Episodic Memories", response)
        
        # Search concepts
        response = await self.make_request("GET", "/memory/concepts", params={
            "query": "artificial intelligence",
            "limit": 3
        })
        self.print_response("Search Semantic Concepts", response)
        
        # 5. Tool APIs
        print("\n🛠️ TOOL FUNCTIONALITY")
        
        # List tools
        response = await self.make_request("GET", "/tools")
        self.print_response("List Available Tools", response)
        
        # Get tool info
        response = await self.make_request("GET", "/tools/calculate")
        self.print_response("Get Calculator Tool Info", response)
        
        # Execute tool
        exec_data = {
            "tool_name": "calculate",
            "parameters": {
                "expression": "sqrt(144) + 2^3"
            }
        }
        response = await self.make_request("POST", "/tools/execute", json=exec_data)
        self.print_response("Execute Calculator Tool", response)
        
        # Get tool categories
        response = await self.make_request("GET", "/tools/categories")
        self.print_response("Get Tool Categories", response)
    
    async def demo_admin_apis(self):
        """Demonstrate Admin APIs."""
        print("\n" + "="*80)
        print("🔐 ADMIN API DEMONSTRATIONS")
        print("="*80)
        
        # 1. System Management
        print("\n📊 SYSTEM MANAGEMENT")
        
        response = await self.make_request("GET", "/system/status")
        self.print_response("Detailed System Status", response)
        
        response = await self.make_request("GET", "/system/statistics")
        self.print_response("System Statistics", response)
        
        # 2. Chat Administration
        print("\n💬 CHAT ADMINISTRATION")
        
        response = await self.make_request("GET", "/chat/sessions")
        self.print_response("List All Chat Sessions", response)
        
        # 3. Memory Administration
        print("\n🧠 MEMORY ADMINISTRATION")
        
        # Store memory (Admin function)
        memory_data = {
            "memory_type": "episodic",
            "content": {
                "title": "Admin Demo Session",
                "description": "Demonstration of admin API functionality",
                "content": {
                    "demo_type": "admin_api",
                    "timestamp": datetime.now().isoformat(),
                    "features_tested": ["system_stats", "memory_admin", "chat_admin"]
                },
                "importance": 0.8,
                "tags": ["demo", "admin", "api_test"]
            }
        }
        response = await self.make_request("POST", "/memory/store", json=memory_data)
        self.print_response("Store Memory (Admin)", response)
        
        # Get memory statistics
        response = await self.make_request("GET", "/memory/statistics")
        self.print_response("Memory System Statistics", response)
        
        # Trigger memory consolidation
        response = await self.make_request("POST", "/memory/consolidate")
        self.print_response("Trigger Memory Consolidation", response)
    
    async def demo_workflow_scenarios(self):
        """Demonstrate real-world workflow scenarios."""
        print("\n" + "="*80)
        print("🎯 WORKFLOW SCENARIOS")
        print("="*80)
        
        # Scenario 1: Research and Analysis Workflow
        print("\n📊 SCENARIO 1: Research & Analysis Workflow")
        
        # Step 1: User asks for research
        chat_data = {
            "message": "I need to research quantum computing trends for my presentation",
            "user_id": "researcher_001",
            "session_id": "research_session_001"
        }
        response = await self.make_request("POST", "/chat", json=chat_data)
        self.print_response("1. User Request", response)
        
        # Step 2: Coordinate research and analysis agents
        coord_data = {
            "description": "Research quantum computing trends and analyze market implications",
            "task_type": "research_analysis",
            "priority": "high"
        }
        response = await self.make_request("POST", "/agents/coordinate", json=coord_data)
        self.print_response("2. Agent Coordination", response)
        
        # Step 3: Use tools for calculations
        calc_data = {
            "tool_name": "calculate",
            "parameters": {
                "expression": "2^10 * 1.5"  # Simulating quantum bit calculations
            }
        }
        response = await self.make_request("POST", "/tools/execute", json=calc_data)
        self.print_response("3. Tool Usage", response)
        
        # Step 4: Query memory for related information
        response = await self.make_request("GET", "/memory/query", params={
            "query": "quantum computing research",
            "limit": 5
        })
        self.print_response("4. Memory Query", response)
        
        # Scenario 2: Learning and Knowledge Building
        print("\n🎓 SCENARIO 2: Learning & Knowledge Building")
        
        # Step 1: User learning session
        chat_data = {
            "message": "Explain machine learning algorithms step by step",
            "user_id": "student_001",
            "session_id": "learning_session_001"
        }
        response = await self.make_request("POST", "/chat", json=chat_data)
        self.print_response("1. Learning Request", response)
        
        # Step 2: Store learning progress in memory
        memory_data = {
            "memory_type": "episodic",
            "content": {
                "title": "ML Learning Session",
                "description": "Student learning about machine learning algorithms",
                "content": {
                    "topic": "machine_learning_algorithms",
                    "student_id": "student_001",
                    "progress": "introduction_completed",
                    "next_steps": ["supervised_learning", "unsupervised_learning"]
                },
                "importance": 0.7,
                "tags": ["learning", "ml", "education"]
            }
        }
        response = await self.make_request("POST", "/memory/store", json=memory_data)
        self.print_response("2. Store Learning Progress", response)
        
        # Step 3: Search for related concepts
        response = await self.make_request("GET", "/memory/concepts", params={
            "query": "machine learning",
            "limit": 5
        })
        self.print_response("3. Find Related Concepts", response)
    
    async def run_full_demo(self):
        """Run the complete API demonstration."""
        print("🚀 CHaBot API Demonstration")
        print("=" * 80)
        print("This demo showcases User and Admin API functionality")
        print("Server should be running on:", self.base_url)
        print("=" * 80)
        
        try:
            # Test server connectivity
            response = await self.make_request("GET", "/health")
            if not response["success"]:
                print("❌ Server not accessible. Please start the CHaBot server first.")
                print("Run: python start_chabot.py --port 8002")
                return False
            
            print("✅ Server is accessible and running")
            
            # Run demonstrations
            await self.demo_user_apis()
            await self.demo_admin_apis()
            await self.demo_workflow_scenarios()
            
            # Summary
            print("\n" + "="*80)
            print("📋 DEMONSTRATION SUMMARY")
            print("="*80)
            print("✅ User APIs: Chat, Agents, Memory, Tools")
            print("✅ Admin APIs: System Management, Memory Administration")
            print("✅ Workflow Scenarios: Research & Learning")
            print("\n🎉 All API demonstrations completed successfully!")
            print("📖 See API_DOCUMENTATION.md for complete API reference")
            
            return True
            
        except Exception as e:
            print(f"❌ Demo failed: {e}")
            return False

async def main():
    """Main demo execution."""
    async with CHaBotAPIDemo() as demo:
        success = await demo.run_full_demo()
        return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Demo execution failed: {e}")
        sys.exit(1)
