#!/usr/bin/env python3
"""
Integrated CHaBot System - Main Application Entry Point
Brings together all components: AI, Agents, Memory, Tools, Reasoning, and Communication
"""

import asyncio
import logging
import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass
import signal
import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import configuration
from integrated_config import Config

# Set up logging first
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import available core AI components
try:
    from ai.memory.enhanced_integrated_memory_system import EnhancedIntegratedMemorySystem
    MEMORY_SYSTEM_AVAILABLE = True
    logger.info("Enhanced Integrated Memory System available")
except ImportError:
    MEMORY_SYSTEM_AVAILABLE = False
    logger.warning("Enhanced Integrated Memory System not available")

try:
    from agents.tools.registry.tool_registry import ToolRegistry
    from agents.tools.calculator.basic_calculator import EnhancedCalculator
    TOOL_SYSTEM_AVAILABLE = True
    logger.info("Tool system available")
except ImportError:
    TOOL_SYSTEM_AVAILABLE = False
    logger.warning("Tool system not available")

# Optional components (will be mocked if not available)
try:
    from ai.llm.local_llm_infrastructure import LocalLLMInfrastructure
    LLM_INFRASTRUCTURE_AVAILABLE = True
    logger.info("LLM Infrastructure available")
except ImportError:
    LLM_INFRASTRUCTURE_AVAILABLE = False
    logger.warning("LLM Infrastructure not available - using mock")

try:
    from ai.reasoning.advanced_reasoning_engine import AdvancedReasoningEngine
    REASONING_ENGINE_AVAILABLE = True
    logger.info("Reasoning Engine available")
except ImportError:
    REASONING_ENGINE_AVAILABLE = False
    logger.warning("Reasoning Engine not available - using mock")

try:
    from agents.orchestrator.agent_orchestrator import AgentOrchestrator
    AGENT_ORCHESTRATOR_AVAILABLE = True
    logger.info("Agent Orchestrator available")
except ImportError:
    AGENT_ORCHESTRATOR_AVAILABLE = False
    logger.warning("Agent Orchestrator not available - using mock")

# Import backend services
from backend.api.chat_service import ChatService
from backend.api.agent_service import AgentService
from backend.api.memory_service import MemoryService
from backend.api.tool_service import ToolService

# Logger will be set up after imports

@dataclass
class SystemStatus:
    """System status information."""
    status: str
    components: Dict[str, bool]
    startup_time: datetime
    uptime_seconds: float
    version: str = "1.0.0"

class IntegratedCHaBotSystem:
    """
    Integrated CHaBot System that orchestrates all components.
    """
    
    def __init__(self):
        """Initialize the integrated system."""
        self.config = Config
        self.startup_time = datetime.now()
        self.is_running = False

        # Component status tracking
        self.component_status = {
            "memory_system": False,
            "tool_registry": False,
            "api_services": False,
            "llm_infrastructure": False,
            "reasoning_engine": False,
            "agent_orchestrator": False
        }

        # Initialize core components
        self.memory_system = None
        self.tool_registry = None

        # Optional components (may be mocked)
        self.llm_infrastructure = None
        self.reasoning_engine = None
        self.agent_orchestrator = None

        # Initialize API services
        self.chat_service = None
        self.agent_service = None
        self.memory_service = None
        self.tool_service = None

        # FastAPI app
        self.app = None

        logger.info("Integrated CHaBot System initialized")
    
    async def initialize_components(self):
        """Initialize all system components."""
        logger.info("🚀 Initializing CHaBot System Components...")

        try:
            # 1. Initialize Memory System (Core Component)
            if MEMORY_SYSTEM_AVAILABLE:
                logger.info("1️⃣ Initializing Memory System...")
                self.memory_system = EnhancedIntegratedMemorySystem(
                    database_path=self.config.DATABASE_PATH,
                    working_memory_capacity=self.config.WORKING_MEMORY_CAPACITY,
                    auto_consolidation=self.config.AUTO_CONSOLIDATION
                )
                await self.memory_system.start()
                self.component_status["memory_system"] = True
                logger.info("✅ Memory System initialized")
            else:
                logger.warning("⚠️ Memory System not available - skipping")

            # 2. Initialize Tool Registry (Core Component)
            if TOOL_SYSTEM_AVAILABLE:
                logger.info("2️⃣ Initializing Tool Registry...")
                self.tool_registry = ToolRegistry()
                await self._register_built_in_tools()
                self.component_status["tool_registry"] = True
                logger.info("✅ Tool Registry initialized")
            else:
                logger.warning("⚠️ Tool Registry not available - skipping")

            # 3. Initialize LLM Infrastructure (Optional)
            if LLM_INFRASTRUCTURE_AVAILABLE:
                logger.info("3️⃣ Initializing LLM Infrastructure...")
                self.llm_infrastructure = LocalLLMInfrastructure()
                # Don't await initialization as it may require external services
                self.component_status["llm_infrastructure"] = True
                logger.info("✅ LLM Infrastructure initialized")
            else:
                logger.warning("⚠️ LLM Infrastructure not available - using mock")

            # 4. Initialize Reasoning Engine (Optional)
            if REASONING_ENGINE_AVAILABLE and self.llm_infrastructure and self.memory_system:
                logger.info("4️⃣ Initializing Reasoning Engine...")
                self.reasoning_engine = AdvancedReasoningEngine(
                    llm_infrastructure=self.llm_infrastructure,
                    memory_system=self.memory_system,
                    tool_registry=self.tool_registry
                )
                self.component_status["reasoning_engine"] = True
                logger.info("✅ Reasoning Engine initialized")
            else:
                logger.warning("⚠️ Reasoning Engine not available - using mock")

            # 5. Initialize Agent Orchestrator (Optional)
            if AGENT_ORCHESTRATOR_AVAILABLE:
                logger.info("5️⃣ Initializing Agent Orchestrator...")
                self.agent_orchestrator = AgentOrchestrator(
                    llm_infrastructure=self.llm_infrastructure,
                    memory_system=self.memory_system,
                    tool_registry=self.tool_registry,
                    reasoning_engine=self.reasoning_engine
                )
                self.component_status["agent_orchestrator"] = True
                logger.info("✅ Agent Orchestrator initialized")
            else:
                logger.warning("⚠️ Agent Orchestrator not available - using mock")

            # 6. Initialize API Services (Always Available)
            logger.info("6️⃣ Initializing API Services...")
            await self._initialize_api_services()
            self.component_status["api_services"] = True
            logger.info("✅ API Services initialized")

            # 7. Create FastAPI Application
            logger.info("7️⃣ Creating FastAPI Application...")
            self._create_fastapi_app()
            logger.info("✅ FastAPI Application created")

            self.is_running = True

            # Log component status
            active_components = sum(self.component_status.values())
            total_components = len(self.component_status)
            logger.info(f"🎉 CHaBot System initialized with {active_components}/{total_components} components active!")

        except Exception as e:
            logger.error(f"❌ Failed to initialize components: {e}")
            await self.shutdown()
            raise
    
    async def _register_built_in_tools(self):
        """Register built-in tools with the tool registry."""
        if not self.tool_registry:
            return

        try:
            # Calculator tools
            if TOOL_SYSTEM_AVAILABLE:
                calculator = EnhancedCalculator()
                self.tool_registry.register_tool(
                    name="calculate",
                    func=calculator.calculate,
                    description="Perform mathematical calculations",
                    category="calculation"
                )
                logger.info("✅ Calculator tool registered")

            # Register other tools if available
            # For now, we'll register mock tools in the tool service

            logger.info("Built-in tools registered successfully")

        except Exception as e:
            logger.warning(f"Failed to register some tools: {e}")
    
    async def _initialize_specialized_agents(self):
        """Initialize specialized agents (if orchestrator is available)."""
        if not self.agent_orchestrator:
            logger.info("Agent orchestrator not available - skipping specialized agents")
            return

        # This would initialize specialized agents if they were available
        # For now, the agent service will provide mock agents
        logger.info("Specialized agents will be provided by agent service")
    
    async def _initialize_api_services(self):
        """Initialize API services."""
        self.chat_service = ChatService(
            reasoning_engine=self.reasoning_engine,
            memory_system=self.memory_system,
            agent_orchestrator=self.agent_orchestrator
        )

        self.agent_service = AgentService(
            agent_orchestrator=self.agent_orchestrator,
            communication_infrastructure=None  # Will be mocked in service
        )

        self.memory_service = MemoryService(
            memory_system=self.memory_system
        )

        self.tool_service = ToolService(
            tool_registry=self.tool_registry
        )

        logger.info("API services initialized")
    
    def _create_fastapi_app(self):
        """Create and configure FastAPI application."""
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            logger.info("FastAPI application starting up...")
            yield
            # Shutdown
            logger.info("FastAPI application shutting down...")
            await self.shutdown()
        
        self.app = FastAPI(
            title="CHaBot Integrated System",
            description="Comprehensive AI Chatbot with Advanced Reasoning and Multi-Agent Coordination",
            version="1.0.0",
            lifespan=lifespan
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.CORS_ORIGINS,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Add routes
        self._add_routes()
    
    def _add_routes(self):
        """Add API routes to FastAPI application."""
        from fastapi import HTTPException
        from pydantic import BaseModel

        # Request models
        class ChatRequest(BaseModel):
            message: str
            user_id: str = "default"
            session_id: str = None

        class MemoryStoreRequest(BaseModel):
            memory_type: str
            content: dict

        class TaskRequest(BaseModel):
            description: str
            task_type: str = "general"
            priority: str = "medium"

        class ToolExecuteRequest(BaseModel):
            tool_name: str
            parameters: dict = {}

        # Basic routes
        @self.app.get("/")
        async def root():
            return {
                "message": "CHaBot Integrated System",
                "status": "running",
                "version": self.config.APP_VERSION,
                "components": self.component_status
            }

        @self.app.get("/health")
        async def health_check():
            return self.get_system_status()

        # Chat endpoints
        @self.app.post("/chat")
        async def chat_endpoint(request: ChatRequest):
            try:
                return await self.chat_service.process_message(
                    request.message,
                    request.user_id,
                    request.session_id
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/chat/sessions")
        async def list_chat_sessions():
            try:
                return await self.chat_service.list_active_sessions()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/chat/sessions/{session_id}")
        async def get_chat_session(session_id: str):
            try:
                session = await self.chat_service.get_session_info(session_id)
                if not session:
                    raise HTTPException(status_code=404, detail="Session not found")
                return session
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # Agent endpoints
        @self.app.get("/agents")
        async def list_agents():
            try:
                return await self.agent_service.list_agents()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/agents/{agent_id}")
        async def get_agent_status(agent_id: str):
            try:
                agent = await self.agent_service.get_agent_status(agent_id)
                if not agent:
                    raise HTTPException(status_code=404, detail="Agent not found")
                return agent
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/agents/{agent_id}/task")
        async def assign_task_to_agent(agent_id: str, request: TaskRequest):
            try:
                task = {
                    "description": request.description,
                    "type": request.task_type,
                    "priority": request.priority
                }
                return await self.agent_service.assign_task(agent_id, task)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/agents/coordinate")
        async def coordinate_agents(request: TaskRequest):
            try:
                task = {
                    "description": request.description,
                    "type": request.task_type,
                    "priority": request.priority
                }
                return await self.agent_service.coordinate_agents(task)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # Memory endpoints
        @self.app.get("/memory/query")
        async def query_memory(query: str, limit: int = 10, memory_types: str = None):
            try:
                types = memory_types.split(",") if memory_types else None
                return await self.memory_service.query_memory(query, limit, types)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/memory/store")
        async def store_memory(request: MemoryStoreRequest):
            try:
                return await self.memory_service.store_memory(request.memory_type, request.content)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/memory/statistics")
        async def memory_statistics():
            try:
                return await self.memory_service.get_memory_statistics()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/memory/consolidate")
        async def consolidate_memory():
            try:
                return await self.memory_service.consolidate_memory()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/memory/episodes")
        async def search_episodes(query: str, limit: int = 10, episode_type: str = None):
            try:
                return await self.memory_service.search_episodes(query, limit, episode_type)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/memory/concepts")
        async def search_concepts(query: str, limit: int = 10, concept_type: str = None):
            try:
                return await self.memory_service.search_concepts(query, limit, concept_type)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # Tool endpoints (order matters - specific routes before parameterized ones)
        @self.app.get("/tools")
        async def list_tools():
            try:
                return await self.tool_service.list_tools()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/tools/categories")
        async def get_tool_categories():
            try:
                return await self.tool_service.get_tool_categories()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/tools/categories/{category}")
        async def get_tools_by_category(category: str):
            try:
                return await self.tool_service.get_tools_by_category(category)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/tools/execute")
        async def execute_tool(request: ToolExecuteRequest):
            try:
                return await self.tool_service.execute_tool(
                    request.tool_name,
                    **request.parameters
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/tools/{tool_name}")
        async def get_tool_info(tool_name: str):
            try:
                tool = await self.tool_service.get_tool_info(tool_name)
                if not tool:
                    raise HTTPException(status_code=404, detail="Tool not found")
                return tool
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # System endpoints
        @self.app.get("/system/status")
        async def system_status():
            return self.get_system_status()

        @self.app.get("/system/statistics")
        async def system_statistics():
            try:
                return await self.get_system_statistics()
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
    
    def get_system_status(self) -> SystemStatus:
        """Get current system status."""
        uptime = (datetime.now() - self.startup_time).total_seconds()
        
        return SystemStatus(
            status="running" if self.is_running else "stopped",
            components=self.component_status.copy(),
            startup_time=self.startup_time,
            uptime_seconds=uptime
        ).__dict__
    
    async def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        stats = {
            "system": self.get_system_status(),
            "memory": {},
            "agents": {},
            "tools": {},
            "reasoning": {},
            "knowledge": {}
        }
        
        try:
            if self.memory_system:
                stats["memory"] = self.memory_system.get_comprehensive_statistics()
            
            if self.agent_orchestrator:
                stats["agents"] = await self.agent_orchestrator.get_statistics()
            
            if self.tool_registry:
                stats["tools"] = self.tool_registry.get_registry_statistics()
            
            if self.reasoning_engine:
                stats["reasoning"] = await self.reasoning_engine.get_statistics()
            
            if self.knowledge_fusion:
                stats["knowledge"] = await self.knowledge_fusion.get_statistics()
                
        except Exception as e:
            logger.error(f"Error getting system statistics: {e}")
        
        return stats
    
    async def shutdown(self):
        """Gracefully shutdown all system components."""
        logger.info("🛑 Shutting down CHaBot System...")

        self.is_running = False

        # Shutdown components safely
        try:
            if self.memory_system:
                await self.memory_system.stop()
                logger.info("✅ Memory system stopped")
        except Exception as e:
            logger.warning(f"Error stopping memory system: {e}")

        try:
            if self.agent_orchestrator and hasattr(self.agent_orchestrator, 'shutdown'):
                await self.agent_orchestrator.shutdown()
                logger.info("✅ Agent orchestrator stopped")
        except Exception as e:
            logger.warning(f"Error stopping agent orchestrator: {e}")

        try:
            if self.reasoning_engine and hasattr(self.reasoning_engine, 'shutdown'):
                await self.reasoning_engine.shutdown()
                logger.info("✅ Reasoning engine stopped")
        except Exception as e:
            logger.warning(f"Error stopping reasoning engine: {e}")

        try:
            if self.llm_infrastructure and hasattr(self.llm_infrastructure, 'shutdown'):
                await self.llm_infrastructure.shutdown()
                logger.info("✅ LLM infrastructure stopped")
        except Exception as e:
            logger.warning(f"Error stopping LLM infrastructure: {e}")

        logger.info("✅ CHaBot System shutdown complete")
    
    async def run_server(self, host: str = "0.0.0.0", port: int = 8000):
        """Run the integrated system server."""
        if not self.is_running:
            await self.initialize_components()
        
        logger.info(f"🚀 Starting CHaBot Server on {host}:{port}")
        
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level=self.config.LOG_LEVEL.lower(),
            reload=self.config.DEV_MODE
        )
        
        server = uvicorn.Server(config)
        await server.serve()

# Global system instance
chabot_system = IntegratedCHaBotSystem()

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    asyncio.create_task(chabot_system.shutdown())
    sys.exit(0)

async def main():
    """Main entry point."""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Run the integrated system
        await chabot_system.run_server(
            host=chabot_system.config.API_HOST,
            port=chabot_system.config.API_PORT
        )
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"System error: {e}")
    finally:
        await chabot_system.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
