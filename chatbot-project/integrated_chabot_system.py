#!/usr/bin/env python3
"""
Integrated CHaBot System - Main Application Entry Point
Brings together all components: AI, Agents, Memory, Tools, Reasoning, and Communication
"""

import asyncio
import logging
import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass
import signal
import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import configuration
from config import Config

# Import core AI components
from ai.llm.local_llm_infrastructure import LocalLLMInfrastructure
from ai.reasoning.advanced_reasoning_engine import AdvancedReasoningEngine
from ai.knowledge.knowledge_fusion_system import KnowledgeFusionSystem
from ai.memory.enhanced_integrated_memory_system import EnhancedIntegratedMemorySystem

# Import agent framework
from agents.orchestrator.agent_orchestrator import AgentOrchestrator
from agents.communication.agent_communication_infrastructure import AgentCommunicationInfrastructure
from agents.specialized.research_agent import ResearchAgent
from agents.specialized.analysis_agent import AnalysisAgent
from agents.specialized.creative_agent import CreativeAgent
from agents.specialized.technical_agent import TechnicalAgent

# Import tool integration
from agents.tools.registry.tool_registry import ToolRegistry
from agents.tools.calculator.basic_calculator import EnhancedCalculator
from agents.tools.database.enhanced_db_tool import EnhancedDatabaseTool
from agents.tools.search.enhanced_search_tool import EnhancedSearchTool
from agents.tools.creation.enhanced_dynamic_tool_creator import EnhancedDynamicToolCreator

# Import backend services
from backend.api.chat_service import ChatService
from backend.api.agent_service import AgentService
from backend.api.memory_service import MemoryService
from backend.api.tool_service import ToolService

# Set up logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class SystemStatus:
    """System status information."""
    status: str
    components: Dict[str, bool]
    startup_time: datetime
    uptime_seconds: float
    version: str = "1.0.0"

class IntegratedCHaBotSystem:
    """
    Integrated CHaBot System that orchestrates all components.
    """
    
    def __init__(self):
        """Initialize the integrated system."""
        self.config = Config()
        self.startup_time = datetime.now()
        self.is_running = False
        
        # Component status tracking
        self.component_status = {
            "llm_infrastructure": False,
            "reasoning_engine": False,
            "knowledge_fusion": False,
            "memory_system": False,
            "agent_orchestrator": False,
            "communication_infrastructure": False,
            "tool_registry": False,
            "api_services": False
        }
        
        # Initialize core components
        self.llm_infrastructure = None
        self.reasoning_engine = None
        self.knowledge_fusion = None
        self.memory_system = None
        self.agent_orchestrator = None
        self.communication_infrastructure = None
        self.tool_registry = None
        
        # Initialize specialized agents
        self.specialized_agents = {}
        
        # Initialize API services
        self.chat_service = None
        self.agent_service = None
        self.memory_service = None
        self.tool_service = None
        
        # FastAPI app
        self.app = None
        
        logger.info("Integrated CHaBot System initialized")
    
    async def initialize_components(self):
        """Initialize all system components."""
        logger.info("🚀 Initializing CHaBot System Components...")
        
        try:
            # 1. Initialize LLM Infrastructure
            logger.info("1️⃣ Initializing LLM Infrastructure...")
            self.llm_infrastructure = LocalLLMInfrastructure()
            await self.llm_infrastructure.initialize()
            self.component_status["llm_infrastructure"] = True
            logger.info("✅ LLM Infrastructure initialized")
            
            # 2. Initialize Memory System
            logger.info("2️⃣ Initializing Memory System...")
            self.memory_system = EnhancedIntegratedMemorySystem(
                database_path=self.config.DATABASE_PATH,
                working_memory_capacity=self.config.WORKING_MEMORY_LIMIT,
                auto_consolidation=True
            )
            await self.memory_system.start()
            self.component_status["memory_system"] = True
            logger.info("✅ Memory System initialized")
            
            # 3. Initialize Tool Registry
            logger.info("3️⃣ Initializing Tool Registry...")
            self.tool_registry = ToolRegistry()
            await self._register_built_in_tools()
            self.component_status["tool_registry"] = True
            logger.info("✅ Tool Registry initialized")
            
            # 4. Initialize Reasoning Engine
            logger.info("4️⃣ Initializing Reasoning Engine...")
            self.reasoning_engine = AdvancedReasoningEngine(
                llm_infrastructure=self.llm_infrastructure,
                memory_system=self.memory_system,
                tool_registry=self.tool_registry
            )
            await self.reasoning_engine.initialize()
            self.component_status["reasoning_engine"] = True
            logger.info("✅ Reasoning Engine initialized")
            
            # 5. Initialize Knowledge Fusion System
            logger.info("5️⃣ Initializing Knowledge Fusion System...")
            self.knowledge_fusion = KnowledgeFusionSystem(
                llm_infrastructure=self.llm_infrastructure,
                memory_system=self.memory_system
            )
            await self.knowledge_fusion.initialize()
            self.component_status["knowledge_fusion"] = True
            logger.info("✅ Knowledge Fusion System initialized")
            
            # 6. Initialize Communication Infrastructure
            logger.info("6️⃣ Initializing Communication Infrastructure...")
            self.communication_infrastructure = AgentCommunicationInfrastructure()
            await self.communication_infrastructure.start()
            self.component_status["communication_infrastructure"] = True
            logger.info("✅ Communication Infrastructure initialized")
            
            # 7. Initialize Agent Orchestrator
            logger.info("7️⃣ Initializing Agent Orchestrator...")
            self.agent_orchestrator = AgentOrchestrator(
                llm_infrastructure=self.llm_infrastructure,
                memory_system=self.memory_system,
                tool_registry=self.tool_registry,
                reasoning_engine=self.reasoning_engine,
                communication_infrastructure=self.communication_infrastructure
            )
            await self.agent_orchestrator.initialize()
            self.component_status["agent_orchestrator"] = True
            logger.info("✅ Agent Orchestrator initialized")
            
            # 8. Initialize Specialized Agents
            logger.info("8️⃣ Initializing Specialized Agents...")
            await self._initialize_specialized_agents()
            logger.info("✅ Specialized Agents initialized")
            
            # 9. Initialize API Services
            logger.info("9️⃣ Initializing API Services...")
            await self._initialize_api_services()
            self.component_status["api_services"] = True
            logger.info("✅ API Services initialized")
            
            # 10. Create FastAPI Application
            logger.info("🔟 Creating FastAPI Application...")
            self._create_fastapi_app()
            logger.info("✅ FastAPI Application created")
            
            self.is_running = True
            logger.info("🎉 All CHaBot System Components initialized successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize components: {e}")
            await self.shutdown()
            raise
    
    async def _register_built_in_tools(self):
        """Register built-in tools with the tool registry."""
        # Calculator tools
        calculator = EnhancedCalculator()
        self.tool_registry.register_tool(
            name="calculate",
            func=calculator.calculate,
            description="Perform mathematical calculations",
            category="calculation"
        )
        
        # Database tools
        db_tool = EnhancedDatabaseTool()
        self.tool_registry.register_tool(
            name="query_database",
            func=db_tool.execute_query,
            description="Execute database queries",
            category="data"
        )
        
        # Search tools
        search_tool = EnhancedSearchTool()
        self.tool_registry.register_tool(
            name="search",
            func=search_tool.search,
            description="Search for information",
            category="search"
        )
        
        # Dynamic tool creator
        tool_creator = EnhancedDynamicToolCreator()
        self.tool_registry.register_tool(
            name="create_tool",
            func=tool_creator.create_tool_from_code,
            description="Create new tools dynamically",
            category="meta"
        )
        
        logger.info("Built-in tools registered successfully")
    
    async def _initialize_specialized_agents(self):
        """Initialize specialized agents."""
        # Research Agent
        if self.config.ENABLE_RESEARCH_AGENT:
            self.specialized_agents["research"] = ResearchAgent(
                llm_infrastructure=self.llm_infrastructure,
                memory_system=self.memory_system,
                tool_registry=self.tool_registry
            )
        
        # Analysis Agent
        if self.config.ENABLE_ANALYSIS_AGENT:
            self.specialized_agents["analysis"] = AnalysisAgent(
                llm_infrastructure=self.llm_infrastructure,
                memory_system=self.memory_system,
                tool_registry=self.tool_registry
            )
        
        # Creative Agent
        if self.config.ENABLE_CREATIVE_AGENT:
            self.specialized_agents["creative"] = CreativeAgent(
                llm_infrastructure=self.llm_infrastructure,
                memory_system=self.memory_system,
                tool_registry=self.tool_registry
            )
        
        # Technical Agent
        if self.config.ENABLE_TECHNICAL_AGENT:
            self.specialized_agents["technical"] = TechnicalAgent(
                llm_infrastructure=self.llm_infrastructure,
                memory_system=self.memory_system,
                tool_registry=self.tool_registry
            )
        
        # Register agents with orchestrator
        for agent_name, agent in self.specialized_agents.items():
            await self.agent_orchestrator.register_agent(agent_name, agent)
        
        logger.info(f"Initialized {len(self.specialized_agents)} specialized agents")
    
    async def _initialize_api_services(self):
        """Initialize API services."""
        self.chat_service = ChatService(
            reasoning_engine=self.reasoning_engine,
            memory_system=self.memory_system,
            agent_orchestrator=self.agent_orchestrator
        )
        
        self.agent_service = AgentService(
            agent_orchestrator=self.agent_orchestrator,
            communication_infrastructure=self.communication_infrastructure
        )
        
        self.memory_service = MemoryService(
            memory_system=self.memory_system
        )
        
        self.tool_service = ToolService(
            tool_registry=self.tool_registry
        )
        
        logger.info("API services initialized")
    
    def _create_fastapi_app(self):
        """Create and configure FastAPI application."""
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            logger.info("FastAPI application starting up...")
            yield
            # Shutdown
            logger.info("FastAPI application shutting down...")
            await self.shutdown()
        
        self.app = FastAPI(
            title="CHaBot Integrated System",
            description="Comprehensive AI Chatbot with Advanced Reasoning and Multi-Agent Coordination",
            version="1.0.0",
            lifespan=lifespan
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.CORS_ORIGINS,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Add routes
        self._add_routes()
    
    def _add_routes(self):
        """Add API routes to FastAPI application."""
        @self.app.get("/")
        async def root():
            return {"message": "CHaBot Integrated System", "status": "running"}
        
        @self.app.get("/health")
        async def health_check():
            return self.get_system_status()
        
        @self.app.post("/chat")
        async def chat_endpoint(message: str, user_id: str = "default"):
            return await self.chat_service.process_message(message, user_id)
        
        @self.app.get("/agents")
        async def list_agents():
            return await self.agent_service.list_agents()
        
        @self.app.get("/memory/query")
        async def query_memory(query: str, limit: int = 10):
            return await self.memory_service.query_memory(query, limit)
        
        @self.app.get("/tools")
        async def list_tools():
            return await self.tool_service.list_tools()
        
        @self.app.get("/system/status")
        async def system_status():
            return self.get_system_status()
        
        @self.app.get("/system/statistics")
        async def system_statistics():
            return await self.get_system_statistics()
    
    def get_system_status(self) -> SystemStatus:
        """Get current system status."""
        uptime = (datetime.now() - self.startup_time).total_seconds()
        
        return SystemStatus(
            status="running" if self.is_running else "stopped",
            components=self.component_status.copy(),
            startup_time=self.startup_time,
            uptime_seconds=uptime
        ).__dict__
    
    async def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        stats = {
            "system": self.get_system_status(),
            "memory": {},
            "agents": {},
            "tools": {},
            "reasoning": {},
            "knowledge": {}
        }
        
        try:
            if self.memory_system:
                stats["memory"] = self.memory_system.get_comprehensive_statistics()
            
            if self.agent_orchestrator:
                stats["agents"] = await self.agent_orchestrator.get_statistics()
            
            if self.tool_registry:
                stats["tools"] = self.tool_registry.get_registry_statistics()
            
            if self.reasoning_engine:
                stats["reasoning"] = await self.reasoning_engine.get_statistics()
            
            if self.knowledge_fusion:
                stats["knowledge"] = await self.knowledge_fusion.get_statistics()
                
        except Exception as e:
            logger.error(f"Error getting system statistics: {e}")
        
        return stats
    
    async def shutdown(self):
        """Gracefully shutdown all system components."""
        logger.info("🛑 Shutting down CHaBot System...")
        
        self.is_running = False
        
        # Shutdown components in reverse order
        if self.agent_orchestrator:
            await self.agent_orchestrator.shutdown()
        
        if self.communication_infrastructure:
            await self.communication_infrastructure.stop()
        
        if self.memory_system:
            await self.memory_system.stop()
        
        if self.reasoning_engine:
            await self.reasoning_engine.shutdown()
        
        if self.knowledge_fusion:
            await self.knowledge_fusion.shutdown()
        
        if self.llm_infrastructure:
            await self.llm_infrastructure.shutdown()
        
        logger.info("✅ CHaBot System shutdown complete")
    
    async def run_server(self, host: str = "0.0.0.0", port: int = 8000):
        """Run the integrated system server."""
        if not self.is_running:
            await self.initialize_components()
        
        logger.info(f"🚀 Starting CHaBot Server on {host}:{port}")
        
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level=self.config.LOG_LEVEL.lower(),
            reload=self.config.DEV_MODE
        )
        
        server = uvicorn.Server(config)
        await server.serve()

# Global system instance
chabot_system = IntegratedCHaBotSystem()

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    asyncio.create_task(chabot_system.shutdown())
    sys.exit(0)

async def main():
    """Main entry point."""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Run the integrated system
        await chabot_system.run_server(
            host=chabot_system.config.API_HOST,
            port=chabot_system.config.API_PORT
        )
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"System error: {e}")
    finally:
        await chabot_system.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
