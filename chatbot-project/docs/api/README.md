# CHaBot API Documentation

## Overview

The CHaBot (Conversational Hierarchical Agent Bot) system provides a comprehensive API for multi-organization chatbot functionality with Advanced Agentic RAG (Retrieval-Augmented Generation) capabilities.

## Architecture

The system is built using a microservices architecture with the following core services:

- **API Gateway** (Port 8000): Central entry point and request routing
- **Authentication Service** (Port 8001): User authentication and authorization
- **Chat Service** (Port 8002): Core chat functionality and conversation management
- **Agent Service** (Port 8003): Multi-agent coordination and specialized agent management
- **Admin Service** (Port 8004): Administrative functions and system management
- **Analytics Service** (Port 8005): Advanced analytics and monitoring

## Quick Start

### 1. Authentication

All API requests (except health checks) require authentication. First, obtain an access token:

```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### 2. Send a Chat Message

```bash
curl -X POST "http://localhost:8000/chat/send" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What is the vacation policy for NUVO AI?",
    "conversation_id": null
  }'
```

Response:
```json
{
  "response": "NUVO AI's vacation policy provides 30 days of annual leave...",
  "conversation_id": "conv_12345",
  "agent_used": "OrganizationAgent_NUVO_AI",
  "confidence": 0.92,
  "reasoning_trace": {
    "method": "multi_agent_coordination",
    "agents_consulted": ["OrganizationAgent_NUVO_AI", "ReasoningAgent"],
    "confidence": 0.92
  }
}
```

## API Endpoints

### Authentication Service (`/auth`)

#### POST `/auth/login`
Authenticate user and obtain access token.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 3600
}
```

#### POST `/auth/register`
Register a new user account.

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "role": "employee"
}
```

#### POST `/auth/verify`
Verify JWT token validity.

**Request Body:**
```json
{
  "token": "string"
}
```

#### POST `/auth/refresh`
Refresh access token.

**Request Body:**
```json
{
  "token": "string"
}
```

### Chat Service (`/chat`)

#### POST `/chat/send`
Send a message to the chatbot.

**Headers:**
- `Authorization: Bearer <token>`
- `X-User-ID: <user_id>` (automatically added by API Gateway)

**Request Body:**
```json
{
  "message": "string",
  "conversation_id": "string|null"
}
```

**Response:**
```json
{
  "response": "string",
  "conversation_id": "string",
  "agent_used": "string",
  "confidence": 0.0,
  "reasoning_trace": {
    "method": "string",
    "agents_consulted": ["string"],
    "confidence": 0.0
  },
  "sources": ["string"]
}
```

#### GET `/chat/conversations`
Get user's conversation history.

**Response:**
```json
[
  {
    "id": "string",
    "created_at": "2023-07-15T14:30:00Z",
    "updated_at": "2023-07-15T14:35:00Z",
    "message_count": 5
  }
]
```

#### GET `/chat/conversations/{conversation_id}/messages`
Get messages from a specific conversation.

**Response:**
```json
[
  {
    "id": "string",
    "content": "string",
    "is_user": true,
    "agent_id": "string",
    "created_at": "2023-07-15T14:30:00Z"
  }
]
```

#### DELETE `/chat/conversations/{conversation_id}`
Delete a conversation.

**Response:**
```json
{
  "message": "Conversation deleted successfully"
}
```

### Agent Service (`/agent`)

#### GET `/agent/status`
Get status of all agents.

**Response:**
```json
{
  "total_agents": 10,
  "active_agents": 8,
  "agent_types": {
    "organization": 2,
    "department": 5,
    "reasoning": 1,
    "tool": 1,
    "critic": 1
  },
  "system_health": "healthy"
}
```

#### GET `/agent/{agent_id}/performance`
Get performance metrics for a specific agent.

**Response:**
```json
{
  "agent_id": "string",
  "agent_type": "string",
  "total_tasks": 150,
  "successful_tasks": 142,
  "failed_tasks": 8,
  "avg_response_time": 1.2,
  "avg_confidence": 0.87,
  "last_active": "2023-07-15T14:30:00Z"
}
```

#### POST `/agent/coordinate`
Coordinate a multi-agent task.

**Request Body:**
```json
{
  "query": "string",
  "user_context": {
    "organization": "string",
    "department": "string",
    "user_role": "string"
  },
  "user_id": "string"
}
```

**Response:**
```json
{
  "agents_used": ["string"],
  "combined_results": [{}],
  "overall_confidence": 0.85,
  "coordination_success": true,
  "reasoning_trace": {
    "coordination_method": "hierarchical",
    "task_decomposition": ["string"],
    "agent_allocation": {}
  }
}
```

### Admin Service (`/admin`)

#### GET `/admin/organizations`
Get all organizations.

**Response:**
```json
[
  {
    "id": "string",
    "name": "NUVO AI",
    "domain": "nuvoai.com",
    "departments": ["HR", "Engineering", "AI/ML"],
    "active": true
  }
]
```

#### POST `/admin/organizations`
Create a new organization.

**Request Body:**
```json
{
  "name": "string",
  "domain": "string",
  "settings": {}
}
```

#### GET `/admin/users`
Get all users (admin only).

**Query Parameters:**
- `organization`: Filter by organization
- `role`: Filter by role
- `active`: Filter by active status

#### GET `/admin/analytics/dashboard`
Get admin analytics dashboard.

**Response:**
```json
{
  "total_users": 150,
  "active_conversations": 25,
  "total_messages_today": 1250,
  "avg_response_time": 1.8,
  "user_satisfaction": 4.2,
  "top_queries": ["string"],
  "agent_performance": {}
}
```

### Analytics Service (`/analytics`)

#### GET `/analytics/dashboard`
Get comprehensive analytics dashboard.

**Query Parameters:**
- `time_range`: "1h", "6h", "24h", "7d", "30d"

**Response:**
```json
{
  "conversations": {
    "active_conversations": 25,
    "completed_conversations": 1200,
    "avg_duration": 180.5,
    "avg_satisfaction": 4.2,
    "avg_success_rate": 0.92
  },
  "agents": {
    "total_agents": 10,
    "active_agents": 8,
    "total_tasks": 5000,
    "overall_success_rate": 0.89,
    "avg_response_time": 1.5,
    "by_type": {}
  },
  "system": {
    "avg_cpu_usage": 45.2,
    "avg_memory_usage": 62.1,
    "avg_error_rate": 0.02,
    "avg_throughput": 15.8
  },
  "insights": [
    {
      "type": "conversation",
      "level": "info",
      "message": "Users are having longer conversations",
      "recommendation": "Consider conversation flow optimization"
    }
  ]
}
```

#### GET `/analytics/agents/performance`
Get detailed agent performance metrics.

**Query Parameters:**
- `agent_id`: Specific agent ID (optional)

#### GET `/analytics/conversations/analytics`
Get conversation analytics.

**Query Parameters:**
- `organization`: Filter by organization

#### GET `/analytics/system/health`
Get current system health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-07-15T14:30:00Z",
  "cpu_usage": 45.2,
  "memory_usage": 62.1,
  "disk_usage": 78.5,
  "error_rate": 0.02,
  "throughput": 15.8,
  "active_connections": 150
}
```

#### GET `/analytics/alerts`
Get recent system alerts.

**Query Parameters:**
- `limit`: Number of alerts to return (default: 20, max: 100)

**Response:**
```json
[
  {
    "type": "system",
    "severity": "warning",
    "message": "High CPU usage: 85.2%",
    "timestamp": "2023-07-15T14:30:00Z"
  }
]
```

## Advanced Features

### Multi-Agent Coordination

The system uses advanced multi-agent coordination for complex queries:

```json
{
  "query": "Compare vacation policies between NUVO AI and Meril Life Sciences",
  "user_context": {
    "organizations": ["NUVO AI", "Meril Life Sciences"],
    "comparison_type": "policy_comparison"
  }
}
```

### Reasoning Visualization

Responses include detailed reasoning traces:

```json
{
  "reasoning_trace": {
    "method": "tree_of_thoughts",
    "reasoning_paths": [
      {
        "path_id": "path_1",
        "steps": [
          {
            "step": "Analyze NUVO AI vacation policy",
            "confidence": 0.9,
            "evidence": ["Policy document v2.1"]
          }
        ],
        "confidence": 0.87
      }
    ],
    "selected_path": "path_1"
  }
}
```

### Real-time Agent Collaboration

Monitor agent collaboration in real-time:

```json
{
  "agent_interactions": [
    {
      "from_agent": "CoordinatorAgent",
      "to_agent": "OrganizationAgent_NUVO_AI",
      "message_type": "task_assignment",
      "timestamp": "2023-07-15T14:30:00Z"
    }
  ]
}
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "detail": "Error description",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2023-07-15T14:30:00Z"
}
```

### Common HTTP Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

## Rate Limiting

API endpoints are rate-limited:

- **Authentication**: 10 requests per minute
- **Chat**: 60 requests per minute
- **Analytics**: 100 requests per minute
- **Admin**: 30 requests per minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Webhooks

Configure webhooks for real-time notifications:

### Conversation Events
```json
{
  "event": "conversation.completed",
  "data": {
    "conversation_id": "string",
    "user_id": "string",
    "duration": 180,
    "satisfaction": 4.5
  }
}
```

### Agent Events
```json
{
  "event": "agent.performance.degraded",
  "data": {
    "agent_id": "string",
    "success_rate": 0.75,
    "threshold": 0.80
  }
}
```

### System Events
```json
{
  "event": "system.alert",
  "data": {
    "type": "high_cpu_usage",
    "value": 85.2,
    "threshold": 80.0
  }
}
```

## SDKs and Libraries

### Python SDK

```python
from chabot_sdk import CHaBotClient

client = CHaBotClient(
    base_url="http://localhost:8000",
    api_key="your_api_key"
)

# Send a message
response = client.chat.send_message(
    message="What is the vacation policy?",
    conversation_id=None
)

print(response.content)
```

### JavaScript SDK

```javascript
import { CHaBotClient } from 'chabot-sdk';

const client = new CHaBotClient({
  baseUrl: 'http://localhost:8000',
  apiKey: 'your_api_key'
});

// Send a message
const response = await client.chat.sendMessage({
  message: 'What is the vacation policy?',
  conversationId: null
});

console.log(response.content);
```

## Testing

### Health Check

```bash
curl http://localhost:8000/health
```

### Authentication Test

```bash
# Login
TOKEN=$(curl -s -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}' | \
  jq -r '.access_token')

# Test authenticated endpoint
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/chat/conversations"
```

## Support

For API support and questions:

- **Documentation**: [Full API Documentation](./api-reference.md)
- **Examples**: [API Examples](./examples/)
- **Troubleshooting**: [Common Issues](./troubleshooting.md)
- **Contact**: <EMAIL>

## Changelog

### Version 1.0.0
- Initial API release
- Multi-agent coordination
- Advanced reasoning capabilities
- Comprehensive analytics

### Version 1.1.0 (Planned)
- Enhanced authentication options
- Improved agent performance
- Additional analytics features
- Mobile SDK support