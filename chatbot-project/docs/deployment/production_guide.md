# CHaBot Production Deployment Guide

## Prerequisites
- Kubernetes cluster (v1.20+)
- Docker registry access
- PostgreSQL database
- Redis instance
- SSL certificates

## Quick Start
```bash
# 1. Clone repository
git clone https://git.nuvoai.io/ma57120v/hr-chatbot.git
cd hr-chatbot/chatbot-project

# 2. Configure environment
cp .env.example .env
# Edit .env with production values

# 3. Build and deploy
make deploy-production
```

## Step-by-Step Deployment

### 1. Environment Setup
```bash
# Create namespace
kubectl create namespace chabot-prod

# Create secrets
kubectl create secret generic chabot-secrets \
  --from-env-file=.env \
  -n chabot-prod

# Create SSL certificates
kubectl create secret tls chabot-tls \
  --cert=path/to/cert.pem \
  --key=path/to/key.pem \
  -n chabot-prod
```

### 2. Database Setup
```bash
# Deploy PostgreSQL
kubectl apply -f infrastructure/kubernetes/deployments/postgres.yaml

# Deploy Redis
kubectl apply -f infrastructure/kubernetes/deployments/redis.yaml

# Deploy Neo4j
kubectl apply -f infrastructure/kubernetes/deployments/neo4j-core.yaml

# Deploy Milvus
kubectl apply -f infrastructure/kubernetes/deployments/milvus.yaml
```

### 3. Application Deployment
```bash
# Deploy microservices
kubectl apply -f infrastructure/kubernetes/deployments/microservices.yaml

# Deploy API Gateway
kubectl apply -f infrastructure/kubernetes/deployments/api-gateway.yaml

# Deploy frontend
kubectl apply -f infrastructure/kubernetes/deployments/chatbot-app.yaml
```

### 4. Monitoring Setup
```bash
# Deploy Prometheus
kubectl apply -f infrastructure/kubernetes/monitoring/prometheus.yaml

# Deploy Grafana
kubectl apply -f infrastructure/kubernetes/monitoring/grafana.yaml
```

### 5. Ingress Configuration
```bash
# Deploy ingress
kubectl apply -f infrastructure/kubernetes/ingress/chatbot-ingress.yaml
```

## Production Configuration

### Environment Variables
```bash
# Core settings
APP_NAME=CHaBot
ENVIRONMENT=production
DEBUG=false

# Database URLs
DATABASE_URL=************************************/chabot
REDIS_URL=redis://redis:6379
NEO4J_URI=bolt://neo4j:7687

# Security
SECRET_KEY=your-production-secret-key-32-chars-min
JWT_EXPIRE_MINUTES=60

# Scaling
API_WORKERS=4
MAX_AGENTS_PER_TYPE=5
```

### Resource Limits
```yaml
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

## Security Hardening

### 1. Network Policies
```bash
kubectl apply -f infrastructure/kubernetes/network-policies/
```

### 2. RBAC Configuration
```bash
kubectl apply -f infrastructure/kubernetes/rbac/
```

### 3. Pod Security Policies
```bash
kubectl apply -f infrastructure/kubernetes/security/pod-security-policy.yaml
```

## Monitoring & Alerting

### Prometheus Metrics
- API response times
- Agent performance
- Database connections
- Error rates

### Grafana Dashboards
- System overview
- Agent performance
- User activity
- Resource utilization

### Alerting Rules
```yaml
groups:
- name: chabot.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    annotations:
      summary: High error rate detected
```

## Backup & Recovery

### Database Backups
```bash
# Automated backup job
kubectl apply -f infrastructure/kubernetes/jobs/postgres-backup.yaml
```

### Disaster Recovery
1. Database restoration procedures
2. Configuration backup
3. Agent state recovery
4. User data migration

## Scaling

### Horizontal Pod Autoscaler
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: chabot-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: chabot-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

## Troubleshooting

### Common Issues
1. **Database Connection Errors**
   - Check connection strings
   - Verify network policies
   - Check service discovery

2. **Agent Communication Issues**
   - Verify Redis connectivity
   - Check gRPC configurations
   - Review service mesh settings

3. **Performance Issues**
   - Monitor resource usage
   - Check database query performance
   - Review agent allocation

### Logs
```bash
# View application logs
kubectl logs -f deployment/chabot-api -n chabot-prod

# View agent logs
kubectl logs -f deployment/chabot-agents -n chabot-prod
```

## Maintenance

### Updates
```bash
# Rolling update
kubectl set image deployment/chabot-api chabot=chabot:v1.2.0 -n chabot-prod

# Blue-green deployment
python deployment/blue_green.py deploy v1.2.0
```

### Health Checks
```bash
# System health
curl https://chabot.company.com/health

# Agent status
curl https://chabot.company.com/agents/status
```