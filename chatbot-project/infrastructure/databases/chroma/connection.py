"""Chroma Vector Memory Database Connection Manager."""

import logging
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)

class ChromaManager:
    """Chroma vector memory database connection and operations manager."""
    
    def __init__(self, host: str = "localhost", port: int = 8000, 
                 collection_name: str = "chabot_memory", 
                 persist_directory: str = "./chroma_db"):
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.persist_directory = persist_directory
        self.client: Optional[chromadb.Client] = None
        self.collection: Optional[chromadb.Collection] = None
        self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name="all-MiniLM-L6-v2"
        )
        
    async def initialize(self):
        """Initialize Chroma connection and collection."""
        try:
            # Try to connect to Chroma server first, fallback to persistent client
            try:
                self.client = chromadb.HttpClient(
                    host=self.host,
                    port=self.port,
                    settings=Settings(allow_reset=True)
                )
                # Test connection
                self.client.heartbeat()
                logger.info(f"✅ Connected to Chroma server at {self.host}:{self.port}")
                
            except Exception as server_error:
                logger.warning(f"Chroma server not available: {server_error}")
                logger.info("Falling back to persistent client")
                
                # Fallback to persistent client
                self.client = chromadb.PersistentClient(
                    path=self.persist_directory,
                    settings=Settings(allow_reset=True)
                )
                logger.info(f"✅ Using persistent Chroma client at {self.persist_directory}")
            
            # Create or get collection
            await self.create_collection()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Chroma: {e}")
            return False
    
    async def create_collection(self):
        """Create or get Chroma collection."""
        try:
            # Try to get existing collection
            try:
                self.collection = self.client.get_collection(
                    name=self.collection_name,
                    embedding_function=self.embedding_function
                )
                logger.info(f"Using existing collection: {self.collection_name}")
                
            except Exception:
                # Create new collection
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    embedding_function=self.embedding_function,
                    metadata={
                        "description": "CHaBot conversational memory and context",
                        "created_at": datetime.now().isoformat()
                    }
                )
                logger.info(f"✅ Created new collection: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Failed to create/get collection: {e}")
            raise
    
    async def close(self):
        """Close Chroma connection."""
        try:
            # Chroma client doesn't need explicit closing
            logger.info("Chroma connection closed")
        except Exception as e:
            logger.error(f"Error closing Chroma connection: {e}")
    
    async def add_memory(self, memories: List[Dict[str, Any]]) -> bool:
        """Add conversational memories to Chroma."""
        try:
            if not self.collection:
                raise RuntimeError("Collection not initialized")
            
            # Prepare data for Chroma
            documents = []
            metadatas = []
            ids = []
            
            for memory in memories:
                # Generate unique ID
                memory_id = memory.get("id", str(uuid.uuid4()))
                ids.append(memory_id)
                
                # Document content
                documents.append(memory["content"])
                
                # Metadata
                metadata = {
                    "session_id": memory.get("session_id", ""),
                    "user_id": memory.get("user_id", ""),
                    "organization": memory.get("organization", ""),
                    "department": memory.get("department", ""),
                    "memory_type": memory.get("memory_type", "conversation"),
                    "importance": memory.get("importance", 0.5),
                    "created_at": memory.get("created_at", datetime.now().isoformat()),
                    "context": memory.get("context", {}),
                    "agent_id": memory.get("agent_id", "")
                }
                metadatas.append(metadata)
            
            # Add to collection
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"✅ Added {len(memories)} memories to Chroma")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to add memories: {e}")
            return False
    
    async def search_memories(self, query: str, n_results: int = 10, 
                            session_id: str = None, user_id: str = None,
                            organization: str = None, memory_type: str = None) -> List[Dict[str, Any]]:
        """Search for relevant memories."""
        try:
            if not self.collection:
                raise RuntimeError("Collection not initialized")
            
            # Build where clause for filtering
            where_clause = {}
            if session_id:
                where_clause["session_id"] = session_id
            if user_id:
                where_clause["user_id"] = user_id
            if organization:
                where_clause["organization"] = organization
            if memory_type:
                where_clause["memory_type"] = memory_type
            
            # Search
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where_clause if where_clause else None,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results
            formatted_results = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    formatted_results.append({
                        "id": results["ids"][0][i],
                        "content": doc,
                        "metadata": results["metadatas"][0][i],
                        "distance": results["distances"][0][i],
                        "relevance_score": 1 - results["distances"][0][i]  # Convert distance to relevance
                    })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ Failed to search memories: {e}")
            return []
    
    async def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation history for a session."""
        try:
            if not self.collection:
                raise RuntimeError("Collection not initialized")
            
            # Get all memories for the session
            results = self.collection.get(
                where={"session_id": session_id},
                include=["documents", "metadatas"]
            )
            
            # Sort by created_at and limit
            memories = []
            if results["documents"]:
                for i, doc in enumerate(results["documents"]):
                    memory = {
                        "id": results["ids"][i],
                        "content": doc,
                        "metadata": results["metadatas"][i]
                    }
                    memories.append(memory)
            
            # Sort by created_at
            memories.sort(key=lambda x: x["metadata"].get("created_at", ""))
            
            return memories[-limit:] if len(memories) > limit else memories
            
        except Exception as e:
            logger.error(f"❌ Failed to get conversation history: {e}")
            return []
    
    async def update_memory_importance(self, memory_id: str, importance: float) -> bool:
        """Update the importance score of a memory."""
        try:
            if not self.collection:
                raise RuntimeError("Collection not initialized")
            
            # Get current memory
            result = self.collection.get(ids=[memory_id], include=["metadatas"])
            
            if not result["metadatas"]:
                logger.warning(f"Memory {memory_id} not found")
                return False
            
            # Update metadata
            metadata = result["metadatas"][0]
            metadata["importance"] = importance
            metadata["updated_at"] = datetime.now().isoformat()
            
            # Update in collection
            self.collection.update(
                ids=[memory_id],
                metadatas=[metadata]
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to update memory importance: {e}")
            return False
    
    async def cleanup_old_memories(self, days_old: int = 30, min_importance: float = 0.3) -> int:
        """Clean up old, low-importance memories."""
        try:
            if not self.collection:
                raise RuntimeError("Collection not initialized")
            
            # Get all memories
            all_memories = self.collection.get(include=["metadatas"])
            
            # Find memories to delete
            cutoff_date = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            ids_to_delete = []
            
            for i, metadata in enumerate(all_memories["metadatas"]):
                created_at = metadata.get("created_at", "")
                importance = metadata.get("importance", 1.0)
                
                try:
                    memory_timestamp = datetime.fromisoformat(created_at).timestamp()
                    if memory_timestamp < cutoff_date and importance < min_importance:
                        ids_to_delete.append(all_memories["ids"][i])
                except:
                    continue
            
            # Delete old memories
            if ids_to_delete:
                self.collection.delete(ids=ids_to_delete)
                logger.info(f"Cleaned up {len(ids_to_delete)} old memories")
            
            return len(ids_to_delete)
            
        except Exception as e:
            logger.error(f"❌ Failed to cleanup old memories: {e}")
            return 0
    
    async def health_check(self) -> bool:
        """Check Chroma database health."""
        try:
            if not self.client:
                return False
            
            # Try to access collection
            if self.collection:
                count = self.collection.count()
                logger.info(f"Chroma health check: {count} memories in collection")
            
            return True
            
        except Exception as e:
            logger.error(f"Chroma health check failed: {e}")
            return False
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        try:
            if not self.collection:
                return {}
            
            count = self.collection.count()
            
            stats = {
                "collection_name": self.collection_name,
                "total_memories": count,
                "embedding_function": str(self.embedding_function),
                "client_type": type(self.client).__name__
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
