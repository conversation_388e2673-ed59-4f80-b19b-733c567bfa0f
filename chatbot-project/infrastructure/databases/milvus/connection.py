"""Milvus Vector Database Connection Manager."""

import logging
from typing import List, Dict, Any, Optional
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
import numpy as np

logger = logging.getLogger(__name__)

class MilvusManager:
    """Milvus vector database connection and operations manager."""
    
    def __init__(self, host: str = "localhost", port: int = 19530, collection_name: str = "chabot_embeddings"):
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.collection: Optional[Collection] = None
        self.connection_alias = "default"
        
    async def initialize(self):
        """Initialize Milvus connection and collection."""
        try:
            # Connect to Milvus
            connections.connect(
                alias=self.connection_alias,
                host=self.host,
                port=self.port
            )
            
            # Create collection if it doesn't exist
            await self.create_collection()
            
            # Load collection
            self.collection = Collection(self.collection_name)
            self.collection.load()
            
            logger.info("✅ Milvus connection and collection initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Milvus: {e}")
            return False
    
    async def create_collection(self):
        """Create Milvus collection with proper schema."""
        if utility.has_collection(self.collection_name):
            logger.info(f"Collection {self.collection_name} already exists")
            return
        
        # Define collection schema
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="document_id", dtype=DataType.VARCHAR, max_length=255),
            FieldSchema(name="chunk_id", dtype=DataType.VARCHAR, max_length=255),
            FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=384),  # all-MiniLM-L6-v2 dimension
            FieldSchema(name="organization", dtype=DataType.VARCHAR, max_length=255),
            FieldSchema(name="department", dtype=DataType.VARCHAR, max_length=255),
            FieldSchema(name="document_type", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="metadata", dtype=DataType.JSON),
            FieldSchema(name="created_at", dtype=DataType.INT64)
        ]
        
        schema = CollectionSchema(
            fields=fields,
            description="CHaBot document embeddings collection"
        )
        
        # Create collection
        collection = Collection(
            name=self.collection_name,
            schema=schema,
            using=self.connection_alias
        )
        
        # Create index for vector field
        index_params = {
            "metric_type": "L2",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        
        collection.create_index(
            field_name="embedding",
            index_params=index_params
        )
        
        logger.info(f"✅ Milvus collection {self.collection_name} created with index")
    
    async def close(self):
        """Close Milvus connection."""
        try:
            if self.collection:
                self.collection.release()
            connections.disconnect(self.connection_alias)
            logger.info("Milvus connection closed")
        except Exception as e:
            logger.error(f"Error closing Milvus connection: {e}")
    
    async def insert_embeddings(self, data: List[Dict[str, Any]]) -> bool:
        """Insert embeddings into Milvus collection."""
        try:
            if not self.collection:
                raise RuntimeError("Collection not initialized")
            
            # Prepare data for insertion
            entities = [
                [item["document_id"] for item in data],
                [item["chunk_id"] for item in data],
                [item["content"] for item in data],
                [item["embedding"] for item in data],
                [item["organization"] for item in data],
                [item["department"] for item in data],
                [item["document_type"] for item in data],
                [item["metadata"] for item in data],
                [item["created_at"] for item in data]
            ]
            
            # Insert data
            insert_result = self.collection.insert(entities)
            self.collection.flush()
            
            logger.info(f"✅ Inserted {len(data)} embeddings into Milvus")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to insert embeddings: {e}")
            return False
    
    async def search_similar(self, query_embedding: List[float], top_k: int = 10, 
                           organization: str = None, department: str = None) -> List[Dict[str, Any]]:
        """Search for similar embeddings."""
        try:
            if not self.collection:
                raise RuntimeError("Collection not initialized")
            
            # Build search expression
            expr = []
            if organization:
                expr.append(f'organization == "{organization}"')
            if department:
                expr.append(f'department == "{department}"')
            
            search_expr = " and ".join(expr) if expr else None
            
            # Search parameters
            search_params = {
                "metric_type": "L2",
                "params": {"nprobe": 10}
            }
            
            # Perform search
            results = self.collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                expr=search_expr,
                output_fields=["document_id", "chunk_id", "content", "organization", 
                              "department", "document_type", "metadata"]
            )
            
            # Format results
            formatted_results = []
            for hits in results:
                for hit in hits:
                    formatted_results.append({
                        "id": hit.id,
                        "distance": hit.distance,
                        "document_id": hit.entity.get("document_id"),
                        "chunk_id": hit.entity.get("chunk_id"),
                        "content": hit.entity.get("content"),
                        "organization": hit.entity.get("organization"),
                        "department": hit.entity.get("department"),
                        "document_type": hit.entity.get("document_type"),
                        "metadata": hit.entity.get("metadata")
                    })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ Failed to search embeddings: {e}")
            return []
    
    async def health_check(self) -> bool:
        """Check Milvus database health."""
        try:
            # Check connection
            if not connections.has_connection(self.connection_alias):
                return False
            
            # Check collection
            if not utility.has_collection(self.collection_name):
                return False
            
            # Get collection stats
            if self.collection:
                stats = self.collection.num_entities
                logger.info(f"Milvus health check: {stats} entities in collection")
            
            return True
            
        except Exception as e:
            logger.error(f"Milvus health check failed: {e}")
            return False
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        try:
            if not self.collection:
                return {}
            
            stats = {
                "num_entities": self.collection.num_entities,
                "collection_name": self.collection_name,
                "schema": str(self.collection.schema),
                "indexes": [str(index) for index in self.collection.indexes]
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
