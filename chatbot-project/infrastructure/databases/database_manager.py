"""Unified Database Manager for all CHaBot databases."""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .postgres.connection import PostgreSQLManager
from .neo4j.connection import Neo4jManager
from .milvus.connection import MilvusManager
from .memgraph.connection import MemgraphManager
from .chroma.connection import ChromaManager

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration container."""
    # PostgreSQL
    postgres_url: str
    
    # Neo4j
    neo4j_uri: str
    neo4j_user: str
    neo4j_password: str
    neo4j_database: str
    
    # Milvus
    milvus_host: str
    milvus_port: int
    milvus_collection: str
    
    # Memgraph
    memgraph_host: str
    memgraph_port: int
    memgraph_user: str
    memgraph_password: str
    
    # Chroma
    chroma_host: str
    chroma_port: int
    chroma_collection: str
    chroma_persist_dir: str

class DatabaseManager:
    """Unified manager for all CHaBot databases."""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        
        # Database managers
        self.postgres: Optional[PostgreSQLManager] = None
        self.neo4j: Optional[Neo4jManager] = None
        self.milvus: Optional[MilvusManager] = None
        self.memgraph: Optional[MemgraphManager] = None
        self.chroma: Optional[ChromaManager] = None
        
        # Connection status
        self.connection_status = {
            "postgres": False,
            "neo4j": False,
            "milvus": False,
            "memgraph": False,
            "chroma": False
        }
    
    async def initialize_all(self) -> Dict[str, bool]:
        """Initialize all database connections."""
        logger.info("🚀 Initializing all database connections...")
        
        # Initialize PostgreSQL
        try:
            self.postgres = PostgreSQLManager(self.config.postgres_url)
            self.connection_status["postgres"] = await self.postgres.initialize()
        except Exception as e:
            logger.error(f"PostgreSQL initialization failed: {e}")
            self.connection_status["postgres"] = False
        
        # Initialize Neo4j
        try:
            self.neo4j = Neo4jManager(
                uri=self.config.neo4j_uri,
                user=self.config.neo4j_user,
                password=self.config.neo4j_password,
                database=self.config.neo4j_database
            )
            self.connection_status["neo4j"] = await self.neo4j.initialize()
        except Exception as e:
            logger.error(f"Neo4j initialization failed: {e}")
            self.connection_status["neo4j"] = False
        
        # Initialize Milvus
        try:
            self.milvus = MilvusManager(
                host=self.config.milvus_host,
                port=self.config.milvus_port,
                collection_name=self.config.milvus_collection
            )
            self.connection_status["milvus"] = await self.milvus.initialize()
        except Exception as e:
            logger.error(f"Milvus initialization failed: {e}")
            self.connection_status["milvus"] = False
        
        # Initialize Memgraph
        try:
            self.memgraph = MemgraphManager(
                host=self.config.memgraph_host,
                port=self.config.memgraph_port,
                user=self.config.memgraph_user,
                password=self.config.memgraph_password
            )
            self.connection_status["memgraph"] = await self.memgraph.initialize()
        except Exception as e:
            logger.error(f"Memgraph initialization failed: {e}")
            self.connection_status["memgraph"] = False
        
        # Initialize Chroma
        try:
            self.chroma = ChromaManager(
                host=self.config.chroma_host,
                port=self.config.chroma_port,
                collection_name=self.config.chroma_collection,
                persist_directory=self.config.chroma_persist_dir
            )
            self.connection_status["chroma"] = await self.chroma.initialize()
        except Exception as e:
            logger.error(f"Chroma initialization failed: {e}")
            self.connection_status["chroma"] = False
        
        # Log results
        connected_dbs = [db for db, status in self.connection_status.items() if status]
        failed_dbs = [db for db, status in self.connection_status.items() if not status]
        
        logger.info(f"✅ Connected databases: {connected_dbs}")
        if failed_dbs:
            logger.warning(f"❌ Failed databases: {failed_dbs}")
        
        return self.connection_status
    
    async def close_all(self):
        """Close all database connections."""
        logger.info("Closing all database connections...")
        
        close_tasks = []
        
        if self.postgres:
            close_tasks.append(self.postgres.close())
        if self.neo4j:
            close_tasks.append(self.neo4j.close())
        if self.milvus:
            close_tasks.append(self.milvus.close())
        if self.memgraph:
            close_tasks.append(self.memgraph.close())
        if self.chroma:
            close_tasks.append(self.chroma.close())
        
        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)
        
        logger.info("All database connections closed")
    
    async def health_check_all(self) -> Dict[str, bool]:
        """Perform health check on all databases."""
        health_status = {}
        
        # Check each database
        if self.postgres:
            health_status["postgres"] = await self.postgres.health_check()
        else:
            health_status["postgres"] = False
            
        if self.neo4j:
            health_status["neo4j"] = await self.neo4j.health_check()
        else:
            health_status["neo4j"] = False
            
        if self.milvus:
            health_status["milvus"] = await self.milvus.health_check()
        else:
            health_status["milvus"] = False
            
        if self.memgraph:
            health_status["memgraph"] = await self.memgraph.health_check()
        else:
            health_status["memgraph"] = False
            
        if self.chroma:
            health_status["chroma"] = await self.chroma.health_check()
        else:
            health_status["chroma"] = False
        
        return health_status
    
    async def setup_initial_data(self):
        """Setup initial data across all databases."""
        logger.info("Setting up initial data...")
        
        # Setup organizational structure in Neo4j
        if self.neo4j and self.connection_status["neo4j"]:
            organizations = [
                {
                    "name": "NuvoAi",
                    "description": "AI and Technology Company",
                    "type": "technology",
                    "departments": [
                        {"name": "AI Department", "type": "technical"},
                        {"name": "HR Department", "type": "administrative"},
                        {"name": "Finance Department", "type": "administrative"},
                        {"name": "Product Department", "type": "business"}
                    ]
                },
                {
                    "name": "Meril",
                    "description": "Healthcare and Medical Devices",
                    "type": "healthcare",
                    "departments": [
                        {"name": "Technical Department", "type": "technical"},
                        {"name": "HR Department", "type": "administrative"},
                        {"name": "Finance Department", "type": "administrative"},
                        {"name": "Research Department", "type": "research"}
                    ]
                }
            ]
            
            await self.neo4j.create_organization_structure(organizations)
        
        # Setup initial organizations in PostgreSQL
        if self.postgres and self.connection_status["postgres"]:
            try:
                # Insert organizations
                await self.postgres.execute_command(
                    "INSERT INTO organizations (name, description) VALUES ($1, $2) ON CONFLICT (name) DO NOTHING",
                    "NuvoAi", "AI and Technology Company"
                )
                await self.postgres.execute_command(
                    "INSERT INTO organizations (name, description) VALUES ($1, $2) ON CONFLICT (name) DO NOTHING",
                    "Meril", "Healthcare and Medical Devices"
                )
                
                logger.info("✅ Initial PostgreSQL data setup complete")
            except Exception as e:
                logger.error(f"Failed to setup PostgreSQL initial data: {e}")
        
        logger.info("✅ Initial data setup complete")
    
    def get_connection_summary(self) -> Dict[str, Any]:
        """Get summary of all database connections."""
        return {
            "connection_status": self.connection_status,
            "total_connected": sum(self.connection_status.values()),
            "total_databases": len(self.connection_status),
            "success_rate": sum(self.connection_status.values()) / len(self.connection_status) * 100
        }
    
    def is_fully_operational(self) -> bool:
        """Check if all critical databases are operational."""
        critical_dbs = ["postgres", "milvus"]  # Minimum required for basic operation
        return all(self.connection_status.get(db, False) for db in critical_dbs)
    
    def get_available_features(self) -> List[str]:
        """Get list of available features based on connected databases."""
        features = []
        
        if self.connection_status.get("postgres"):
            features.extend(["user_management", "conversation_history", "basic_chat"])
        
        if self.connection_status.get("milvus"):
            features.extend(["vector_search", "document_retrieval", "semantic_search"])
        
        if self.connection_status.get("neo4j"):
            features.extend(["organizational_knowledge", "relationship_queries", "policy_navigation"])
        
        if self.connection_status.get("memgraph"):
            features.extend(["real_time_reasoning", "agent_coordination", "reasoning_traces"])
        
        if self.connection_status.get("chroma"):
            features.extend(["conversational_memory", "context_retention", "personalization"])
        
        return features
