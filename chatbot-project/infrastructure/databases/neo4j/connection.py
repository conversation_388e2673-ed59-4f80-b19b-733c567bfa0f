"""Neo4j Graph Database Connection Manager."""

import logging
from typing import Dict, Any, List, Optional
from neo4j import GraphDatabase, Driver
from neo4j.exceptions import ServiceUnavailable, AuthError

logger = logging.getLogger(__name__)

class Neo4jManager:
    """Neo4j graph database connection and query manager."""
    
    def __init__(self, uri: str, user: str, password: str, database: str = "neo4j"):
        self.uri = uri
        self.user = user
        self.password = password
        self.database = database
        self.driver: Optional[Driver] = None
        
    async def initialize(self):
        """Initialize Neo4j connection."""
        try:
            self.driver = GraphDatabase.driver(
                self.uri,
                auth=(self.user, self.password),
                max_connection_lifetime=3600,
                max_connection_pool_size=50,
                connection_acquisition_timeout=60
            )
            
            # Test connection
            await self.health_check()
            
            # Create initial schema
            await self.create_schema()
            
            logger.info("✅ Neo4j connection initialized")
            return True
            
        except (ServiceUnavailable, AuthError) as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error connecting to Neo4j: {e}")
            return False
    
    async def create_schema(self):
        """Create initial graph schema for organizational knowledge."""
        schema_queries = [
            # Create constraints
            "CREATE CONSTRAINT organization_name IF NOT EXISTS FOR (o:Organization) REQUIRE o.name IS UNIQUE",
            "CREATE CONSTRAINT department_name IF NOT EXISTS FOR (d:Department) REQUIRE (d.name, d.organization) IS UNIQUE",
            "CREATE CONSTRAINT user_email IF NOT EXISTS FOR (u:User) REQUIRE u.email IS UNIQUE",
            "CREATE CONSTRAINT document_id IF NOT EXISTS FOR (doc:Document) REQUIRE doc.id IS UNIQUE",
            
            # Create indexes
            "CREATE INDEX organization_type IF NOT EXISTS FOR (o:Organization) ON (o.type)",
            "CREATE INDEX department_type IF NOT EXISTS FOR (d:Department) ON (d.type)",
            "CREATE INDEX user_role IF NOT EXISTS FOR (u:User) ON (u.role)",
            "CREATE INDEX document_type IF NOT EXISTS FOR (doc:Document) ON (doc.type)",
            "CREATE INDEX policy_category IF NOT EXISTS FOR (p:Policy) ON (p.category)"
        ]
        
        for query in schema_queries:
            try:
                await self.execute_query(query)
            except Exception as e:
                logger.warning(f"Schema creation warning: {e}")
        
        logger.info("✅ Neo4j schema created/verified")
    
    async def close(self):
        """Close Neo4j connection."""
        if self.driver:
            await self.driver.close()
            logger.info("Neo4j connection closed")
    
    async def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute a Cypher query and return results."""
        if not self.driver:
            raise RuntimeError("Neo4j driver not initialized")
        
        parameters = parameters or {}
        
        async with self.driver.session(database=self.database) as session:
            result = await session.run(query, parameters)
            records = await result.data()
            return records
    
    async def execute_write_query(self, query: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a write query (CREATE, UPDATE, DELETE)."""
        if not self.driver:
            raise RuntimeError("Neo4j driver not initialized")
        
        parameters = parameters or {}
        
        async with self.driver.session(database=self.database) as session:
            result = await session.run(query, parameters)
            summary = await result.consume()
            return {
                "nodes_created": summary.counters.nodes_created,
                "relationships_created": summary.counters.relationships_created,
                "properties_set": summary.counters.properties_set
            }
    
    async def health_check(self) -> bool:
        """Check Neo4j database health."""
        try:
            result = await self.execute_query("RETURN 1 as health")
            return len(result) > 0 and result[0].get("health") == 1
        except Exception as e:
            logger.error(f"Neo4j health check failed: {e}")
            return False
    
    async def create_organization_structure(self, organizations: List[Dict[str, Any]]):
        """Create organizational structure in the graph."""
        for org in organizations:
            # Create organization node
            org_query = """
            MERGE (o:Organization {name: $name})
            SET o.description = $description,
                o.type = $type,
                o.created_at = datetime()
            """
            await self.execute_write_query(org_query, {
                "name": org["name"],
                "description": org.get("description", ""),
                "type": org.get("type", "company")
            })
            
            # Create departments
            for dept in org.get("departments", []):
                dept_query = """
                MATCH (o:Organization {name: $org_name})
                MERGE (d:Department {name: $dept_name, organization: $org_name})
                SET d.description = $description,
                    d.type = $type,
                    d.created_at = datetime()
                MERGE (o)-[:HAS_DEPARTMENT]->(d)
                """
                await self.execute_write_query(dept_query, {
                    "org_name": org["name"],
                    "dept_name": dept["name"],
                    "description": dept.get("description", ""),
                    "type": dept.get("type", "department")
                })
        
        logger.info("✅ Organizational structure created in Neo4j")
    
    async def search_relationships(self, start_node: str, relationship_type: str = None, max_depth: int = 3) -> List[Dict[str, Any]]:
        """Search for relationships in the graph."""
        if relationship_type:
            query = f"""
            MATCH path = (start)-[r:{relationship_type}*1..{max_depth}]-(end)
            WHERE start.name CONTAINS $start_node OR start.id = $start_node
            RETURN path, length(path) as depth
            ORDER BY depth
            LIMIT 50
            """
        else:
            query = f"""
            MATCH path = (start)-[*1..{max_depth}]-(end)
            WHERE start.name CONTAINS $start_node OR start.id = $start_node
            RETURN path, length(path) as depth
            ORDER BY depth
            LIMIT 50
            """
        
        return await self.execute_query(query, {"start_node": start_node})
