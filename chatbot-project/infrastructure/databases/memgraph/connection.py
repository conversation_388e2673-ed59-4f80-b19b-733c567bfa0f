"""Memgraph Real-time Graph Database Connection Manager."""

import logging
from typing import Dict, Any, List, Optional
from neo4j import GraphDatabase, Driver
from neo4j.exceptions import ServiceUnavailable, AuthError

logger = logging.getLogger(__name__)

class MemgraphManager:
    """Memgraph real-time graph database connection and query manager."""
    
    def __init__(self, host: str = "localhost", port: int = 7687, user: str = "", password: str = "", database: str = "memgraph"):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.uri = f"bolt://{host}:{port}"
        self.driver: Optional[Driver] = None
        
    async def initialize(self):
        """Initialize Memgraph connection."""
        try:
            # Create driver (Memgraph uses Neo4j driver protocol)
            auth = (self.user, self.password) if self.user else None
            
            self.driver = GraphDatabase.driver(
                self.uri,
                auth=auth,
                max_connection_lifetime=3600,
                max_connection_pool_size=50,
                connection_acquisition_timeout=60
            )
            
            # Test connection
            await self.health_check()
            
            # Create initial schema for real-time reasoning
            await self.create_reasoning_schema()
            
            logger.info("✅ Memgraph connection initialized")
            return True
            
        except (ServiceUnavailable, AuthError) as e:
            logger.error(f"❌ Failed to connect to Memgraph: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error connecting to Memgraph: {e}")
            return False
    
    async def create_reasoning_schema(self):
        """Create schema for real-time agent reasoning graphs."""
        schema_queries = [
            # Create indexes for performance
            "CREATE INDEX ON :ReasoningSession(session_id)",
            "CREATE INDEX ON :ReasoningStep(step_id)",
            "CREATE INDEX ON :Agent(agent_id)",
            "CREATE INDEX ON :Query(query_id)",
            "CREATE INDEX ON :Knowledge(knowledge_id)",
            "CREATE INDEX ON :Tool(tool_id)",
            
            # Create constraints if supported
            "CREATE CONSTRAINT ON (s:ReasoningSession) ASSERT s.session_id IS UNIQUE",
            "CREATE CONSTRAINT ON (a:Agent) ASSERT a.agent_id IS UNIQUE",
            "CREATE CONSTRAINT ON (q:Query) ASSERT q.query_id IS UNIQUE"
        ]
        
        for query in schema_queries:
            try:
                await self.execute_query(query)
            except Exception as e:
                # Memgraph might not support all constraint types
                logger.debug(f"Schema creation note: {e}")
        
        logger.info("✅ Memgraph reasoning schema created/verified")
    
    async def close(self):
        """Close Memgraph connection."""
        if self.driver:
            await self.driver.close()
            logger.info("Memgraph connection closed")
    
    async def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute a Cypher query and return results."""
        if not self.driver:
            raise RuntimeError("Memgraph driver not initialized")
        
        parameters = parameters or {}
        
        async with self.driver.session() as session:
            result = await session.run(query, parameters)
            records = await result.data()
            return records
    
    async def execute_write_query(self, query: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a write query (CREATE, UPDATE, DELETE)."""
        if not self.driver:
            raise RuntimeError("Memgraph driver not initialized")
        
        parameters = parameters or {}
        
        async with self.driver.session() as session:
            result = await session.run(query, parameters)
            summary = await result.consume()
            return {
                "nodes_created": summary.counters.nodes_created,
                "relationships_created": summary.counters.relationships_created,
                "properties_set": summary.counters.properties_set
            }
    
    async def health_check(self) -> bool:
        """Check Memgraph database health."""
        try:
            result = await self.execute_query("RETURN 1 as health")
            return len(result) > 0 and result[0].get("health") == 1
        except Exception as e:
            logger.error(f"Memgraph health check failed: {e}")
            return False
    
    async def create_reasoning_session(self, session_id: str, query: str, user_context: Dict[str, Any]) -> bool:
        """Create a new reasoning session graph."""
        try:
            query_cypher = """
            CREATE (s:ReasoningSession {
                session_id: $session_id,
                query: $query,
                user_context: $user_context,
                created_at: timestamp(),
                status: 'active'
            })
            CREATE (q:Query {
                query_id: $session_id + '_query',
                content: $query,
                created_at: timestamp()
            })
            CREATE (s)-[:HAS_QUERY]->(q)
            """
            
            await self.execute_write_query(query_cypher, {
                "session_id": session_id,
                "query": query,
                "user_context": user_context
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create reasoning session: {e}")
            return False
    
    async def add_reasoning_step(self, session_id: str, step_id: str, agent_id: str, 
                               step_type: str, input_data: Dict[str, Any], 
                               output_data: Dict[str, Any] = None) -> bool:
        """Add a reasoning step to the session graph."""
        try:
            query = """
            MATCH (s:ReasoningSession {session_id: $session_id})
            CREATE (step:ReasoningStep {
                step_id: $step_id,
                agent_id: $agent_id,
                step_type: $step_type,
                input_data: $input_data,
                output_data: $output_data,
                created_at: timestamp(),
                status: 'completed'
            })
            CREATE (s)-[:HAS_STEP]->(step)
            
            // Create or connect to agent
            MERGE (a:Agent {agent_id: $agent_id})
            CREATE (step)-[:EXECUTED_BY]->(a)
            """
            
            await self.execute_write_query(query, {
                "session_id": session_id,
                "step_id": step_id,
                "agent_id": agent_id,
                "step_type": step_type,
                "input_data": input_data,
                "output_data": output_data or {}
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add reasoning step: {e}")
            return False
    
    async def add_agent_communication(self, from_agent: str, to_agent: str, 
                                    message_type: str, content: Dict[str, Any], 
                                    session_id: str) -> bool:
        """Record agent-to-agent communication."""
        try:
            query = """
            MATCH (s:ReasoningSession {session_id: $session_id})
            MERGE (a1:Agent {agent_id: $from_agent})
            MERGE (a2:Agent {agent_id: $to_agent})
            CREATE (comm:Communication {
                from_agent: $from_agent,
                to_agent: $to_agent,
                message_type: $message_type,
                content: $content,
                timestamp: timestamp()
            })
            CREATE (s)-[:HAS_COMMUNICATION]->(comm)
            CREATE (a1)-[:SENT]->(comm)
            CREATE (comm)-[:RECEIVED_BY]->(a2)
            """
            
            await self.execute_write_query(query, {
                "session_id": session_id,
                "from_agent": from_agent,
                "to_agent": to_agent,
                "message_type": message_type,
                "content": content
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to record agent communication: {e}")
            return False
    
    async def get_reasoning_trace(self, session_id: str) -> List[Dict[str, Any]]:
        """Get the complete reasoning trace for a session."""
        try:
            query = """
            MATCH (s:ReasoningSession {session_id: $session_id})
            OPTIONAL MATCH (s)-[:HAS_STEP]->(step:ReasoningStep)-[:EXECUTED_BY]->(agent:Agent)
            OPTIONAL MATCH (s)-[:HAS_COMMUNICATION]->(comm:Communication)
            RETURN s, step, agent, comm
            ORDER BY step.created_at, comm.timestamp
            """
            
            result = await self.execute_query(query, {"session_id": session_id})
            return result
            
        except Exception as e:
            logger.error(f"Failed to get reasoning trace: {e}")
            return []
    
    async def cleanup_old_sessions(self, hours_old: int = 24) -> int:
        """Clean up old reasoning sessions."""
        try:
            query = """
            MATCH (s:ReasoningSession)
            WHERE s.created_at < timestamp() - $milliseconds_old
            DETACH DELETE s
            RETURN count(s) as deleted_count
            """
            
            milliseconds_old = hours_old * 60 * 60 * 1000
            result = await self.execute_query(query, {"milliseconds_old": milliseconds_old})
            
            deleted_count = result[0].get("deleted_count", 0) if result else 0
            logger.info(f"Cleaned up {deleted_count} old reasoning sessions")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old sessions: {e}")
            return 0
