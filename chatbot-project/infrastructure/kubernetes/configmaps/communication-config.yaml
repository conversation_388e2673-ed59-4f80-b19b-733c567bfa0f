apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-cluster-config
  namespace: chatbot-dev
data:
  redis.conf: |
    bind 0.0.0.0
    port 6379
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 5000
    appendonly yes
    appendfsync everysec
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    save 900 1
    save 300 10
    save 60 10000
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-topics-config
  namespace: chatbot-dev
data:
  create-topics.sh: |
    #!/bin/bash
    
    # Wait for Kafka to be ready
    until kafka-topics --bootstrap-server kafka-lb-service:9092 --list; do
      echo "Waiting for Kafka to be ready..."
      sleep 5
    done
    
    # Agent Communication Topics
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic agent-requests --partitions 12 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic agent-responses --partitions 12 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic agent-events --partitions 12 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic agent-heartbeat --partitions 6 --replication-factor 3 --if-not-exists
    
    # Reasoning and Decision Topics
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic reasoning-steps --partitions 12 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic decision-events --partitions 6 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic knowledge-updates --partitions 6 --replication-factor 3 --if-not-exists
    
    # User Interaction Topics
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic user-queries --partitions 12 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic user-responses --partitions 12 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic conversation-events --partitions 6 --replication-factor 3 --if-not-exists
    
    # System Topics
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic system-metrics --partitions 6 --replication-factor 3 --if-not-exists
    kafka-topics --bootstrap-server kafka-lb-service:9092 --create --topic error-events --partitions 6 --replication-factor 3 --if-not-exists
    
    echo "All topics created successfully"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: agent-communication-config
  namespace: chatbot-dev
data:
  communication.yaml: |
    # Agent Communication Configuration
    message_broker:
      type: kafka
      bootstrap_servers: kafka-lb-service:9092
      security_protocol: PLAINTEXT
      acks: all
      retries: 3
      batch_size: 16384
      linger_ms: 10
      buffer_memory: 33554432
      
    cache:
      type: redis_cluster
      nodes:
        - redis-cluster-0.redis-cluster-service:6379
        - redis-cluster-1.redis-cluster-service:6379
        - redis-cluster-2.redis-cluster-service:6379
        - redis-cluster-3.redis-cluster-service:6379
        - redis-cluster-4.redis-cluster-service:6379
        - redis-cluster-5.redis-cluster-service:6379
      decode_responses: true
      skip_full_coverage_check: true
      
    agent_registry:
      heartbeat_interval: 30
      timeout_threshold: 90
      max_retries: 3
      retry_backoff: 5
      
    message_routing:
      default_timeout: 30
      max_message_size: 1048576
      compression: gzip
      
    topics:
      agent_requests: agent-requests
      agent_responses: agent-responses
      agent_events: agent-events
      agent_heartbeat: agent-heartbeat
      reasoning_steps: reasoning-steps
      decision_events: decision-events
      knowledge_updates: knowledge-updates
      user_queries: user-queries
      user_responses: user-responses
      conversation_events: conversation-events
      system_metrics: system-metrics
      error_events: error-events