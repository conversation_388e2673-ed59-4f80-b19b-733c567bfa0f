apiVersion: v1
kind: ConfigMap
metadata:
  name: grpc-proto-config
  namespace: chatbot-dev
data:
  agent_communication.proto: |
    syntax = "proto3";
    
    package agent_communication;
    
    service AgentCommunication {
      rpc SendMessage(AgentMessage) returns (MessageResponse);
      rpc StreamMessages(stream AgentMessage) returns (stream AgentMessage);
      rpc Subscribe(SubscriptionRequest) returns (stream AgentMessage);
      rpc GetAgentStatus(AgentStatusRequest) returns (AgentStatusResponse);
      rpc RegisterAgent(AgentRegistration) returns (RegistrationResponse);
    }
    
    message AgentMessage {
      string message_id = 1;
      string correlation_id = 2;
      string sender_id = 3;
      string receiver_id = 4;
      string agent_type = 5;
      MessageType message_type = 6;
      bytes payload = 7;
      map<string, string> metadata = 8;
      int64 timestamp = 9;
      int32 priority = 10;
      int32 ttl_seconds = 11;
    }
    
    message MessageResponse {
      string message_id = 1;
      string correlation_id = 2;
      ResponseStatus status = 3;
      string error_message = 4;
      int64 timestamp = 5;
    }
    
    message SubscriptionRequest {
      string agent_id = 1;
      repeated string message_types = 2;
      repeated string agent_types = 3;
    }
    
    message AgentStatusRequest {
      string agent_id = 1;
    }
    
    message AgentStatusResponse {
      string agent_id = 1;
      AgentStatus status = 2;
      map<string, string> metrics = 3;
      int64 last_heartbeat = 4;
    }
    
    message AgentRegistration {
      string agent_id = 1;
      string agent_type = 2;
      repeated string capabilities = 3;
      map<string, string> configuration = 4;
      string endpoint = 5;
    }
    
    message RegistrationResponse {
      bool success = 1;
      string message = 2;
      string agent_id = 3;
    }
    
    enum MessageType {
      REQUEST = 0;
      RESPONSE = 1;
      EVENT = 2;
      HEARTBEAT = 3;
      REASONING_STEP = 4;
      DECISION = 5;
      KNOWLEDGE_UPDATE = 6;
    }
    
    enum ResponseStatus {
      SUCCESS = 0;
      ERROR = 1;
      TIMEOUT = 2;
      NOT_FOUND = 3;
    }
    
    enum AgentStatus {
      ACTIVE = 0;
      INACTIVE = 1;
      BUSY = 2;
      ERROR_STATE = 3;
    }
  
  workflow_orchestration.proto: |
    syntax = "proto3";
    
    package workflow_orchestration;
    
    service WorkflowOrchestration {
      rpc StartWorkflow(StartWorkflowRequest) returns (WorkflowResponse);
      rpc GetWorkflowStatus(WorkflowStatusRequest) returns (WorkflowStatusResponse);
      rpc SignalWorkflow(SignalWorkflowRequest) returns (WorkflowResponse);
      rpc CancelWorkflow(CancelWorkflowRequest) returns (WorkflowResponse);
    }
    
    message StartWorkflowRequest {
      string workflow_id = 1;
      string workflow_type = 2;
      bytes input = 3;
      map<string, string> metadata = 4;
      int32 timeout_seconds = 5;
    }
    
    message WorkflowResponse {
      string workflow_id = 1;
      string run_id = 2;
      bool success = 3;
      string message = 4;
    }
    
    message WorkflowStatusRequest {
      string workflow_id = 1;
      string run_id = 2;
    }
    
    message WorkflowStatusResponse {
      string workflow_id = 1;
      string run_id = 2;
      WorkflowStatus status = 3;
      bytes result = 4;
      string error_message = 5;
    }
    
    message SignalWorkflowRequest {
      string workflow_id = 1;
      string run_id = 2;
      string signal_name = 3;
      bytes signal_data = 4;
    }
    
    message CancelWorkflowRequest {
      string workflow_id = 1;
      string run_id = 2;
      string reason = 3;
    }
    
    enum WorkflowStatus {
      RUNNING = 0;
      COMPLETED = 1;
      FAILED = 2;
      CANCELLED = 3;
      TERMINATED = 4;
    }