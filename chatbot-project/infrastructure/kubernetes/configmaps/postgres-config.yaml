apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: chatbot-dev
data:
  postgresql.conf: |
    # Connection settings
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    
    # Memory settings
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB
    maintenance_work_mem = 64MB
    
    # WAL settings for replication
    wal_level = replica
    max_wal_senders = 3
    max_replication_slots = 3
    wal_keep_size = 64MB
    
    # Checkpoint settings
    checkpoint_completion_target = 0.7
    wal_buffers = 16MB
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    
    # Performance
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
    # Backup settings
    archive_mode = on
    archive_command = 'cp %p /var/lib/postgresql/archive/%f'
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-config
  namespace: chatbot-dev
data:
  01-init-replication.sql: |
    -- Create replication user
    CREATE USER replicator WITH REPLICATION ENCRYPTED PASSWORD 'repl_password_123';
    
    -- Grant necessary permissions
    GRANT CONNECT ON DATABASE chatbot_db TO replicator;
  
  02-init-schema.sql: |
    -- Initialize database schema
    \c chatbot_db;
    
    -- Create users table with enhanced fields
    CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        employee_id VARCHAR(100) UNIQUE NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        organization VARCHAR(255) NOT NULL,
        department VARCHAR(255) NOT NULL,
        job_role VARCHAR(255) NOT NULL,
        manager VARCHAR(255),
        password_hash VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP,
        failed_login_attempts INTEGER DEFAULT 0,
        account_locked_until TIMESTAMP,
        preferences JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Create organizations table
    CREATE TABLE IF NOT EXISTS organizations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        domain VARCHAR(255),
        settings JSONB DEFAULT '{}',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Create departments table
    CREATE TABLE IF NOT EXISTS departments (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
        description TEXT,
        manager_id INTEGER REFERENCES users(id),
        settings JSONB DEFAULT '{}',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(name, organization_id)
    );
    
    -- Create conversations table
    CREATE TABLE IF NOT EXISTS conversations (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255),
        status VARCHAR(50) DEFAULT 'active',
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Create messages table
    CREATE TABLE IF NOT EXISTS messages (
        id SERIAL PRIMARY KEY,
        conversation_id INTEGER REFERENCES conversations(id) ON DELETE CASCADE,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        content TEXT NOT NULL,
        message_type VARCHAR(50) NOT NULL CHECK (message_type IN ('user', 'assistant')),
        agent_id VARCHAR(255),
        confidence_score FLOAT,
        processing_time_ms INTEGER,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Create agent_registry table
    CREATE TABLE IF NOT EXISTS agent_registry (
        id SERIAL PRIMARY KEY,
        agent_id VARCHAR(255) UNIQUE NOT NULL,
        agent_type VARCHAR(100) NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        capabilities JSONB DEFAULT '[]',
        status VARCHAR(50) DEFAULT 'active',
        configuration JSONB DEFAULT '{}',
        performance_metrics JSONB DEFAULT '{}',
        last_heartbeat TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Create knowledge_sources table
    CREATE TABLE IF NOT EXISTS knowledge_sources (
        id SERIAL PRIMARY KEY,
        source_id VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL,
        organization_id INTEGER REFERENCES organizations(id),
        department_id INTEGER REFERENCES departments(id),
        content_hash VARCHAR(64),
        metadata JSONB DEFAULT '{}',
        is_active BOOLEAN DEFAULT TRUE,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_organization ON users(organization);
    CREATE INDEX IF NOT EXISTS idx_users_department ON users(department);
    CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
    CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
    CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
    CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
    CREATE INDEX IF NOT EXISTS idx_messages_agent_id ON messages(agent_id);
    CREATE INDEX IF NOT EXISTS idx_agent_registry_type ON agent_registry(agent_type);
    CREATE INDEX IF NOT EXISTS idx_agent_registry_status ON agent_registry(status);
    CREATE INDEX IF NOT EXISTS idx_knowledge_sources_org ON knowledge_sources(organization_id);
    CREATE INDEX IF NOT EXISTS idx_knowledge_sources_dept ON knowledge_sources(department_id);
    CREATE INDEX IF NOT EXISTS idx_knowledge_sources_type ON knowledge_sources(type);
    
    -- Insert default organizations
    INSERT INTO organizations (name, description, domain) VALUES 
        ('NuvoAi', 'AI and Technology Company', 'nuvoai.com'),
        ('Meril', 'Healthcare and Medical Devices Company', 'meril.com')
    ON CONFLICT (name) DO NOTHING;
    
    -- Insert default departments
    WITH org_ids AS (
        SELECT id, name FROM organizations WHERE name IN ('NuvoAi', 'Meril')
    )
    INSERT INTO departments (name, organization_id, description) 
    SELECT 'AI Department', o.id, 'Artificial Intelligence Research and Development'
    FROM org_ids o WHERE o.name = 'NuvoAi'
    UNION ALL
    SELECT 'Product Department', o.id, 'Product Management and Development'
    FROM org_ids o WHERE o.name = 'NuvoAi'
    UNION ALL
    SELECT 'HR Department', o.id, 'Human Resources'
    FROM org_ids o WHERE o.name = 'NuvoAi'
    UNION ALL
    SELECT 'Technical Department', o.id, 'Technical Operations and Engineering'
    FROM org_ids o WHERE o.name = 'Meril'
    UNION ALL
    SELECT 'Finance Department', o.id, 'Financial Operations'
    FROM org_ids o WHERE o.name = 'Meril'
    UNION ALL
    SELECT 'HR Department', o.id, 'Human Resources'
    FROM org_ids o WHERE o.name = 'Meril'
    ON CONFLICT (name, organization_id) DO NOTHING;