apiVersion: v1
kind: ConfigMap
metadata:
  name: grpc-gateway-code
  namespace: chatbot-dev
data:
  grpc_gateway.py: |
    import asyncio
    import grpc
    import json
    import logging
    import redis
    from concurrent import futures
    from datetime import datetime
    from kafka import KafkaProducer
    from prometheus_client import Counter, Histogram, start_http_server
    
    import agent_communication_pb2
    import agent_communication_pb2_grpc
    
    # Metrics
    grpc_requests = Counter('grpc_requests_total', 'Total gRPC requests', ['method', 'status'])
    grpc_duration = Histogram('grpc_request_duration_seconds', 'gRPC request duration', ['method'])
    
    class AgentCommunicationService(agent_communication_pb2_grpc.AgentCommunicationServicer):
        def __init__(self):
            self.redis_client = redis.Redis(host='redis-cluster-lb-service', port=6379)
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=['kafka-lb-service:9092'],
                value_serializer=lambda x: json.dumps(x).encode('utf-8')
            )
            self.active_streams = {}
            
        def SendMessage(self, request, context):
            with grpc_duration.labels(method='SendMessage').time():
                try:
                    # Convert protobuf to dict
                    message_data = {
                        'message_id': request.message_id,
                        'correlation_id': request.correlation_id,
                        'sender_id': request.sender_id,
                        'receiver_id': request.receiver_id,
                        'agent_type': request.agent_type,
                        'message_type': request.message_type,
                        'payload': request.payload.decode('utf-8'),
                        'metadata': dict(request.metadata),
                        'timestamp': request.timestamp,
                        'priority': request.priority,
                        'ttl_seconds': request.ttl_seconds
                    }
                    
                    # Route to appropriate Kafka topic
                    topic = self._get_topic_for_message_type(request.message_type)
                    self.kafka_producer.send(topic, value=message_data, key=request.receiver_id.encode())
                    
                    # Store in Redis for tracking
                    self.redis_client.setex(
                        f"message:{request.message_id}",
                        request.ttl_seconds or 300,
                        json.dumps(message_data)
                    )
                    
                    grpc_requests.labels(method='SendMessage', status='success').inc()
                    
                    return agent_communication_pb2.MessageResponse(
                        message_id=request.message_id,
                        correlation_id=request.correlation_id,
                        status=agent_communication_pb2.ResponseStatus.SUCCESS,
                        timestamp=int(datetime.now().timestamp())
                    )
                    
                except Exception as e:
                    grpc_requests.labels(method='SendMessage', status='error').inc()
                    return agent_communication_pb2.MessageResponse(
                        message_id=request.message_id,
                        correlation_id=request.correlation_id,
                        status=agent_communication_pb2.ResponseStatus.ERROR,
                        error_message=str(e),
                        timestamp=int(datetime.now().timestamp())
                    )
        
        def StreamMessages(self, request_iterator, context):
            with grpc_duration.labels(method='StreamMessages').time():
                try:
                    for request in request_iterator:
                        # Process incoming message
                        response = self.SendMessage(request, context)
                        yield response
                        
                except Exception as e:
                    grpc_requests.labels(method='StreamMessages', status='error').inc()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(str(e))
        
        def Subscribe(self, request, context):
            with grpc_duration.labels(method='Subscribe').time():
                try:
                    # Create subscription in Redis
                    subscription_key = f"subscription:{request.agent_id}"
                    subscription_data = {
                        'agent_id': request.agent_id,
                        'message_types': list(request.message_types),
                        'agent_types': list(request.agent_types),
                        'created_at': datetime.now().isoformat()
                    }
                    
                    self.redis_client.set(subscription_key, json.dumps(subscription_data))
                    
                    # Stream messages to subscriber
                    while context.is_active():
                        # Check for new messages
                        messages = self._get_messages_for_subscriber(request.agent_id)
                        for message in messages:
                            yield message
                        
                        # Sleep to prevent busy waiting
                        time.sleep(0.1)
                        
                except Exception as e:
                    grpc_requests.labels(method='Subscribe', status='error').inc()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(str(e))
        
        def _get_topic_for_message_type(self, message_type):
            topic_map = {
                0: 'agent-requests',    # REQUEST
                1: 'agent-responses',   # RESPONSE
                2: 'agent-events',      # EVENT
                3: 'agent-heartbeat',   # HEARTBEAT
                4: 'reasoning-steps',   # REASONING_STEP
                5: 'decision-events',   # DECISION
                6: 'knowledge-updates'  # KNOWLEDGE_UPDATE
            }
            return topic_map.get(message_type, 'agent-events')
        
        def _get_messages_for_subscriber(self, agent_id):
            # Implementation for getting messages from Redis/Kafka for subscriber
            messages = []
            # This would typically involve reading from Redis Streams or Kafka
            return messages
    
    def serve():
        server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
        agent_communication_pb2_grpc.add_AgentCommunicationServicer_to_server(
            AgentCommunicationService(), server
        )
        
        listen_addr = '[::]:50051'
        server.add_insecure_port(listen_addr)
        
        # Start Prometheus metrics server
        start_http_server(9090)
        
        server.start()
        print(f"gRPC server started on {listen_addr}")
        
        try:
            server.wait_for_termination()
        except KeyboardInterrupt:
            server.stop(0)
    
    if __name__ == '__main__':
        serve()
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-streams-code
  namespace: chatbot-dev
data:
  streams_processor.py: |
    import asyncio
    import json
    import logging
    import os
    import redis
    import time
    from datetime import datetime
    from kafka import KafkaProducer, KafkaConsumer
    from prometheus_client import Counter, Histogram, start_http_server
    from http.server import HTTPServer, BaseHTTPRequestHandler
    
    # Metrics
    stream_events = Counter('redis_stream_events_total', 'Total Redis stream events', ['stream', 'consumer_group'])
    processing_duration = Histogram('stream_processing_duration_seconds', 'Stream processing duration', ['stream'])
    
    class HealthHandler(BaseHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/health':
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(b'{"status": "healthy"}')
            elif self.path == '/ready':
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(b'{"status": "ready"}')
            elif self.path == '/metrics':
                from prometheus_client import generate_latest
                self.send_response(200)
                self.send_header('Content-type', 'text/plain')
                self.end_headers()
                self.wfile.write(generate_latest())
    
    class RedisStreamsProcessor:
        def __init__(self):
            self.redis_client = redis.Redis(host='redis-cluster-lb-service', port=6379, decode_responses=True)
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=['kafka-lb-service:9092'],
                value_serializer=lambda x: json.dumps(x).encode('utf-8')
            )
            self.consumer_group = os.getenv('CONSUMER_GROUP', 'agent-coordination-group')
            self.processor_id = os.getenv('PROCESSOR_ID', 'processor-1')
            
            # Stream definitions
            self.streams = {
                'agent-coordination': 'agent-coordination-group',
                'reasoning-events': 'reasoning-group',
                'decision-events': 'decision-group',
                'knowledge-events': 'knowledge-group'
            }
            
        async def initialize_streams(self):
            """Initialize Redis streams and consumer groups"""
            for stream_name, group_name in self.streams.items():
                try:
                    # Create consumer group if it doesn't exist
                    self.redis_client.xgroup_create(stream_name, group_name, id='0', mkstream=True)
                except redis.exceptions.ResponseError as e:
                    if "BUSYGROUP" not in str(e):
                        raise
                        
        async def process_streams(self):
            """Process messages from Redis streams"""
            while True:
                try:
                    for stream_name, group_name in self.streams.items():
                        with processing_duration.labels(stream=stream_name).time():
                            # Read messages from stream
                            messages = self.redis_client.xreadgroup(
                                group_name,
                                self.processor_id,
                                {stream_name: '>'},
                                count=10,
                                block=1000
                            )
                            
                            for stream, msgs in messages:
                                for msg_id, fields in msgs:
                                    await self.process_message(stream.decode(), msg_id.decode(), fields)
                                    
                                    # Acknowledge message
                                    self.redis_client.xack(stream, group_name, msg_id)
                                    
                                    stream_events.labels(
                                        stream=stream.decode(),
                                        consumer_group=group_name
                                    ).inc()
                                    
                except Exception as e:
                    logging.error(f"Error processing streams: {e}")
                    await asyncio.sleep(5)
        
        async def process_message(self, stream_name, msg_id, fields):
            """Process individual stream message"""
            try:
                # Convert Redis fields to dict
                message_data = {k.decode() if isinstance(k, bytes) else k: 
                              v.decode() if isinstance(v, bytes) else v 
                              for k, v in fields.items()}
                
                message_data['stream_name'] = stream_name
                message_data['message_id'] = msg_id
                message_data['processed_at'] = datetime.now().isoformat()
                
                # Route to appropriate Kafka topic based on stream
                kafka_topic = self._get_kafka_topic(stream_name)
                
                self.kafka_producer.send(
                    kafka_topic,
                    value=message_data,
                    key=message_data.get('agent_id', '').encode()
                )
                
                logging.info(f"Processed message {msg_id} from stream {stream_name}")
                
            except Exception as e:
                logging.error(f"Error processing message {msg_id}: {e}")
        
        def _get_kafka_topic(self, stream_name):
            """Map Redis stream to Kafka topic"""
            topic_map = {
                'agent-coordination': 'agent-events',
                'reasoning-events': 'reasoning-steps',
                'decision-events': 'decision-events',
                'knowledge-events': 'knowledge-updates'
            }
            return topic_map.get(stream_name, 'agent-events')
        
        async def publish_to_stream(self, stream_name, data):
            """Publish data to Redis stream"""
            try:
                message_id = self.redis_client.xadd(stream_name, data)
                logging.info(f"Published message {message_id} to stream {stream_name}")
                return message_id
            except Exception as e:
                logging.error(f"Error publishing to stream {stream_name}: {e}")
                return None
    
    async def main():
        # Start health check server
        health_server = HTTPServer(('0.0.0.0', 8080), HealthHandler)
        health_thread = threading.Thread(target=health_server.serve_forever)
        health_thread.daemon = True
        health_thread.start()
        
        # Initialize and start stream processor
        processor = RedisStreamsProcessor()
        await processor.initialize_streams()
        
        # Start processing
        await processor.process_streams()
    
    if __name__ == '__main__':
        import threading
        logging.basicConfig(level=logging.INFO)
        asyncio.run(main())
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: temporal-worker-code
  namespace: chatbot-dev
data:
  temporal_worker.py: |
    import asyncio
    import json
    import logging
    import os
    from datetime import timedelta
    from temporalio import activity, workflow
    from temporalio.client import Client
    from temporalio.worker import Worker
    import redis
    from kafka import KafkaProducer
    
    # Workflow definitions
    @workflow.defn
    class AgentCoordinationWorkflow:
        @workflow.run
        async def run(self, coordination_request: dict) -> dict:
            # Multi-agent coordination workflow
            agents = coordination_request.get('agents', [])
            task_type = coordination_request.get('task_type', 'general')
            
            results = []
            
            # Assign tasks to agents
            for agent_id in agents:
                task_result = await workflow.execute_activity(
                    assign_task_to_agent,
                    args=[agent_id, task_type, coordination_request],
                    start_to_close_timeout=timedelta(minutes=5)
                )
                results.append(task_result)
            
            # Aggregate results
            final_result = await workflow.execute_activity(
                aggregate_agent_results,
                args=[results],
                start_to_close_timeout=timedelta(minutes=2)
            )
            
            return final_result
    
    @workflow.defn
    class ReasoningWorkflow:
        @workflow.run
        async def run(self, reasoning_request: dict) -> dict:
            # Multi-step reasoning workflow
            steps = reasoning_request.get('reasoning_steps', [])
            context = reasoning_request.get('context', {})
            
            reasoning_context = context
            
            for step in steps:
                step_result = await workflow.execute_activity(
                    execute_reasoning_step,
                    args=[step, reasoning_context],
                    start_to_close_timeout=timedelta(minutes=3)
                )
                
                # Update context with step result
                reasoning_context.update(step_result.get('context_updates', {}))
            
            return {
                'final_context': reasoning_context,
                'reasoning_complete': True,
                'workflow_id': workflow.info().workflow_id
            }
    
    # Activity definitions
    @activity.defn
    async def assign_task_to_agent(agent_id: str, task_type: str, task_data: dict) -> dict:
        """Assign a task to a specific agent"""
        try:
            # Connect to Redis and Kafka
            redis_client = redis.Redis(host='redis-cluster-lb-service', port=6379)
            kafka_producer = KafkaProducer(
                bootstrap_servers=['kafka-lb-service:9092'],
                value_serializer=lambda x: json.dumps(x).encode('utf-8')
            )
            
            # Create task assignment message
            task_message = {
                'agent_id': agent_id,
                'task_type': task_type,
                'task_data': task_data,
                'assigned_at': datetime.now().isoformat(),
                'workflow_id': activity.info().workflow_id,
                'activity_id': activity.info().activity_id
            }
            
            # Send to agent via Kafka
            kafka_producer.send('agent-requests', value=task_message, key=agent_id.encode())
            
            # Store task in Redis for tracking
            redis_client.setex(
                f"task:{activity.info().activity_id}",
                300,  # 5 minutes TTL
                json.dumps(task_message)
            )
            
            # Wait for agent response (simplified - in real implementation would use signals)
            await asyncio.sleep(2)  # Simulate processing time
            
            return {
                'agent_id': agent_id,
                'task_completed': True,
                'result': f"Task {task_type} completed by {agent_id}"
            }
            
        except Exception as e:
            logging.error(f"Error assigning task to agent {agent_id}: {e}")
            return {
                'agent_id': agent_id,
                'task_completed': False,
                'error': str(e)
            }
    
    @activity.defn
    async def aggregate_agent_results(results: list) -> dict:
        """Aggregate results from multiple agents"""
        successful_results = [r for r in results if r.get('task_completed', False)]
        failed_results = [r for r in results if not r.get('task_completed', False)]
        
        return {
            'total_agents': len(results),
            'successful_count': len(successful_results),
            'failed_count': len(failed_results),
            'success_rate': len(successful_results) / len(results) if results else 0,
            'results': successful_results,
            'failures': failed_results
        }
    
    @activity.defn
    async def execute_reasoning_step(step: dict, context: dict) -> dict:
        """Execute a single reasoning step"""
        try:
            step_type = step.get('type', 'analysis')
            step_data = step.get('data', {})
            
            # Simulate reasoning step execution
            result = {
                'step_type': step_type,
                'step_completed': True,
                'context_updates': {
                    f"{step_type}_result": f"Processed {step_type} with context",
                    'step_timestamp': datetime.now().isoformat()
                }
            }
            
            # Log reasoning step to Redis stream
            redis_client = redis.Redis(host='redis-cluster-lb-service', port=6379)
            redis_client.xadd('reasoning-events', {
                'step_type': step_type,
                'context': json.dumps(context),
                'result': json.dumps(result),
                'timestamp': datetime.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            logging.error(f"Error executing reasoning step: {e}")
            return {
                'step_completed': False,
                'error': str(e),
                'context_updates': {}
            }
    
    async def main():
        # Connect to Temporal server
        client = await Client.connect(
            f"{os.getenv('TEMPORAL_HOST', 'temporal-server-service')}:{os.getenv('TEMPORAL_PORT', '7233')}"
        )
        
        # Create worker
        worker = Worker(
            client,
            task_queue="agent-coordination-queue",
            workflows=[AgentCoordinationWorkflow, ReasoningWorkflow],
            activities=[assign_task_to_agent, aggregate_agent_results, execute_reasoning_step]
        )
        
        logging.info("Starting Temporal worker...")
        await worker.run()
    
    if __name__ == '__main__':
        from datetime import datetime
        logging.basicConfig(level=logging.INFO)
        asyncio.run(main())