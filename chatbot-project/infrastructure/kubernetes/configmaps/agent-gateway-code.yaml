apiVersion: v1
kind: ConfigMap
metadata:
  name: agent-gateway-code
  namespace: chatbot-dev
data:
  agent_gateway.py: |
    import asyncio
    import json
    import logging
    import os
    import time
    from typing import Dict, List, Optional
    from datetime import datetime
    
    import redis.asyncio as redis
    from fastapi import FastAPI, HTTPException, BackgroundTasks
    from kafka import KafkaProducer, KafkaConsumer
    from prometheus_client import Counter, Histogram, Gauge, generate_latest
    from pydantic import BaseModel
    import uvicorn
    
    # Metrics
    message_counter = Counter('agent_messages_total', 'Total agent messages', ['agent_type', 'message_type'])
    response_time = Histogram('agent_response_time_seconds', 'Agent response time')
    active_agents = Gauge('active_agents_total', 'Number of active agents', ['agent_type'])
    
    # Models
    class AgentMessage(BaseModel):
        agent_id: str
        agent_type: str
        target_agent_id: Optional[str] = None
        message_type: str
        content: Dict
        priority: int = 5
        timeout: int = 30
        correlation_id: Optional[str] = None
    
    class AgentResponse(BaseModel):
        agent_id: str
        correlation_id: str
        success: bool
        content: Dict
        processing_time_ms: int
        timestamp: str
    
    # Gateway Service
    class AgentGateway:
        def __init__(self):
            self.kafka_servers = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka-lb-service:9092')
            self.redis_nodes = os.getenv('REDIS_CLUSTER_NODES', 'redis-cluster-lb-service:6379')
            
            self.producer = KafkaProducer(
                bootstrap_servers=[self.kafka_servers],
                value_serializer=lambda x: json.dumps(x).encode('utf-8'),
                acks='all',
                retries=3
            )
            
            self.redis_client = None
            self.active_consumers = {}
            
        async def initialize_redis(self):
            self.redis_client = redis.Redis.from_url(f"redis://{self.redis_nodes}")
            
        async def send_message(self, message: AgentMessage) -> str:
            """Send message to agent via Kafka"""
            correlation_id = message.correlation_id or f"{message.agent_id}_{int(time.time() * 1000)}"
            
            kafka_message = {
                'correlation_id': correlation_id,
                'agent_id': message.agent_id,
                'agent_type': message.agent_type,
                'target_agent_id': message.target_agent_id,
                'message_type': message.message_type,
                'content': message.content,
                'priority': message.priority,
                'timeout': message.timeout,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Route message based on type
            if message.target_agent_id:
                topic = 'agent-requests'
                key = message.target_agent_id
            else:
                topic = 'agent-events'
                key = message.agent_type
                
            self.producer.send(topic, value=kafka_message, key=key.encode('utf-8'))
            self.producer.flush()
            
            # Cache message for tracking
            await self.redis_client.setex(
                f"message:{correlation_id}",
                message.timeout,
                json.dumps(kafka_message)
            )
            
            message_counter.labels(
                agent_type=message.agent_type,
                message_type=message.message_type
            ).inc()
            
            return correlation_id
            
        async def get_response(self, correlation_id: str, timeout: int = 30) -> Optional[Dict]:
            """Get response for correlation ID"""
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                response = await self.redis_client.get(f"response:{correlation_id}")
                if response:
                    return json.loads(response)
                await asyncio.sleep(0.1)
                
            return None
            
        async def register_agent(self, agent_id: str, agent_type: str, capabilities: List[str]):
            """Register agent in Redis"""
            agent_info = {
                'agent_id': agent_id,
                'agent_type': agent_type,
                'capabilities': capabilities,
                'status': 'active',
                'last_heartbeat': datetime.utcnow().isoformat(),
                'registered_at': datetime.utcnow().isoformat()
            }
            
            await self.redis_client.hset(
                f"agent:{agent_id}",
                mapping=agent_info
            )
            
            await self.redis_client.sadd(f"agents:{agent_type}", agent_id)
            active_agents.labels(agent_type=agent_type).inc()
            
        async def heartbeat(self, agent_id: str):
            """Update agent heartbeat"""
            await self.redis_client.hset(
                f"agent:{agent_id}",
                "last_heartbeat",
                datetime.utcnow().isoformat()
            )
    
    # FastAPI App
    app = FastAPI(title="Agent Gateway", version="1.0.0")
    gateway = AgentGateway()
    
    @app.on_event("startup")
    async def startup():
        await gateway.initialize_redis()
        
    @app.post("/agents/register")
    async def register_agent(agent_id: str, agent_type: str, capabilities: List[str]):
        await gateway.register_agent(agent_id, agent_type, capabilities)
        return {"status": "registered", "agent_id": agent_id}
        
    @app.post("/agents/{agent_id}/heartbeat")
    async def agent_heartbeat(agent_id: str):
        await gateway.heartbeat(agent_id)
        return {"status": "ok", "timestamp": datetime.utcnow().isoformat()}
        
    @app.post("/messages/send")
    async def send_message(message: AgentMessage):
        correlation_id = await gateway.send_message(message)
        return {"correlation_id": correlation_id, "status": "sent"}
        
    @app.get("/messages/{correlation_id}/response")
    async def get_response(correlation_id: str, timeout: int = 30):
        with response_time.time():
            response = await gateway.get_response(correlation_id, timeout)
            
        if response:
            return response
        else:
            raise HTTPException(status_code=404, detail="Response not found or timeout")
            
    @app.get("/health")
    async def health():
        return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
        
    @app.get("/ready")
    async def ready():
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
        
    @app.get("/metrics")
    async def metrics():
        return generate_latest()
    
    if __name__ == "__main__":
        uvicorn.run(app, host="0.0.0.0", port=8080)
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: agent-registry-code
  namespace: chatbot-dev
data:
  agent_registry.py: |
    import asyncio
    import json
    import logging
    import os
    import time
    from typing import Dict, List, Optional
    from datetime import datetime, timedelta
    
    import psycopg2
    import redis.asyncio as redis
    from fastapi import FastAPI, HTTPException
    from prometheus_client import Counter, Gauge, generate_latest
    from pydantic import BaseModel
    import uvicorn
    
    # Metrics
    registered_agents = Gauge('registered_agents_total', 'Total registered agents', ['agent_type', 'status'])
    agent_operations = Counter('agent_registry_operations_total', 'Agent registry operations', ['operation'])
    
    # Models
    class AgentRegistration(BaseModel):
        agent_id: str
        agent_type: str
        capabilities: List[str]
        configuration: Dict = {}
        
    class AgentStatus(BaseModel):
        agent_id: str
        status: str
        performance_metrics: Dict = {}
    
    # Registry Service
    class AgentRegistry:
        def __init__(self):
            self.redis_nodes = os.getenv('REDIS_CLUSTER_NODES', 'redis-cluster-lb-service:6379')
            self.postgres_config = {
                'host': os.getenv('POSTGRES_HOST', 'postgres-primary-service'),
                'port': int(os.getenv('POSTGRES_PORT', '5432')),
                'database': os.getenv('POSTGRES_DB', 'chatbot_db'),
                'user': os.getenv('POSTGRES_USER', 'chatbot_user'),
                'password': os.getenv('POSTGRES_PASSWORD', 'chatbot_pass')
            }
            
            self.redis_client = None
            self.postgres_conn = None
            
        async def initialize(self):
            # Initialize Redis
            self.redis_client = redis.Redis.from_url(f"redis://{self.redis_nodes}")
            
            # Initialize PostgreSQL
            self.postgres_conn = psycopg2.connect(**self.postgres_config)
            
        async def register_agent(self, registration: AgentRegistration):
            """Register agent in both Redis and PostgreSQL"""
            agent_info = {
                'agent_id': registration.agent_id,
                'agent_type': registration.agent_type,
                'capabilities': json.dumps(registration.capabilities),
                'configuration': json.dumps(registration.configuration),
                'status': 'active',
                'last_heartbeat': datetime.utcnow().isoformat(),
                'registered_at': datetime.utcnow().isoformat()
            }
            
            # Store in Redis for fast access
            await self.redis_client.hset(
                f"agent:{registration.agent_id}",
                mapping=agent_info
            )
            
            await self.redis_client.sadd(f"agents:{registration.agent_type}", registration.agent_id)
            
            # Store in PostgreSQL for persistence
            with self.postgres_conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO agent_registry 
                    (agent_id, agent_type, name, capabilities, configuration, status)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT (agent_id) DO UPDATE SET
                        capabilities = EXCLUDED.capabilities,
                        configuration = EXCLUDED.configuration,
                        status = EXCLUDED.status,
                        updated_at = CURRENT_TIMESTAMP
                """, (
                    registration.agent_id,
                    registration.agent_type,
                    registration.agent_id,
                    json.dumps(registration.capabilities),
                    json.dumps(registration.configuration),
                    'active'
                ))
                self.postgres_conn.commit()
                
            registered_agents.labels(
                agent_type=registration.agent_type,
                status='active'
            ).inc()
            
            agent_operations.labels(operation='register').inc()
            
        async def update_status(self, agent_status: AgentStatus):
            """Update agent status"""
            await self.redis_client.hset(
                f"agent:{agent_status.agent_id}",
                mapping={
                    'status': agent_status.status,
                    'performance_metrics': json.dumps(agent_status.performance_metrics),
                    'last_heartbeat': datetime.utcnow().isoformat()
                }
            )
            
            with self.postgres_conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE agent_registry 
                    SET status = %s, 
                        performance_metrics = %s,
                        last_heartbeat = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE agent_id = %s
                """, (
                    agent_status.status,
                    json.dumps(agent_status.performance_metrics),
                    agent_status.agent_id
                ))
                self.postgres_conn.commit()
                
            agent_operations.labels(operation='status_update').inc()
            
        async def get_agents_by_type(self, agent_type: str) -> List[Dict]:
            """Get all agents of specific type"""
            agent_ids = await self.redis_client.smembers(f"agents:{agent_type}")
            agents = []
            
            for agent_id in agent_ids:
                agent_info = await self.redis_client.hgetall(f"agent:{agent_id.decode()}")
                if agent_info:
                    agents.append({
                        k.decode(): v.decode() if isinstance(v, bytes) else v
                        for k, v in agent_info.items()
                    })
                    
            return agents
            
        async def cleanup_inactive_agents(self):
            """Remove agents that haven't sent heartbeat in 5 minutes"""
            cutoff_time = datetime.utcnow() - timedelta(minutes=5)
            
            # Get all agent keys
            agent_keys = await self.redis_client.keys("agent:*")
            
            for key in agent_keys:
                agent_info = await self.redis_client.hgetall(key)
                if agent_info:
                    last_heartbeat = agent_info.get(b'last_heartbeat', b'').decode()
                    if last_heartbeat:
                        heartbeat_time = datetime.fromisoformat(last_heartbeat)
                        if heartbeat_time < cutoff_time:
                            agent_id = agent_info.get(b'agent_id', b'').decode()
                            agent_type = agent_info.get(b'agent_type', b'').decode()
                            
                            # Remove from Redis
                            await self.redis_client.delete(key)
                            await self.redis_client.srem(f"agents:{agent_type}", agent_id)
                            
                            # Update PostgreSQL
                            with self.postgres_conn.cursor() as cursor:
                                cursor.execute("""
                                    UPDATE agent_registry 
                                    SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
                                    WHERE agent_id = %s
                                """, (agent_id,))
                                self.postgres_conn.commit()
                                
                            registered_agents.labels(
                                agent_type=agent_type,
                                status='active'
                            ).dec()
                            
                            registered_agents.labels(
                                agent_type=agent_type,
                                status='inactive'
                            ).inc()
    
    # FastAPI App
    app = FastAPI(title="Agent Registry", version="1.0.0")
    registry = AgentRegistry()
    
    @app.on_event("startup")
    async def startup():
        await registry.initialize()
        
        # Start cleanup task
        asyncio.create_task(cleanup_task())
        
    async def cleanup_task():
        while True:
            await asyncio.sleep(60)  # Run every minute
            await registry.cleanup_inactive_agents()
            
    @app.post("/agents/register")
    async def register_agent(registration: AgentRegistration):
        await registry.register_agent(registration)
        return {"status": "registered", "agent_id": registration.agent_id}
        
    @app.put("/agents/{agent_id}/status")
    async def update_agent_status(agent_id: str, status: AgentStatus):
        status.agent_id = agent_id
        await registry.update_status(status)
        return {"status": "updated", "agent_id": agent_id}
        
    @app.get("/agents/type/{agent_type}")
    async def get_agents_by_type(agent_type: str):
        agents = await registry.get_agents_by_type(agent_type)
        return {"agents": agents, "count": len(agents)}
        
    @app.get("/health")
    async def health():
        return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
        
    @app.get("/ready")
    async def ready():
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
        
    @app.get("/metrics")
    async def metrics():
        return generate_latest()
    
    if __name__ == "__main__":
        uvicorn.run(app, host="0.0.0.0", port=8082)