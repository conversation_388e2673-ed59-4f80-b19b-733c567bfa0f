apiVersion: v1
kind: ConfigMap
metadata:
  name: chatbot-config
  namespace: chatbot-dev
data:
  # Database Configuration
  POSTGRES_HOST: "postgres-service"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "chatbot_db"
  NEO4J_HOST: "neo4j-service"
  NEO4J_PORT: "7687"
  MILVUS_HOST: "milvus-service"
  MILVUS_PORT: "19530"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  MEMGRAPH_HOST: "memgraph-service"
  MEMGRAPH_PORT: "7687"
  
  # API Configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  
  # Agent Configuration
  AGENT_REGISTRY_URL: "http://agent-service:8001"
  AGENT_COMMUNICATION_TIMEOUT: "30"
  MAX_REASONING_DEPTH: "5"
  
  # LLM Configuration
  EMBEDDING_MODEL: "sentence-transformers/all-MiniLM-L6-v2"
  MAX_CONTEXT_LENGTH: "4096"
  
  # Logging
  LOG_LEVEL: "INFO"
  LOG_FORMAT: "json"