apiVersion: v1
kind: ConfigMap
metadata:
  name: message-broker-code
  namespace: chatbot-dev
data:
  message_broker.py: |
    import asyncio
    import json
    import logging
    import os
    import time
    import uuid
    from datetime import datetime, timedelta
    from typing import Dict, List, Optional, Any
    from dataclasses import dataclass, asdict
    from enum import Enum
    import heapq
    
    import redis
    from fastapi import FastAPI, HTTPException, BackgroundTasks
    from kafka import KafkaProducer, KafkaConsumer
    from prometheus_client import Counter, Histogram, Gauge, generate_latest
    from pydantic import BaseModel
    import uvicorn
    
    # Metrics
    message_counter = Counter('broker_messages_total', 'Total messages processed', ['priority', 'status'])
    routing_duration = Histogram('message_routing_duration_seconds', 'Message routing duration')
    queue_size = Gauge('message_queue_size', 'Current message queue size', ['priority'])
    delivery_counter = Counter('message_delivery_total', 'Message delivery attempts', ['status'])
    
    class MessagePriority(Enum):
        CRITICAL = 1
        HIGH = 2
        NORMAL = 3
        LOW = 4
        BACKGROUND = 5
    
    class MessageStatus(Enum):
        PENDING = "pending"
        PROCESSING = "processing"
        DELIVERED = "delivered"
        FAILED = "failed"
        EXPIRED = "expired"
    
    @dataclass
    class BrokerMessage:
        message_id: str
        sender_id: str
        receiver_id: str
        message_type: str
        payload: Dict[str, Any]
        priority: int
        ttl_seconds: int
        created_at: float
        correlation_id: Optional[str] = None
        retry_count: int = 0
        max_retries: int = 3
        status: str = MessageStatus.PENDING.value
        
        def __lt__(self, other):
            # For priority queue - lower priority number = higher priority
            return self.priority < other.priority
        
        def is_expired(self) -> bool:
            return time.time() - self.created_at > self.ttl_seconds
        
        def to_dict(self) -> Dict:
            return asdict(self)
    
    class MessageRouter:
        def __init__(self):
            self.routing_rules = {
                'coordinator': ['reasoning', 'tool', 'critic'],
                'reasoning': ['coordinator', 'knowledge'],
                'tool': ['coordinator'],
                'critic': ['coordinator'],
                'knowledge': ['reasoning', 'coordinator']
            }
            
        def get_route(self, sender_type: str, receiver_type: str) -> Optional[str]:
            """Get routing path between agent types"""
            allowed_receivers = self.routing_rules.get(sender_type, [])
            if receiver_type in allowed_receivers or receiver_type == 'broadcast':
                return f"{sender_type}->{receiver_type}"
            return None
        
        def is_valid_route(self, sender_type: str, receiver_type: str) -> bool:
            return self.get_route(sender_type, receiver_type) is not None
    
    class MessageTracker:
        def __init__(self, redis_client):
            self.redis = redis_client
            
        async def track_message(self, message: BrokerMessage):
            """Track message in Redis for debugging"""
            tracking_data = {
                'message_id': message.message_id,
                'sender_id': message.sender_id,
                'receiver_id': message.receiver_id,
                'status': message.status,
                'created_at': message.created_at,
                'priority': message.priority,
                'retry_count': message.retry_count
            }
            
            # Store message tracking info
            await self.redis.setex(
                f"track:{message.message_id}",
                message.ttl_seconds,
                json.dumps(tracking_data)
            )
            
            # Add to sender's message list
            await self.redis.lpush(f"sent:{message.sender_id}", message.message_id)
            await self.redis.expire(f"sent:{message.sender_id}", 3600)  # 1 hour
            
            # Add to receiver's message list
            await self.redis.lpush(f"received:{message.receiver_id}", message.message_id)
            await self.redis.expire(f"received:{message.receiver_id}", 3600)  # 1 hour
        
        async def update_status(self, message_id: str, status: MessageStatus):
            """Update message status"""
            tracking_key = f"track:{message_id}"
            tracking_data = await self.redis.get(tracking_key)
            
            if tracking_data:
                data = json.loads(tracking_data)
                data['status'] = status.value
                data['updated_at'] = time.time()
                
                await self.redis.setex(tracking_key, 3600, json.dumps(data))
        
        async def get_message_history(self, agent_id: str, message_type: str = 'sent') -> List[Dict]:
            """Get message history for an agent"""
            message_ids = await self.redis.lrange(f"{message_type}:{agent_id}", 0, 50)
            messages = []
            
            for msg_id in message_ids:
                tracking_data = await self.redis.get(f"track:{msg_id.decode()}")
                if tracking_data:
                    messages.append(json.loads(tracking_data))
                    
            return messages
    
    class EnhancedMessageBroker:
        def __init__(self):
            self.redis_client = redis.Redis(host='redis-cluster-lb-service', port=6379, decode_responses=True)
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=['kafka-lb-service:9092'],
                value_serializer=lambda x: json.dumps(x).encode('utf-8'),
                acks='all',
                retries=3
            )
            
            self.router = MessageRouter()
            self.tracker = MessageTracker(self.redis_client)
            
            # Priority queues for different priority levels
            self.priority_queues = {
                priority.value: [] for priority in MessagePriority
            }
            
            # Message processing state
            self.processing_messages = {}
            self.failed_messages = []
            
        async def send_message(self, message: BrokerMessage) -> str:
            """Send message with priority and routing"""
            with routing_duration.time():
                try:
                    # Validate routing
                    sender_type = message.payload.get('sender_type', 'unknown')
                    receiver_type = message.payload.get('receiver_type', 'unknown')
                    
                    if not self.router.is_valid_route(sender_type, receiver_type):
                        raise ValueError(f"Invalid route: {sender_type} -> {receiver_type}")
                    
                    # Check if message is expired
                    if message.is_expired():
                        message.status = MessageStatus.EXPIRED.value
                        await self.tracker.track_message(message)
                        message_counter.labels(priority=message.priority, status='expired').inc()
                        return message.message_id
                    
                    # Add to priority queue
                    heapq.heappush(self.priority_queues[message.priority], message)
                    queue_size.labels(priority=message.priority).inc()
                    
                    # Track message
                    await self.tracker.track_message(message)
                    
                    # Process message asynchronously
                    asyncio.create_task(self._process_message_queue())
                    
                    message_counter.labels(priority=message.priority, status='queued').inc()
                    return message.message_id
                    
                except Exception as e:
                    message_counter.labels(priority=message.priority, status='error').inc()
                    logging.error(f"Error sending message {message.message_id}: {e}")
                    raise
        
        async def _process_message_queue(self):
            """Process messages from priority queues"""
            try:
                # Process messages in priority order
                for priority in sorted(self.priority_queues.keys()):
                    queue = self.priority_queues[priority]
                    
                    while queue:
                        message = heapq.heappop(queue)
                        queue_size.labels(priority=priority).dec()
                        
                        if message.is_expired():
                            message.status = MessageStatus.EXPIRED.value
                            await self.tracker.update_status(message.message_id, MessageStatus.EXPIRED)
                            message_counter.labels(priority=priority, status='expired').inc()
                            continue
                        
                        # Process message
                        success = await self._deliver_message(message)
                        
                        if success:
                            message.status = MessageStatus.DELIVERED.value
                            await self.tracker.update_status(message.message_id, MessageStatus.DELIVERED)
                            message_counter.labels(priority=priority, status='delivered').inc()
                            delivery_counter.labels(status='success').inc()
                        else:
                            # Retry logic
                            message.retry_count += 1
                            if message.retry_count < message.max_retries:
                                # Re-queue with exponential backoff
                                await asyncio.sleep(2 ** message.retry_count)
                                heapq.heappush(queue, message)
                                queue_size.labels(priority=priority).inc()
                            else:
                                message.status = MessageStatus.FAILED.value
                                await self.tracker.update_status(message.message_id, MessageStatus.FAILED)
                                self.failed_messages.append(message)
                                message_counter.labels(priority=priority, status='failed').inc()
                                delivery_counter.labels(status='failed').inc()
                        
                        # Small delay to prevent overwhelming
                        await asyncio.sleep(0.01)
                        
            except Exception as e:
                logging.error(f"Error processing message queue: {e}")
        
        async def _deliver_message(self, message: BrokerMessage) -> bool:
            """Deliver message via Kafka"""
            try:
                # Determine Kafka topic based on message type
                topic = self._get_kafka_topic(message.message_type)
                
                # Prepare message for Kafka
                kafka_message = message.to_dict()
                kafka_message['broker_processed_at'] = time.time()
                
                # Send to Kafka
                future = self.kafka_producer.send(
                    topic,
                    value=kafka_message,
                    key=message.receiver_id.encode()
                )
                
                # Wait for confirmation (with timeout)
                record_metadata = future.get(timeout=10)
                
                logging.info(f"Message {message.message_id} delivered to {topic}:{record_metadata.partition}")
                return True
                
            except Exception as e:
                logging.error(f"Failed to deliver message {message.message_id}: {e}")
                return False
        
        def _get_kafka_topic(self, message_type: str) -> str:
            """Map message type to Kafka topic"""
            topic_map = {
                'request': 'agent-requests',
                'response': 'agent-responses',
                'event': 'agent-events',
                'heartbeat': 'agent-heartbeat',
                'reasoning': 'reasoning-steps',
                'decision': 'decision-events',
                'knowledge': 'knowledge-updates'
            }
            return topic_map.get(message_type, 'agent-events')
        
        async def get_queue_stats(self) -> Dict:
            """Get current queue statistics"""
            stats = {}
            for priority, queue in self.priority_queues.items():
                stats[f"priority_{priority}"] = len(queue)
            
            stats['processing'] = len(self.processing_messages)
            stats['failed'] = len(self.failed_messages)
            
            return stats
        
        async def get_message_debug_info(self, message_id: str) -> Optional[Dict]:
            """Get debug information for a message"""
            tracking_data = await self.redis_client.get(f"track:{message_id}")
            if tracking_data:
                return json.loads(tracking_data)
            return None
    
    # FastAPI App
    app = FastAPI(title="Enhanced Message Broker", version="1.0.0")
    broker = EnhancedMessageBroker()
    
    class MessageRequest(BaseModel):
        sender_id: str
        receiver_id: str
        message_type: str
        payload: Dict[str, Any]
        priority: int = 3
        ttl_seconds: int = 300
        correlation_id: Optional[str] = None
    
    @app.post("/messages/send")
    async def send_message(request: MessageRequest):
        message = BrokerMessage(
            message_id=str(uuid.uuid4()),
            sender_id=request.sender_id,
            receiver_id=request.receiver_id,
            message_type=request.message_type,
            payload=request.payload,
            priority=request.priority,
            ttl_seconds=request.ttl_seconds,
            created_at=time.time(),
            correlation_id=request.correlation_id
        )
        
        message_id = await broker.send_message(message)
        return {"message_id": message_id, "status": "queued"}
    
    @app.get("/messages/{message_id}/debug")
    async def get_message_debug(message_id: str):
        debug_info = await broker.get_message_debug_info(message_id)
        if debug_info:
            return debug_info
        else:
            raise HTTPException(status_code=404, detail="Message not found")
    
    @app.get("/agents/{agent_id}/messages")
    async def get_agent_messages(agent_id: str, message_type: str = 'sent'):
        messages = await broker.tracker.get_message_history(agent_id, message_type)
        return {"agent_id": agent_id, "messages": messages}
    
    @app.get("/broker/stats")
    async def get_broker_stats():
        stats = await broker.get_queue_stats()
        return {"broker_stats": stats, "timestamp": datetime.now().isoformat()}
    
    @app.get("/health")
    async def health():
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}
    
    @app.get("/ready")
    async def ready():
        return {"status": "ready", "timestamp": datetime.now().isoformat()}
    
    @app.get("/metrics")
    async def metrics():
        return generate_latest()
    
    if __name__ == "__main__":
        logging.basicConfig(level=logging.INFO)
        uvicorn.run(app, host="0.0.0.0", port=8080)