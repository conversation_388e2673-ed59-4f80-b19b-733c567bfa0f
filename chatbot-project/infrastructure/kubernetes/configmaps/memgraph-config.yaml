apiVersion: v1
kind: ConfigMap
metadata:
  name: memgraph-init-config
  namespace: chatbot-dev
data:
  reasoning_graph_init.py: |
    import mgp
    import json
    from datetime import datetime
    from typing import Dict, List, Any
    
    @mgp.read_proc
    def get_agent_reasoning_path(context: mgp.ProcCtx, agent_id: str, session_id: str) -> mgp.Record(path=mgp.List[mgp.Vertex]):
        """Get reasoning path for an agent in a specific session"""
        query = """
        MATCH path = (a:Agent {agent_id: $agent_id})-[:REASONING_SESSION]->(s:Session {session_id: $session_id})
                    -[:HAS_STEP*]->(step:ReasoningStep)
        RETURN nodes(path) as reasoning_path
        ORDER BY step.timestamp
        """
        result = context.graph.execute(query, {"agent_id": agent_id, "session_id": session_id})
        paths = []
        for record in result:
            paths.extend(record["reasoning_path"])
        return mgp.Record(path=paths)
    
    @mgp.write_proc
    def add_reasoning_step(context: mgp.ProcCtx, agent_id: str, session_id: str, 
                          step_type: str, content: str, confidence: float,
                          metadata: str = "{}") -> mgp.Record(step_id=str):
        """Add a new reasoning step to the graph"""
        timestamp = datetime.now().isoformat()
        step_id = f"{agent_id}_{session_id}_{timestamp}"
        
        query = """
        MERGE (a:Agent {agent_id: $agent_id})
        MERGE (s:Session {session_id: $session_id, agent_id: $agent_id})
        MERGE (a)-[:REASONING_SESSION]->(s)
        CREATE (step:ReasoningStep {
            step_id: $step_id,
            step_type: $step_type,
            content: $content,
            confidence: $confidence,
            timestamp: $timestamp,
            metadata: $metadata
        })
        MERGE (s)-[:HAS_STEP]->(step)
        
        // Link to previous step if exists
        OPTIONAL MATCH (s)-[:HAS_STEP]->(prev:ReasoningStep)
        WHERE prev.timestamp < $timestamp
        WITH step, prev
        ORDER BY prev.timestamp DESC
        LIMIT 1
        FOREACH (p IN CASE WHEN prev IS NOT NULL THEN [prev] ELSE [] END |
            CREATE (p)-[:NEXT_STEP]->(step)
        )
        
        RETURN $step_id as step_id
        """
        
        result = context.graph.execute(query, {
            "agent_id": agent_id,
            "session_id": session_id,
            "step_id": step_id,
            "step_type": step_type,
            "content": content,
            "confidence": confidence,
            "timestamp": timestamp,
            "metadata": metadata
        })
        
        return mgp.Record(step_id=step_id)
    
    @mgp.read_proc
    def analyze_reasoning_patterns(context: mgp.ProcCtx, agent_type: str, 
                                 time_window_hours: int = 24) -> mgp.Record(patterns=mgp.List[mgp.Map]):
        """Analyze reasoning patterns for agent optimization"""
        query = """
        MATCH (a:Agent {agent_type: $agent_type})-[:REASONING_SESSION]->(s:Session)
              -[:HAS_STEP]->(step:ReasoningStep)
        WHERE datetime(step.timestamp) > datetime() - duration({hours: $time_window_hours})
        WITH step.step_type as step_type, 
             avg(step.confidence) as avg_confidence,
             count(step) as step_count,
             collect(step.metadata) as metadata_list
        RETURN {
            step_type: step_type,
            avg_confidence: avg_confidence,
            frequency: step_count,
            metadata_patterns: metadata_list
        } as pattern
        """
        
        result = context.graph.execute(query, {
            "agent_type": agent_type,
            "time_window_hours": time_window_hours
        })
        
        patterns = [record["pattern"] for record in result]
        return mgp.Record(patterns=patterns)
  
  streaming_updates.py: |
    import mgp
    import json
    from datetime import datetime
    
    @mgp.transformation
    def process_agent_event(context: mgp.TransCtx, messages: mgp.Messages) -> mgp.Record():
        """Process streaming agent events and update reasoning graph"""
        for message in messages:
            try:
                event_data = json.loads(message.payload().decode('utf-8'))
                event_type = event_data.get('event_type')
                
                if event_type == 'reasoning_step':
                    _handle_reasoning_step(context, event_data)
                elif event_type == 'agent_communication':
                    _handle_agent_communication(context, event_data)
                elif event_type == 'knowledge_update':
                    _handle_knowledge_update(context, event_data)
                    
            except Exception as e:
                print(f"Error processing message: {e}")
                continue
        
        return mgp.Record()
    
    def _handle_reasoning_step(context: mgp.TransCtx, event_data: dict):
        """Handle reasoning step events"""
        query = """
        MERGE (a:Agent {agent_id: $agent_id})
        SET a.last_activity = $timestamp,
            a.status = 'active'
        
        MERGE (s:Session {session_id: $session_id, agent_id: $agent_id})
        SET s.last_update = $timestamp
        
        CREATE (step:ReasoningStep {
            step_id: $step_id,
            step_type: $step_type,
            content: $content,
            confidence: $confidence,
            timestamp: $timestamp,
            processing_time_ms: $processing_time_ms
        })
        
        MERGE (a)-[:REASONING_SESSION]->(s)
        MERGE (s)-[:HAS_STEP]->(step)
        """
        
        context.graph.execute(query, event_data)
    
    def _handle_agent_communication(context: mgp.TransCtx, event_data: dict):
        """Handle agent-to-agent communication events"""
        query = """
        MATCH (sender:Agent {agent_id: $sender_id})
        MATCH (receiver:Agent {agent_id: $receiver_id})
        
        CREATE (comm:Communication {
            communication_id: $communication_id,
            message_type: $message_type,
            content: $content,
            timestamp: $timestamp,
            success: $success
        })
        
        CREATE (sender)-[:SENT]->(comm)-[:RECEIVED_BY]->(receiver)
        """
        
        context.graph.execute(query, event_data)
    
    def _handle_knowledge_update(context: mgp.TransCtx, event_data: dict):
        """Handle knowledge graph updates"""
        query = """
        MERGE (k:Knowledge {knowledge_id: $knowledge_id})
        SET k.content = $content,
            k.last_updated = $timestamp,
            k.version = $version
        
        WITH k
        MATCH (a:Agent {agent_id: $agent_id})
        MERGE (a)-[:ACCESSED_KNOWLEDGE {timestamp: $timestamp}]->(k)
        """
        
        context.graph.execute(query, event_data)
  
  performance_optimization.py: |
    import mgp
    from datetime import datetime, timedelta
    
    @mgp.read_proc
    def get_hot_reasoning_paths(context: mgp.ProcCtx, 
                               time_window_minutes: int = 60) -> mgp.Record(paths=mgp.List[mgp.Map]):
        """Identify frequently used reasoning paths for caching"""
        query = """
        MATCH path = (s:Session)-[:HAS_STEP*2..10]->(end:ReasoningStep)
        WHERE datetime(s.last_update) > datetime() - duration({minutes: $time_window_minutes})
        WITH nodes(path) as path_nodes, count(*) as frequency
        WHERE frequency > 5
        RETURN {
            path: [n IN path_nodes | {id: n.step_id, type: n.step_type}],
            frequency: frequency,
            avg_confidence: avg([n IN path_nodes WHERE n:ReasoningStep | n.confidence])
        } as hot_path
        ORDER BY frequency DESC
        LIMIT 20
        """
        
        result = context.graph.execute(query, {"time_window_minutes": time_window_minutes})
        paths = [record["hot_path"] for record in result]
        return mgp.Record(paths=paths)
    
    @mgp.write_proc
    def cleanup_old_reasoning_data(context: mgp.ProcCtx, 
                                  retention_hours: int = 168) -> mgp.Record(deleted_count=int):
        """Clean up old reasoning data to maintain performance"""
        query = """
        MATCH (step:ReasoningStep)
        WHERE datetime(step.timestamp) < datetime() - duration({hours: $retention_hours})
        WITH step
        LIMIT 1000
        DETACH DELETE step
        RETURN count(step) as deleted_count
        """
        
        result = context.graph.execute(query, {"retention_hours": retention_hours})
        deleted_count = next(result)["deleted_count"]
        return mgp.Record(deleted_count=deleted_count)
    
    @mgp.read_proc
    def get_agent_performance_metrics(context: mgp.ProcCtx, 
                                    agent_id: str) -> mgp.Record(metrics=mgp.Map):
        """Get performance metrics for a specific agent"""
        query = """
        MATCH (a:Agent {agent_id: $agent_id})-[:REASONING_SESSION]->(s:Session)
              -[:HAS_STEP]->(step:ReasoningStep)
        WHERE datetime(step.timestamp) > datetime() - duration({hours: 24})
        WITH a, s, step
        RETURN {
            agent_id: a.agent_id,
            total_steps: count(step),
            avg_confidence: avg(step.confidence),
            avg_processing_time: avg(step.processing_time_ms),
            unique_sessions: count(DISTINCT s),
            step_types: collect(DISTINCT step.step_type)
        } as metrics
        """
        
        result = context.graph.execute(query, {"agent_id": agent_id})
        metrics = next(result, {}).get("metrics", {})
        return mgp.Record(metrics=metrics)