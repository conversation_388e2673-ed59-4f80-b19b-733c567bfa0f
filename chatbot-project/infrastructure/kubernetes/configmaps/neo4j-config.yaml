apiVersion: v1
kind: ConfigMap
metadata:
  name: neo4j-init-config
  namespace: chatbot-dev
data:
  01-schema-setup.cypher: |
    // Create constraints and indexes for organizational structure
    CREATE CONSTRAINT org_name_unique IF NOT EXISTS FOR (o:Organization) REQUIRE o.name IS UNIQUE;
    CREATE CONSTRAINT dept_name_org_unique IF NOT EXISTS FOR (d:Department) REQUIRE (d.name, d.organization_id) IS UNIQUE;
    CREATE CONSTRAINT user_email_unique IF NOT EXISTS FOR (u:User) REQUIRE u.email IS UNIQUE;
    CREATE CONSTRAINT user_employee_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.employee_id IS UNIQUE;
    CREATE CONSTRAINT agent_id_unique IF NOT EXISTS FOR (a:Agent) REQUIRE a.agent_id IS UNIQUE;
    CREATE CONSTRAINT knowledge_source_id_unique IF NOT EXISTS FOR (k:KnowledgeSource) REQUIRE k.source_id IS UNIQUE;
    
    // Create indexes for performance
    CREATE INDEX user_organization_idx IF NOT EXISTS FOR (u:User) ON (u.organization);
    CREATE INDEX user_department_idx IF NOT EXISTS FOR (u:User) ON (u.department);
    CREATE INDEX knowledge_type_idx IF NOT EXISTS FOR (k:KnowledgeSource) ON (k.type);
    CREATE INDEX knowledge_org_idx IF NOT EXISTS FOR (k:KnowledgeSource) ON (k.organization_id);
    CREATE INDEX agent_type_idx IF NOT EXISTS FOR (a:Agent) ON (a.agent_type);
    CREATE INDEX agent_status_idx IF NOT EXISTS FOR (a:Agent) ON (a.status);
    
  02-initial-data.cypher: |
    // Create Organizations
    MERGE (nuvo:Organization {name: 'NuvoAi', organization_id: 1})
    SET nuvo.description = 'AI and Technology Company',
        nuvo.domain = 'nuvoai.com',
        nuvo.created_at = datetime(),
        nuvo.is_active = true;
    
    MERGE (meril:Organization {name: 'Meril', organization_id: 2})
    SET meril.description = 'Healthcare and Medical Devices Company',
        meril.domain = 'meril.com',
        meril.created_at = datetime(),
        meril.is_active = true;
    
    // Create Departments for NuvoAi
    MERGE (ai_dept:Department {name: 'AI Department', department_id: 1, organization_id: 1})
    SET ai_dept.description = 'Artificial Intelligence Research and Development',
        ai_dept.created_at = datetime(),
        ai_dept.is_active = true;
    
    MERGE (product_dept:Department {name: 'Product Department', department_id: 2, organization_id: 1})
    SET product_dept.description = 'Product Management and Development',
        product_dept.created_at = datetime(),
        product_dept.is_active = true;
    
    MERGE (hr_nuvo:Department {name: 'HR Department', department_id: 3, organization_id: 1})
    SET hr_nuvo.description = 'Human Resources',
        hr_nuvo.created_at = datetime(),
        hr_nuvo.is_active = true;
    
    // Create Departments for Meril
    MERGE (tech_dept:Department {name: 'Technical Department', department_id: 4, organization_id: 2})
    SET tech_dept.description = 'Technical Operations and Engineering',
        tech_dept.created_at = datetime(),
        tech_dept.is_active = true;
    
    MERGE (finance_dept:Department {name: 'Finance Department', department_id: 5, organization_id: 2})
    SET finance_dept.description = 'Financial Operations',
        finance_dept.created_at = datetime(),
        finance_dept.is_active = true;
    
    MERGE (hr_meril:Department {name: 'HR Department', department_id: 6, organization_id: 2})
    SET hr_meril.description = 'Human Resources',
        hr_meril.created_at = datetime(),
        hr_meril.is_active = true;
    
    // Create relationships
    MERGE (ai_dept)-[:BELONGS_TO]->(nuvo);
    MERGE (product_dept)-[:BELONGS_TO]->(nuvo);
    MERGE (hr_nuvo)-[:BELONGS_TO]->(nuvo);
    MERGE (tech_dept)-[:BELONGS_TO]->(meril);
    MERGE (finance_dept)-[:BELONGS_TO]->(meril);
    MERGE (hr_meril)-[:BELONGS_TO]->(meril);
    
  03-agent-partitions.cypher: |
    // Create Agent Types and Partitions
    MERGE (coord_partition:AgentPartition {name: 'coordinator', partition_id: 'coord_001'})
    SET coord_partition.description = 'Coordinator Agent Partition',
        coord_partition.max_agents = 5,
        coord_partition.created_at = datetime();
    
    MERGE (org_partition:AgentPartition {name: 'organization', partition_id: 'org_001'})
    SET org_partition.description = 'Organization Agent Partition',
        org_partition.max_agents = 10,
        org_partition.created_at = datetime();
    
    MERGE (dept_partition:AgentPartition {name: 'department', partition_id: 'dept_001'})
    SET dept_partition.description = 'Department Agent Partition',
        dept_partition.max_agents = 20,
        dept_partition.created_at = datetime();
    
    MERGE (reasoning_partition:AgentPartition {name: 'reasoning', partition_id: 'reason_001'})
    SET reasoning_partition.description = 'Reasoning Agent Partition',
        reasoning_partition.max_agents = 15,
        reasoning_partition.created_at = datetime();
    
    MERGE (tool_partition:AgentPartition {name: 'tool', partition_id: 'tool_001'})
    SET tool_partition.description = 'Tool Agent Partition',
        tool_partition.max_agents = 25,
        tool_partition.created_at = datetime();
    
    MERGE (critic_partition:AgentPartition {name: 'critic', partition_id: 'critic_001'})
    SET critic_partition.description = 'Critic Agent Partition',
        critic_partition.max_agents = 10,
        critic_partition.created_at = datetime();
    
    // Create Knowledge Domains
    MERGE (hr_domain:KnowledgeDomain {name: 'HR_POLICIES', domain_id: 'hr_001'})
    SET hr_domain.description = 'Human Resources Policies and Procedures',
        hr_domain.access_level = 'organization',
        hr_domain.created_at = datetime();
    
    MERGE (tech_domain:KnowledgeDomain {name: 'TECHNICAL_DOCS', domain_id: 'tech_001'})
    SET tech_domain.description = 'Technical Documentation and Procedures',
        tech_domain.access_level = 'department',
        tech_domain.created_at = datetime();
    
    MERGE (finance_domain:KnowledgeDomain {name: 'FINANCE_POLICIES', domain_id: 'fin_001'})
    SET finance_domain.description = 'Financial Policies and Procedures',
        finance_domain.access_level = 'department',
        finance_domain.created_at = datetime();
    
    // Link domains to organizations
    MERGE (hr_domain)-[:ACCESSIBLE_BY]->(nuvo);
    MERGE (hr_domain)-[:ACCESSIBLE_BY]->(meril);
    MERGE (tech_domain)-[:ACCESSIBLE_BY]->(tech_dept);
    MERGE (finance_domain)-[:ACCESSIBLE_BY]->(finance_dept);
    
  04-knowledge-graph.cypher: |
    // Create Knowledge Source Templates
    MERGE (policy_template:KnowledgeTemplate {name: 'POLICY_DOCUMENT', template_id: 'policy_001'})
    SET policy_template.structure = {
        sections: ['overview', 'procedures', 'compliance', 'contacts'],
        required_fields: ['title', 'version', 'effective_date', 'owner'],
        metadata_schema: {
            classification: 'string',
            department: 'string',
            review_cycle: 'number'
        }
    };
    
    MERGE (procedure_template:KnowledgeTemplate {name: 'PROCEDURE_DOCUMENT', template_id: 'proc_001'})
    SET procedure_template.structure = {
        sections: ['purpose', 'scope', 'steps', 'references'],
        required_fields: ['title', 'version', 'owner', 'approval_date'],
        metadata_schema: {
            complexity: 'string',
            frequency: 'string',
            tools_required: 'array'
        }
    };
    
    // Create Relationship Types for Knowledge Graph
    MERGE (depends_rel:RelationshipType {name: 'DEPENDS_ON', type_id: 'dep_001'})
    SET depends_rel.description = 'Document dependency relationship',
        depends_rel.properties = ['dependency_type', 'strength', 'created_at'];
    
    MERGE (references_rel:RelationshipType {name: 'REFERENCES', type_id: 'ref_001'})
    SET references_rel.description = 'Document reference relationship',
        references_rel.properties = ['reference_type', 'page_number', 'section'];
    
    MERGE (supersedes_rel:RelationshipType {name: 'SUPERSEDES', type_id: 'sup_001'})
    SET supersedes_rel.description = 'Document version relationship',
        supersedes_rel.properties = ['supersede_date', 'reason', 'migration_notes'];