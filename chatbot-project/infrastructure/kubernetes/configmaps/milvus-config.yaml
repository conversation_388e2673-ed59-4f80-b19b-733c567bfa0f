apiVersion: v1
kind: ConfigMap
metadata:
  name: milvus-config
  namespace: chatbot-dev
data:
  milvus.yaml: |
    # Milvus Configuration
    etcd:
      endpoints:
        - etcd-service:2379
      rootPath: milvus
      metaSubPath: meta
      kvSubPath: kv
    
    minio:
      address: minio-service
      port: 9000
      accessKeyID: milvus
      secretAccessKey: milvus123
      useSSL: false
      bucketName: milvus-bucket
      rootPath: files
    
    common:
      defaultPartitionName: _default
      defaultIndexName: _default_idx
      entityExpiration: -1
      indexSliceSize: 16
    
    log:
      level: info
      file:
        rootPath: ""
        maxSize: 300
        maxAge: 10
        maxBackups: 20
      format: text
    
    grpc:
      serverMaxRecvSize: 268435456
      serverMaxSendSize: 268435456
      clientMaxRecvSize: 268435456
      clientMaxSendSize: 268435456
    
    queryCoord:
      autoHandoff: true
      autoBalance: true
      overloadedMemoryThresholdPercentage: 90
      balanceIntervalSeconds: 60
      memoryUsageMaxDifferencePercentage: 30
    
    queryNode:
      cacheSize: 32
      loadMemoryUsageFactor: 1
      enableDisk: true
      diskCapacityLimit: 50
      maxDiskUsagePercentage: 95
    
    indexCoord:
      bindIndexNodeMode:
        enable: false
        address: localhost
        withCred: false
        nodeID: 0
    
    indexNode:
      enableDisk: true
      diskCapacityLimit: 50
      maxDiskUsagePercentage: 95
    
    dataCoord:
      enableCompaction: true
      enableGarbageCollection: true
      gcInterval: 3600
      gcMissingTolerance: 86400
      enableActiveStandby: false
    
    dataNode:
      dataSync:
        flowGraph:
          maxQueueLength: 1024
          maxParallelism: 1024
      flush:
        insertBufSize: 16777216
        deleteBufBytes: 67108864
        autoFlushInterval: 600
    
    # Vector search optimization
    knowhere:
      simd_type: auto
    
    # Sharding configuration
    rootCoord:
      dmlChannelNum: 16
      maxPartitionNum: 4096
      minSegmentSizeToEnableIndex: 1024
    
    # Replication settings
    common:
      retentionDuration: 432000
      indexSliceSize: 16
      DiskIndex:
        MaxDegree: 56
        SearchListSize: 100
        PQCodeBudgetGBRatio: 0.125
        BuildNumThreadsRatio: 1.0
        SearchCacheBudgetGBRatio: 0.10
        LoadNumThreadRatio: 8.0
        BeamWidthRatio: 4.0
    
    # Hybrid search configuration
    queryNode:
      enableDisk: true
      diskCapacityLimit: 50
      maxDiskUsagePercentage: 95
      cache:
        enabled: true
        cacheSize: 2147483648
    
    # Monitoring
    metrics:
      address: 0.0.0.0
      port: 9091