apiVersion: v1
kind: ConfigMap
metadata:
  name: stream-processor-config
  namespace: chatbot-dev
data:
  stream_processor.py: |
    import json
    import os
    import time
    from kafka import KafkaConsumer
    from neo4j import GraphDatabase
    from datetime import datetime
    import logging
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    class MemgraphStreamProcessor:
        def __init__(self):
            self.kafka_servers = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka-service:9092')
            self.memgraph_uri = os.getenv('MEMGRAPH_URI', 'bolt://memgraph-lb-service:7687')
            self.memgraph_user = os.getenv('MEMGRAPH_USER', 'memgraph')
            self.memgraph_password = os.getenv('MEMGRAPH_PASSWORD', 'chatbot_memgraph_pass')
            
            self.driver = GraphDatabase.driver(
                self.memgraph_uri,
                auth=(self.memgraph_user, self.memgraph_password)
            )
            
            self.consumer = KafkaConsumer(
                'agent-reasoning-events',
                'agent-communication-events',
                'knowledge-update-events',
                bootstrap_servers=[self.kafka_servers],
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                group_id='memgraph-stream-processor'
            )
        
        def process_events(self):
            logger.info("Starting event processing...")
            
            for message in self.consumer:
                try:
                    event_data = message.value
                    topic = message.topic
                    
                    if topic == 'agent-reasoning-events':
                        self.handle_reasoning_event(event_data)
                    elif topic == 'agent-communication-events':
                        self.handle_communication_event(event_data)
                    elif topic == 'knowledge-update-events':
                        self.handle_knowledge_event(event_data)
                        
                except Exception as e:
                    logger.error(f"Error processing event: {e}")
                    continue
        
        def handle_reasoning_event(self, event_data):
            """Handle agent reasoning step events"""
            with self.driver.session() as session:
                query = """
                MERGE (a:Agent {agent_id: $agent_id})
                SET a.last_activity = $timestamp,
                    a.status = 'active',
                    a.agent_type = $agent_type
                
                MERGE (s:ReasoningSession {
                    session_id: $session_id, 
                    agent_id: $agent_id
                })
                SET s.last_update = $timestamp
                
                CREATE (step:ReasoningStep {
                    step_id: $step_id,
                    step_type: $step_type,
                    content: $content,
                    confidence: $confidence,
                    timestamp: $timestamp,
                    processing_time_ms: $processing_time_ms,
                    input_tokens: $input_tokens,
                    output_tokens: $output_tokens
                })
                
                MERGE (a)-[:HAS_REASONING_SESSION]->(s)
                MERGE (s)-[:HAS_STEP]->(step)
                
                // Link to previous step
                OPTIONAL MATCH (s)-[:HAS_STEP]->(prev:ReasoningStep)
                WHERE prev.timestamp < $timestamp
                WITH step, prev
                ORDER BY prev.timestamp DESC
                LIMIT 1
                FOREACH (p IN CASE WHEN prev IS NOT NULL THEN [prev] ELSE [] END |
                    CREATE (p)-[:NEXT_STEP {
                        time_diff_ms: duration.between(
                            datetime(p.timestamp), 
                            datetime($timestamp)
                        ).milliseconds
                    }]->(step)
                )
                """
                
                session.run(query, event_data)
                logger.info(f"Processed reasoning event for agent {event_data.get('agent_id')}")
        
        def handle_communication_event(self, event_data):
            """Handle agent-to-agent communication events"""
            with self.driver.session() as session:
                query = """
                MATCH (sender:Agent {agent_id: $sender_id})
                MATCH (receiver:Agent {agent_id: $receiver_id})
                
                CREATE (comm:Communication {
                    communication_id: $communication_id,
                    message_type: $message_type,
                    content: $content,
                    timestamp: $timestamp,
                    success: $success,
                    response_time_ms: $response_time_ms,
                    message_size_bytes: $message_size_bytes
                })
                
                CREATE (sender)-[:SENT {timestamp: $timestamp}]->(comm)
                CREATE (comm)-[:RECEIVED_BY {timestamp: $timestamp}]->(receiver)
                
                // Update agent communication stats
                SET sender.total_messages_sent = COALESCE(sender.total_messages_sent, 0) + 1,
                    receiver.total_messages_received = COALESCE(receiver.total_messages_received, 0) + 1
                """
                
                session.run(query, event_data)
                logger.info(f"Processed communication event: {event_data.get('sender_id')} -> {event_data.get('receiver_id')}")
        
        def handle_knowledge_event(self, event_data):
            """Handle knowledge graph update events"""
            with self.driver.session() as session:
                query = """
                MERGE (k:Knowledge {knowledge_id: $knowledge_id})
                SET k.content = $content,
                    k.last_updated = $timestamp,
                    k.version = $version,
                    k.knowledge_type = $knowledge_type,
                    k.organization_id = $organization_id,
                    k.department_id = $department_id
                
                WITH k
                MATCH (a:Agent {agent_id: $agent_id})
                MERGE (a)-[access:ACCESSED_KNOWLEDGE]->(k)
                SET access.timestamp = $timestamp,
                    access.access_type = $access_type,
                    access.confidence_score = $confidence_score
                """
                
                session.run(query, event_data)
                logger.info(f"Processed knowledge event for {event_data.get('knowledge_id')}")
        
        def close(self):
            self.consumer.close()
            self.driver.close()
    
    if __name__ == "__main__":
        processor = MemgraphStreamProcessor()
        try:
            processor.process_events()
        except KeyboardInterrupt:
            logger.info("Shutting down stream processor...")
        finally:
            processor.close()
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-connect-config
  namespace: chatbot-dev
data:
  memgraph-sink-connector.json: |
    {
      "name": "memgraph-sink-connector",
      "config": {
        "connector.class": "com.memgraph.kafka.MemgraphSinkConnector",
        "topics": "agent-reasoning-events,agent-communication-events,knowledge-update-events",
        "memgraph.uri": "bolt://memgraph-lb-service:7687",
        "memgraph.username": "memgraph",
        "memgraph.password": "chatbot_memgraph_pass",
        "memgraph.database": "memgraph",
        "batch.size": "100",
        "batch.timeout.ms": "1000",
        "max.retries": "3",
        "retry.backoff.ms": "1000"
      }
    }