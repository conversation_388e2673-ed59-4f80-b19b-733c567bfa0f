# Vector Database & Monitoring Infrastructure

This directory contains Milvus vector database deployment with comprehensive monitoring using Prometheus and Grafana.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Mi<PERSON><PERSON><PERSON>      │    │     <PERSON><PERSON><PERSON><PERSON>      │    │     Milvu<PERSON>      │
│   Instance 1    │    │   Instance 2    │    │   Instance 3    │
│ (Vector Store)  │    │ (Vector Store)  │    │ (Vector Store)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │      etcd       │
                    │   Cluster       │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │     MinIO       │
                    │ Object Storage  │
                    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Prometheus    │───▶│    Grafana      │    │  AlertManager   │
│   (Metrics)     │    │  (Dashboard)    │    │   (Alerts)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components

### Milvus Vector Database
- **StatefulSet**: `milvus` (3 replicas)
- **Service**: `milvus-service`
- **Purpose**: Vector similarity search and embeddings storage
- **Collections**: Document embeddings, Query embeddings

### Supporting Infrastructure
- **etcd**: Metadata storage and coordination (3 replicas)
- **MinIO**: Object storage for vector data (1 replica)
- **Persistent Storage**: 100Gi for Milvu<PERSON>, 200Gi for MinIO

### Monitoring Stack
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization dashboards
- **Alert Rules**: Database health and performance alerts

## Deployment

### Quick Setup
```bash
# Deploy vector database and monitoring
./scripts/vector-db-setup.sh
```

### Manual Deployment
```bash
# 1. Create secrets and config
kubectl apply -f infrastructure/kubernetes/secrets/milvus-secrets.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/milvus-config.yaml

# 2. Deploy supporting infrastructure
kubectl apply -f infrastructure/kubernetes/deployments/milvus.yaml
kubectl apply -f infrastructure/kubernetes/services/milvus-services.yaml

# 3. Wait for components to be ready
kubectl wait --for=condition=ready pod -l app=etcd -n chatbot-dev --timeout=300s
kubectl wait --for=condition=ready pod -l app=minio -n chatbot-dev --timeout=180s
kubectl wait --for=condition=ready pod -l app=milvus -n chatbot-dev --timeout=300s

# 4. Deploy monitoring
kubectl apply -f infrastructure/kubernetes/monitoring/prometheus.yaml
kubectl apply -f infrastructure/kubernetes/monitoring/grafana.yaml

# 5. Setup backup
kubectl apply -f infrastructure/kubernetes/jobs/milvus-backup.yaml
```

## Connection Information

### Milvus Vector Database
```
Host: milvus-service
Port: 19530 (gRPC)
Metrics: 9091
```

### Monitoring Services
```
Prometheus: prometheus-service:9090
Grafana: grafana-service:3000
Username: admin
Password: admin123
```

### Object Storage (MinIO)
```
Host: minio-service
API Port: 9000
Console: 9001
Access Key: milvus
Secret Key: milvus123
```

## Vector Collections

### Document Embeddings Collection
```python
# Schema
{
    'id': INT64 (Primary, Auto-generated),
    'document_id': VARCHAR(255),
    'organization_id': INT64,
    'department_id': INT64,
    'embedding': FLOAT_VECTOR(384),  # Sentence transformer embeddings
    'content': VARCHAR(65535),
    'metadata': JSON
}

# Index: IVF_FLAT for similarity search
# Metric: L2 (Euclidean distance)
```

### Query Embeddings Collection
```python
# Schema
{
    'id': INT64 (Primary, Auto-generated),
    'query_id': VARCHAR(255),
    'user_id': INT64,
    'embedding': FLOAT_VECTOR(384),
    'query_text': VARCHAR(65535),
    'timestamp': VARCHAR(50)
}

# Index: IVF_FLAT for similarity search
# Metric: L2 (Euclidean distance)
```

## Usage Examples

### Python Client Usage
```python
from pymilvus import connections, Collection

# Connect to Milvus
connections.connect("default", host="milvus-service", port="19530")

# Get collection
collection = Collection("document_embeddings")

# Insert vectors
entities = [
    [1, 2, 3],  # organization_ids
    [1, 1, 2],  # department_ids
    [[0.1, 0.2, ...], [0.3, 0.4, ...], [0.5, 0.6, ...]],  # embeddings
    ["doc1", "doc2", "doc3"],  # document_ids
    ["content1", "content2", "content3"],  # content
    [{"type": "policy"}, {"type": "manual"}, {"type": "faq"}]  # metadata
]

collection.insert(entities)

# Search similar vectors
search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
results = collection.search(
    data=[[0.1, 0.2, ...]],  # query vector
    anns_field="embedding",
    param=search_params,
    limit=10,
    expr="organization_id == 1"  # filter by organization
)
```

### REST API Usage
```bash
# Health check
curl http://milvus-service:9091/healthz

# Get collection info
curl -X POST http://milvus-service:19530/v1/collection/info \
  -H "Content-Type: application/json" \
  -d '{"collection_name": "document_embeddings"}'
```

## Monitoring and Metrics

### Prometheus Metrics
- **milvus_proxy_search_vectors_count**: Vector search operations
- **milvus_proxy_insert_vectors_count**: Vector insert operations
- **milvus_proxy_search_latency**: Search latency metrics
- **milvus_querynode_memory_usage**: Memory usage per query node
- **milvus_indexnode_index_task_count**: Indexing task metrics

### Grafana Dashboards
- **Chatbot System Overview**: High-level system health
- **Vector Database Performance**: Milvus-specific metrics
- **Resource Utilization**: CPU, memory, and disk usage
- **Alert Status**: Current alerts and their status

### Alert Rules
- **MilvusDown**: Milvus service unavailable
- **HighVectorSearchLatency**: Search operations taking too long
- **HighMemoryUsage**: Memory usage above 90%
- **HighDiskUsage**: Disk usage above 85%
- **IndexingFailures**: Vector indexing failures

## Performance Optimization

### Milvus Configuration
- **Memory Limit**: 4GB per instance
- **Cache Size**: 32GB for query nodes
- **Disk Usage**: Up to 95% disk utilization
- **Compaction**: Enabled for storage optimization
- **Garbage Collection**: Hourly cleanup

### Index Optimization
- **IVF_FLAT**: Good balance of speed and accuracy
- **nlist**: 1024 (adjustable based on data size)
- **nprobe**: 10 (adjustable based on accuracy needs)
- **Metric Type**: L2 (Euclidean distance)

### Search Optimization
```python
# Optimized search parameters
search_params = {
    "metric_type": "L2",
    "params": {
        "nprobe": 10,  # Higher = more accurate, slower
        "radius": 0.1,  # Maximum distance threshold
        "range_filter": 0.05  # Minimum distance threshold
    }
}

# Use filters to reduce search space
expr = "organization_id == 1 and department_id in [1, 2, 3]"
```

## Backup and Recovery

### Automated Backups
- **Schedule**: Daily at 5:00 AM UTC
- **Components**: Collection metadata + MinIO object data
- **Format**: Compressed tar.gz files
- **Retention**: 7 days automatic cleanup

### Manual Backup
```bash
# Create immediate backup
kubectl create job --from=cronjob/milvus-backup milvus-backup-manual -n chatbot-dev

# Export collection data
kubectl exec milvus-0 -n chatbot-dev -- python3 -c "
from pymilvus import connections, Collection, utility
connections.connect('default', host='localhost', port='19530')
collections = utility.list_collections()
for name in collections:
    collection = Collection(name)
    print(f'{name}: {collection.num_entities} entities')
"
```

### Recovery Process
```bash
# Restore from backup
kubectl exec milvus-0 -n chatbot-dev -- python3 -c "
from pymilvus import connections, Collection, utility
import json

# Connect and recreate collections from backup metadata
connections.connect('default', host='localhost', port='19530')

# Load backup info and recreate collections
with open('/backup/backup_info.json', 'r') as f:
    backup_info = json.load(f)

for collection_info in backup_info['collections']:
    # Recreate collection with same schema
    print(f'Restoring collection: {collection_info[\"name\"]}')
"
```

## Troubleshooting

### Check Cluster Status
```bash
kubectl get pods -l app=milvus -n chatbot-dev
kubectl get pods -l app=etcd -n chatbot-dev
kubectl get pods -l app=minio -n chatbot-dev
```

### View Logs
```bash
kubectl logs milvus-0 -n chatbot-dev
kubectl logs etcd-0 -n chatbot-dev
kubectl logs minio-0 -n chatbot-dev
```

### Test Connections
```bash
# Test Milvus connection
kubectl exec -it milvus-0 -n chatbot-dev -- python3 -c "
from pymilvus import connections
connections.connect('default', host='localhost', port='19530')
print('Milvus connection successful')
"

# Test MinIO connection
kubectl exec -it minio-0 -n chatbot-dev -- mc admin info local
```

### Performance Monitoring
```bash
# Port forward to access monitoring
kubectl port-forward service/prometheus-service 9090:9090 -n chatbot-dev
kubectl port-forward service/grafana-service 3000:3000 -n chatbot-dev

# Access dashboards
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000 (admin/admin123)
```

### Common Issues

#### Milvus Won't Start
- Check etcd cluster health
- Verify MinIO connectivity
- Check resource limits and availability

#### Slow Vector Search
- Increase nprobe parameter
- Check index type and parameters
- Monitor memory usage and disk I/O

#### High Memory Usage
- Reduce cache size in configuration
- Enable disk-based operations
- Scale horizontally with more replicas

## Security Considerations

- **Network**: All services use ClusterIP (internal only)
- **Authentication**: MinIO uses access keys
- **Encryption**: Enable TLS for production deployments
- **Access Control**: Use Kubernetes RBAC
- **Secrets**: Store credentials in Kubernetes secrets