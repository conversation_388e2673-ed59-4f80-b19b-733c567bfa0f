apiVersion: apps/v1
kind: Deployment
metadata:
  name: enhanced-message-broker
  namespace: chatbot-dev
  labels:
    app: enhanced-message-broker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: enhanced-message-broker
  template:
    metadata:
      labels:
        app: enhanced-message-broker
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: message-broker
        image: python:3.11-slim
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: debug
        command:
        - /bin/bash
        - -c
        - |
          pip install fastapi uvicorn redis kafka-python prometheus-client priority-queue
          python /app/message_broker.py
        env:
        - name: REDIS_CLUSTER_NODES
          value: "redis-cluster-lb-service:6379"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-lb-service:9092"
        - name: BROKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        volumeMounts:
        - name: broker-code
          mountPath: /app
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: broker-code
        configMap:
          name: message-broker-code