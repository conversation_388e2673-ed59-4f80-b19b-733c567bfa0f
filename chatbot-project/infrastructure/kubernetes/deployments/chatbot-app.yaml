apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-app
  namespace: chatbot-dev
  labels:
    app: chatbot-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: chatbot-app
  template:
    metadata:
      labels:
        app: chatbot-app
    spec:
      containers:
      - name: chatbot-app
        image: chatbot-app:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: chatbot-config
        - secretRef:
            name: chatbot-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"