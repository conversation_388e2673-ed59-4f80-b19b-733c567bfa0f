apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: chabot
spec:
  replicas: 2
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: chabot/auth-service:latest
        ports:
        - containerPort: 8001
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: chabot-secrets
              key: database-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: chabot-secrets
              key: secret-key
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: chabot
spec:
  selector:
    app: auth-service
  ports:
  - port: 8001
    targetPort: 8001
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-service
  namespace: chabot
spec:
  replicas: 3
  selector:
    matchLabels:
      app: chat-service
  template:
    metadata:
      labels:
        app: chat-service
    spec:
      containers:
      - name: chat-service
        image: chabot/chat-service:latest
        ports:
        - containerPort: 8002
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: chabot-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: chabot-secrets
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "400m"
---
apiVersion: v1
kind: Service
metadata:
  name: chat-service
  namespace: chabot
spec:
  selector:
    app: chat-service
  ports:
  - port: 8002
    targetPort: 8002
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-service
  namespace: chabot
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-service
  template:
    metadata:
      labels:
        app: agent-service
    spec:
      containers:
      - name: agent-service
        image: chabot/agent-service:latest
        ports:
        - containerPort: 8003
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: chabot-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: chabot-secrets
              key: redis-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "300m"
          limits:
            memory: "1Gi"
            cpu: "600m"
---
apiVersion: v1
kind: Service
metadata:
  name: agent-service
  namespace: chabot
spec:
  selector:
    app: agent-service
  ports:
  - port: 8003
    targetPort: 8003
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service
  namespace: chabot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin-service
  template:
    metadata:
      labels:
        app: admin-service
    spec:
      containers:
      - name: admin-service
        image: chabot/admin-service:latest
        ports:
        - containerPort: 8004
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: chabot-secrets
              key: database-url
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: admin-service
  namespace: chabot
spec:
  selector:
    app: admin-service
  ports:
  - port: 8004
    targetPort: 8004