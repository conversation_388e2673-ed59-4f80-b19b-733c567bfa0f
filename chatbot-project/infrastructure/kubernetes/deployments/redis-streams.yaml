apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-streams-processor
  namespace: chatbot-dev
  labels:
    app: redis-streams-processor
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis-streams-processor
  template:
    metadata:
      labels:
        app: redis-streams-processor
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: streams-processor
        image: python:3.11-slim
        ports:
        - containerPort: 8080
          name: metrics
        command:
        - /bin/bash
        - -c
        - |
          pip install redis kafka-python prometheus-client
          python /app/streams_processor.py
        env:
        - name: REDIS_CLUSTER_NODES
          value: "redis-cluster-lb-service:6379"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-lb-service:9092"
        - name: CONSUMER_GROUP
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: PROCESSOR_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        volumeMounts:
        - name: streams-code
          mountPath: /app
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "400m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: streams-code
        configMap:
          name: redis-streams-code