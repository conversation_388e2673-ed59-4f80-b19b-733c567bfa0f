apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: neo4j-replica
  namespace: chatbot-dev
  labels:
    app: neo4j-replica
    role: replica
spec:
  serviceName: neo4j-replica-service
  replicas: 2
  selector:
    matchLabels:
      app: neo4j-replica
      role: replica
  template:
    metadata:
      labels:
        app: neo4j-replica
        role: replica
    spec:
      containers:
      - name: neo4j
        image: neo4j:5.15-community
        ports:
        - containerPort: 7474
          name: http
        - containerPort: 7687
          name: bolt
        - containerPort: 7473
          name: https
        env:
        - name: NEO4J_AUTH
          valueFrom:
            secretKeyRef:
              name: neo4j-secrets
              key: NEO4J_AUTH
        - name: NEO4J_PLUGINS
          value: '["apoc", "graph-data-science"]'
        - name: NEO4J_dbms_mode
          value: "READ_REPLICA"
        - name: NEO4J_causal__clustering_initial__discovery__members
          value: "neo4j-core-0.neo4j-core-service:5000,neo4j-core-1.neo4j-core-service:5000,neo4j-core-2.neo4j-core-service:5000"
        - name: NEO4J_dbms_connector_bolt_advertised__address
          value: "$(hostname -f):7687"
        - name: NEO4J_dbms_connector_http_advertised__address
          value: "$(hostname -f):7474"
        - name: NEO4J_dbms_memory_heap_initial__size
          value: "256m"
        - name: NEO4J_dbms_memory_heap_max__size
          value: "512m"
        - name: NEO4J_dbms_memory_pagecache_size
          value: "256m"
        volumeMounts:
        - name: neo4j-data
          mountPath: /data
        - name: neo4j-logs
          mountPath: /logs
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 7474
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 7474
          initialDelaySeconds: 30
          periodSeconds: 10
  volumeClaimTemplates:
  - metadata:
      name: neo4j-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
  - metadata:
      name: neo4j-logs
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 2Gi