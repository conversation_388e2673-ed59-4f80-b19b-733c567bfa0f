apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-gateway
  namespace: chatbot-dev
  labels:
    app: agent-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent-gateway
  template:
    metadata:
      labels:
        app: agent-gateway
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: agent-gateway
        image: python:3.11-slim
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: grpc
        command:
        - /bin/bash
        - -c
        - |
          pip install fastapi uvicorn kafka-python redis prometheus-client grpcio grpcio-tools
          python /app/agent_gateway.py
        env:
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-lb-service:9092"
        - name: REDIS_CLUSTER_NODES
          value: "redis-cluster-lb-service:6379"
        - name: AGENT_REGISTRY_URL
          value: "http://agent-registry-service:8082"
        - name: LOG_LEVEL
          value: "INFO"
        volumeMounts:
        - name: gateway-code
          mountPath: /app
        - name: communication-config
          mountPath: /config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: gateway-code
        configMap:
          name: agent-gateway-code
      - name: communication-config
        configMap:
          name: agent-communication-config
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-registry
  namespace: chatbot-dev
  labels:
    app: agent-registry
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-registry
  template:
    metadata:
      labels:
        app: agent-registry
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8082"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: agent-registry
        image: python:3.11-slim
        ports:
        - containerPort: 8082
          name: http
        command:
        - /bin/bash
        - -c
        - |
          pip install fastapi uvicorn redis psycopg2-binary prometheus-client
          python /app/agent_registry.py
        env:
        - name: REDIS_CLUSTER_NODES
          value: "redis-cluster-lb-service:6379"
        - name: POSTGRES_HOST
          value: "postgres-primary-service"
        - name: POSTGRES_PORT
          value: "5432"
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: chatbot-config
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: chatbot-secrets
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chatbot-secrets
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: registry-code
          mountPath: /app
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "400m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8082
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8082
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: registry-code
        configMap:
          name: agent-registry-code