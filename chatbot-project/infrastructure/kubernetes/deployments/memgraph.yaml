apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: memgraph
  namespace: chatbot-dev
  labels:
    app: memgraph
spec:
  serviceName: memgraph-service
  replicas: 3
  selector:
    matchLabels:
      app: memgraph
  template:
    metadata:
      labels:
        app: memgraph
    spec:
      containers:
      - name: memgraph
        image: memgraph/memgraph:2.11
        ports:
        - containerPort: 7687
          name: bolt
        - containerPort: 7444
          name: monitoring
        - containerPort: 10000
          name: replication
        env:
        - name: MEMGRAPH_USER
          valueFrom:
            secretKeyRef:
              name: memgraph-secrets
              key: MEMGRAPH_USER
        - name: MEMGRAPH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: memgraph-secrets
              key: MEMGRAPH_PASSWORD
        - name: MEMGRAPH_ENTERPRISE_LICENSE
          value: ""
        - name: MEMGRAPH_LOG_LEVEL
          value: "INFO"
        args:
        - --bolt-port=7687
        - --monitoring-port=7444
        - --replication-port=10000
        - --memory-limit=2048
        - --query-execution-timeout-sec=600
        - --storage-parallel-schema-recovery=true
        - --storage-snapshot-interval-sec=300
        - --storage-wal-enabled=true
        - --storage-snapshot-retention-count=3
        - --log-level=INFO
        - --also-log-to-stderr=true
        - --telemetry-enabled=false
        volumeMounts:
        - name: memgraph-data
          mountPath: /var/lib/memgraph
        - name: memgraph-log
          mountPath: /var/log/memgraph
        - name: memgraph-init
          mountPath: /usr/lib/memgraph/query_modules
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "3Gi"
            cpu: "2000m"
        livenessProbe:
          tcpSocket:
            port: 7687
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 7687
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: memgraph-init
        configMap:
          name: memgraph-init-config
  volumeClaimTemplates:
  - metadata:
      name: memgraph-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi
  - metadata:
      name: memgraph-log
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi