apiVersion: apps/v1
kind: Deployment
metadata:
  name: grpc-gateway
  namespace: chatbot-dev
  labels:
    app: grpc-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: grpc-gateway
  template:
    metadata:
      labels:
        app: grpc-gateway
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: grpc-gateway
        image: python:3.11-slim
        ports:
        - containerPort: 50051
          name: grpc
        - containerPort: 9090
          name: metrics
        command:
        - /bin/bash
        - -c
        - |
          pip install grpcio grpcio-tools redis kafka-python prometheus-client
          python -m grpc_tools.protoc -I/proto --python_out=/app --grpc_python_out=/app /proto/*.proto
          python /app/grpc_gateway.py
        env:
        - name: REDIS_CLUSTER_NODES
          value: "redis-cluster-lb-service:6379"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-lb-service:9092"
        - name: GRPC_PORT
          value: "50051"
        - name: METRICS_PORT
          value: "9090"
        volumeMounts:
        - name: grpc-code
          mountPath: /app
        - name: proto-files
          mountPath: /proto
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "grpc_health_probe -addr=localhost:50051"
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "grpc_health_probe -addr=localhost:50051"
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: grpc-code
        configMap:
          name: grpc-gateway-code
      - name: proto-files
        configMap:
          name: grpc-proto-config