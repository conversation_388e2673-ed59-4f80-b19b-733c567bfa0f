apiVersion: apps/v1
kind: Deployment
metadata:
  name: memgraph-streaming
  namespace: chatbot-dev
  labels:
    app: memgraph-streaming
spec:
  replicas: 2
  selector:
    matchLabels:
      app: memgraph-streaming
  template:
    metadata:
      labels:
        app: memgraph-streaming
    spec:
      containers:
      - name: kafka-connect
        image: confluentinc/cp-kafka-connect:7.4.0
        ports:
        - containerPort: 8083
        env:
        - name: CONNECT_BOOTSTRAP_SERVERS
          value: "kafka-service:9092"
        - name: CONNECT_REST_ADVERTISED_HOST_NAME
          value: "memgraph-streaming"
        - name: CONNECT_GROUP_ID
          value: "memgraph-connect-group"
        - name: CONNECT_CONFIG_STORAGE_TOPIC
          value: "memgraph-connect-configs"
        - name: CONNECT_OFFSET_STORAGE_TOPIC
          value: "memgraph-connect-offsets"
        - name: CONNECT_STATUS_STORAGE_TOPIC
          value: "memgraph-connect-status"
        - name: CONNECT_KEY_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_VALUE_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_INTERNAL_KEY_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_INTERNAL_VALUE_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_REST_PORT
          value: "8083"
        - name: CONNECT_LOG4J_ROOT_LOGLEVEL
          value: "INFO"
        - name: CONNECT_PLUGIN_PATH
          value: "/usr/share/java,/usr/share/confluent-hub-components"
        volumeMounts:
        - name: connect-config
          mountPath: /etc/kafka-connect
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      - name: stream-processor
        image: python:3.11-slim
        command:
        - /bin/bash
        - -c
        - |
          pip install kafka-python neo4j-driver
          python /app/stream_processor.py
        env:
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-service:9092"
        - name: MEMGRAPH_URI
          value: "bolt://memgraph-lb-service:7687"
        - name: MEMGRAPH_USER
          valueFrom:
            secretKeyRef:
              name: memgraph-secrets
              key: MEMGRAPH_USER
        - name: MEMGRAPH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: memgraph-secrets
              key: MEMGRAPH_PASSWORD
        volumeMounts:
        - name: stream-processor-code
          mountPath: /app
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "400m"
      volumes:
      - name: connect-config
        configMap:
          name: kafka-connect-config
      - name: stream-processor-code
        configMap:
          name: stream-processor-config