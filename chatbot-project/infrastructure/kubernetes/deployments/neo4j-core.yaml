apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: neo4j-core
  namespace: chatbot-dev
  labels:
    app: neo4j-core
    role: core
spec:
  serviceName: neo4j-core-service
  replicas: 3
  selector:
    matchLabels:
      app: neo4j-core
      role: core
  template:
    metadata:
      labels:
        app: neo4j-core
        role: core
    spec:
      containers:
      - name: neo4j
        image: neo4j:5.15-community
        ports:
        - containerPort: 7474
          name: http
        - containerPort: 7687
          name: bolt
        - containerPort: 7473
          name: https
        - containerPort: 6362
          name: backup
        - containerPort: 5000
          name: discovery
        - containerPort: 6000
          name: raft
        - containerPort: 7000
          name: tx
        env:
        - name: NEO4J_AUTH
          valueFrom:
            secretKeyRef:
              name: neo4j-secrets
              key: NEO4J_AUTH
        - name: NEO4J_PLUGINS
          value: '["apoc", "graph-data-science"]'
        - name: NEO4J_dbms_mode
          value: "CORE"
        - name: NEO4J_causal__clustering_minimum__core__cluster__size__at__formation
          value: "3"
        - name: NEO4J_causal__clustering_minimum__core__cluster__size__at__runtime
          value: "3"
        - name: NEO4J_causal__clustering_initial__discovery__members
          value: "neo4j-core-0.neo4j-core-service:5000,neo4j-core-1.neo4j-core-service:5000,neo4j-core-2.neo4j-core-service:5000"
        - name: NEO4J_dbms_connector_bolt_advertised__address
          value: "$(hostname -f):7687"
        - name: NEO4J_dbms_connector_http_advertised__address
          value: "$(hostname -f):7474"
        - name: NEO4J_causal__clustering_discovery__advertised__address
          value: "$(hostname -f):5000"
        - name: NEO4J_causal__clustering_transaction__advertised__address
          value: "$(hostname -f):6000"
        - name: NEO4J_causal__clustering_raft__advertised__address
          value: "$(hostname -f):7000"
        - name: NEO4J_dbms_memory_heap_initial__size
          value: "512m"
        - name: NEO4J_dbms_memory_heap_max__size
          value: "1G"
        - name: NEO4J_dbms_memory_pagecache_size
          value: "512m"
        volumeMounts:
        - name: neo4j-data
          mountPath: /data
        - name: neo4j-logs
          mountPath: /logs
        - name: neo4j-init
          mountPath: /var/lib/neo4j/init
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /
            port: 7474
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 7474
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: neo4j-init
        configMap:
          name: neo4j-init-config
  volumeClaimTemplates:
  - metadata:
      name: neo4j-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
  - metadata:
      name: neo4j-logs
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 5Gi