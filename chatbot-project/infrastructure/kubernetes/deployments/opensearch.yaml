apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: opensearch
  namespace: chatbot-dev
  labels:
    app: opensearch
spec:
  serviceName: opensearch-service
  replicas: 3
  selector:
    matchLabels:
      app: opensearch
  template:
    metadata:
      labels:
        app: opensearch
    spec:
      initContainers:
      - name: init-sysctl
        image: busybox:1.35
        command:
        - sh
        - -c
        - |
          sysctl -w vm.max_map_count=262144
          echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
        securityContext:
          privileged: true
      containers:
      - name: opensearch
        image: opensearchproject/opensearch:2.11.1
        ports:
        - containerPort: 9200
          name: http
        - containerPort: 9300
          name: transport
        env:
        - name: cluster.name
          value: "chatbot-logs"
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: discovery.seed_hosts
          value: "opensearch-0.opensearch-service,opensearch-1.opensearch-service,opensearch-2.opensearch-service"
        - name: cluster.initial_cluster_manager_nodes
          value: "opensearch-0,opensearch-1,opensearch-2"
        - name: bootstrap.memory_lock
          value: "true"
        - name: OPENSEARCH_JAVA_OPTS
          value: "-Xms1g -Xmx1g"
        - name: DISABLE_SECURITY_PLUGIN
          value: "true"
        - name: DISABLE_INSTALL_DEMO_CONFIG
          value: "true"
        volumeMounts:
        - name: opensearch-data
          mountPath: /usr/share/opensearch/data
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "3Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /_cluster/health
            port: 9200
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /_cluster/health
            port: 9200
          initialDelaySeconds: 30
          periodSeconds: 10
      securityContext:
        fsGroup: 1000
  volumeClaimTemplates:
  - metadata:
      name: opensearch-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: opensearch-dashboards
  namespace: chatbot-dev
  labels:
    app: opensearch-dashboards
spec:
  replicas: 1
  selector:
    matchLabels:
      app: opensearch-dashboards
  template:
    metadata:
      labels:
        app: opensearch-dashboards
    spec:
      containers:
      - name: opensearch-dashboards
        image: opensearchproject/opensearch-dashboards:2.11.1
        ports:
        - containerPort: 5601
        env:
        - name: OPENSEARCH_HOSTS
          value: '["http://opensearch-service:9200"]'
        - name: DISABLE_SECURITY_DASHBOARDS_PLUGIN
          value: "true"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 30
          periodSeconds: 10