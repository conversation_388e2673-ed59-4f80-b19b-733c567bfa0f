apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres-replica
  namespace: chatbot-dev
  labels:
    app: postgres-replica
    role: replica
spec:
  serviceName: postgres-replica-service
  replicas: 2
  selector:
    matchLabels:
      app: postgres-replica
      role: replica
  template:
    metadata:
      labels:
        app: postgres-replica
        role: replica
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
        env:
        - name: PGUSER
          value: "replicator"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chatbot-secrets
              key: POSTGRES_REPLICATION_PASSWORD
        - name: POSTGRES_PRIMARY_HOST
          value: "postgres-primary-service"
        - name: POSTGRES_PRIMARY_PORT
          value: "5432"
        command:
        - /bin/bash
        - -c
        - |
          until pg_isready -h $POSTGRES_PRIMARY_HOST -p $POSTGRES_PRIMARY_PORT -U replicator; do
            echo "Waiting for primary to be ready..."
            sleep 2
          done
          
          pg_basebackup -h $POSTGRES_PRIMARY_HOST -D /var/lib/postgresql/data -U replicator -v -P -W
          echo "standby_mode = 'on'" >> /var/lib/postgresql/data/recovery.conf
          echo "primary_conninfo = 'host=$POSTGRES_PRIMARY_HOST port=$POSTGRES_PRIMARY_PORT user=replicator'" >> /var/lib/postgresql/data/recovery.conf
          
          postgres
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - replicator
          initialDelaySeconds: 60
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - replicator
          initialDelaySeconds: 30
          periodSeconds: 5
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi