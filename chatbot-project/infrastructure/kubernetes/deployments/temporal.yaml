apiVersion: apps/v1
kind: Deployment
metadata:
  name: temporal-server
  namespace: chatbot-dev
  labels:
    app: temporal-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: temporal-server
  template:
    metadata:
      labels:
        app: temporal-server
    spec:
      containers:
      - name: temporal
        image: temporalio/auto-setup:1.22.0
        ports:
        - containerPort: 7233
          name: frontend
        - containerPort: 7234
          name: history
        - containerPort: 7235
          name: matching
        - containerPort: 7239
          name: worker
        env:
        - name: DB
          value: "postgresql"
        - name: DB_PORT
          value: "5432"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: chatbot-secrets
              key: POSTGRES_USER
        - name: POSTGRES_PWD
          valueFrom:
            secretKeyRef:
              name: chatbot-secrets
              key: POSTGRES_PASSWORD
        - name: POSTGRES_SEEDS
          value: "postgres-primary-service"
        - name: DYNAMIC_CONFIG_FILE_PATH
          value: "config/dynamicconfig/development-sql.yaml"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          tcpSocket:
            port: 7233
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          tcpSocket:
            port: 7233
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: temporal-web
  namespace: chatbot-dev
  labels:
    app: temporal-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: temporal-web
  template:
    metadata:
      labels:
        app: temporal-web
    spec:
      containers:
      - name: temporal-web
        image: temporalio/web:2.21.3
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: TEMPORAL_ADDRESS
          value: "temporal-server-service:7233"
        - name: TEMPORAL_CORS_ORIGINS
          value: "http://localhost:3000"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "400m"
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: temporal-worker
  namespace: chatbot-dev
  labels:
    app: temporal-worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: temporal-worker
  template:
    metadata:
      labels:
        app: temporal-worker
    spec:
      containers:
      - name: temporal-worker
        image: python:3.11-slim
        command:
        - /bin/bash
        - -c
        - |
          pip install temporalio redis kafka-python
          python /app/temporal_worker.py
        env:
        - name: TEMPORAL_HOST
          value: "temporal-server-service"
        - name: TEMPORAL_PORT
          value: "7233"
        - name: REDIS_CLUSTER_NODES
          value: "redis-cluster-lb-service:6379"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-lb-service:9092"
        volumeMounts:
        - name: worker-code
          mountPath: /app
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: worker-code
        configMap:
          name: temporal-worker-code