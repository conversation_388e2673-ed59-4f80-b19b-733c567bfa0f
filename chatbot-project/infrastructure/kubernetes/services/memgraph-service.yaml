apiVersion: v1
kind: Service
metadata:
  name: memgraph-service
  namespace: chatbot-dev
  labels:
    app: memgraph
spec:
  clusterIP: None
  selector:
    app: memgraph
  ports:
  - port: 7687
    targetPort: 7687
    name: bolt
  - port: 7444
    targetPort: 7444
    name: monitoring
  - port: 10000
    targetPort: 10000
    name: replication
---
apiVersion: v1
kind: Service
metadata:
  name: memgraph-lb-service
  namespace: chatbot-dev
  labels:
    app: memgraph
    role: loadbalancer
spec:
  selector:
    app: memgraph
  ports:
  - port: 7687
    targetPort: 7687
    name: bolt
  - port: 7444
    targetPort: 7444
    name: monitoring
  type: ClusterIP
  sessionAffinity: ClientIP