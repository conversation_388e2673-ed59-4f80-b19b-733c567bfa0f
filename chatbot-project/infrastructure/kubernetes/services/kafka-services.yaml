apiVersion: v1
kind: Service
metadata:
  name: kafka-service
  namespace: chatbot-dev
  labels:
    app: kafka
spec:
  clusterIP: None
  selector:
    app: kafka
  ports:
  - port: 9092
    targetPort: 9092
    name: kafka
  - port: 9093
    targetPort: 9093
    name: kafka-ssl
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-lb-service
  namespace: chatbot-dev
  labels:
    app: kafka
    role: loadbalancer
spec:
  selector:
    app: kafka
  ports:
  - port: 9092
    targetPort: 9092
    name: kafka
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: zookeeper-service
  namespace: chatbot-dev
  labels:
    app: zookeeper
spec:
  clusterIP: None
  selector:
    app: zookeeper
  ports:
  - port: 2181
    targetPort: 2181
    name: client
  - port: 2888
    targetPort: 2888
    name: server
  - port: 3888
    targetPort: 3888
    name: leader-election