apiVersion: v1
kind: Service
metadata:
  name: redis-cluster-service
  namespace: chatbot-dev
  labels:
    app: redis-cluster
spec:
  clusterIP: None
  selector:
    app: redis-cluster
  ports:
  - port: 6379
    targetPort: 6379
    name: client
  - port: 16379
    targetPort: 16379
    name: gossip
---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster-lb-service
  namespace: chatbot-dev
  labels:
    app: redis-cluster
    role: loadbalancer
spec:
  selector:
    app: redis-cluster
  ports:
  - port: 6379
    targetPort: 6379
    name: client
  type: ClusterIP