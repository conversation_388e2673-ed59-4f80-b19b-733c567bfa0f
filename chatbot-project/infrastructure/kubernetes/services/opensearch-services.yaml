apiVersion: v1
kind: Service
metadata:
  name: opensearch-service
  namespace: chatbot-dev
  labels:
    app: opensearch
spec:
  clusterIP: None
  selector:
    app: opensearch
  ports:
  - port: 9200
    targetPort: 9200
    name: http
  - port: 9300
    targetPort: 9300
    name: transport
---
apiVersion: v1
kind: Service
metadata:
  name: opensearch-lb-service
  namespace: chatbot-dev
  labels:
    app: opensearch
    role: loadbalancer
spec:
  selector:
    app: opensearch
  ports:
  - port: 9200
    targetPort: 9200
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: opensearch-dashboards-service
  namespace: chatbot-dev
  labels:
    app: opensearch-dashboards
spec:
  selector:
    app: opensearch-dashboards
  ports:
  - port: 5601
    targetPort: 5601
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: fluent-bit-service
  namespace: chatbot-dev
  labels:
    app: fluent-bit
spec:
  selector:
    app: fluent-bit
  ports:
  - port: 2020
    targetPort: 2020
    name: http
  type: ClusterIP