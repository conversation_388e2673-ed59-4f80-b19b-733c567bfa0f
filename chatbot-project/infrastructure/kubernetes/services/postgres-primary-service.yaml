apiVersion: v1
kind: Service
metadata:
  name: postgres-primary-service
  namespace: chatbot-dev
  labels:
    app: postgres-primary
    role: primary
spec:
  selector:
    app: postgres-primary
    role: primary
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-replica-service
  namespace: chatbot-dev
  labels:
    app: postgres-replica
    role: replica
spec:
  selector:
    app: postgres-replica
    role: replica
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-read-service
  namespace: chatbot-dev
  labels:
    app: postgres
    role: read
spec:
  selector:
    role: replica
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  type: ClusterIP