apiVersion: v1
kind: Service
metadata:
  name: grpc-gateway-service
  namespace: chatbot-dev
  labels:
    app: grpc-gateway
spec:
  selector:
    app: grpc-gateway
  ports:
  - port: 50051
    targetPort: 50051
    name: grpc
  - port: 9090
    targetPort: 9090
    name: metrics
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: temporal-server-service
  namespace: chatbot-dev
  labels:
    app: temporal-server
spec:
  selector:
    app: temporal-server
  ports:
  - port: 7233
    targetPort: 7233
    name: frontend
  - port: 7234
    targetPort: 7234
    name: history
  - port: 7235
    targetPort: 7235
    name: matching
  - port: 7239
    targetPort: 7239
    name: worker
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: temporal-web-service
  namespace: chatbot-dev
  labels:
    app: temporal-web
spec:
  selector:
    app: temporal-web
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: redis-streams-service
  namespace: chatbot-dev
  labels:
    app: redis-streams-processor
spec:
  selector:
    app: redis-streams-processor
  ports:
  - port: 8080
    targetPort: 8080
    name: metrics
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: enhanced-message-broker-service
  namespace: chatbot-dev
  labels:
    app: enhanced-message-broker
spec:
  selector:
    app: enhanced-message-broker
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8081
    targetPort: 8081
    name: debug
  type: ClusterIP