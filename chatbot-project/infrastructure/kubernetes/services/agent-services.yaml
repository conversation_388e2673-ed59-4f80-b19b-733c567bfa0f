apiVersion: v1
kind: Service
metadata:
  name: agent-gateway-service
  namespace: chatbot-dev
  labels:
    app: agent-gateway
spec:
  selector:
    app: agent-gateway
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8081
    targetPort: 8081
    name: grpc
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: agent-registry-service
  namespace: chatbot-dev
  labels:
    app: agent-registry
spec:
  selector:
    app: agent-registry
  ports:
  - port: 8082
    targetPort: 8082
    name: http
  type: ClusterIP