apiVersion: v1
kind: Service
metadata:
  name: milvus-service
  namespace: chatbot-dev
  labels:
    app: milvus
spec:
  selector:
    app: milvus
  ports:
  - port: 19530
    targetPort: 19530
    name: grpc
  - port: 9091
    targetPort: 9091
    name: metrics
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: etcd-service
  namespace: chatbot-dev
  labels:
    app: etcd
spec:
  clusterIP: None
  selector:
    app: etcd
  ports:
  - port: 2379
    targetPort: 2379
    name: client
  - port: 2380
    targetPort: 2380
    name: peer
---
apiVersion: v1
kind: Service
metadata:
  name: minio-service
  namespace: chatbot-dev
  labels:
    app: minio
spec:
  selector:
    app: minio
  ports:
  - port: 9000
    targetPort: 9000
    name: api
  - port: 9001
    targetPort: 9001
    name: console
  type: ClusterIP