apiVersion: v1
kind: Service
metadata:
  name: neo4j-core-service
  namespace: chatbot-dev
  labels:
    app: neo4j-core
    role: core
spec:
  clusterIP: None
  selector:
    app: neo4j-core
    role: core
  ports:
  - port: 7474
    targetPort: 7474
    name: http
  - port: 7687
    targetPort: 7687
    name: bolt
  - port: 7473
    targetPort: 7473
    name: https
  - port: 5000
    targetPort: 5000
    name: discovery
  - port: 6000
    targetPort: 6000
    name: raft
  - port: 7000
    targetPort: 7000
    name: tx
---
apiVersion: v1
kind: Service
metadata:
  name: neo4j-replica-service
  namespace: chatbot-dev
  labels:
    app: neo4j-replica
    role: replica
spec:
  clusterIP: None
  selector:
    app: neo4j-replica
    role: replica
  ports:
  - port: 7474
    targetPort: 7474
    name: http
  - port: 7687
    targetPort: 7687
    name: bolt
  - port: 7473
    targetPort: 7473
    name: https
---
apiVersion: v1
kind: Service
metadata:
  name: neo4j-write-service
  namespace: chatbot-dev
  labels:
    app: neo4j
    role: write
spec:
  selector:
    app: neo4j-core
    role: core
  ports:
  - port: 7474
    targetPort: 7474
    name: http
  - port: 7687
    targetPort: 7687
    name: bolt
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: neo4j-read-service
  namespace: chatbot-dev
  labels:
    app: neo4j
    role: read
spec:
  selector:
    role: replica
  ports:
  - port: 7474
    targetPort: 7474
    name: http
  - port: 7687
    targetPort: 7687
    name: bolt
  type: ClusterIP