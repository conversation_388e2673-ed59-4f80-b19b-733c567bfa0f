# Neo4j Graph Database Cluster

This directory contains Neo4j Community Edition cluster configuration with high availability, graph partitioning, and automated backup.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Neo4j Core    │    │   Neo4j Core    │    │   Neo4j Core    │
│   Instance 1    │◄──►│   Instance 2    │◄──►│   Instance 3    │
│  (Read/Write)   │    │  (Read/Write)   │    │  (Read/Write)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐    ┌─────────────────┐
                    │  Read Replica   │    │  Read Replica   │
                    │   Instance 1    │    │   Instance 2    │
                    │  (Read Only)    │    │  (Read Only)    │
                    └─────────────────┘    └─────────────────┘
```

## Components

### Core Cluster
- **StatefulSet**: `neo4j-core` (3 replicas)
- **Service**: `neo4j-write-service`
- **Role**: Read/Write operations with Raft consensus
- **Clustering**: Causal clustering with minimum 3 core instances

### Read Replicas
- **StatefulSet**: `neo4j-replica` (2 replicas)
- **Service**: `neo4j-read-service`
- **Role**: Read-only operations
- **Replication**: Async replication from core cluster

### Graph Schema

#### Node Types
- **Organization**: Multi-tenant organization entities
- **Department**: Organizational structure within companies
- **User**: User profiles and relationships
- **Agent**: AI agent instances and configurations
- **KnowledgeSource**: Document and content sources
- **AgentPartition**: Agent-specific knowledge partitions
- **KnowledgeDomain**: Knowledge domain classifications

#### Relationship Types
- **BELONGS_TO**: Department to Organization relationships
- **WORKS_IN**: User to Department relationships
- **MANAGES**: Manager to subordinate relationships
- **ACCESSIBLE_BY**: Knowledge domain access permissions
- **ASSIGNED_TO**: Agent to partition assignments
- **DEPENDS_ON**: Document dependency relationships
- **REFERENCES**: Cross-document references
- **SUPERSEDES**: Document version relationships

## Deployment

### Quick Setup
```bash
# Deploy Neo4j cluster
./scripts/neo4j-setup.sh
```

### Manual Deployment
```bash
# 1. Create secrets and config
kubectl apply -f infrastructure/kubernetes/secrets/neo4j-secrets.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/neo4j-config.yaml

# 2. Deploy core cluster
kubectl apply -f infrastructure/kubernetes/deployments/neo4j-core.yaml
kubectl apply -f infrastructure/kubernetes/services/neo4j-services.yaml

# 3. Wait for core cluster
kubectl wait --for=condition=ready pod -l app=neo4j-core -n chatbot-dev --timeout=600s

# 4. Initialize schema
kubectl exec neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass -f /var/lib/neo4j/init/01-schema-setup.cypher

# 5. Deploy read replicas
kubectl apply -f infrastructure/kubernetes/deployments/neo4j-replica.yaml

# 6. Setup backup
kubectl apply -f infrastructure/kubernetes/jobs/neo4j-backup.yaml
```

## Connection Information

### Write Operations (Core Cluster)
```
Host: neo4j-write-service
Port: 7687 (Bolt), 7474 (HTTP)
Database: neo4j
User: neo4j
Password: chatbot_neo4j_pass
```

### Read Operations (Replicas)
```
Host: neo4j-read-service
Port: 7687 (Bolt), 7474 (HTTP)
Database: neo4j
User: neo4j
Password: chatbot_neo4j_pass
```

## Graph Partitioning Strategy

### Agent-Specific Partitions
Each agent type has dedicated knowledge partitions:

1. **Coordinator Partition** (`coord_001`)
   - Task orchestration knowledge
   - Agent coordination patterns
   - Workflow templates

2. **Organization Partition** (`org_001`)
   - Organization-specific policies
   - Company structure data
   - Cross-departmental procedures

3. **Department Partition** (`dept_001`)
   - Department-specific knowledge
   - Role-based procedures
   - Local policies and guidelines

4. **Reasoning Partition** (`reason_001`)
   - Reasoning patterns and templates
   - Decision trees and logic flows
   - Historical reasoning traces

5. **Tool Partition** (`tool_001`)
   - Tool configurations and usage
   - Integration patterns
   - API documentation

6. **Critic Partition** (`critic_001`)
   - Validation rules and patterns
   - Quality assessment criteria
   - Compliance requirements

### Knowledge Domains
- **HR_POLICIES**: Human resources policies (organization-level)
- **TECHNICAL_DOCS**: Technical documentation (department-level)
- **FINANCE_POLICIES**: Financial policies (department-level)

## Backup and Recovery

### Automated Backups
- **Schedule**: Daily at 3:00 AM UTC
- **Format**: Neo4j native backup + database dump
- **Compression**: tar.gz format
- **Location**: `/backup` in persistent volume
- **Retention**: 7 days automatic cleanup

### Manual Backup
```bash
# Create immediate backup
kubectl create job --from=cronjob/neo4j-backup neo4j-backup-manual -n chatbot-dev
```

### Recovery Process
```bash
# Restore from backup
./scripts/neo4j-recovery.sh /backup/neo4j_20240101_030000.tar.gz
```

## Graph Queries Examples

### Organizational Structure
```cypher
// Get all departments in an organization
MATCH (o:Organization {name: 'NuvoAi'})<-[:BELONGS_TO]-(d:Department)
RETURN o.name, collect(d.name) as departments

// Find users in a specific department
MATCH (u:User)-[:WORKS_IN]->(d:Department {name: 'AI Department'})
RETURN u.name, u.email, u.job_role
```

### Agent Partitioning
```cypher
// Get agents in a specific partition
MATCH (a:Agent)-[:ASSIGNED_TO]->(p:AgentPartition {name: 'reasoning'})
RETURN a.agent_id, a.status, a.capabilities

// Find knowledge accessible to an agent type
MATCH (p:AgentPartition {name: 'organization'})-[:CAN_ACCESS]->(k:KnowledgeDomain)
RETURN p.name, collect(k.name) as accessible_domains
```

### Knowledge Graph Navigation
```cypher
// Find document dependencies
MATCH (d1:KnowledgeSource)-[:DEPENDS_ON]->(d2:KnowledgeSource)
WHERE d1.organization_id = 1
RETURN d1.name, d2.name, d1.type, d2.type

// Get knowledge sources for a department
MATCH (d:Department {name: 'HR Department'})<-[:BELONGS_TO]-(k:KnowledgeSource)
RETURN k.name, k.type, k.last_updated
```

## High Availability Features

### Cluster Management
- **Consensus**: Raft consensus protocol for core cluster
- **Leader Election**: Automatic leader election and failover
- **Split-Brain Protection**: Minimum cluster size enforcement

### Monitoring and Health Checks
- **Liveness Probes**: HTTP health checks every 30 seconds
- **Readiness Probes**: Service readiness validation
- **Cluster Status**: Built-in cluster status monitoring

## Performance Optimization

### Memory Configuration
- **Heap Size**: 1GB for core, 512MB for replicas
- **Page Cache**: 512MB for core, 256MB for replicas
- **Query Cache**: Enabled for read replicas

### Indexing Strategy
- **Unique Constraints**: Organization names, user emails, agent IDs
- **Performance Indexes**: Organization, department, agent type, knowledge type
- **Composite Indexes**: Multi-property indexes for complex queries

## Security Considerations

- **Authentication**: Password-based authentication
- **Network**: Cluster-internal communication only
- **Secrets**: Kubernetes secrets for credential management
- **Access Control**: Role-based access through application layer
- **Backup Security**: Encrypted backup storage recommended for production

## Troubleshooting

### Check Cluster Status
```bash
kubectl exec neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass -c "CALL dbms.cluster.overview()"
```

### View Logs
```bash
kubectl logs neo4j-core-0 -n chatbot-dev
kubectl logs neo4j-replica-0 -n chatbot-dev
```

### Connection Test
```bash
# Test core connection
kubectl exec -it neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass

# Test replica connection
kubectl exec -it neo4j-replica-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass
```