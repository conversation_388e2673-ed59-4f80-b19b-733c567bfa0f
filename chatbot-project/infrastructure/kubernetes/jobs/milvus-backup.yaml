apiVersion: batch/v1
kind: CronJob
metadata:
  name: milvus-backup
  namespace: chatbot-dev
spec:
  schedule: "0 5 * * *"  # Daily at 5 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: milvus-backup
            image: python:3.11-slim
            command:
            - /bin/bash
            - -c
            - |
              pip install pymilvus boto3
              
              BACKUP_DIR="/backup/milvus_$(date +%Y%m%d_%H%M%S)"
              mkdir -p $BACKUP_DIR
              
              python3 << 'EOF'
              import pymilvus
              import json
              import os
              from datetime import datetime
              
              # Connect to Milvus
              connections = pymilvus.connections
              connections.connect('default', host='milvus-service', port='19530')
              
              # List all collections
              from pymilvus import utility
              collections = utility.list_collections()
              
              backup_info = {
                  'timestamp': datetime.now().isoformat(),
                  'collections': []
              }
              
              for collection_name in collections:
                  print(f"Backing up collection: {collection_name}")
                  collection = pymilvus.Collection(collection_name)
                  
                  # Get collection info
                  collection_info = {
                      'name': collection_name,
                      'schema': collection.schema.to_dict(),
                      'num_entities': collection.num_entities
                  }
                  backup_info['collections'].append(collection_info)
              
              # Save backup metadata
              backup_dir = os.environ.get('BACKUP_DIR', '/backup')
              with open(f"{backup_dir}/backup_info.json", 'w') as f:
                  json.dump(backup_info, f, indent=2)
              
              print(f"Backup metadata saved to {backup_dir}/backup_info.json")
              EOF
              
              # Backup MinIO data (vector files)
              echo "Backing up MinIO data..."
              mc alias set minio http://minio-service:9000 milvus milvus123
              mc mirror minio/milvus-bucket $BACKUP_DIR/minio-data
              
              # Compress backup
              tar -czf "${BACKUP_DIR}.tar.gz" -C /backup $(basename $BACKUP_DIR)
              rm -rf $BACKUP_DIR
              
              # Keep only last 7 days of backups
              find /backup -name "milvus_*.tar.gz" -mtime +7 -delete
              
              echo "Milvus backup completed: ${BACKUP_DIR}.tar.gz"
            env:
            - name: BACKUP_DIR
              value: "/backup/milvus_$(date +%Y%m%d_%H%M%S)"
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                memory: "512Mi"
                cpu: "250m"
              limits:
                memory: "1Gi"
                cpu: "500m"
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: milvus-backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: milvus-backup-pvc
  namespace: chatbot-dev
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi