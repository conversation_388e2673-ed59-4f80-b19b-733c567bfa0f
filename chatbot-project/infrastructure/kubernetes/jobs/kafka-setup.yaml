apiVersion: batch/v1
kind: Job
metadata:
  name: kafka-topics-setup
  namespace: chatbot-dev
spec:
  template:
    spec:
      containers:
      - name: kafka-setup
        image: confluentinc/cp-kafka:7.4.0
        command:
        - /bin/bash
        - /scripts/create-topics.sh
        volumeMounts:
        - name: kafka-scripts
          mountPath: /scripts
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: kafka-scripts
        configMap:
          name: kafka-topics-config
          defaultMode: 0755
      restartPolicy: OnFailure
---
apiVersion: batch/v1
kind: Job
metadata:
  name: redis-cluster-setup
  namespace: chatbot-dev
spec:
  template:
    spec:
      containers:
      - name: redis-setup
        image: redis:7-alpine
        command:
        - /bin/sh
        - -c
        - |
          # Wait for all Redis instances to be ready
          for i in 0 1 2 3 4 5; do
            until redis-cli -h redis-cluster-$i.redis-cluster-service -p 6379 ping; do
              echo "Waiting for redis-cluster-$i to be ready..."
              sleep 5
            done
          done
          
          # Create Redis cluster
          redis-cli --cluster create \
            redis-cluster-0.redis-cluster-service:6379 \
            redis-cluster-1.redis-cluster-service:6379 \
            redis-cluster-2.redis-cluster-service:6379 \
            redis-cluster-3.redis-cluster-service:6379 \
            redis-cluster-4.redis-cluster-service:6379 \
            redis-cluster-5.redis-cluster-service:6379 \
            --cluster-replicas 1 --cluster-yes
          
          echo "Redis cluster setup completed"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      restartPolicy: OnFailure