apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: chatbot-dev
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15
            command:
            - /bin/bash
            - -c
            - |
              BACKUP_FILE="/backup/chatbot_db_$(date +%Y%m%d_%H%M%S).sql"
              pg_dump -h postgres-primary-service -U $POSTGRES_USER -d $POSTGRES_DB > $BACKUP_FILE
              
              # Compress backup
              gzip $BACKUP_FILE
              
              # Keep only last 7 days of backups
              find /backup -name "*.sql.gz" -mtime +7 -delete
              
              echo "Backup completed: ${BACKUP_FILE}.gz"
            env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: chatbot-secrets
                  key: POSTGRES_USER
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: chatbot-config
                  key: POSTGRES_DB
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: chatbot-secrets
                  key: POSTGRES_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                memory: "128Mi"
                cpu: "100m"
              limits:
                memory: "256Mi"
                cpu: "200m"
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: postgres-backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-backup-pvc
  namespace: chatbot-dev
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi