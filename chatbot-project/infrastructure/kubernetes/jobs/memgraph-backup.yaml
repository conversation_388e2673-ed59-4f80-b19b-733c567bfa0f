apiVersion: batch/v1
kind: CronJob
metadata:
  name: memgraph-backup
  namespace: chatbot-dev
spec:
  schedule: "0 4 * * *"  # Daily at 4 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: memgraph-backup
            image: memgraph/memgraph:2.11
            command:
            - /bin/bash
            - -c
            - |
              BACKUP_DIR="/backup/memgraph_$(date +%Y%m%d_%H%M%S)"
              mkdir -p $BACKUP_DIR
              
              # Create snapshot backup
              echo "CREATE SNAPSHOT;" | mgconsole --host memgraph-0.memgraph-service --port 7687 --username memgraph --password chatbot_memgraph_pass
              
              # Copy snapshot files
              kubectl cp memgraph-0:/var/lib/memgraph/snapshots $BACKUP_DIR/snapshots -n chatbot-dev
              
              # Export reasoning graph data
              echo "
              MATCH (a:Agent)-[:HAS_REASONING_SESSION]->(s:ReasoningSession)-[:HAS_STEP]->(step:ReasoningStep)
              WHERE datetime(step.timestamp) > datetime() - duration({days: 7})
              RETURN a.agent_id, s.session_id, step.step_id, step.step_type, step.content, 
                     step.confidence, step.timestamp, step.processing_time_ms
              " | mgconsole --host memgraph-0.memgraph-service --port 7687 --username memgraph --password chatbot_memgraph_pass --output-format=csv > $BACKUP_DIR/reasoning_data.csv
              
              # Compress backup
              tar -czf "${BACKUP_DIR}.tar.gz" -C /backup $(basename $BACKUP_DIR)
              rm -rf $BACKUP_DIR
              
              # Keep only last 7 days of backups
              find /backup -name "memgraph_*.tar.gz" -mtime +7 -delete
              
              echo "Memgraph backup completed: ${BACKUP_DIR}.tar.gz"
            env:
            - name: MEMGRAPH_USER
              valueFrom:
                secretKeyRef:
                  name: memgraph-secrets
                  key: MEMGRAPH_USER
            - name: MEMGRAPH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: memgraph-secrets
                  key: MEMGRAPH_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                memory: "256Mi"
                cpu: "200m"
              limits:
                memory: "512Mi"
                cpu: "400m"
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: memgraph-backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: memgraph-backup-pvc
  namespace: chatbot-dev
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi