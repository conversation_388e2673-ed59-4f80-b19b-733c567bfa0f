apiVersion: batch/v1
kind: CronJob
metadata:
  name: neo4j-backup
  namespace: chatbot-dev
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: neo4j-backup
            image: neo4j:5.15-community
            command:
            - /bin/bash
            - -c
            - |
              BACKUP_DIR="/backup/neo4j_$(date +%Y%m%d_%H%M%S)"
              mkdir -p $BACKUP_DIR
              
              # Create database backup
              neo4j-admin database backup --to-path=$BACKUP_DIR neo4j
              
              # Create dump for portability
              neo4j-admin database dump --to-path=$BACKUP_DIR neo4j
              
              # Compress backup
              tar -czf "${BACKUP_DIR}.tar.gz" -C /backup $(basename $BACKUP_DIR)
              rm -rf $BACKUP_DIR
              
              # Keep only last 7 days of backups
              find /backup -name "neo4j_*.tar.gz" -mtime +7 -delete
              
              echo "Neo4j backup completed: ${BACKUP_DIR}.tar.gz"
            env:
            - name: NEO4J_AUTH
              valueFrom:
                secretKeyRef:
                  name: neo4j-secrets
                  key: NEO4J_AUTH
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            - name: neo4j-data
              mountPath: /data
            resources:
              requests:
                memory: "256Mi"
                cpu: "200m"
              limits:
                memory: "512Mi"
                cpu: "400m"
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: neo4j-backup-pvc
          - name: neo4j-data
            persistentVolumeClaim:
              claimName: neo4j-core-data-neo4j-core-0
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: neo4j-backup-pvc
  namespace: chatbot-dev
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi