# Memgraph Real-time Agent Reasoning Graphs

This directory contains Memgraph deployment for real-time agent reasoning graphs with high-performance operations and streaming updates.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Memgraph      │    │   Memgraph      │    │   Memgraph      │
│   Instance 1    │    │   Instance 2    │    │   Instance 3    │
│  (Reasoning)    │    │  (Reasoning)    │    │  (Reasoning)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Load Balancer  │
                    │    Service      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐    ┌─────────────────┐
                    │ Kafka Connect   │    │ Stream          │
                    │ Streaming       │    │ Processor       │
                    └─────────────────┘    └─────────────────┘
```

## Components

### Memgraph Cluster
- **StatefulSet**: `memgraph` (3 replicas)
- **Service**: `memgraph-lb-service` (load balanced)
- **Role**: Real-time graph operations for agent reasoning
- **Performance**: Optimized for high-frequency updates

### Streaming Components
- **Kafka Connect**: Stream processing integration
- **Stream Processor**: Python-based event processor
- **Topics**: agent-reasoning-events, agent-communication-events, knowledge-update-events

### Custom Procedures
- **Reasoning Path Analysis**: Track agent reasoning flows
- **Performance Optimization**: Hot path identification and caching
- **Real-time Updates**: Streaming graph updates
- **Cleanup Operations**: Automated data retention management

## Graph Schema for Agent Reasoning

### Node Types
- **Agent**: AI agent instances with reasoning capabilities
- **ReasoningSession**: Individual reasoning sessions per agent
- **ReasoningStep**: Individual steps in reasoning process
- **Communication**: Agent-to-agent communication events
- **Knowledge**: Knowledge items accessed during reasoning

### Relationship Types
- **HAS_REASONING_SESSION**: Agent to reasoning session
- **HAS_STEP**: Session to reasoning steps
- **NEXT_STEP**: Sequential step relationships
- **SENT/RECEIVED_BY**: Communication relationships
- **ACCESSED_KNOWLEDGE**: Knowledge access relationships

## Deployment

### Quick Setup
```bash
# Deploy Memgraph cluster
./scripts/memgraph-setup.sh
```

### Manual Deployment
```bash
# 1. Create secrets and config
kubectl apply -f infrastructure/kubernetes/secrets/memgraph-secrets.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/memgraph-config.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/streaming-config.yaml

# 2. Deploy Memgraph cluster
kubectl apply -f infrastructure/kubernetes/deployments/memgraph.yaml
kubectl apply -f infrastructure/kubernetes/services/memgraph-service.yaml

# 3. Wait for cluster to be ready
kubectl wait --for=condition=ready pod -l app=memgraph -n chatbot-dev --timeout=300s

# 4. Deploy streaming components
kubectl apply -f infrastructure/kubernetes/deployments/memgraph-streaming.yaml

# 5. Setup backup
kubectl apply -f infrastructure/kubernetes/jobs/memgraph-backup.yaml
```

## Connection Information

### Bolt Protocol
```
Host: memgraph-lb-service
Port: 7687
Username: memgraph
Password: chatbot_memgraph_pass
```

### Monitoring
```
Host: memgraph-lb-service
Port: 7444
```

## Custom Procedures

### Reasoning Analysis
```cypher
// Get reasoning path for an agent
CALL get_agent_reasoning_path("agent_001", "session_123") 
YIELD path RETURN path;

// Add new reasoning step
CALL add_reasoning_step("agent_001", "session_123", "analysis", 
                       "Analyzing user query", 0.85, "{}") 
YIELD step_id RETURN step_id;

// Analyze reasoning patterns
CALL analyze_reasoning_patterns("coordinator", 24) 
YIELD patterns RETURN patterns;
```

### Performance Optimization
```cypher
// Get hot reasoning paths
CALL get_hot_reasoning_paths(60) 
YIELD paths RETURN paths;

// Get agent performance metrics
CALL get_agent_performance_metrics("agent_001") 
YIELD metrics RETURN metrics;

// Cleanup old data
CALL cleanup_old_reasoning_data(168) 
YIELD deleted_count RETURN deleted_count;
```

## Streaming Updates

### Event Types

#### Reasoning Step Event
```json
{
  "event_type": "reasoning_step",
  "agent_id": "agent_001",
  "session_id": "session_123",
  "step_id": "step_456",
  "step_type": "analysis",
  "content": "Analyzing user query about HR policies",
  "confidence": 0.85,
  "timestamp": "2024-01-01T12:00:00Z",
  "processing_time_ms": 150,
  "input_tokens": 100,
  "output_tokens": 50
}
```

#### Agent Communication Event
```json
{
  "event_type": "agent_communication",
  "sender_id": "coordinator_001",
  "receiver_id": "hr_agent_001",
  "communication_id": "comm_789",
  "message_type": "task_assignment",
  "content": "Please analyze HR policy document",
  "timestamp": "2024-01-01T12:00:00Z",
  "success": true,
  "response_time_ms": 25,
  "message_size_bytes": 256
}
```

#### Knowledge Update Event
```json
{
  "event_type": "knowledge_update",
  "agent_id": "hr_agent_001",
  "knowledge_id": "hr_policy_001",
  "content": "Updated HR leave policy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.2",
  "knowledge_type": "policy",
  "organization_id": 1,
  "department_id": 3,
  "access_type": "read",
  "confidence_score": 0.92
}
```

## Performance Features

### High-Performance Configuration
- **Memory Limit**: 3GB per instance
- **Query Timeout**: 600 seconds
- **Parallel Recovery**: Enabled
- **WAL**: Enabled for durability
- **Snapshot Interval**: 5 minutes

### Real-time Optimizations
- **Session Affinity**: Client IP-based session stickiness
- **Connection Pooling**: Load balanced connections
- **Batch Processing**: 100 events per batch
- **Retry Logic**: 3 retries with backoff

### Monitoring Metrics
- **Reasoning Path Length**: Average steps per reasoning session
- **Agent Performance**: Processing time and confidence scores
- **Communication Patterns**: Inter-agent communication frequency
- **Knowledge Access**: Most accessed knowledge items
- **Hot Paths**: Frequently used reasoning patterns

## Query Examples

### Real-time Reasoning Analysis
```cypher
// Get active reasoning sessions
MATCH (a:Agent)-[:HAS_REASONING_SESSION]->(s:ReasoningSession)
WHERE datetime(s.last_update) > datetime() - duration({minutes: 5})
RETURN a.agent_id, s.session_id, s.last_update
ORDER BY s.last_update DESC;

// Find reasoning bottlenecks
MATCH (step:ReasoningStep)
WHERE step.processing_time_ms > 1000
AND datetime(step.timestamp) > datetime() - duration({hours: 1})
RETURN step.agent_id, step.step_type, 
       avg(step.processing_time_ms) as avg_time,
       count(step) as slow_steps
ORDER BY avg_time DESC;
```

### Agent Communication Analysis
```cypher
// Get communication patterns
MATCH (sender:Agent)-[:SENT]->(comm:Communication)-[:RECEIVED_BY]->(receiver:Agent)
WHERE datetime(comm.timestamp) > datetime() - duration({hours: 1})
RETURN sender.agent_id, receiver.agent_id, 
       count(comm) as message_count,
       avg(comm.response_time_ms) as avg_response_time
ORDER BY message_count DESC;

// Find communication failures
MATCH (comm:Communication)
WHERE comm.success = false
AND datetime(comm.timestamp) > datetime() - duration({hours: 24})
RETURN comm.sender_id, comm.receiver_id, comm.message_type,
       count(comm) as failure_count
ORDER BY failure_count DESC;
```

### Knowledge Access Patterns
```cypher
// Most accessed knowledge
MATCH (a:Agent)-[access:ACCESSED_KNOWLEDGE]->(k:Knowledge)
WHERE datetime(access.timestamp) > datetime() - duration({hours: 24})
RETURN k.knowledge_id, k.knowledge_type,
       count(access) as access_count,
       avg(access.confidence_score) as avg_confidence
ORDER BY access_count DESC
LIMIT 10;
```

## Backup and Recovery

### Automated Backups
- **Schedule**: Daily at 4:00 AM UTC
- **Format**: Snapshot + CSV export
- **Retention**: 7 days
- **Data**: Recent reasoning data (last 7 days)

### Manual Operations
```bash
# Create immediate backup
kubectl create job --from=cronjob/memgraph-backup memgraph-backup-manual -n chatbot-dev

# Export reasoning data
kubectl exec memgraph-0 -n chatbot-dev -- mgconsole --host localhost --port 7687 --username memgraph --password chatbot_memgraph_pass -c "
MATCH (a:Agent)-[:HAS_REASONING_SESSION]->(s:ReasoningSession)-[:HAS_STEP]->(step:ReasoningStep)
RETURN a.agent_id, s.session_id, step.step_type, step.confidence, step.timestamp
"
```

## Troubleshooting

### Check Cluster Status
```bash
kubectl get pods -l app=memgraph -n chatbot-dev
kubectl logs memgraph-0 -n chatbot-dev
```

### Test Connection
```bash
kubectl exec -it memgraph-0 -n chatbot-dev -- mgconsole --host localhost --port 7687 --username memgraph --password chatbot_memgraph_pass
```

### Monitor Streaming
```bash
kubectl logs -l app=memgraph-streaming -n chatbot-dev -f
```

### Performance Monitoring
```bash
# Check memory usage
kubectl exec memgraph-0 -n chatbot-dev -- mgconsole --host localhost --port 7687 --username memgraph --password chatbot_memgraph_pass -c "SHOW STORAGE INFO;"

# Check active queries
kubectl exec memgraph-0 -n chatbot-dev -- mgconsole --host localhost --port 7687 --username memgraph --password chatbot_memgraph_pass -c "SHOW QUERIES;"
```