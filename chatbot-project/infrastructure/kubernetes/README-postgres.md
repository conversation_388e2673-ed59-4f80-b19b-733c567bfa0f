# PostgreSQL High Availability Setup

This directory contains PostgreSQL cluster configuration with high availability, replication, and automated backup.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  PostgreSQL     │    │  PostgreSQL     │    │  PostgreSQL     │
│  Primary        │───▶│  Replica 1      │    │  Replica 2      │
│  (Read/Write)   │    │  (Read Only)    │    │  (Read Only)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Load Balancer  │
                    │  (Read Service) │
                    └─────────────────┘
```

## Components

### Primary Database
- **StatefulSet**: `postgres-primary`
- **Service**: `postgres-primary-service`
- **Role**: Read/Write operations
- **Replication**: Streaming replication enabled

### Replica Databases
- **StatefulSet**: `postgres-replica` (2 replicas)
- **Service**: `postgres-read-service`
- **Role**: Read-only operations
- **Replication**: Async streaming from primary

### Backup System
- **CronJob**: Daily automated backups at 2 AM
- **Retention**: 7 days
- **Compression**: gzip compression
- **Storage**: 50Gi persistent volume

## Deployment

### Quick Setup
```bash
# Deploy PostgreSQL cluster
./scripts/postgres-setup.sh
```

### Manual Deployment
```bash
# 1. Create secrets and config
kubectl apply -f infrastructure/kubernetes/secrets/postgres-secrets.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/postgres-config.yaml

# 2. Deploy primary
kubectl apply -f infrastructure/kubernetes/deployments/postgres-primary.yaml
kubectl apply -f infrastructure/kubernetes/services/postgres-primary-service.yaml

# 3. Wait for primary to be ready
kubectl wait --for=condition=ready pod -l app=postgres-primary -n chatbot-dev --timeout=300s

# 4. Deploy replicas
kubectl apply -f infrastructure/kubernetes/deployments/postgres-replica.yaml

# 5. Setup backup
kubectl apply -f infrastructure/kubernetes/jobs/postgres-backup.yaml

# 6. Setup monitoring
kubectl apply -f infrastructure/kubernetes/monitoring/postgres-monitoring.yaml
```

## Connection Information

### Write Operations (Primary)
```
Host: postgres-primary-service
Port: 5432
Database: chatbot_db
User: chatbot_user
```

### Read Operations (Replicas)
```
Host: postgres-read-service
Port: 5432
Database: chatbot_db
User: chatbot_user
```

## Database Schema

### Core Tables
- **users**: User profiles and authentication
- **organizations**: Multi-tenant organization data
- **departments**: Department structure within organizations
- **conversations**: Chat conversation metadata
- **messages**: Individual chat messages
- **agent_registry**: Agent configuration and status
- **knowledge_sources**: Knowledge base source tracking

### Key Features
- **Multi-tenancy**: Organization and department isolation
- **Audit trails**: Created/updated timestamps
- **Performance**: Optimized indexes
- **Constraints**: Foreign key relationships and data validation

## Backup and Recovery

### Automated Backups
- **Schedule**: Daily at 2:00 AM UTC
- **Format**: SQL dump with gzip compression
- **Location**: `/backup` in persistent volume
- **Retention**: 7 days automatic cleanup

### Manual Backup
```bash
# Create immediate backup
kubectl create job --from=cronjob/postgres-backup postgres-backup-manual -n chatbot-dev
```

### Recovery Process
```bash
# Restore from backup
./scripts/postgres-recovery.sh /backup/chatbot_db_20240101_020000.sql.gz
```

## Monitoring

### Metrics Available
- Database size and growth
- Connection counts
- Query performance
- Replication lag
- Table statistics
- Index usage

### Access Metrics
```bash
# Port forward to Prometheus
kubectl port-forward service/postgres-exporter-service 9187:9187 -n chatbot-dev

# View metrics
curl http://localhost:9187/metrics
```

## High Availability Features

### Automatic Failover
- **Detection**: Health checks every 10 seconds
- **Promotion**: Manual replica promotion to primary
- **Recovery**: Automatic replica rebuild

### Load Distribution
- **Writes**: Directed to primary only
- **Reads**: Load balanced across replicas
- **Connection Pooling**: Application-level pooling recommended

## Maintenance

### Scaling Replicas
```bash
# Scale to 3 replicas
kubectl scale statefulset postgres-replica --replicas=3 -n chatbot-dev
```

### Manual Failover
```bash
# Promote replica to primary (manual process)
kubectl exec postgres-replica-0 -n chatbot-dev -- pg_promote
```

### Performance Tuning
- **shared_buffers**: 25% of available memory
- **effective_cache_size**: 75% of available memory
- **work_mem**: Adjusted based on concurrent connections
- **checkpoint_completion_target**: 0.7 for write-heavy workloads

## Troubleshooting

### Check Replication Status
```bash
kubectl exec postgres-primary-0 -n chatbot-dev -- psql -U chatbot_user -d chatbot_db -c "SELECT * FROM pg_stat_replication;"
```

### View Logs
```bash
kubectl logs postgres-primary-0 -n chatbot-dev
kubectl logs postgres-replica-0 -n chatbot-dev
```

### Connection Test
```bash
# Test primary connection
kubectl exec -it postgres-primary-0 -n chatbot-dev -- psql -U chatbot_user -d chatbot_db

# Test replica connection
kubectl exec -it postgres-replica-0 -n chatbot-dev -- psql -U chatbot_user -d chatbot_db
```

## Security Considerations

- **Encryption**: TLS encryption for client connections
- **Authentication**: Password-based authentication
- **Network**: Cluster-internal communication only
- **Secrets**: Kubernetes secrets for credential management
- **Backup**: Encrypted backup storage recommended for production