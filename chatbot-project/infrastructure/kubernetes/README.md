# Kubernetes Infrastructure

This directory contains Kubernetes manifests for deploying the Multi-Organization Chatbot with Advanced Agentic RAG.

## Directory Structure

```
kubernetes/
├── namespaces/          # Namespace definitions
├── configmaps/          # Configuration maps
├── secrets/             # Secret configurations
├── deployments/         # Application deployments
├── services/            # Service definitions
├── ingress/             # Ingress configurations
├── monitoring/          # Monitoring setup
├── persistent-volumes.yaml  # Storage definitions
└── kustomization.yaml   # Kustomize configuration
```

## Quick Start

### 1. Local Development Setup

```bash
# Setup local Kubernetes cluster (kind/minikube)
./scripts/local-k8s-setup.sh

# Deploy to Kubernetes
./scripts/k8s-setup.sh
```

### 2. Manual Deployment

```bash
# Create namespaces
kubectl apply -f namespaces/namespaces.yaml

# Create persistent volumes
kubectl apply -f persistent-volumes.yaml

# Create configuration
kubectl apply -f configmaps/app-config.yaml
kubectl apply -f secrets/app-secrets.yaml

# Deploy databases
kubectl apply -f deployments/postgres.yaml
kubectl apply -f deployments/redis.yaml

# Create services
kubectl apply -f services/postgres-service.yaml
kubectl apply -f services/redis-service.yaml

# Deploy application (after building image)
docker build -t chatbot-app .
kubectl apply -f deployments/chatbot-app.yaml
kubectl apply -f services/chatbot-service.yaml
kubectl apply -f ingress/chatbot-ingress.yaml
```

### 3. Using Kustomize

```bash
# Deploy everything with kustomize
kubectl apply -k .
```

## Environment Configuration

### Development
- Namespace: `chatbot-dev`
- Resources: Basic resource limits
- Storage: Local persistent volumes

### Staging
- Namespace: `chatbot-staging`
- Resources: Production-like limits
- Storage: Network storage

### Production
- Namespace: `chatbot-prod`
- Resources: High availability
- Storage: Replicated storage

## Monitoring

Basic monitoring is included with Prometheus:

```bash
# Deploy monitoring
kubectl apply -f monitoring/monitoring.yaml

# Access Prometheus
kubectl port-forward service/prometheus-service 9090:9090 -n chatbot-dev
```

## Scaling

### Horizontal Pod Autoscaler

```bash
# Enable HPA for chatbot app
kubectl autoscale deployment chatbot-app --cpu-percent=70 --min=2 --max=10 -n chatbot-dev
```

### Manual Scaling

```bash
# Scale chatbot app
kubectl scale deployment chatbot-app --replicas=5 -n chatbot-dev
```

## Troubleshooting

### Check Pod Status
```bash
kubectl get pods -n chatbot-dev
kubectl describe pod <pod-name> -n chatbot-dev
```

### View Logs
```bash
kubectl logs -f deployment/chatbot-app -n chatbot-dev
kubectl logs -f deployment/postgres -n chatbot-dev
```

### Debug Services
```bash
kubectl get services -n chatbot-dev
kubectl describe service chatbot-service -n chatbot-dev
```

### Port Forward for Testing
```bash
# Access chatbot app
kubectl port-forward service/chatbot-service 8080:80 -n chatbot-dev

# Access PostgreSQL
kubectl port-forward service/postgres-service 5432:5432 -n chatbot-dev

# Access Redis
kubectl port-forward service/redis-service 6379:6379 -n chatbot-dev
```

## Cleanup

```bash
# Remove all resources
./scripts/k8s-cleanup.sh

# Or manually
kubectl delete namespace chatbot-dev
```

## Security Considerations

1. **Secrets Management**: Use external secret management in production
2. **Network Policies**: Implement network segmentation
3. **RBAC**: Configure role-based access control
4. **Pod Security**: Use security contexts and policies
5. **Image Security**: Scan images for vulnerabilities

## Resource Requirements

### Minimum Requirements
- CPU: 4 cores
- Memory: 8GB RAM
- Storage: 20GB

### Recommended for Production
- CPU: 16 cores
- Memory: 32GB RAM
- Storage: 100GB SSD