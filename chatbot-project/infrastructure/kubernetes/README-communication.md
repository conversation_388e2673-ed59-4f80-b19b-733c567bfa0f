# Agent Communication Infrastructure

This directory contains the complete agent communication infrastructure with message queuing, event streaming, and inter-agent communication protocols.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Zookeeper     │    │   Zookeeper     │    │   Zookeeper     │
│   Instance 1    │◄──►│   Instance 2    │◄──►│   Instance 3    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Ka<PERSON>ka       │    │     Kafka       │    │     Kafka       │
│   Broker 1      │◄──►│   Broker 2      │◄──►│   Broker 3      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Redis Cluster  │    │  Redis Cluster  │    │  Redis Cluster  │
│   Node 1-2      │◄──►│   Node 3-4      │◄──►│   Node 5-6      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐
         │ Agent Gateway   │    │ Agent Registry  │
         │   (3 replicas)  │    │   (2 replicas)  │
         └─────────────────┘    └─────────────────┘
```

## Components

### Message Queuing (Kafka)
- **Kafka Cluster**: 3 brokers for high availability
- **Zookeeper**: 3-node ensemble for coordination
- **Topics**: 12 specialized topics for different message types
- **Partitioning**: 6-12 partitions per topic for scalability
- **Replication**: 3x replication factor for fault tolerance

### Caching Layer (Redis Cluster)
- **Redis Cluster**: 6 nodes (3 masters + 3 replicas)
- **Purpose**: Agent state caching, message correlation, session storage
- **Configuration**: Cluster mode with automatic failover
- **Memory**: 256MB per node with LRU eviction

### Agent Communication Services
- **Agent Gateway**: Message routing and correlation (3 replicas)
- **Agent Registry**: Agent discovery and health monitoring (2 replicas)
- **Load Balancing**: Kubernetes services with session affinity
- **Monitoring**: Prometheus metrics integration

## Kafka Topics

### Agent Communication Topics
```
agent-requests      (12 partitions) - Direct agent-to-agent requests
agent-responses     (12 partitions) - Agent response messages
agent-events        (12 partitions) - Agent lifecycle and status events
agent-heartbeat     (6 partitions)  - Agent health monitoring
```

### Reasoning and Decision Topics
```
reasoning-steps     (12 partitions) - Individual reasoning steps
decision-events     (6 partitions)  - Decision outcomes and rationale
knowledge-updates   (6 partitions)  - Knowledge base updates
```

### User Interaction Topics
```
user-queries        (12 partitions) - User input messages
user-responses      (12 partitions) - System responses to users
conversation-events (6 partitions)  - Conversation lifecycle events
```

### System Topics
```
system-metrics      (6 partitions)  - System performance metrics
error-events        (6 partitions)  - Error and exception tracking
```

## Deployment

### Quick Setup
```bash
# Deploy complete communication infrastructure
./scripts/communication-setup.sh
```

### Manual Deployment
```bash
# 1. Deploy Zookeeper and Kafka
kubectl apply -f infrastructure/kubernetes/deployments/kafka.yaml
kubectl apply -f infrastructure/kubernetes/services/kafka-services.yaml

# 2. Deploy Redis cluster
kubectl apply -f infrastructure/kubernetes/configmaps/communication-config.yaml
kubectl apply -f infrastructure/kubernetes/deployments/redis-cluster.yaml
kubectl apply -f infrastructure/kubernetes/services/redis-cluster-service.yaml

# 3. Setup topics and cluster
kubectl apply -f infrastructure/kubernetes/jobs/kafka-setup.yaml

# 4. Deploy agent services
kubectl apply -f infrastructure/kubernetes/configmaps/agent-gateway-code.yaml
kubectl apply -f infrastructure/kubernetes/deployments/agent-gateway.yaml
kubectl apply -f infrastructure/kubernetes/services/agent-services.yaml
```

## Connection Information

### Kafka Cluster
```
Bootstrap Servers: kafka-lb-service:9092
Security Protocol: PLAINTEXT
Acks: all
Retries: 3
Batch Size: 16384
```

### Redis Cluster
```
Cluster Nodes: redis-cluster-lb-service:6379
Mode: Cluster
Decode Responses: true
Skip Full Coverage Check: true
```

### Agent Services
```
Agent Gateway: agent-gateway-service:8080
Agent Registry: agent-registry-service:8082
```

## API Endpoints

### Agent Gateway API

#### Register Agent
```http
POST /agents/register
{
  "agent_id": "string",
  "agent_type": "string", 
  "capabilities": ["string"]
}
```

#### Send Message
```http
POST /messages/send
{
  "agent_id": "string",
  "agent_type": "string",
  "target_agent_id": "string",
  "message_type": "string",
  "content": {},
  "priority": 5,
  "timeout": 30
}
```

#### Get Response
```http
GET /messages/{correlation_id}/response?timeout=30
```

#### Agent Heartbeat
```http
POST /agents/{agent_id}/heartbeat
```

### Agent Registry API

#### Register Agent
```http
POST /agents/register
{
  "agent_id": "string",
  "agent_type": "string",
  "capabilities": ["string"],
  "configuration": {}
}
```

#### Update Status
```http
PUT /agents/{agent_id}/status
{
  "status": "string",
  "performance_metrics": {}
}
```

#### Get Agents by Type
```http
GET /agents/type/{agent_type}
```

## Message Flow Examples

### Agent-to-Agent Communication
```python
# Send request
message = {
    "agent_id": "coordinator_001",
    "agent_type": "coordinator",
    "target_agent_id": "hr_agent_001", 
    "message_type": "policy_query",
    "content": {
        "query": "What is the leave policy?",
        "user_id": "user_123",
        "organization_id": 1
    },
    "priority": 5,
    "timeout": 30
}

# Response handling
response = await gateway.send_message(message)
correlation_id = response["correlation_id"]
result = await gateway.get_response(correlation_id, timeout=30)
```

### Event Broadcasting
```python
# Broadcast event to all agents of type
event = {
    "agent_id": "system",
    "agent_type": "system",
    "message_type": "knowledge_update",
    "content": {
        "update_type": "policy_change",
        "affected_documents": ["hr_policy_001"],
        "timestamp": "2024-01-01T12:00:00Z"
    }
}
```

## Performance Characteristics

### Throughput
- **Kafka**: 100K+ messages/second per broker
- **Redis**: 100K+ operations/second per node
- **Gateway**: 10K+ requests/second per replica
- **Registry**: 5K+ operations/second per replica

### Latency
- **Message Routing**: <10ms p99
- **Cache Operations**: <1ms p99
- **Agent Discovery**: <5ms p99
- **End-to-End**: <50ms p99

### Scalability
- **Horizontal**: Add more Kafka brokers and Redis nodes
- **Vertical**: Increase resources per pod
- **Partitioning**: Automatic message distribution
- **Load Balancing**: Kubernetes service mesh

## Monitoring and Metrics

### Kafka Metrics
- **Throughput**: Messages per second per topic
- **Latency**: Producer and consumer latency
- **Lag**: Consumer group lag monitoring
- **Errors**: Failed message counts

### Redis Metrics
- **Memory Usage**: Per node memory utilization
- **Operations**: Commands per second
- **Connections**: Active client connections
- **Cluster Health**: Node status and failover events

### Agent Service Metrics
- **Message Count**: Total messages by type and agent
- **Response Time**: Request processing latency
- **Active Agents**: Number of registered agents by type
- **Error Rate**: Failed operations percentage

### Prometheus Integration
```yaml
# Scrape configuration
- job_name: 'agent-gateway'
  static_configs:
    - targets: ['agent-gateway-service:8080']
  metrics_path: /metrics

- job_name: 'agent-registry'  
  static_configs:
    - targets: ['agent-registry-service:8082']
  metrics_path: /metrics
```

## High Availability Features

### Kafka HA
- **Multi-Broker**: 3 brokers with leader election
- **Replication**: 3x replication factor
- **Partitioning**: Automatic partition distribution
- **Zookeeper**: 3-node ensemble for coordination

### Redis HA
- **Cluster Mode**: 6 nodes with automatic sharding
- **Replication**: Each master has 1 replica
- **Failover**: Automatic master promotion
- **Split-Brain Protection**: Cluster consensus

### Service HA
- **Multiple Replicas**: Gateway (3), Registry (2)
- **Load Balancing**: Kubernetes service distribution
- **Health Checks**: Liveness and readiness probes
- **Rolling Updates**: Zero-downtime deployments

## Security Considerations

### Network Security
- **Internal Only**: All services use ClusterIP
- **No External Access**: Communication within cluster only
- **Service Mesh**: Optional Istio integration

### Message Security
- **Serialization**: JSON with schema validation
- **Correlation IDs**: Message tracking and deduplication
- **Timeouts**: Prevent resource exhaustion
- **Rate Limiting**: Per-agent message limits

### Access Control
- **Agent Authentication**: Agent ID validation
- **Topic ACLs**: Kafka topic access control
- **Redis AUTH**: Optional password authentication
- **RBAC**: Kubernetes role-based access

## Troubleshooting

### Check Cluster Status
```bash
# Kafka cluster
kubectl exec kafka-0 -n chatbot-dev -- kafka-topics --bootstrap-server localhost:9092 --list

# Redis cluster
kubectl exec redis-cluster-0 -n chatbot-dev -- redis-cli cluster nodes

# Agent services
kubectl logs -l app=agent-gateway -n chatbot-dev
kubectl logs -l app=agent-registry -n chatbot-dev
```

### Performance Testing
```bash
# Kafka throughput test
kubectl exec kafka-0 -n chatbot-dev -- kafka-producer-perf-test --topic agent-requests --num-records 10000 --record-size 1024 --throughput 1000 --producer-props bootstrap.servers=localhost:9092

# Redis performance test
kubectl exec redis-cluster-0 -n chatbot-dev -- redis-cli --latency-history -i 1
```

### Common Issues

#### Kafka Issues
- **Topic Creation Fails**: Check Zookeeper connectivity
- **High Lag**: Increase consumer instances or partitions
- **Connection Errors**: Verify service DNS resolution

#### Redis Issues
- **Cluster Formation**: Ensure all 6 nodes are running
- **Memory Pressure**: Monitor memory usage and eviction
- **Network Partitions**: Check node connectivity

#### Agent Service Issues
- **Registration Failures**: Check PostgreSQL connectivity
- **Message Timeouts**: Verify Kafka and Redis health
- **High Latency**: Scale up replicas or resources

## Best Practices

### Message Design
- **Small Messages**: Keep message size under 1MB
- **Idempotent**: Design for message replay
- **Versioned**: Include schema version in messages
- **Correlation**: Always use correlation IDs

### Performance Optimization
- **Batching**: Batch small messages together
- **Compression**: Enable compression for large payloads
- **Partitioning**: Use appropriate partition keys
- **Caching**: Cache frequently accessed data

### Operational Excellence
- **Monitoring**: Comprehensive metrics and alerting
- **Logging**: Structured logging with correlation IDs
- **Testing**: Load testing and chaos engineering
- **Documentation**: Keep runbooks updated