apiVersion: v1
kind: Secret
metadata:
  name: chatbot-secrets
  namespace: chatbot-dev
type: Opaque
data:
  # Base64 encoded secrets (use: echo -n 'password' | base64)
  POSTGRES_USER: Y2hhdGJvdF91c2Vy  # chatbot_user
  POSTGRES_PASSWORD: Y2hhdGJvdF9wYXNz  # chatbot_pass
  NEO4J_USER: bmVvNGo=  # neo4j
  NEO4J_PASSWORD: Y2hhdGJvdF9wYXNz  # chatbot_pass
  SECRET_KEY: eW91ci1zZWNyZXQta2V5LWhlcmU=  # your-secret-key-here
  MEMGRAPH_USER: bWVtZ3JhcGg=  # memgraph
  MEMGRAPH_PASSWORD: Y2hhdGJvdF9wYXNz  # chatbot_pass