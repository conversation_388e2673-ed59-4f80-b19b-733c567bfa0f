apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chatbot-ingress
  namespace: chatbot-dev
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
spec:
  ingressClassName: nginx
  rules:
  - host: chatbot-dev.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chatbot-service
            port:
              number: 80
  - host: api.chatbot-dev.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chatbot-service
            port:
              number: 80