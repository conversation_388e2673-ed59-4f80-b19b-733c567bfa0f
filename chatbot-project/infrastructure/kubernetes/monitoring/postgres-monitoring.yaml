apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-exporter-config
  namespace: chatbot-dev
data:
  queries.yaml: |
    pg_database:
      query: "SELECT pg_database.datname, pg_database_size(pg_database.datname) as size FROM pg_database"
      master: true
      metrics:
        - datname:
            usage: "LABEL"
            description: "Name of the database"
        - size:
            usage: "GAUGE"
            description: "Disk space used by the database"
    
    pg_stat_user_tables:
      query: "SELECT schemaname, relname, seq_scan, seq_tup_read, idx_scan, idx_tup_fetch, n_tup_ins, n_tup_upd, n_tup_del FROM pg_stat_user_tables"
      master: true
      metrics:
        - schemaname:
            usage: "LABEL"
            description: "Name of the schema"
        - relname:
            usage: "LABEL"
            description: "Name of the table"
        - seq_scan:
            usage: "COUNTER"
            description: "Number of sequential scans"
        - seq_tup_read:
            usage: "COUNTER"
            description: "Number of tuples read by sequential scans"
        - idx_scan:
            usage: "COUNTER"
            description: "Number of index scans"
        - idx_tup_fetch:
            usage: "COUNTER"
            description: "Number of tuples fetched by index scans"
        - n_tup_ins:
            usage: "COUNTER"
            description: "Number of tuples inserted"
        - n_tup_upd:
            usage: "COUNTER"
            description: "Number of tuples updated"
        - n_tup_del:
            usage: "COUNTER"
            description: "Number of tuples deleted"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-exporter
  namespace: chatbot-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-exporter
  template:
    metadata:
      labels:
        app: postgres-exporter
    spec:
      containers:
      - name: postgres-exporter
        image: prometheuscommunity/postgres-exporter:latest
        ports:
        - containerPort: 9187
        env:
        - name: DATA_SOURCE_NAME
          value: "********************************************************************/chatbot_db?sslmode=disable"
        - name: PG_EXPORTER_EXTEND_QUERY_PATH
          value: "/etc/postgres_exporter/queries.yaml"
        volumeMounts:
        - name: queries
          mountPath: /etc/postgres_exporter
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: queries
        configMap:
          name: postgres-exporter-config
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-exporter-service
  namespace: chatbot-dev
  labels:
    app: postgres-exporter
spec:
  selector:
    app: postgres-exporter
  ports:
  - port: 9187
    targetPort: 9187
  type: ClusterIP