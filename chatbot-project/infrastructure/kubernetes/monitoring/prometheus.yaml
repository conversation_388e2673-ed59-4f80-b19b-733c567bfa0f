apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: chatbot-dev
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager-service:9093
    
    scrape_configs:
    - job_name: 'prometheus'
      static_configs:
        - targets: ['localhost:9090']
    
    - job_name: 'milvus'
      static_configs:
        - targets: ['milvus-service:9091']
      metrics_path: /metrics
      scrape_interval: 30s
    
    - job_name: 'postgres-exporter'
      static_configs:
        - targets: ['postgres-exporter-service:9187']
      scrape_interval: 30s
    
    - job_name: 'neo4j-metrics'
      static_configs:
        - targets: ['neo4j-write-service:2004']
      metrics_path: /metrics
      scrape_interval: 30s
    
    - job_name: 'memgraph-monitoring'
      static_configs:
        - targets: ['memgraph-lb-service:7444']
      metrics_path: /metrics
      scrape_interval: 30s
    
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - chatbot-dev
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
    
    - job_name: 'kubernetes-nodes'
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        regex: (.+)
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics
    
    - job_name: 'opensearch'
      static_configs:
        - targets: ['opensearch-lb-service:9200']
      metrics_path: /_prometheus/metrics
      scrape_interval: 30s
    
    - job_name: 'fluent-bit'
      static_configs:
        - targets: ['fluent-bit-service:2020']
      metrics_path: /api/v1/metrics/prometheus
      scrape_interval: 30s
    
    - job_name: 'agent-metrics'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - chatbot-dev
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_agent_metrics_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_agent_metrics_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_agent_metrics_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
  
  alert_rules.yml: |
    groups:
    - name: database_alerts
      rules:
      - alert: PostgreSQLDown
        expr: up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL has been down for more than 1 minute"
      
      - alert: MilvusDown
        expr: up{job="milvus"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Milvus is down"
          description: "Milvus vector database has been down for more than 1 minute"
      
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 90% for more than 5 minutes"
      
      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is above 85% for more than 5 minutes"
      
      - alert: OpenSearchDown
        expr: up{job="opensearch"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "OpenSearch is down"
          description: "OpenSearch log aggregation service has been down for more than 1 minute"
      
      - alert: AgentHighErrorRate
        expr: rate(agent_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High agent error rate detected"
          description: "Agent error rate is above 10% for more than 2 minutes"
      
      - alert: AgentResponseTimeHigh
        expr: histogram_quantile(0.95, rate(agent_response_time_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High agent response time"
          description: "95th percentile agent response time is above 5 seconds"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: chatbot-dev
  labels:
    app: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:v2.48.0
        ports:
        - containerPort: 9090
        args:
        - '--config.file=/etc/prometheus/prometheus.yml'
        - '--storage.tsdb.path=/prometheus'
        - '--web.console.libraries=/etc/prometheus/console_libraries'
        - '--web.console.templates=/etc/prometheus/consoles'
        - '--storage.tsdb.retention.time=30d'
        - '--web.enable-lifecycle'
        - '--web.enable-admin-api'
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-storage
          mountPath: /prometheus
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 15
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-storage-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage-pvc
  namespace: chatbot-dev
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: chatbot-dev
  labels:
    app: prometheus
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090
  type: ClusterIP