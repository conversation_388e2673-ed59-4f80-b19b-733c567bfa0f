apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: chatbot-dev
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus-service:9090
      isDefault: true
      editable: true
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards-config
  namespace: chatbot-dev
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-chatbot
  namespace: chatbot-dev
data:
  chatbot-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Chatbot System Overview",
        "tags": ["chatbot", "overview"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Database Status",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=~\"postgres-exporter|milvus|neo4j-metrics|memgraph-monitoring\"}",
                "legendFormat": "{{job}}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes",
                "legendFormat": "Used Memory"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Vector Database Operations",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(milvus_proxy_search_vectors_count[5m])",
                "legendFormat": "Search Operations/sec"
              },
              {
                "expr": "rate(milvus_proxy_insert_vectors_count[5m])",
                "legendFormat": "Insert Operations/sec"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Agent Performance",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(agent_requests_total[5m])",
                "legendFormat": "Requests/sec"
              },
              {
                "expr": "rate(agent_errors_total[5m])",
                "legendFormat": "Errors/sec"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          },
          {
            "id": 5,
            "title": "Log Volume",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(fluentbit_output_records_total[5m])",
                "legendFormat": "Logs/sec"
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "30s",
        "templating": {
          "list": [
            {
              "name": "agent_type",
              "type": "query",
              "query": "label_values(agent_requests_total, agent_type)",
              "refresh": 1,
              "includeAll": true,
              "multi": true
            }
          ]
        }
      }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: chatbot-dev
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:10.2.2
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GF_INSTALL_PLUGINS
          value: "grafana-piechart-panel,grafana-worldmap-panel"
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
        - name: grafana-dashboards-config
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage-pvc
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-dashboards-config
        configMap:
          name: grafana-dashboards-config
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboard-chatbot
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage-pvc
  namespace: chatbot-dev
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: chatbot-dev
  labels:
    app: grafana
spec:
  selector:
    app: grafana
  ports:
  - port: 3000
    targetPort: 3000
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: chatbot-dev
type: Opaque
data:
  # Base64 encoded: admin123
  admin-password: YWRtaW4xMjM=