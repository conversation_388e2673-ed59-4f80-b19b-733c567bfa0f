{"dashboard": {"id": null, "title": "CHaBot System Dashboard", "tags": ["chabot", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "API Gateway Requests", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"api-gateway\"}[5m])", "legendFormat": "{{method}} {{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Chat Service Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"chat-service\"}[5m]))", "legendFormat": "95th percentile"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Agent Service Load", "type": "graph", "targets": [{"expr": "agent_load{job=\"agent-service\"}", "legendFormat": "{{agent_id}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Database Connections", "type": "graph", "targets": [{"expr": "pg_stat_database_numbackends{job=\"postgres\"}", "legendFormat": "{{datname}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}