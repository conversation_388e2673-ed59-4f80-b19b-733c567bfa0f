global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'api-gateway'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - chabot
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: api-gateway

  - job_name: 'chat-service'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - chabot
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: chat-service

  - job_name: 'agent-service'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - chabot
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: agent-service

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']