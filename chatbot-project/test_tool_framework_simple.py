#!/usr/bin/env python3
"""
Simplified Test Suite for the Tool Integration Framework.
Tests core functionality without complex sandbox execution.
"""

import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import tool integration components
from agents.tools.calculator.basic_calculator import (
    EnhancedCalculator, calculate_expression, add_numbers, calculate_statistics
)
from agents.tools.database.enhanced_db_tool import (
    EnhancedDatabaseTool, create_database_table, insert_database_data
)
from agents.tools.creation.enhanced_dynamic_tool_creator import (
    EnhancedDynamicToolCreator, create_tool_from_code, create_simple_calculator
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleToolFrameworkTest:
    """Simplified test suite for core tool functionality."""
    
    def __init__(self):
        self.test_results = {}
        
        # Initialize components
        self.calculator = EnhancedCalculator()
        self.db_tool = EnhancedDatabaseTool()
        self.tool_creator = EnhancedDynamicToolCreator()
    
    def run_all_tests(self):
        """Run all simplified tool tests."""
        logger.info("🚀 Starting Simplified Tool Framework Tests")
        logger.info("=" * 80)
        
        tests = [
            ("Calculator Tools", self.test_calculator_tools),
            ("Database Tools", self.test_database_tools),
            ("Dynamic Tool Creation", self.test_dynamic_tool_creation),
            ("Tool Integration", self.test_tool_integration)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running {test_name} test...")
            try:
                result = test_func()
                if result:
                    logger.info(f"✅ PASSED: {test_name}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ FAILED: {test_name}")
                self.test_results[test_name] = result
            except Exception as e:
                logger.error(f"❌ FAILED: {test_name} - {e}")
                self.test_results[test_name] = False
        
        # Print summary
        logger.info("\n" + "=" * 80)
        logger.info("🎯 SIMPLIFIED TOOL FRAMEWORK TEST SUMMARY")
        logger.info("=" * 80)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if passed_tests == total_tests:
            logger.info("🎉 All core Tool Framework components are working!")
            logger.info("🚀 Ready for integration!")
        else:
            logger.warning("⚠️ Some tests failed. Please review the issues above.")
        
        return passed_tests == total_tests
    
    def test_calculator_tools(self) -> bool:
        """Test Calculator Tools functionality."""
        try:
            logger.info("🧮 Testing Calculator Tools...")
            
            # Test basic operations
            operations = [
                ("add", (10, 5), 15),
                ("subtract", (10, 5), 5),
                ("multiply", (10, 5), 50),
                ("divide", (10, 5), 2.0),
                ("power", (2, 3), 8)
            ]
            
            for op_name, args, expected in operations:
                method = getattr(self.calculator, op_name)
                result = method(*args)
                
                if result.result != expected:
                    logger.error(f"Calculator {op_name} failed: expected {expected}, got {result.result}")
                    return False
                
                logger.info(f"    ✅ {op_name}: {args[0]} {op_name} {args[1]} = {result.result}")
            
            # Test expression calculation
            expressions = [
                ("2 + 3 * 4", 14),
                ("5 * 5", 25),
                ("10 / 2", 5.0),
                ("2 ** 3", 8)
            ]
            
            for expr, expected in expressions:
                result = self.calculator.calculate(expr)
                
                if abs(result.result - expected) > 0.0001:
                    logger.error(f"Expression calculation failed: {expr} = {result.result}, expected {expected}")
                    return False
                
                logger.info(f"    ✅ Expression: {expr} = {result.result}")
            
            # Test statistical calculations
            numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
            stats_result = self.calculator.statistics_calculate(numbers, "mean")
            
            if stats_result.result != 5.5:
                logger.error(f"Statistics calculation failed: expected 5.5, got {stats_result.result}")
                return False
            
            logger.info(f"    ✅ Statistics: mean of {numbers} = {stats_result.result}")
            
            # Test tool functions
            tool_result = calculate_expression("5 + 3 * 2")
            
            if not tool_result["success"] or tool_result["result"] != 11:
                logger.error("Calculator tool function failed")
                return False
            
            logger.info("✅ Calculator tool functions working")
            
            logger.info("✅ Calculator Tools test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Calculator tools test failed: {e}")
            return False
    
    def test_database_tools(self) -> bool:
        """Test Database Tools functionality."""
        try:
            logger.info("🗄️ Testing Database Tools...")
            
            # Test table creation
            table_result = self.db_tool.create_table(
                "test_table",
                {
                    "id": "INTEGER PRIMARY KEY",
                    "name": "TEXT NOT NULL",
                    "value": "REAL"
                }
            )
            
            if not table_result.success:
                logger.error(f"Table creation failed: {table_result.error}")
                return False
            
            logger.info("✅ Table creation successful")
            
            # Test data insertion
            test_data = [
                {"name": "item1", "value": 10.5},
                {"name": "item2", "value": 20.3},
                {"name": "item3", "value": 15.7}
            ]
            
            insert_result = self.db_tool.insert_data("test_table", test_data)
            
            if not insert_result.success or insert_result.affected_rows != 3:
                logger.error(f"Data insertion failed: {insert_result.error}")
                return False
            
            logger.info(f"✅ Data insertion successful: {insert_result.affected_rows} rows")
            
            # Test data selection
            select_result = self.db_tool.select_data("test_table")
            
            if not select_result.success or len(select_result.results) != 3:
                logger.error(f"Data selection failed: {select_result.error}")
                return False
            
            logger.info(f"✅ Data selection successful: {len(select_result.results)} rows")
            
            # Test filtered selection
            filtered_result = self.db_tool.select_data(
                "test_table",
                conditions={"name": "item1"}
            )
            
            if not filtered_result.success or len(filtered_result.results) != 1:
                logger.error("Filtered selection failed")
                return False
            
            logger.info("✅ Filtered selection successful")
            
            # Test statistics
            stats = self.db_tool.get_statistics()
            logger.info(f"✅ Database statistics: {stats['total_queries']} queries, "
                       f"success rate: {stats['success_rate']:.2f}")
            
            logger.info("✅ Database Tools test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database tools test failed: {e}")
            return False
        finally:
            self.db_tool.disconnect()
    
    def test_dynamic_tool_creation(self) -> bool:
        """Test Dynamic Tool Creation functionality."""
        try:
            logger.info("🔧 Testing Dynamic Tool Creation...")
            
            # Test tool creation from code
            test_code = """
def multiply_numbers(a: int, b: int) -> int:
    \"\"\"Multiply two numbers.\"\"\"
    return a * b
"""
            
            creation_result = self.tool_creator.create_tool_from_code(
                code=test_code,
                name="multiply_tool",
                description="Tool to multiply two numbers"
            )
            
            if not creation_result["success"]:
                logger.error(f"Tool creation from code failed: {creation_result['error']}")
                return False
            
            logger.info("✅ Tool creation from code successful")
            
            # Test direct tool execution (without sandbox)
            created_tool = self.tool_creator.get_tool("multiply_tool")
            if created_tool:
                direct_result = created_tool.function(6, 7)
                if direct_result != 42:
                    logger.error(f"Direct tool execution failed: expected 42, got {direct_result}")
                    return False
                
                logger.info(f"✅ Direct tool execution successful: 6 * 7 = {direct_result}")
            
            # Test tool creation from function
            def divide_numbers(a: float, b: float) -> float:
                """Divide two numbers."""
                if b == 0:
                    raise ValueError("Cannot divide by zero")
                return a / b
            
            func_creation_result = self.tool_creator.create_tool_from_function(
                function=divide_numbers,
                name="divide_tool",
                description="Tool to divide two numbers"
            )
            
            if not func_creation_result["success"]:
                logger.error("Tool creation from function failed")
                return False
            
            logger.info("✅ Tool creation from function successful")
            
            # Test function-based tool direct execution
            divide_tool = self.tool_creator.get_tool("divide_tool")
            if divide_tool:
                divide_result = divide_tool.function(20.0, 4.0)
                if divide_result != 5.0:
                    logger.error(f"Function-based tool execution failed: expected 5.0, got {divide_result}")
                    return False
                
                logger.info(f"✅ Function-based tool execution successful: 20 / 4 = {divide_result}")
            
            # Test tool listing
            created_tools = self.tool_creator.list_created_tools()
            
            if len(created_tools) != 2:
                logger.error(f"Expected 2 created tools, got {len(created_tools)}")
                return False
            
            logger.info(f"✅ Tool listing successful: {len(created_tools)} tools created")
            
            # Test tool functions
            tool_result = create_tool_from_code(
                "def add_three(a, b, c): return a + b + c",
                "add_three_tool",
                "Add three numbers"
            )
            
            if not tool_result["success"]:
                logger.error("Dynamic tool creation function failed")
                return False
            
            logger.info("✅ Dynamic tool creation functions working")
            
            # Test statistics
            stats = self.tool_creator.get_creation_statistics()
            logger.info(f"✅ Creation statistics: {stats['total_creations']} creations, "
                       f"success rate: {stats['success_rate']:.2f}")
            
            logger.info("✅ Dynamic Tool Creation test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Dynamic tool creation test failed: {e}")
            return False
    
    def test_tool_integration(self) -> bool:
        """Test integration between different tool components."""
        try:
            logger.info("🔄 Testing Tool Integration...")
            
            # Step 1: Create a complex tool that uses multiple components
            complex_tool_code = """
def data_analysis_tool(numbers: list, operation: str = "summary") -> dict:
    \"\"\"Analyze a list of numbers with various operations.\"\"\"
    import statistics
    
    if not numbers:
        return {"error": "No data provided"}
    
    result = {
        "input_size": len(numbers),
        "operation": operation
    }
    
    if operation == "summary":
        result.update({
            "sum": sum(numbers),
            "mean": statistics.mean(numbers),
            "median": statistics.median(numbers),
            "min": min(numbers),
            "max": max(numbers)
        })
    elif operation == "variance":
        if len(numbers) > 1:
            result["variance"] = statistics.variance(numbers)
            result["stdev"] = statistics.stdev(numbers)
        else:
            result["error"] = "Need at least 2 values for variance"
    else:
        result["error"] = f"Unknown operation: {operation}"
    
    return result
"""
            
            # Create the complex tool
            complex_creation = self.tool_creator.create_tool_from_code(
                code=complex_tool_code,
                name="data_analysis",
                description="Comprehensive data analysis tool"
            )
            
            if not complex_creation["success"]:
                logger.error("Complex tool creation failed")
                return False
            
            logger.info("✅ Complex tool created successfully")
            
            # Step 2: Test the complex tool
            data_tool = self.tool_creator.get_tool("data_analysis")
            test_data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
            
            summary_result = data_tool.function(test_data, "summary")
            
            expected_mean = 5.5
            if summary_result["mean"] != expected_mean:
                logger.error(f"Complex tool failed: expected mean {expected_mean}, got {summary_result['mean']}")
                return False
            
            logger.info(f"✅ Complex tool execution successful: mean = {summary_result['mean']}")
            
            # Step 3: Store results in database
            analysis_data = {
                "operation": summary_result["operation"],
                "input_size": summary_result["input_size"],
                "result_mean": summary_result["mean"],
                "result_sum": summary_result["sum"]
            }
            
            # Create analysis results table
            analysis_table = self.db_tool.create_table(
                "analysis_results",
                {
                    "id": "INTEGER PRIMARY KEY",
                    "operation": "TEXT",
                    "input_size": "INTEGER",
                    "result_mean": "REAL",
                    "result_sum": "REAL"
                }
            )
            
            if not analysis_table.success:
                logger.error("Analysis table creation failed")
                return False
            
            # Insert analysis results
            insert_analysis = self.db_tool.insert_data("analysis_results", analysis_data)
            
            if not insert_analysis.success:
                logger.error("Analysis data insertion failed")
                return False
            
            logger.info("✅ Analysis results stored in database")
            
            # Step 4: Verify integration
            stored_results = self.db_tool.select_data("analysis_results")
            
            if not stored_results.success or len(stored_results.results) != 1:
                logger.error("Failed to retrieve stored analysis results")
                return False
            
            stored_mean = stored_results.results[0]["result_mean"]
            if stored_mean != expected_mean:
                logger.error(f"Stored data mismatch: expected {expected_mean}, got {stored_mean}")
                return False
            
            logger.info(f"✅ Data integration verified: stored mean = {stored_mean}")
            
            logger.info("✅ Tool Integration test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Tool integration test failed: {e}")
            return False

def main():
    """Main test execution function."""
    test_suite = SimpleToolFrameworkTest()
    
    try:
        success = test_suite.run_all_tests()
        
        # Save test results
        results_file = "simple_tool_framework_test_results.json"
        with open(results_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "test_results": {k: v for k, v in test_suite.test_results.items()},
                "overall_success": success,
                "summary": f"{sum(test_suite.test_results.values())}/{len(test_suite.test_results)} tests passed"
            }, f, indent=2)
        
        logger.info(f"📄 Detailed results saved to {results_file}")
        
        return success
        
    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return False

if __name__ == "__main__":
    # Run the simplified test suite
    success = main()
    sys.exit(0 if success else 1)
