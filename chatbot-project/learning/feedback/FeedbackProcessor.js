class FeedbackProcessor {
    constructor() {
        this.feedbackQueue = [];
        this.classificationRules = new Map();
        this.improvementMechanisms = new Map();
        this.processingStats = new Map();
        this.initializeProcessor();
    }

    // Initialize feedback processor
    initializeProcessor() {
        this.setupClassificationRules();
        this.setupImprovementMechanisms();
        this.initializeStats();
    }

    // Set up classification rules
    setupClassificationRules() {
        const rules = {
            accuracy: {
                name: 'Response Accuracy',
                keywords: ['wrong', 'incorrect', 'mistake', 'error', 'inaccurate'],
                positiveKeywords: ['correct', 'accurate', 'right', 'precise'],
                weight: 0.9,
                category: 'content_quality'
            },
            relevance: {
                name: 'Response Relevance',
                keywords: ['irrelevant', 'off-topic', 'unrelated', 'not helpful'],
                positiveKeywords: ['relevant', 'helpful', 'on-topic', 'useful'],
                weight: 0.8,
                category: 'content_quality'
            },
            completeness: {
                name: 'Response Completeness',
                keywords: ['incomplete', 'missing', 'partial', 'not enough'],
                positiveKeywords: ['complete', 'thorough', 'comprehensive', 'detailed'],
                weight: 0.7,
                category: 'content_quality'
            },
            clarity: {
                name: 'Response Clarity',
                keywords: ['confusing', 'unclear', 'hard to understand', 'complex'],
                positiveKeywords: ['clear', 'easy', 'understandable', 'simple'],
                weight: 0.8,
                category: 'presentation'
            },
            speed: {
                name: 'Response Speed',
                keywords: ['slow', 'delayed', 'taking too long', 'timeout'],
                positiveKeywords: ['fast', 'quick', 'immediate', 'responsive'],
                weight: 0.6,
                category: 'performance'
            },
            reasoning: {
                name: 'Reasoning Quality',
                keywords: ['illogical', 'doesn\'t make sense', 'poor reasoning'],
                positiveKeywords: ['logical', 'well-reasoned', 'makes sense', 'good explanation'],
                weight: 0.9,
                category: 'reasoning'
            }
        };

        Object.entries(rules).forEach(([id, rule]) => {
            this.classificationRules.set(id, {
                ...rule,
                id,
                matches: 0,
                lastUsed: null
            });
        });
    }

    // Set up improvement mechanisms
    setupImprovementMechanisms() {
        const mechanisms = {
            content_quality: {
                name: 'Content Quality Improvement',
                actions: ['update_knowledge_base', 'refine_responses', 'add_examples'],
                priority: 'high',
                threshold: 3 // Trigger after 3 negative feedbacks
            },
            presentation: {
                name: 'Presentation Improvement',
                actions: ['simplify_language', 'improve_formatting', 'add_structure'],
                priority: 'medium',
                threshold: 5
            },
            performance: {
                name: 'Performance Improvement',
                actions: ['optimize_processing', 'cache_responses', 'parallel_execution'],
                priority: 'high',
                threshold: 2
            },
            reasoning: {
                name: 'Reasoning Improvement',
                actions: ['enhance_logic_chains', 'add_validation_steps', 'improve_explanations'],
                priority: 'critical',
                threshold: 2
            }
        };

        Object.entries(mechanisms).forEach(([id, mechanism]) => {
            this.improvementMechanisms.set(id, {
                ...mechanism,
                id,
                triggers: 0,
                lastTriggered: null,
                improvements: []
            });
        });
    }

    // Initialize processing statistics
    initializeStats() {
        this.processingStats.set('total_processed', 0);
        this.processingStats.set('positive_feedback', 0);
        this.processingStats.set('negative_feedback', 0);
        this.processingStats.set('neutral_feedback', 0);
        this.processingStats.set('improvements_triggered', 0);
    }

    // Create structured feedback collection
    async createStructuredFeedbackCollection(rawFeedback) {
        const structuredFeedback = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            source: rawFeedback.source || 'user',
            type: rawFeedback.type || 'general',
            content: rawFeedback.content || rawFeedback.text,
            rating: rawFeedback.rating || null,
            context: {
                sessionId: rawFeedback.sessionId,
                queryId: rawFeedback.queryId,
                responseId: rawFeedback.responseId,
                userRole: rawFeedback.userRole || 'employee'
            },
            metadata: {
                processed: false,
                classification: null,
                sentiment: null,
                priority: 'normal'
            }
        };

        // Add to processing queue
        this.feedbackQueue.push(structuredFeedback);
        
        return structuredFeedback;
    }

    // Implement feedback classification
    async implementFeedbackClassification(feedback) {
        const classification = {
            feedbackId: feedback.id,
            categories: [],
            sentiment: 'neutral',
            confidence: 0,
            issues: [],
            strengths: []
        };

        const content = feedback.content.toLowerCase();
        
        // Apply classification rules
        for (const [ruleId, rule] of this.classificationRules) {
            const negativeMatches = rule.keywords.filter(keyword => 
                content.includes(keyword.toLowerCase())
            ).length;
            
            const positiveMatches = rule.positiveKeywords.filter(keyword => 
                content.includes(keyword.toLowerCase())
            ).length;

            if (negativeMatches > 0 || positiveMatches > 0) {
                const score = positiveMatches - negativeMatches;
                const category = {
                    rule: ruleId,
                    name: rule.name,
                    category: rule.category,
                    score,
                    confidence: Math.min(0.9, (negativeMatches + positiveMatches) * 0.3),
                    weight: rule.weight
                };

                classification.categories.push(category);
                
                if (score < 0) {
                    classification.issues.push({
                        type: ruleId,
                        severity: Math.abs(score) * rule.weight,
                        description: `Issue with ${rule.name.toLowerCase()}`
                    });
                } else if (score > 0) {
                    classification.strengths.push({
                        type: ruleId,
                        strength: score * rule.weight,
                        description: `Strength in ${rule.name.toLowerCase()}`
                    });
                }

                // Update rule statistics
                rule.matches++;
                rule.lastUsed = new Date().toISOString();
            }
        }

        // Calculate overall sentiment
        if (classification.categories.length > 0) {
            const weightedScore = classification.categories.reduce((sum, cat) => 
                sum + (cat.score * cat.weight), 0
            );
            const totalWeight = classification.categories.reduce((sum, cat) => 
                sum + cat.weight, 0
            );
            
            const normalizedScore = weightedScore / totalWeight;
            
            if (normalizedScore > 0.2) {
                classification.sentiment = 'positive';
            } else if (normalizedScore < -0.2) {
                classification.sentiment = 'negative';
            }
            
            classification.confidence = Math.min(0.95, Math.abs(normalizedScore));
        }

        // Consider rating if provided
        if (feedback.rating !== null) {
            if (feedback.rating >= 4) {
                classification.sentiment = 'positive';
            } else if (feedback.rating <= 2) {
                classification.sentiment = 'negative';
            }
            classification.confidence = Math.max(classification.confidence, 0.8);
        }

        return classification;
    }

    // Build targeted improvement mechanisms
    async buildTargetedImprovementMechanisms(feedback, classification) {
        const improvements = [];
        
        // Group issues by category
        const issuesByCategory = {};
        classification.issues.forEach(issue => {
            const rule = this.classificationRules.get(issue.type);
            const category = rule.category;
            
            if (!issuesByCategory[category]) {
                issuesByCategory[category] = [];
            }
            issuesByCategory[category].push(issue);
        });

        // Trigger improvements for each category
        for (const [category, issues] of Object.entries(issuesByCategory)) {
            const mechanism = this.improvementMechanisms.get(category);
            if (!mechanism) continue;

            mechanism.triggers++;
            
            // Check if threshold is reached
            if (mechanism.triggers >= mechanism.threshold) {
                const improvement = await this.triggerImprovement(mechanism, issues, feedback);
                improvements.push(improvement);
                
                // Reset trigger count
                mechanism.triggers = 0;
                mechanism.lastTriggered = new Date().toISOString();
                mechanism.improvements.push(improvement);
                
                this.processingStats.set('improvements_triggered', 
                    this.processingStats.get('improvements_triggered') + 1
                );
            }
        }

        return improvements;
    }

    // Trigger specific improvement
    async triggerImprovement(mechanism, issues, feedback) {
        const improvement = {
            id: Date.now(),
            mechanism: mechanism.id,
            name: mechanism.name,
            priority: mechanism.priority,
            issues: issues.map(i => i.type),
            actions: [],
            status: 'planned',
            timestamp: new Date().toISOString()
        };

        // Select appropriate actions based on issues
        for (const action of mechanism.actions) {
            const actionPlan = await this.planImprovementAction(action, issues, feedback);
            improvement.actions.push(actionPlan);
        }

        return improvement;
    }

    // Plan improvement action
    async planImprovementAction(action, issues, feedback) {
        const actionPlan = {
            action,
            description: this.getActionDescription(action),
            priority: this.calculateActionPriority(action, issues),
            estimatedImpact: this.estimateActionImpact(action, issues),
            resources: this.estimateActionResources(action),
            timeline: this.estimateActionTimeline(action)
        };

        return actionPlan;
    }

    // Get action description
    getActionDescription(action) {
        const descriptions = {
            'update_knowledge_base': 'Update knowledge base with correct information',
            'refine_responses': 'Refine response templates and patterns',
            'add_examples': 'Add more examples and use cases',
            'simplify_language': 'Simplify language and reduce complexity',
            'improve_formatting': 'Improve response formatting and structure',
            'add_structure': 'Add better organization to responses',
            'optimize_processing': 'Optimize processing algorithms',
            'cache_responses': 'Implement response caching',
            'parallel_execution': 'Enable parallel processing',
            'enhance_logic_chains': 'Improve logical reasoning chains',
            'add_validation_steps': 'Add validation to reasoning steps',
            'improve_explanations': 'Enhance explanation quality'
        };

        return descriptions[action] || `Execute ${action}`;
    }

    // Calculate action priority
    calculateActionPriority(action, issues) {
        const severitySum = issues.reduce((sum, issue) => sum + issue.severity, 0);
        const avgSeverity = severitySum / issues.length;
        
        if (avgSeverity > 0.8) return 'critical';
        if (avgSeverity > 0.6) return 'high';
        if (avgSeverity > 0.4) return 'medium';
        return 'low';
    }

    // Estimate action impact
    estimateActionImpact(action, issues) {
        const impactMap = {
            'update_knowledge_base': 0.8,
            'refine_responses': 0.7,
            'add_examples': 0.6,
            'simplify_language': 0.5,
            'improve_formatting': 0.4,
            'add_structure': 0.5,
            'optimize_processing': 0.9,
            'cache_responses': 0.7,
            'parallel_execution': 0.8,
            'enhance_logic_chains': 0.9,
            'add_validation_steps': 0.8,
            'improve_explanations': 0.7
        };

        return impactMap[action] || 0.5;
    }

    // Estimate action resources
    estimateActionResources(action) {
        const resourceMap = {
            'update_knowledge_base': { time: 'high', complexity: 'medium' },
            'refine_responses': { time: 'medium', complexity: 'medium' },
            'add_examples': { time: 'low', complexity: 'low' },
            'simplify_language': { time: 'medium', complexity: 'low' },
            'improve_formatting': { time: 'low', complexity: 'low' },
            'add_structure': { time: 'medium', complexity: 'medium' },
            'optimize_processing': { time: 'high', complexity: 'high' },
            'cache_responses': { time: 'medium', complexity: 'medium' },
            'parallel_execution': { time: 'high', complexity: 'high' },
            'enhance_logic_chains': { time: 'high', complexity: 'high' },
            'add_validation_steps': { time: 'medium', complexity: 'medium' },
            'improve_explanations': { time: 'medium', complexity: 'medium' }
        };

        return resourceMap[action] || { time: 'medium', complexity: 'medium' };
    }

    // Estimate action timeline
    estimateActionTimeline(action) {
        const timelineMap = {
            'update_knowledge_base': '1-2 weeks',
            'refine_responses': '3-5 days',
            'add_examples': '1-2 days',
            'simplify_language': '2-3 days',
            'improve_formatting': '1 day',
            'add_structure': '2-3 days',
            'optimize_processing': '1-2 weeks',
            'cache_responses': '3-5 days',
            'parallel_execution': '1-2 weeks',
            'enhance_logic_chains': '1-2 weeks',
            'add_validation_steps': '3-5 days',
            'improve_explanations': '3-5 days'
        };

        return timelineMap[action] || '3-5 days';
    }

    // Process feedback pipeline
    async processFeedbackPipeline(rawFeedback) {
        try {
            // Step 1: Structure feedback
            const structuredFeedback = await this.createStructuredFeedbackCollection(rawFeedback);
            
            // Step 2: Classify feedback
            const classification = await this.implementFeedbackClassification(structuredFeedback);
            
            // Step 3: Build improvements
            const improvements = await this.buildTargetedImprovementMechanisms(structuredFeedback, classification);
            
            // Update feedback with results
            structuredFeedback.metadata.processed = true;
            structuredFeedback.metadata.classification = classification;
            structuredFeedback.metadata.priority = this.calculateFeedbackPriority(classification);
            
            // Update statistics
            this.updateProcessingStats(classification);
            
            return {
                feedback: structuredFeedback,
                classification,
                improvements,
                processed: true,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            return {
                feedback: rawFeedback,
                error: error.message,
                processed: false,
                timestamp: new Date().toISOString()
            };
        }
    }

    // Calculate feedback priority
    calculateFeedbackPriority(classification) {
        if (classification.issues.length === 0) return 'low';
        
        const maxSeverity = Math.max(...classification.issues.map(i => i.severity));
        
        if (maxSeverity > 0.8) return 'critical';
        if (maxSeverity > 0.6) return 'high';
        if (maxSeverity > 0.4) return 'medium';
        return 'low';
    }

    // Update processing statistics
    updateProcessingStats(classification) {
        this.processingStats.set('total_processed', 
            this.processingStats.get('total_processed') + 1
        );

        switch (classification.sentiment) {
            case 'positive':
                this.processingStats.set('positive_feedback', 
                    this.processingStats.get('positive_feedback') + 1
                );
                break;
            case 'negative':
                this.processingStats.set('negative_feedback', 
                    this.processingStats.get('negative_feedback') + 1
                );
                break;
            default:
                this.processingStats.set('neutral_feedback', 
                    this.processingStats.get('neutral_feedback') + 1
                );
        }
    }

    // Get processing statistics
    getProcessingStats() {
        return {
            stats: Object.fromEntries(this.processingStats),
            classificationRules: Array.from(this.classificationRules.values()).map(rule => ({
                id: rule.id,
                name: rule.name,
                matches: rule.matches,
                category: rule.category
            })),
            improvementMechanisms: Array.from(this.improvementMechanisms.values()).map(mechanism => ({
                id: mechanism.id,
                name: mechanism.name,
                triggers: mechanism.triggers,
                improvements: mechanism.improvements.length
            })),
            queueSize: this.feedbackQueue.length
        };
    }

    // Generate feedback processing report
    generateProcessingReport() {
        const stats = this.getProcessingStats();
        const totalProcessed = stats.stats.total_processed;
        
        return {
            summary: {
                totalProcessed,
                positiveRate: totalProcessed > 0 ? (stats.stats.positive_feedback / totalProcessed) * 100 : 0,
                negativeRate: totalProcessed > 0 ? (stats.stats.negative_feedback / totalProcessed) * 100 : 0,
                improvementsTriggered: stats.stats.improvements_triggered
            },
            classification: {
                rules: stats.classificationRules,
                mostActiveRule: this.findMostActiveRule(),
                categoryBreakdown: this.getCategoryBreakdown()
            },
            improvements: {
                mechanisms: stats.improvementMechanisms,
                mostTriggeredMechanism: this.findMostTriggeredMechanism(),
                totalImprovements: this.getTotalImprovements()
            },
            recommendations: this.generateProcessingRecommendations(stats)
        };
    }

    // Find most active classification rule
    findMostActiveRule() {
        let mostActive = null;
        let maxMatches = 0;

        for (const rule of this.classificationRules.values()) {
            if (rule.matches > maxMatches) {
                maxMatches = rule.matches;
                mostActive = rule;
            }
        }

        return mostActive ? { id: mostActive.id, name: mostActive.name, matches: mostActive.matches } : null;
    }

    // Get category breakdown
    getCategoryBreakdown() {
        const breakdown = {};
        
        for (const rule of this.classificationRules.values()) {
            if (!breakdown[rule.category]) {
                breakdown[rule.category] = { rules: 0, matches: 0 };
            }
            breakdown[rule.category].rules++;
            breakdown[rule.category].matches += rule.matches;
        }

        return breakdown;
    }

    // Find most triggered improvement mechanism
    findMostTriggeredMechanism() {
        let mostTriggered = null;
        let maxTriggers = 0;

        for (const mechanism of this.improvementMechanisms.values()) {
            if (mechanism.triggers > maxTriggers) {
                maxTriggers = mechanism.triggers;
                mostTriggered = mechanism;
            }
        }

        return mostTriggered ? { 
            id: mostTriggered.id, 
            name: mostTriggered.name, 
            triggers: mostTriggered.triggers 
        } : null;
    }

    // Get total improvements
    getTotalImprovements() {
        return Array.from(this.improvementMechanisms.values())
            .reduce((sum, mechanism) => sum + mechanism.improvements.length, 0);
    }

    // Generate processing recommendations
    generateProcessingRecommendations(stats) {
        const recommendations = [];
        const totalProcessed = stats.stats.total_processed;

        if (totalProcessed === 0) {
            recommendations.push({
                priority: 'medium',
                category: 'data',
                recommendation: 'No feedback processed yet - ensure feedback collection is active'
            });
        }

        if (totalProcessed > 0) {
            const negativeRate = (stats.stats.negative_feedback / totalProcessed) * 100;
            
            if (negativeRate > 30) {
                recommendations.push({
                    priority: 'high',
                    category: 'quality',
                    recommendation: `High negative feedback rate (${negativeRate.toFixed(1)}%) - review system performance`
                });
            }

            if (stats.stats.improvements_triggered === 0 && negativeRate > 10) {
                recommendations.push({
                    priority: 'medium',
                    category: 'improvement',
                    recommendation: 'Consider lowering improvement thresholds to trigger more improvements'
                });
            }
        }

        return recommendations;
    }
}

export default FeedbackProcessor;