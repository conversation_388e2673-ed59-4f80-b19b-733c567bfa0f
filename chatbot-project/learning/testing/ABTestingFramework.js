class ABTestingFramework {
    constructor() {
        this.experiments = new Map();
        this.variants = new Map();
        this.assignments = new Map();
        this.results = new Map();
        this.statisticalAnalyzer = new Map();
        this.initializeFramework();
    }

    // Initialize A/B testing framework
    initializeFramework() {
        this.setupExperimentDesignTools();
        this.initializeStatisticalAnalysis();
    }

    // Set up experiment design tools
    setupExperimentDesignTools() {
        const designTemplates = {
            response_quality: {
                name: 'Response Quality Test',
                description: 'Test different response generation approaches',
                metrics: ['accuracy', 'relevance', 'user_satisfaction'],
                duration: '2 weeks',
                sample_size: 1000
            },
            prompt_optimization: {
                name: 'Prompt Optimization Test',
                description: 'Compare different prompt variations',
                metrics: ['response_quality', 'task_completion', 'user_engagement'],
                duration: '1 week',
                sample_size: 500
            },
            reasoning_approach: {
                name: 'Reasoning Approach Test',
                description: 'Test different reasoning methodologies',
                metrics: ['logical_consistency', 'explanation_clarity', 'user_trust'],
                duration: '3 weeks',
                sample_size: 1500
            },
            ui_interaction: {
                name: 'UI Interaction Test',
                description: 'Test different user interface elements',
                metrics: ['click_through_rate', 'task_completion_time', 'user_satisfaction'],
                duration: '1 week',
                sample_size: 800
            }
        };

        Object.entries(designTemplates).forEach(([id, template]) => {
            this.experiments.set(`template_${id}`, {
                ...template,
                id: `template_${id}`,
                type: 'template',
                created: new Date().toISOString()
            });
        });
    }

    // Initialize statistical analysis
    initializeStatisticalAnalysis() {
        const analyzers = {
            t_test: {
                name: 'T-Test Analysis',
                description: 'Compare means between two groups',
                assumptions: ['normal_distribution', 'equal_variance'],
                min_sample_size: 30
            },
            chi_square: {
                name: 'Chi-Square Test',
                description: 'Test independence of categorical variables',
                assumptions: ['categorical_data', 'expected_frequency_5'],
                min_sample_size: 50
            },
            mann_whitney: {
                name: 'Mann-Whitney U Test',
                description: 'Non-parametric comparison of two groups',
                assumptions: ['ordinal_data', 'independent_samples'],
                min_sample_size: 20
            },
            anova: {
                name: 'ANOVA',
                description: 'Compare means across multiple groups',
                assumptions: ['normal_distribution', 'equal_variance', 'independence'],
                min_sample_size: 50
            }
        };

        Object.entries(analyzers).forEach(([id, analyzer]) => {
            this.statisticalAnalyzer.set(id, {
                ...analyzer,
                id,
                usage_count: 0,
                last_used: null
            });
        });
    }

    // Implement experiment design tools
    async implementExperimentDesignTools(experimentConfig) {
        const experiment = {
            id: Date.now(),
            name: experimentConfig.name,
            description: experimentConfig.description,
            type: experimentConfig.type || 'ab_test',
            status: 'draft',
            created: new Date().toISOString(),
            config: {
                metrics: experimentConfig.metrics || ['conversion_rate'],
                duration: experimentConfig.duration || '1 week',
                sample_size: experimentConfig.sample_size || 1000,
                significance_level: experimentConfig.significance_level || 0.05,
                power: experimentConfig.power || 0.8,
                traffic_split: experimentConfig.traffic_split || [50, 50]
            },
            variants: [],
            assignments: new Map(),
            results: {
                started: null,
                ended: null,
                data: [],
                analysis: null
            }
        };

        // Validate experiment design
        const validation = await this.validateExperimentDesign(experiment);
        if (!validation.valid) {
            throw new Error(`Invalid experiment design: ${validation.errors.join(', ')}`);
        }

        // Store experiment
        this.experiments.set(experiment.id, experiment);

        return experiment;
    }

    // Validate experiment design
    async validateExperimentDesign(experiment) {
        const validation = {
            valid: true,
            errors: [],
            warnings: []
        };

        // Check required fields
        if (!experiment.name) {
            validation.errors.push('Experiment name is required');
        }

        if (!experiment.config.metrics || experiment.config.metrics.length === 0) {
            validation.errors.push('At least one metric is required');
        }

        // Check sample size
        if (experiment.config.sample_size < 100) {
            validation.warnings.push('Sample size is small - results may not be reliable');
        }

        // Check traffic split
        const totalTraffic = experiment.config.traffic_split.reduce((sum, split) => sum + split, 0);
        if (Math.abs(totalTraffic - 100) > 0.1) {
            validation.errors.push('Traffic split must sum to 100%');
        }

        validation.valid = validation.errors.length === 0;
        return validation;
    }

    // Develop variant management
    async developVariantManagement(experimentId, variantConfig) {
        const experiment = this.experiments.get(experimentId);
        if (!experiment) {
            throw new Error(`Experiment ${experimentId} not found`);
        }

        const variant = {
            id: Date.now(),
            experiment_id: experimentId,
            name: variantConfig.name,
            description: variantConfig.description,
            type: variantConfig.type || 'treatment',
            config: variantConfig.config || {},
            traffic_percentage: variantConfig.traffic_percentage || 50,
            created: new Date().toISOString(),
            status: 'active',
            metrics: {
                impressions: 0,
                conversions: 0,
                engagement: 0
            }
        };

        // Validate variant
        const validation = await this.validateVariant(variant, experiment);
        if (!validation.valid) {
            throw new Error(`Invalid variant: ${validation.errors.join(', ')}`);
        }

        // Store variant
        this.variants.set(variant.id, variant);
        experiment.variants.push(variant.id);

        return variant;
    }

    // Validate variant
    async validateVariant(variant, experiment) {
        const validation = {
            valid: true,
            errors: []
        };

        if (!variant.name) {
            validation.errors.push('Variant name is required');
        }

        if (variant.traffic_percentage <= 0 || variant.traffic_percentage > 100) {
            validation.errors.push('Traffic percentage must be between 0 and 100');
        }

        // Check total traffic allocation
        const existingVariants = experiment.variants.map(id => this.variants.get(id)).filter(v => v);
        const totalTraffic = existingVariants.reduce((sum, v) => sum + v.traffic_percentage, 0) + variant.traffic_percentage;
        
        if (totalTraffic > 100) {
            validation.errors.push('Total traffic allocation exceeds 100%');
        }

        validation.valid = validation.errors.length === 0;
        return validation;
    }

    // Assign user to variant
    async assignUserToVariant(experimentId, userId, userContext = {}) {
        const experiment = this.experiments.get(experimentId);
        if (!experiment || experiment.status !== 'running') {
            return null;
        }

        // Check if user already assigned
        const assignmentKey = `${experimentId}_${userId}`;
        if (this.assignments.has(assignmentKey)) {
            return this.assignments.get(assignmentKey);
        }

        // Get experiment variants
        const variants = experiment.variants.map(id => this.variants.get(id)).filter(v => v && v.status === 'active');
        if (variants.length === 0) {
            return null;
        }

        // Assign based on traffic percentage
        const assignment = await this.performVariantAssignment(variants, userId, userContext);
        
        if (assignment) {
            assignment.experiment_id = experimentId;
            assignment.user_id = userId;
            assignment.assigned_at = new Date().toISOString();
            assignment.context = userContext;
            
            this.assignments.set(assignmentKey, assignment);
        }

        return assignment;
    }

    // Perform variant assignment
    async performVariantAssignment(variants, userId, userContext) {
        // Use deterministic hash-based assignment for consistency
        const hash = this.hashUserId(userId);
        const hashValue = hash % 100; // 0-99

        let cumulativePercentage = 0;
        for (const variant of variants) {
            cumulativePercentage += variant.traffic_percentage;
            if (hashValue < cumulativePercentage) {
                return {
                    variant_id: variant.id,
                    variant_name: variant.name,
                    variant_config: variant.config
                };
            }
        }

        // Fallback to first variant
        return variants.length > 0 ? {
            variant_id: variants[0].id,
            variant_name: variants[0].name,
            variant_config: variants[0].config
        } : null;
    }

    // Hash user ID for consistent assignment
    hashUserId(userId) {
        let hash = 0;
        const str = userId.toString();
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    // Record experiment event
    async recordExperimentEvent(experimentId, userId, eventType, eventData = {}) {
        const assignment = this.assignments.get(`${experimentId}_${userId}`);
        if (!assignment) {
            return false; // User not in experiment
        }

        const event = {
            id: Date.now(),
            experiment_id: experimentId,
            variant_id: assignment.variant_id,
            user_id: userId,
            event_type: eventType,
            event_data: eventData,
            timestamp: new Date().toISOString()
        };

        // Store event
        const resultKey = `${experimentId}_${assignment.variant_id}`;
        if (!this.results.has(resultKey)) {
            this.results.set(resultKey, []);
        }
        this.results.get(resultKey).push(event);

        // Update variant metrics
        const variant = this.variants.get(assignment.variant_id);
        if (variant) {
            this.updateVariantMetrics(variant, eventType, eventData);
        }

        return true;
    }

    // Update variant metrics
    updateVariantMetrics(variant, eventType, eventData) {
        switch (eventType) {
            case 'impression':
                variant.metrics.impressions++;
                break;
            case 'conversion':
                variant.metrics.conversions++;
                break;
            case 'engagement':
                variant.metrics.engagement += eventData.engagement_score || 1;
                break;
        }
    }

    // Build statistical analysis
    async buildStatisticalAnalysis(experimentId, analysisType = 't_test') {
        const experiment = this.experiments.get(experimentId);
        if (!experiment) {
            throw new Error(`Experiment ${experimentId} not found`);
        }

        const analyzer = this.statisticalAnalyzer.get(analysisType);
        if (!analyzer) {
            throw new Error(`Analysis type ${analysisType} not supported`);
        }

        const analysis = {
            id: Date.now(),
            experiment_id: experimentId,
            analysis_type: analysisType,
            timestamp: new Date().toISOString(),
            data: await this.prepareAnalysisData(experimentId),
            results: {},
            conclusions: []
        };

        // Perform statistical analysis
        switch (analysisType) {
            case 't_test':
                analysis.results = await this.performTTest(analysis.data);
                break;
            case 'chi_square':
                analysis.results = await this.performChiSquareTest(analysis.data);
                break;
            case 'mann_whitney':
                analysis.results = await this.performMannWhitneyTest(analysis.data);
                break;
            case 'anova':
                analysis.results = await this.performANOVA(analysis.data);
                break;
        }

        // Generate conclusions
        analysis.conclusions = this.generateStatisticalConclusions(analysis.results);

        // Update analyzer usage
        analyzer.usage_count++;
        analyzer.last_used = new Date().toISOString();

        return analysis;
    }

    // Prepare analysis data
    async prepareAnalysisData(experimentId) {
        const experiment = this.experiments.get(experimentId);
        const data = {
            variants: [],
            metrics: {}
        };

        // Collect data for each variant
        for (const variantId of experiment.variants) {
            const variant = this.variants.get(variantId);
            const events = this.results.get(`${experimentId}_${variantId}`) || [];
            
            const variantData = {
                id: variantId,
                name: variant.name,
                sample_size: events.length,
                metrics: this.calculateVariantMetrics(events)
            };

            data.variants.push(variantData);
        }

        return data;
    }

    // Calculate variant metrics
    calculateVariantMetrics(events) {
        const metrics = {
            conversion_rate: 0,
            engagement_rate: 0,
            average_session_time: 0
        };

        if (events.length === 0) return metrics;

        const conversions = events.filter(e => e.event_type === 'conversion').length;
        const engagements = events.filter(e => e.event_type === 'engagement').length;
        
        metrics.conversion_rate = (conversions / events.length) * 100;
        metrics.engagement_rate = (engagements / events.length) * 100;
        
        // Calculate average session time (simulated)
        const sessionTimes = events.map(e => e.event_data?.session_time || Math.random() * 300 + 60);
        metrics.average_session_time = sessionTimes.reduce((a, b) => a + b, 0) / sessionTimes.length;

        return metrics;
    }

    // Perform T-Test
    async performTTest(data) {
        if (data.variants.length !== 2) {
            throw new Error('T-Test requires exactly 2 variants');
        }

        const [variant1, variant2] = data.variants;
        
        // Simulate T-Test calculation
        const mean1 = variant1.metrics.conversion_rate;
        const mean2 = variant2.metrics.conversion_rate;
        const n1 = variant1.sample_size;
        const n2 = variant2.sample_size;
        
        // Simulated standard deviations
        const std1 = Math.sqrt(mean1 * (100 - mean1) / n1);
        const std2 = Math.sqrt(mean2 * (100 - mean2) / n2);
        
        const pooledStd = Math.sqrt(((n1 - 1) * std1 * std1 + (n2 - 1) * std2 * std2) / (n1 + n2 - 2));
        const tStatistic = (mean1 - mean2) / (pooledStd * Math.sqrt(1/n1 + 1/n2));
        const pValue = this.calculatePValue(Math.abs(tStatistic), n1 + n2 - 2);

        return {
            test_type: 't_test',
            variant1: { name: variant1.name, mean: mean1, n: n1 },
            variant2: { name: variant2.name, mean: mean2, n: n2 },
            t_statistic: tStatistic,
            p_value: pValue,
            significant: pValue < 0.05,
            confidence_interval: this.calculateConfidenceInterval(mean1, mean2, pooledStd, n1, n2)
        };
    }

    // Perform Chi-Square Test
    async performChiSquareTest(data) {
        // Simulate Chi-Square test for categorical data
        const observed = data.variants.map(v => v.metrics.conversion_rate * v.sample_size / 100);
        const total = observed.reduce((a, b) => a + b, 0);
        const expected = data.variants.map(v => total * v.sample_size / data.variants.reduce((sum, variant) => sum + variant.sample_size, 0));
        
        const chiSquare = observed.reduce((sum, obs, i) => {
            return sum + Math.pow(obs - expected[i], 2) / expected[i];
        }, 0);
        
        const degreesOfFreedom = data.variants.length - 1;
        const pValue = this.calculateChiSquarePValue(chiSquare, degreesOfFreedom);

        return {
            test_type: 'chi_square',
            chi_square_statistic: chiSquare,
            degrees_of_freedom: degreesOfFreedom,
            p_value: pValue,
            significant: pValue < 0.05,
            observed: observed,
            expected: expected
        };
    }

    // Perform Mann-Whitney Test
    async performMannWhitneyTest(data) {
        if (data.variants.length !== 2) {
            throw new Error('Mann-Whitney Test requires exactly 2 variants');
        }

        // Simulate Mann-Whitney U test
        const [variant1, variant2] = data.variants;
        const u1 = variant1.sample_size * variant2.sample_size + (variant1.sample_size * (variant1.sample_size + 1)) / 2;
        const u2 = variant1.sample_size * variant2.sample_size - u1;
        const uStatistic = Math.min(u1, u2);
        
        // Approximate p-value calculation
        const meanU = (variant1.sample_size * variant2.sample_size) / 2;
        const stdU = Math.sqrt((variant1.sample_size * variant2.sample_size * (variant1.sample_size + variant2.sample_size + 1)) / 12);
        const zScore = (uStatistic - meanU) / stdU;
        const pValue = 2 * (1 - this.normalCDF(Math.abs(zScore)));

        return {
            test_type: 'mann_whitney',
            u_statistic: uStatistic,
            z_score: zScore,
            p_value: pValue,
            significant: pValue < 0.05
        };
    }

    // Perform ANOVA
    async performANOVA(data) {
        if (data.variants.length < 3) {
            throw new Error('ANOVA requires at least 3 variants');
        }

        // Simulate ANOVA calculation
        const grandMean = data.variants.reduce((sum, v) => sum + v.metrics.conversion_rate * v.sample_size, 0) / 
                         data.variants.reduce((sum, v) => sum + v.sample_size, 0);
        
        const ssBetween = data.variants.reduce((sum, v) => {
            return sum + v.sample_size * Math.pow(v.metrics.conversion_rate - grandMean, 2);
        }, 0);
        
        const ssWithin = data.variants.reduce((sum, v) => {
            return sum + (v.sample_size - 1) * Math.pow(v.metrics.conversion_rate * 0.1, 2); // Simulated variance
        }, 0);
        
        const dfBetween = data.variants.length - 1;
        const dfWithin = data.variants.reduce((sum, v) => sum + v.sample_size, 0) - data.variants.length;
        
        const msBetween = ssBetween / dfBetween;
        const msWithin = ssWithin / dfWithin;
        const fStatistic = msBetween / msWithin;
        const pValue = this.calculateFPValue(fStatistic, dfBetween, dfWithin);

        return {
            test_type: 'anova',
            f_statistic: fStatistic,
            df_between: dfBetween,
            df_within: dfWithin,
            p_value: pValue,
            significant: pValue < 0.05,
            ss_between: ssBetween,
            ss_within: ssWithin
        };
    }

    // Calculate p-value (simplified approximation)
    calculatePValue(tStat, df) {
        // Simplified p-value calculation
        return Math.max(0.001, Math.min(0.999, 2 * (1 - this.normalCDF(Math.abs(tStat)))));
    }

    // Calculate Chi-Square p-value (simplified)
    calculateChiSquarePValue(chiSquare, df) {
        // Simplified Chi-Square p-value calculation
        return Math.max(0.001, Math.min(0.999, Math.exp(-chiSquare / 2)));
    }

    // Calculate F p-value (simplified)
    calculateFPValue(fStat, df1, df2) {
        // Simplified F p-value calculation
        return Math.max(0.001, Math.min(0.999, Math.exp(-fStat / 2)));
    }

    // Normal CDF approximation
    normalCDF(x) {
        return 0.5 * (1 + this.erf(x / Math.sqrt(2)));
    }

    // Error function approximation
    erf(x) {
        const a1 =  0.254829592;
        const a2 = -0.284496736;
        const a3 =  1.421413741;
        const a4 = -1.453152027;
        const a5 =  1.061405429;
        const p  =  0.3275911;

        const sign = x < 0 ? -1 : 1;
        x = Math.abs(x);

        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

        return sign * y;
    }

    // Calculate confidence interval
    calculateConfidenceInterval(mean1, mean2, pooledStd, n1, n2) {
        const diff = mean1 - mean2;
        const se = pooledStd * Math.sqrt(1/n1 + 1/n2);
        const margin = 1.96 * se; // 95% confidence interval
        
        return {
            lower: diff - margin,
            upper: diff + margin,
            difference: diff
        };
    }

    // Generate statistical conclusions
    generateStatisticalConclusions(results) {
        const conclusions = [];

        if (results.significant) {
            conclusions.push({
                type: 'significance',
                conclusion: `The test shows a statistically significant difference (p = ${results.p_value.toFixed(4)})`
            });
        } else {
            conclusions.push({
                type: 'no_significance',
                conclusion: `No statistically significant difference found (p = ${results.p_value.toFixed(4)})`
            });
        }

        if (results.test_type === 't_test') {
            const winner = results.variant1.mean > results.variant2.mean ? results.variant1.name : results.variant2.name;
            conclusions.push({
                type: 'performance',
                conclusion: `${winner} shows better performance with ${Math.abs(results.variant1.mean - results.variant2.mean).toFixed(2)}% difference`
            });
        }

        return conclusions;
    }

    // Get experiment status
    getExperimentStatus(experimentId) {
        const experiment = this.experiments.get(experimentId);
        if (!experiment) return null;

        const variants = experiment.variants.map(id => this.variants.get(id)).filter(v => v);
        const totalAssignments = Array.from(this.assignments.values())
            .filter(a => a.experiment_id === experimentId).length;

        return {
            experiment: {
                id: experiment.id,
                name: experiment.name,
                status: experiment.status,
                created: experiment.created
            },
            variants: variants.map(v => ({
                id: v.id,
                name: v.name,
                traffic_percentage: v.traffic_percentage,
                metrics: v.metrics
            })),
            assignments: totalAssignments,
            sample_size_target: experiment.config.sample_size,
            progress: (totalAssignments / experiment.config.sample_size) * 100
        };
    }

    // Generate A/B testing report
    generateABTestingReport() {
        const activeExperiments = Array.from(this.experiments.values()).filter(e => e.status === 'running');
        const completedExperiments = Array.from(this.experiments.values()).filter(e => e.status === 'completed');

        return {
            summary: {
                totalExperiments: this.experiments.size,
                activeExperiments: activeExperiments.length,
                completedExperiments: completedExperiments.length,
                totalVariants: this.variants.size,
                totalAssignments: this.assignments.size
            },
            activeExperiments: activeExperiments.map(exp => this.getExperimentStatus(exp.id)),
            recentResults: this.getRecentResults(),
            statisticalAnalysis: {
                analyzers: Object.fromEntries(this.statisticalAnalyzer),
                mostUsedAnalyzer: this.findMostUsedAnalyzer()
            },
            recommendations: this.generateABTestingRecommendations()
        };
    }

    // Get recent results
    getRecentResults() {
        const recentResults = [];
        
        for (const [key, events] of this.results) {
            if (events.length > 0) {
                const [experimentId, variantId] = key.split('_');
                const experiment = this.experiments.get(parseInt(experimentId));
                const variant = this.variants.get(parseInt(variantId));
                
                if (experiment && variant) {
                    recentResults.push({
                        experiment: experiment.name,
                        variant: variant.name,
                        events: events.length,
                        lastEvent: events[events.length - 1].timestamp
                    });
                }
            }
        }

        return recentResults.slice(-10); // Last 10 results
    }

    // Find most used analyzer
    findMostUsedAnalyzer() {
        let mostUsed = null;
        let maxUsage = 0;

        for (const [id, analyzer] of this.statisticalAnalyzer) {
            if (analyzer.usage_count > maxUsage) {
                maxUsage = analyzer.usage_count;
                mostUsed = { id, name: analyzer.name, usage: analyzer.usage_count };
            }
        }

        return mostUsed;
    }

    // Generate A/B testing recommendations
    generateABTestingRecommendations() {
        const recommendations = [];
        const activeExperiments = Array.from(this.experiments.values()).filter(e => e.status === 'running');

        if (activeExperiments.length === 0) {
            recommendations.push({
                priority: 'medium',
                category: 'experiments',
                recommendation: 'No active experiments - consider starting new tests to optimize performance'
            });
        }

        if (this.assignments.size < 100) {
            recommendations.push({
                priority: 'low',
                category: 'sample_size',
                recommendation: 'Low number of experiment assignments - ensure adequate traffic allocation'
            });
        }

        activeExperiments.forEach(exp => {
            const assignments = Array.from(this.assignments.values()).filter(a => a.experiment_id === exp.id).length;
            const progress = (assignments / exp.config.sample_size) * 100;
            
            if (progress < 20) {
                recommendations.push({
                    priority: 'medium',
                    category: 'progress',
                    recommendation: `Experiment "${exp.name}" has low progress (${progress.toFixed(1)}%) - consider increasing traffic allocation`
                });
            }
        });

        return recommendations;
    }
}

export default ABTestingFramework;