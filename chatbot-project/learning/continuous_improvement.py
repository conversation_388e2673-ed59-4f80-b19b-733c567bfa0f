"""Continuous improvement and learning system."""

from typing import Dict, Any, List
import json
from datetime import datetime

class ContinuousLearningEngine:
    def __init__(self):
        self.feedback_store = []
        self.performance_metrics = {}
        self.improvement_actions = []
        
    def process_feedback(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """Process user feedback for learning."""
        self.feedback_store.append({
            **feedback,
            "timestamp": datetime.now().isoformat(),
            "processed": False
        })
        
        # Analyze feedback patterns
        analysis = self.analyze_feedback_patterns()
        
        # Generate improvement actions
        actions = self.generate_improvement_actions(analysis)
        
        return {
            "feedback_processed": True,
            "analysis": analysis,
            "improvement_actions": actions
        }
    
    def analyze_feedback_patterns(self) -> Dict[str, Any]:
        """Analyze feedback patterns for insights."""
        if not self.feedback_store:
            return {"patterns": [], "insights": []}
        
        # Satisfaction analysis
        ratings = [f.get("rating", 3) for f in self.feedback_store if "rating" in f]
        avg_rating = sum(ratings) / len(ratings) if ratings else 3.0
        
        # Common issues
        issues = []
        for feedback in self.feedback_store:
            if feedback.get("rating", 3) < 3:
                issues.append(feedback.get("issue_type", "unknown"))
        
        issue_counts = {}
        for issue in issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        return {
            "avg_satisfaction": avg_rating,
            "total_feedback": len(self.feedback_store),
            "common_issues": issue_counts,
            "improvement_needed": avg_rating < 3.5
        }
    
    def generate_improvement_actions(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate specific improvement actions."""
        actions = []
        
        if analysis.get("improvement_needed", False):
            # Low satisfaction - improve response quality
            actions.append({
                "type": "response_quality",
                "action": "Enhance knowledge base and reasoning",
                "priority": "high",
                "target_metric": "satisfaction_rating"
            })
        
        # Address common issues
        for issue, count in analysis.get("common_issues", {}).items():
            if count > 2:  # Recurring issue
                actions.append({
                    "type": "issue_resolution",
                    "action": f"Address recurring issue: {issue}",
                    "priority": "medium",
                    "frequency": count
                })
        
        return actions
    
    def update_agent_performance(self, agent_id: str, metrics: Dict[str, Any]):
        """Update agent performance metrics."""
        if agent_id not in self.performance_metrics:
            self.performance_metrics[agent_id] = []
        
        self.performance_metrics[agent_id].append({
            **metrics,
            "timestamp": datetime.now().isoformat()
        })
        
        # Trigger improvement if performance drops
        if metrics.get("success_rate", 1.0) < 0.7:
            self.trigger_agent_improvement(agent_id, metrics)
    
    def trigger_agent_improvement(self, agent_id: str, metrics: Dict[str, Any]):
        """Trigger improvement for underperforming agent."""
        improvement_plan = {
            "agent_id": agent_id,
            "current_performance": metrics,
            "improvement_targets": {
                "success_rate": 0.85,
                "response_time": metrics.get("response_time", 5.0) * 0.8,
                "confidence": 0.8
            },
            "actions": [
                "Retrain on recent failures",
                "Update knowledge base",
                "Optimize reasoning paths"
            ]
        }
        
        self.improvement_actions.append(improvement_plan)
        return improvement_plan
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """Get current learning insights."""
        return {
            "total_feedback": len(self.feedback_store),
            "performance_trends": self.calculate_performance_trends(),
            "active_improvements": len(self.improvement_actions),
            "learning_status": "active"
        }
    
    def calculate_performance_trends(self) -> Dict[str, Any]:
        """Calculate performance trends across agents."""
        trends = {}
        
        for agent_id, metrics_list in self.performance_metrics.items():
            if len(metrics_list) >= 2:
                recent = metrics_list[-5:]  # Last 5 measurements
                success_rates = [m.get("success_rate", 0) for m in recent]
                
                if len(success_rates) >= 2:
                    trend = "improving" if success_rates[-1] > success_rates[0] else "declining"
                    trends[agent_id] = {
                        "trend": trend,
                        "current_rate": success_rates[-1],
                        "change": success_rates[-1] - success_rates[0]
                    }
        
        return trends