#!/usr/bin/env python3
"""
Test Local LLM Infrastructure with Available Models
Tests the implemented LLM infrastructure using local Ollama models.
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_local_ollama_models():
    """Test local Ollama models (deepseek-r1 and llama2)."""
    logger.info("🧪 Testing Local Ollama Models...")
    
    try:
        from ai.llm.ollama_client import OllamaClient
        
        # Connect to Ollama server
        client = OllamaClient(base_url="http://10.10.110.25:11434")
        
        if not client.is_available():
            logger.error("❌ Local Ollama not available")
            return False
        
        logger.info(f"✅ Connected to local Ollama")
        logger.info(f"Available models: {client.available_models}")
        logger.info(f"Default model: {client.default_model}")
        
        # Test with Llama3 (should be available)
        if "llama3:latest" in client.available_models:
            logger.info("🦙 Testing Llama3 model...")

            result = await client.generate_async(
                prompt="What is the capital of France? Answer briefly.",
                model="llama3:latest",
                max_tokens=50,
                temperature=0.3
            )

            if "error" not in result:
                logger.info(f"✅ Llama3 response: {result['response'][:100]}...")
                logger.info(f"✅ Performance: {result.get('eval_count', 0)} tokens in {result.get('eval_duration', 0)/1000000:.2f}ms")
            else:
                logger.error(f"❌ Llama3 error: {result['error']}")
                return False

        # Test with Phi4 if available
        if "phi4:latest" in client.available_models:
            logger.info("🧠 Testing Phi4 model...")

            result = await client.generate_async(
                prompt="Explain machine learning in one sentence.",
                model="phi4:latest",
                max_tokens=100,
                temperature=0.7
            )

            if "error" not in result:
                logger.info(f"✅ Phi4 response: {result['response'][:100]}...")
            else:
                logger.error(f"❌ Phi4 error: {result['error']}")
        
        # Test model capabilities
        for model in client.available_models:
            capabilities = client.get_model_capabilities(model)
            logger.info(f"✅ {model} capabilities: {capabilities['type']}, context: {capabilities.get('context_length', 'unknown')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Local Ollama test failed: {e}")
        return False

async def test_multi_agent_with_local_models():
    """Test multi-agent inference with local models."""
    logger.info("🧪 Testing Multi-Agent Inference with Local Models...")
    
    try:
        from ai.llm.inference.multi_agent_inference import MultiAgentInferenceEngine, InferenceRequest
        
        # Initialize with local Ollama
        engine = MultiAgentInferenceEngine(max_agents=3)
        success = await engine.initialize()
        
        if not success:
            logger.error("❌ Multi-agent engine initialization failed")
            return False
        
        logger.info(f"✅ Multi-agent engine initialized")
        
        # Get agent stats
        stats = engine.get_agent_stats()
        logger.info(f"✅ Agents: {stats['total_agents']} total, {stats['active_agents']} active")
        
        # Test reasoning task (should use DeepSeek-R1)
        reasoning_request = InferenceRequest(
            prompt="If a train travels 120 km in 2 hours, what is its average speed? Show your calculation.",
            max_tokens=150,
            temperature=0.3
        )
        
        response = await engine.inference(
            reasoning_request, 
            agent_selection_criteria={"task_type": "reasoning"}
        )
        
        logger.info(f"✅ Reasoning response: {response.response[:100]}...")
        logger.info(f"✅ Used agent: {response.metadata.get('agent_name', 'unknown')}")
        
        # Test conversation task
        conversation_request = InferenceRequest(
            prompt="Hello! How are you today?",
            max_tokens=100,
            temperature=0.7
        )
        
        response = await engine.inference(
            conversation_request,
            agent_selection_criteria={"task_type": "conversation"}
        )
        
        logger.info(f"✅ Conversation response: {response.response[:100]}...")
        
        # Test batch processing
        batch_requests = [
            InferenceRequest(prompt="What is 2+2?", max_tokens=30),
            InferenceRequest(prompt="What is the capital of Japan?", max_tokens=30),
            InferenceRequest(prompt="Name a programming language", max_tokens=30)
        ]
        
        batch_responses = await engine.batch_inference(batch_requests)
        logger.info(f"✅ Batch processing: {len(batch_responses)} responses")
        
        for i, resp in enumerate(batch_responses):
            logger.info(f"  Response {i+1}: {resp.response[:50]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Multi-agent test failed: {e}")
        return False

async def test_infrastructure_manager_local():
    """Test infrastructure manager with local models."""
    logger.info("🧪 Testing Infrastructure Manager with Local Models...")
    
    try:
        from ai.llm.llm_infrastructure_manager import (
            LLMInfrastructureManager, LLMInfrastructureConfig
        )
        
        # Configure for Ollama server
        config = LLMInfrastructureConfig(
            ollama_url="http://10.10.110.25:11434",
            enable_onnx_optimization=True,
            enable_model_versioning=True,
            enable_multi_agent=True,
            max_agents=3
        )
        
        # Initialize infrastructure
        manager = LLMInfrastructureManager(config)
        success = await manager.initialize()
        
        if not success:
            logger.error("❌ Infrastructure manager initialization failed")
            return False
        
        logger.info("✅ Infrastructure manager initialized")
        
        # Test single inference
        response = await manager.inference(
            "Explain the concept of artificial intelligence in simple terms",
            max_tokens=200,
            temperature=0.7
        )
        
        logger.info(f"✅ Infrastructure inference: {response.get('response', '')[:100]}...")
        
        # Test batch inference
        prompts = [
            "What is Python programming?",
            "How do computers work?",
            "What is the internet?"
        ]
        
        batch_responses = await manager.batch_inference(prompts, max_tokens=100)
        logger.info(f"✅ Infrastructure batch: {len(batch_responses)} responses")
        
        # Get comprehensive status
        status = manager.get_infrastructure_status()
        logger.info(f"✅ Infrastructure status:")
        logger.info(f"  - Initialized: {status['initialized']}")
        logger.info(f"  - Active components: {sum(status['components'].values())}/{len(status['components'])}")
        logger.info(f"  - Total requests: {status['metrics']['total_requests']}")
        logger.info(f"  - Success rate: {status['metrics']['successful_requests']}/{status['metrics']['total_requests']}")
        
        if 'ollama_status' in status:
            logger.info(f"  - Available models: {status['ollama_status']['available_models']}")
            logger.info(f"  - Default model: {status['ollama_status']['default_model']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Infrastructure manager test failed: {e}")
        return False

async def test_performance_benchmarks_local():
    """Test performance with local models."""
    logger.info("🧪 Testing Performance with Local Models...")
    
    try:
        from ai.llm.llm_infrastructure_manager import get_llm_infrastructure, LLMInfrastructureConfig
        
        # Configure for Ollama server
        config = LLMInfrastructureConfig(
            ollama_url="http://10.10.110.25:11434",
            enable_multi_agent=True,
            max_agents=3
        )
        
        infrastructure = await get_llm_infrastructure(config)
        
        # Single inference benchmark
        logger.info("📊 Benchmarking single inference...")
        start_time = time.time()
        
        response = await infrastructure.inference(
            "Write a haiku about technology",
            max_tokens=100,
            temperature=0.8
        )
        
        single_latency = time.time() - start_time
        logger.info(f"✅ Single inference: {single_latency:.2f}s")
        logger.info(f"✅ Response: {response.get('response', '')[:100]}...")
        
        # Batch inference benchmark
        logger.info("📊 Benchmarking batch inference...")
        prompts = [
            "What is AI?",
            "How does ML work?",
            "Explain neural networks",
            "What is deep learning?",
            "Define computer vision"
        ]
        
        start_time = time.time()
        batch_responses = await infrastructure.batch_inference(prompts, max_tokens=80)
        batch_latency = time.time() - start_time
        
        logger.info(f"✅ Batch inference: {batch_latency:.2f}s for {len(prompts)} prompts")
        logger.info(f"✅ Average per prompt: {batch_latency/len(prompts):.2f}s")
        logger.info(f"✅ Throughput: {len(prompts)/batch_latency:.2f} prompts/second")
        
        # Model comparison
        logger.info("📊 Comparing model performance...")
        test_prompt = "Explain quantum computing briefly"

        for model in ["llama3:latest", "phi4:latest"]:
            start_time = time.time()
            response = await infrastructure.inference(
                test_prompt,
                model=model,
                max_tokens=100,
                temperature=0.5
            )
            model_latency = time.time() - start_time

            logger.info(f"✅ {model}: {model_latency:.2f}s")
            logger.info(f"   Response: {response.get('response', '')[:80]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance benchmark failed: {e}")
        return False

async def main():
    """Run comprehensive local LLM infrastructure tests."""
    logger.info("🚀 Starting Local LLM Infrastructure Tests")
    logger.info("=" * 80)
    
    tests = [
        ("Local Ollama Models", test_local_ollama_models),
        ("Multi-Agent with Local Models", test_multi_agent_with_local_models),
        ("Infrastructure Manager Local", test_infrastructure_manager_local),
        ("Performance Benchmarks Local", test_performance_benchmarks_local)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name} test...")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"❌ FAILED: {test_name} - {e}")
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("🎯 LOCAL LLM INFRASTRUCTURE TEST SUMMARY")
    logger.info("=" * 80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\n📊 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All local LLM infrastructure components are working perfectly!")
        logger.info("🚀 Ready for production use with DeepSeek-R1 and Llama2!")
    elif passed >= total * 0.75:
        logger.info("✅ Most local LLM infrastructure components are working well!")
    else:
        logger.warning("⚠️ Some local LLM infrastructure components need attention!")
    
    # Save detailed results
    detailed_results = {
        "timestamp": time.time(),
        "test_results": results,
        "summary": {
            "passed": passed,
            "total": total,
            "success_rate": f"{(passed/total)*100:.1f}%"
        },
        "local_models": ["llama3:latest", "phi4:latest", "llama3.1:8b", "llama3.3:70b"],
        "infrastructure_components": [
            "Enhanced Ollama Client",
            "ONNX Optimization", 
            "Enhanced Model Server",
            "Model Registry",
            "Multi-Agent Inference",
            "Infrastructure Manager"
        ]
    }
    
    with open("local_llm_infrastructure_test_results.json", "w") as f:
        json.dump(detailed_results, f, indent=2)
    
    logger.info("📄 Detailed results saved to local_llm_infrastructure_test_results.json")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
