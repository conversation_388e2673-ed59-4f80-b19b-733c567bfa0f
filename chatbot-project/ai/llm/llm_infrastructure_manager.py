"""LLM Infrastructure Manager - Orchestrates all LLM components."""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

from .ollama_client import OllamaClient
from .optimization.onnx_optimizer import ONNXOptimizer, OptimizationConfig
from .serving.enhanced_model_server import EnhancedModelServer
from .versioning.enhanced_model_registry import EnhancedModelRegistry
from .inference.multi_agent_inference import MultiAgentInferenceEngine, InferenceRequest

logger = logging.getLogger(__name__)

@dataclass
class LLMInfrastructureConfig:
    """Configuration for LLM infrastructure."""
    ollama_url: str = "http://10.10.110.25:11434"
    enable_onnx_optimization: bool = True
    enable_model_versioning: bool = True
    enable_multi_agent: bool = True
    max_agents: int = 10
    cache_dir: str = "./llm_cache"
    registry_dir: str = "./model_registry"
    auto_optimize_models: bool = False
    enable_performance_monitoring: bool = True

class LLMInfrastructureManager:
    """Comprehensive LLM infrastructure manager."""
    
    def __init__(self, config: LLMInfrastructureConfig = None):
        self.config = config or LLMInfrastructureConfig()
        
        # Core components
        self.ollama_client: Optional[OllamaClient] = None
        self.onnx_optimizer: Optional[ONNXOptimizer] = None
        self.model_server: Optional[EnhancedModelServer] = None
        self.model_registry: Optional[EnhancedModelRegistry] = None
        self.multi_agent_engine: Optional[MultiAgentInferenceEngine] = None
        
        # Status tracking
        self.initialized = False
        self.components_status = {
            "ollama_client": False,
            "onnx_optimizer": False,
            "model_server": False,
            "model_registry": False,
            "multi_agent_engine": False
        }
        
        # Performance metrics
        self.infrastructure_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_latency": 0.0,
            "uptime": 0.0,
            "active_models": 0,
            "optimization_jobs": 0
        }
    
    async def initialize(self) -> bool:
        """Initialize all LLM infrastructure components."""
        try:
            logger.info("Initializing LLM Infrastructure Manager...")
            
            # 1. Initialize Ollama Client (Core dependency)
            await self._initialize_ollama_client()
            
            # 2. Initialize ONNX Optimizer (Optional)
            if self.config.enable_onnx_optimization:
                await self._initialize_onnx_optimizer()
            
            # 3. Initialize Model Registry (Optional)
            if self.config.enable_model_versioning:
                await self._initialize_model_registry()
            
            # 4. Initialize Enhanced Model Server
            await self._initialize_model_server()
            
            # 5. Initialize Multi-Agent Engine (Optional)
            if self.config.enable_multi_agent:
                await self._initialize_multi_agent_engine()
            
            # 6. Start monitoring if enabled
            if self.config.enable_performance_monitoring:
                asyncio.create_task(self._monitor_infrastructure())
            
            self.initialized = True
            logger.info("✅ LLM Infrastructure Manager initialized successfully")
            
            # Log component status
            self._log_component_status()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM Infrastructure Manager: {e}")
            return False
    
    async def _initialize_ollama_client(self):
        """Initialize Ollama client."""
        try:
            self.ollama_client = OllamaClient(base_url=self.config.ollama_url)
            
            if self.ollama_client.is_available():
                self.components_status["ollama_client"] = True
                logger.info(f"✅ Ollama client connected to {self.config.ollama_url}")
                logger.info(f"Available models: {self.ollama_client.available_models}")
            else:
                raise Exception("Ollama client not available")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize Ollama client: {e}")
            raise
    
    async def _initialize_onnx_optimizer(self):
        """Initialize ONNX optimizer."""
        try:
            self.onnx_optimizer = ONNXOptimizer(cache_dir=self.config.cache_dir)
            self.components_status["onnx_optimizer"] = True
            logger.info("✅ ONNX optimizer initialized")
            
        except ImportError:
            logger.warning("⚠️ ONNX optimizer not available (missing dependencies)")
        except Exception as e:
            logger.error(f"❌ Failed to initialize ONNX optimizer: {e}")
    
    async def _initialize_model_registry(self):
        """Initialize model registry."""
        try:
            self.model_registry = EnhancedModelRegistry(registry_path=self.config.registry_dir)
            self.components_status["model_registry"] = True
            logger.info("✅ Model registry initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize model registry: {e}")
    
    async def _initialize_model_server(self):
        """Initialize enhanced model server."""
        try:
            self.model_server = EnhancedModelServer()
            success = await self.model_server.initialize_server()
            
            if success:
                self.components_status["model_server"] = True
                logger.info("✅ Enhanced model server initialized")
            else:
                raise Exception("Model server initialization failed")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize model server: {e}")
            raise
    
    async def _initialize_multi_agent_engine(self):
        """Initialize multi-agent inference engine."""
        try:
            self.multi_agent_engine = MultiAgentInferenceEngine(max_agents=self.config.max_agents)
            success = await self.multi_agent_engine.initialize()
            
            if success:
                self.components_status["multi_agent_engine"] = True
                logger.info("✅ Multi-agent inference engine initialized")
            else:
                raise Exception("Multi-agent engine initialization failed")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize multi-agent engine: {e}")
    
    async def inference(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Perform inference using the best available method."""
        if not self.initialized:
            raise Exception("LLM Infrastructure not initialized")
        
        try:
            # Create inference request
            request = InferenceRequest(
                prompt=prompt,
                model=kwargs.get("model"),
                max_tokens=kwargs.get("max_tokens", 500),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 0.9),
                stream=kwargs.get("stream", False),
                timeout=kwargs.get("timeout", 60)
            )
            
            # Use multi-agent engine if available, otherwise use model server
            if self.multi_agent_engine and self.components_status["multi_agent_engine"]:
                inference_response = await self.multi_agent_engine.inference(request)
                response = {
                    "response": inference_response.response,
                    "model": inference_response.model,
                    "status": inference_response.status,
                    "tokens_generated": inference_response.tokens_generated,
                    "latency_ms": inference_response.latency_ms,
                    "metadata": inference_response.metadata
                }
            elif self.model_server and self.components_status["model_server"]:
                inference_response = await self.model_server.single_inference(request)
                response = {
                    "response": inference_response.response,
                    "model": inference_response.model,
                    "status": inference_response.status,
                    "tokens_generated": inference_response.tokens_generated,
                    "latency_ms": inference_response.latency_ms,
                    "metadata": inference_response.metadata
                }
            else:
                # Fallback to direct Ollama client
                result = await self.ollama_client.generate_async(prompt, **kwargs)
                response = {
                    "response": result.get("response", ""),
                    "model": result.get("model", "unknown"),
                    "status": "success" if "error" not in result else "error"
                }
            
            # Update metrics
            self.infrastructure_metrics["total_requests"] += 1
            if response.get("status") == "success":
                self.infrastructure_metrics["successful_requests"] += 1
            else:
                self.infrastructure_metrics["failed_requests"] += 1
            
            return response
            
        except Exception as e:
            self.infrastructure_metrics["failed_requests"] += 1
            logger.error(f"Inference failed: {e}")
            raise
    
    async def batch_inference(self, prompts: List[str], **kwargs) -> List[Dict[str, Any]]:
        """Perform batch inference."""
        if not self.initialized:
            raise Exception("LLM Infrastructure not initialized")
        
        try:
            # Use multi-agent engine for batch processing if available
            if self.multi_agent_engine and self.components_status["multi_agent_engine"]:
                requests = [
                    InferenceRequest(
                        prompt=prompt,
                        model=kwargs.get("model"),
                        max_tokens=kwargs.get("max_tokens", 500),
                        temperature=kwargs.get("temperature", 0.7)
                    )
                    for prompt in prompts
                ]
                responses = await self.multi_agent_engine.batch_inference(requests)
                return [{
                    "response": r.response,
                    "model": r.model,
                    "status": r.status,
                    "tokens_generated": r.tokens_generated,
                    "latency_ms": r.latency_ms,
                    "metadata": r.metadata
                } for r in responses]
            
            # Fallback to sequential processing
            results = []
            for prompt in prompts:
                result = await self.inference(prompt, **kwargs)
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Batch inference failed: {e}")
            raise
    
    async def optimize_model(self, model_path: str, optimization_config: OptimizationConfig = None) -> Dict[str, Any]:
        """Optimize a model using ONNX."""
        if not self.onnx_optimizer:
            raise Exception("ONNX optimizer not available")
        
        try:
            if not optimization_config:
                # Get automatic recommendations
                model_info = {"model_size_mb": 1000}  # Placeholder
                optimization_config = self.onnx_optimizer.get_optimization_recommendations(model_info)
            
            result = self.onnx_optimizer.optimize_model(model_path, optimization_config)
            self.infrastructure_metrics["optimization_jobs"] += 1
            
            return result
            
        except Exception as e:
            logger.error(f"Model optimization failed: {e}")
            raise
    
    def get_infrastructure_status(self) -> Dict[str, Any]:
        """Get comprehensive infrastructure status."""
        status = {
            "initialized": self.initialized,
            "components": self.components_status.copy(),
            "metrics": self.infrastructure_metrics.copy(),
            "configuration": {
                "ollama_url": self.config.ollama_url,
                "enable_onnx_optimization": self.config.enable_onnx_optimization,
                "enable_model_versioning": self.config.enable_model_versioning,
                "enable_multi_agent": self.config.enable_multi_agent,
                "max_agents": self.config.max_agents
            }
        }
        
        # Add component-specific status
        if self.ollama_client:
            status["ollama_status"] = {
                "available_models": self.ollama_client.available_models,
                "default_model": self.ollama_client.default_model,
                "performance_metrics": self.ollama_client.get_performance_metrics()
            }
        
        if self.multi_agent_engine:
            status["multi_agent_status"] = self.multi_agent_engine.get_agent_stats()
        
        if self.model_server:
            status["model_server_status"] = self.model_server.get_server_stats()
        
        return status
    
    async def _monitor_infrastructure(self):
        """Background task to monitor infrastructure health."""
        import time
        start_time = time.time()
        
        while True:
            try:
                # Update uptime
                self.infrastructure_metrics["uptime"] = time.time() - start_time
                
                # Update active models count
                if self.ollama_client:
                    self.infrastructure_metrics["active_models"] = len(self.ollama_client.available_models)
                
                # Check component health
                await self._health_check()
                
                await asyncio.sleep(60)  # Monitor every minute
                
            except Exception as e:
                logger.error(f"Infrastructure monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _health_check(self):
        """Perform health checks on all components."""
        try:
            # Check Ollama client
            if self.ollama_client:
                self.components_status["ollama_client"] = self.ollama_client.is_available()
            
            # Check other components (simplified)
            # In a real implementation, you'd have more sophisticated health checks
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
    
    def _log_component_status(self):
        """Log the status of all components."""
        logger.info("📊 Component Status Summary:")
        for component, status in self.components_status.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"  {status_icon} {component}: {'Active' if status else 'Inactive'}")
    
    async def shutdown(self):
        """Gracefully shutdown the infrastructure."""
        logger.info("Shutting down LLM Infrastructure Manager...")
        
        # Stop background tasks and cleanup resources
        # Implementation would depend on specific component cleanup needs
        
        self.initialized = False
        logger.info("✅ LLM Infrastructure Manager shutdown complete")

# Global instance for easy access
llm_infrastructure = None

async def get_llm_infrastructure(config: LLMInfrastructureConfig = None) -> LLMInfrastructureManager:
    """Get or create the global LLM infrastructure instance."""
    global llm_infrastructure
    
    if llm_infrastructure is None:
        llm_infrastructure = LLMInfrastructureManager(config)
        await llm_infrastructure.initialize()
    
    return llm_infrastructure

# Convenience functions
async def llm_inference(prompt: str, **kwargs) -> Dict[str, Any]:
    """Convenience function for single inference."""
    infrastructure = await get_llm_infrastructure()
    return await infrastructure.inference(prompt, **kwargs)

async def llm_batch_inference(prompts: List[str], **kwargs) -> List[Dict[str, Any]]:
    """Convenience function for batch inference."""
    infrastructure = await get_llm_infrastructure()
    return await infrastructure.batch_inference(prompts, **kwargs)
