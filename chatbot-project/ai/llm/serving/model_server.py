from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Any, Optional, AsyncGenerator
import asyncio
import time
import uuid
from datetime import datetime
import queue
import threading
from enum import Enum

class Priority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class InferenceRequest(BaseModel):
    prompt: str
    max_tokens: int = 150
    temperature: float = 0.7
    context: Optional[Dict[str, Any]] = None
    priority: Priority = Priority.MEDIUM
    stream: bool = False

class BatchInferenceRequest(BaseModel):
    prompts: List[str]
    max_tokens: int = 150
    temperature: float = 0.7
    batch_size: int = 4

class ModelServer:
    def __init__(self, llm_deployment):
        self.deployment = llm_deployment
        self.request_queue = queue.PriorityQueue()
        self.active_requests = {}
        self.batch_processor = None
        self.server_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_latency": 0.0,
            "requests_per_second": 0.0
        }
        self.latency_history = []
        
    async def initialize_server(self) -> bool:
        """Initialize model server"""
        try:
            # Deploy model
            success = await self.deployment.deploy_model()
            if not success:
                return False
            
            # Start batch processor
            self.batch_processor = threading.Thread(target=self._batch_processor_worker, daemon=True)
            self.batch_processor.start()
            
            print("Model server initialized successfully")
            return True
            
        except Exception as e:
            print(f"Server initialization error: {e}")
            return False
    
    async def single_inference(self, request: InferenceRequest) -> Dict[str, Any]:
        """Handle single inference request"""
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            self.server_stats["total_requests"] += 1
            
            # Add to active requests
            self.active_requests[request_id] = {
                "start_time": start_time,
                "status": "processing",
                "priority": request.priority
            }
            
            # Generate response
            if request.stream:
                response = await self._streaming_inference(request)
            else:
                response = await self._standard_inference(request)
            
            # Update stats
            end_time = time.time()
            latency = end_time - start_time
            self._update_stats(latency, True)
            
            # Clean up
            del self.active_requests[request_id]
            
            return {
                "request_id": request_id,
                "response": response,
                "latency": latency,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self._update_stats(time.time() - start_time, False)
            if request_id in self.active_requests:
                del self.active_requests[request_id]
            
            raise HTTPException(status_code=500, detail=f"Inference failed: {str(e)}")
    
    async def _standard_inference(self, request: InferenceRequest) -> str:
        """Standard inference without streaming"""
        if not self.deployment.model or not self.deployment.tokenizer:
            raise Exception("Model not deployed")
        
        # Prepare input
        input_text = self.deployment.tokenizer.encode(
            request.prompt, 
            return_tensors="pt", 
            max_length=512, 
            truncation=True
        )
        
        if hasattr(self.deployment, 'device') and 'cuda' in str(self.deployment.device):
            input_text = input_text.cuda()
        
        # Generate
        import torch
        with torch.no_grad():
            outputs = self.deployment.model.generate(
                input_text,
                max_length=input_text.shape[1] + request.max_tokens,
                temperature=request.temperature,
                do_sample=True,
                pad_token_id=self.deployment.tokenizer.eos_token_id
            )
        
        # Decode
        response = self.deployment.tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = response[len(request.prompt):].strip()
        
        return generated_text if generated_text else "I need more information to provide a response."
    
    async def _streaming_inference(self, request: InferenceRequest) -> AsyncGenerator[str, None]:
        """Streaming inference for real-time responses"""
        # Simplified streaming - in production, implement token-by-token streaming
        response = await self._standard_inference(request)
        
        # Simulate streaming by yielding chunks
        words = response.split()
        for i, word in enumerate(words):
            yield word + " "
            await asyncio.sleep(0.1)  # Simulate streaming delay
    
    async def batch_inference(self, request: BatchInferenceRequest) -> Dict[str, Any]:
        """Handle batch inference requests"""
        start_time = time.time()
        
        try:
            responses = []
            
            # Process in batches
            for i in range(0, len(request.prompts), request.batch_size):
                batch = request.prompts[i:i + request.batch_size]
                batch_responses = await self._process_batch(batch, request)
                responses.extend(batch_responses)
            
            end_time = time.time()
            
            return {
                "batch_id": str(uuid.uuid4()),
                "responses": responses,
                "total_prompts": len(request.prompts),
                "processing_time": end_time - start_time,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Batch inference failed: {str(e)}")
    
    async def _process_batch(self, prompts: List[str], request: BatchInferenceRequest) -> List[str]:
        """Process a batch of prompts"""
        if not self.deployment.model or not self.deployment.tokenizer:
            raise Exception("Model not deployed")
        
        responses = []
        
        # Simple sequential processing - in production, implement true batching
        for prompt in prompts:
            inference_request = InferenceRequest(
                prompt=prompt,
                max_tokens=request.max_tokens,
                temperature=request.temperature
            )
            
            response = await self._standard_inference(inference_request)
            responses.append(response)
        
        return responses
    
    def _batch_processor_worker(self):
        """Background worker for processing queued requests"""
        while True:
            try:
                if not self.request_queue.empty():
                    priority, request_data = self.request_queue.get()
                    # Process request (simplified)
                    time.sleep(0.1)  # Simulate processing
                else:
                    time.sleep(0.1)  # Wait for requests
            except Exception as e:
                print(f"Batch processor error: {e}")
    
    def add_to_queue(self, request: InferenceRequest) -> str:
        """Add request to priority queue"""
        request_id = str(uuid.uuid4())
        
        # Priority queue uses negative values for higher priority
        priority_value = -request.priority.value
        
        self.request_queue.put((priority_value, {
            "request_id": request_id,
            "request": request,
            "timestamp": datetime.now().isoformat()
        }))
        
        return request_id
    
    def _update_stats(self, latency: float, success: bool):
        """Update server statistics"""
        if success:
            self.server_stats["successful_requests"] += 1
        else:
            self.server_stats["failed_requests"] += 1
        
        # Update latency
        self.latency_history.append(latency)
        if len(self.latency_history) > 1000:  # Keep last 1000 requests
            self.latency_history = self.latency_history[-1000:]
        
        self.server_stats["avg_latency"] = sum(self.latency_history) / len(self.latency_history)
        
        # Calculate requests per second (simplified)
        if len(self.latency_history) > 1:
            self.server_stats["requests_per_second"] = 1.0 / self.server_stats["avg_latency"]
    
    def get_server_stats(self) -> Dict[str, Any]:
        """Get server statistics"""
        return {
            **self.server_stats,
            "active_requests": len(self.active_requests),
            "queue_size": self.request_queue.qsize(),
            "deployment_status": self.deployment.get_deployment_status()
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get server health status"""
        memory_usage = self.deployment.get_memory_usage()
        
        health_status = {
            "status": "healthy",
            "model_loaded": self.deployment.model is not None,
            "memory_usage": memory_usage,
            "active_requests": len(self.active_requests),
            "queue_size": self.request_queue.qsize(),
            "uptime": "unknown"  # Would track actual uptime in production
        }
        
        # Determine health status
        if memory_usage.get("memory_percent", 0) > 90:
            health_status["status"] = "warning"
        
        if not self.deployment.model:
            health_status["status"] = "unhealthy"
        
        return health_status

# Enhanced FastAPI application
from .enhanced_model_server import EnhancedModelServer, InferenceRequest, BatchInferenceRequest
from fastapi.responses import StreamingResponse

app = FastAPI(
    title="Enhanced LLM Model Server",
    description="Advanced LLM serving with Llama 3 support, streaming, batch processing, and load balancing",
    version="2.0.0"
)

# Global enhanced server instance
enhanced_server = None

@app.on_event("startup")
async def startup_event():
    global enhanced_server
    enhanced_server = EnhancedModelServer()

    success = await enhanced_server.initialize_server()
    if not success:
        logger.error("Failed to initialize enhanced model server")
        raise Exception("Server initialization failed")

    logger.info("Enhanced LLM Model Server started successfully")

@app.post("/inference", response_model=InferenceResponse)
async def inference_endpoint(request: InferenceRequest):
    """Single inference endpoint with enhanced features."""
    if not enhanced_server:
        raise HTTPException(status_code=503, detail="Model server not initialized")

    return await enhanced_server.single_inference(request)

@app.post("/inference/batch", response_model=List[InferenceResponse])
async def batch_inference_endpoint(request: BatchInferenceRequest):
    """Batch inference endpoint for processing multiple prompts."""
    if not enhanced_server:
        raise HTTPException(status_code=503, detail="Model server not initialized")

    return await enhanced_server.batch_inference(request)

@app.post("/inference/stream")
async def stream_inference_endpoint(request: InferenceRequest):
    """Streaming inference endpoint."""
    if not enhanced_server:
        raise HTTPException(status_code=503, detail="Model server not initialized")

    # Force streaming mode
    request.stream = True

    return StreamingResponse(
        enhanced_server.stream_inference(request),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@app.get("/models")
async def list_models():
    """List available models and their capabilities."""
    if not enhanced_server:
        raise HTTPException(status_code=503, detail="Model server not initialized")

    models_info = {}
    for model in enhanced_server.ollama_client.available_models:
        capabilities = enhanced_server.ollama_client.get_model_capabilities(model)
        models_info[model] = capabilities

    return {
        "available_models": models_info,
        "default_model": enhanced_server.ollama_client.default_model
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    if not enhanced_server:
        return {"status": "unhealthy", "reason": "Server not initialized"}

    return {
        "status": "healthy",
        "server_stats": enhanced_server.get_server_stats(),
        "ollama_available": enhanced_server.ollama_client.is_available()
    }

@app.get("/stats")
async def get_stats():
    """Get detailed server statistics."""
    if not enhanced_server:
        raise HTTPException(status_code=503, detail="Model server not initialized")

    return enhanced_server.get_server_stats()

@app.post("/batch_inference")
async def batch_inference_endpoint(request: BatchInferenceRequest):
    if not model_server:
        raise HTTPException(status_code=503, detail="Model server not initialized")
    
    return await model_server.batch_inference(request)

@app.get("/stats")
async def get_stats():
    if not model_server:
        raise HTTPException(status_code=503, detail="Model server not initialized")
    
    return model_server.get_server_stats()

@app.get("/health")
async def health_check():
    if not model_server:
        return {"status": "unhealthy", "reason": "Model server not initialized"}
    
    return model_server.get_health_status()