"""Enhanced Model Serving API with Advanced Features."""

import asyncio
import time
import uuid
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import logging
from contextlib import asynccontextmanager
import json

from ..ollama_client import OllamaClient, ModelType
from ..optimization.onnx_optimizer import ONNXOptimizer, OptimizationConfig

logger = logging.getLogger(__name__)

class RequestPriority(Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"

class ModelStatus(Enum):
    LOADING = "loading"
    READY = "ready"
    BUSY = "busy"
    ERROR = "error"
    MAINTENANCE = "maintenance"

@dataclass
class ModelInstance:
    model_name: str
    status: ModelStatus
    load_time: float
    last_used: float
    request_count: int
    error_count: int
    average_latency: float
    memory_usage: float

class InferenceRequest(BaseModel):
    prompt: str
    model: Optional[str] = None
    max_tokens: int = Field(default=500, ge=1, le=4096)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    top_p: float = Field(default=0.9, ge=0.0, le=1.0)
    top_k: int = Field(default=40, ge=1, le=100)
    stream: bool = False
    priority: RequestPriority = RequestPriority.NORMAL
    timeout: int = Field(default=60, ge=1, le=300)
    context_length: int = Field(default=4096, ge=512, le=8192)
    request_id: Optional[str] = None

class BatchInferenceRequest(BaseModel):
    prompts: List[str]
    model: Optional[str] = None
    max_tokens: int = Field(default=500, ge=1, le=4096)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_concurrent: int = Field(default=5, ge=1, le=20)
    priority: RequestPriority = RequestPriority.NORMAL
    timeout: int = Field(default=120, ge=1, le=600)

class InferenceResponse(BaseModel):
    request_id: str
    response: str
    model: str
    tokens_generated: int
    latency_ms: float
    status: str
    metadata: Dict[str, Any] = {}

class ModelLoadBalancer:
    """Load balancer for multiple model instances."""
    
    def __init__(self):
        self.model_instances: Dict[str, List[ModelInstance]] = {}
        self.request_queue: Dict[RequestPriority, asyncio.Queue] = {
            priority: asyncio.Queue() for priority in RequestPriority
        }
        self.active_requests: Dict[str, Dict[str, Any]] = {}
        
    def register_model_instance(self, model_name: str, instance: ModelInstance):
        """Register a model instance."""
        if model_name not in self.model_instances:
            self.model_instances[model_name] = []
        self.model_instances[model_name].append(instance)
        logger.info(f"Registered model instance: {model_name}")
    
    def get_best_instance(self, model_name: str) -> Optional[ModelInstance]:
        """Get the best available instance for a model."""
        if model_name not in self.model_instances:
            return None
        
        available_instances = [
            instance for instance in self.model_instances[model_name]
            if instance.status == ModelStatus.READY
        ]
        
        if not available_instances:
            return None
        
        # Select instance with lowest load (request count / average latency)
        best_instance = min(
            available_instances,
            key=lambda x: x.request_count * x.average_latency
        )
        
        return best_instance
    
    def update_instance_metrics(self, model_name: str, instance_id: str, 
                              latency: float, success: bool):
        """Update metrics for a model instance."""
        for instance in self.model_instances.get(model_name, []):
            if instance.model_name == instance_id:
                instance.request_count += 1
                instance.last_used = time.time()
                
                # Update average latency (exponential moving average)
                alpha = 0.1
                instance.average_latency = (
                    alpha * latency + (1 - alpha) * instance.average_latency
                )
                
                if not success:
                    instance.error_count += 1
                
                break

class EnhancedModelServer:
    """Enhanced model server with advanced features."""
    
    def __init__(self):
        self.ollama_client = OllamaClient()
        self.onnx_optimizer = None
        self.load_balancer = ModelLoadBalancer()
        self.request_metrics: Dict[str, Any] = {}
        self.server_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_latency": 0.0,
            "uptime": time.time()
        }
        
        # Initialize ONNX optimizer if available
        try:
            self.onnx_optimizer = ONNXOptimizer()
            logger.info("ONNX optimizer initialized")
        except ImportError:
            logger.warning("ONNX optimizer not available")
    
    async def initialize_server(self) -> bool:
        """Initialize the model server."""
        try:
            # Check Ollama connection
            if not self.ollama_client.is_available():
                logger.error("Ollama client not available")
                return False
            
            # Register available models as instances
            for model_name in self.ollama_client.available_models:
                instance = ModelInstance(
                    model_name=model_name,
                    status=ModelStatus.READY,
                    load_time=time.time(),
                    last_used=0.0,
                    request_count=0,
                    error_count=0,
                    average_latency=0.0,
                    memory_usage=0.0
                )
                self.load_balancer.register_model_instance(model_name, instance)
            
            # Start background tasks
            asyncio.create_task(self._process_request_queue())
            asyncio.create_task(self._monitor_health())
            
            logger.info("Enhanced model server initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize server: {e}")
            return False
    
    async def single_inference(self, request: InferenceRequest) -> InferenceResponse:
        """Process a single inference request."""
        request_id = request.request_id or str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Update server stats
            self.server_stats["total_requests"] += 1
            
            # Get best model instance
            model_name = request.model or self.ollama_client.default_model
            instance = self.load_balancer.get_best_instance(model_name)
            
            if not instance:
                raise HTTPException(
                    status_code=503, 
                    detail=f"No available instances for model {model_name}"
                )
            
            # Mark instance as busy
            instance.status = ModelStatus.BUSY
            
            # Process request
            if request.stream:
                # For streaming, we'll handle it differently
                raise HTTPException(
                    status_code=400,
                    detail="Use /inference/stream endpoint for streaming requests"
                )
            
            # Generate response
            result = await self.ollama_client.generate_async(
                prompt=request.prompt,
                model=model_name,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                timeout=request.timeout,
                context_length=request.context_length
            )
            
            # Calculate metrics
            latency_ms = (time.time() - start_time) * 1000
            
            # Update instance metrics
            success = "error" not in result
            self.load_balancer.update_instance_metrics(
                model_name, instance.model_name, latency_ms / 1000, success
            )
            
            # Mark instance as ready
            instance.status = ModelStatus.READY
            
            if success:
                self.server_stats["successful_requests"] += 1
                
                # Update average latency
                total_requests = self.server_stats["total_requests"]
                current_avg = self.server_stats["average_latency"]
                self.server_stats["average_latency"] = (
                    (current_avg * (total_requests - 1) + latency_ms) / total_requests
                )
                
                return InferenceResponse(
                    request_id=request_id,
                    response=result["response"],
                    model=model_name,
                    tokens_generated=result.get("eval_count", 0),
                    latency_ms=latency_ms,
                    status="success",
                    metadata={
                        "performance_metrics": result.get("performance_metrics", {}),
                        "instance_id": instance.model_name
                    }
                )
            else:
                self.server_stats["failed_requests"] += 1
                raise HTTPException(
                    status_code=500,
                    detail=f"Generation failed: {result.get('error', 'Unknown error')}"
                )
                
        except Exception as e:
            self.server_stats["failed_requests"] += 1
            logger.error(f"Inference failed for request {request_id}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def batch_inference(self, request: BatchInferenceRequest) -> List[InferenceResponse]:
        """Process batch inference requests."""
        if not request.prompts:
            raise HTTPException(status_code=400, detail="No prompts provided")
        
        try:
            # Process batch using Ollama client
            results = await self.ollama_client.batch_generate(
                prompts=request.prompts,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                max_concurrent=request.max_concurrent,
                timeout=request.timeout
            )
            
            # Convert to response format
            responses = []
            for i, result in enumerate(results):
                request_id = str(uuid.uuid4())
                
                if "error" not in result:
                    response = InferenceResponse(
                        request_id=request_id,
                        response=result["response"],
                        model=result.get("model", request.model or self.ollama_client.default_model),
                        tokens_generated=result.get("eval_count", 0),
                        latency_ms=0.0,  # Individual latency not tracked in batch
                        status="success",
                        metadata={"batch_index": i}
                    )
                else:
                    response = InferenceResponse(
                        request_id=request_id,
                        response="",
                        model=request.model or self.ollama_client.default_model,
                        tokens_generated=0,
                        latency_ms=0.0,
                        status="failed",
                        metadata={"batch_index": i, "error": result.get("error")}
                    )
                
                responses.append(response)
            
            return responses
            
        except Exception as e:
            logger.error(f"Batch inference failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def stream_inference(self, request: InferenceRequest) -> AsyncGenerator[str, None]:
        """Process streaming inference request."""
        try:
            model_name = request.model or self.ollama_client.default_model
            
            async for chunk in self.ollama_client.generate_stream(
                prompt=request.prompt,
                model=model_name,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                timeout=request.timeout
            ):
                # Format as Server-Sent Events
                data = {
                    "id": request.request_id or str(uuid.uuid4()),
                    "object": "text_completion",
                    "created": int(time.time()),
                    "model": model_name,
                    "choices": [{
                        "text": chunk.get("response", ""),
                        "index": 0,
                        "finish_reason": "stop" if chunk.get("done", False) else None
                    }]
                }
                
                yield f"data: {json.dumps(data)}\n\n"
                
                if chunk.get("done", False):
                    yield "data: [DONE]\n\n"
                    break
                    
        except Exception as e:
            error_data = {
                "error": {
                    "message": str(e),
                    "type": "server_error"
                }
            }
            yield f"data: {json.dumps(error_data)}\n\n"
    
    async def _process_request_queue(self):
        """Background task to process request queue."""
        while True:
            try:
                # Process requests by priority
                for priority in [RequestPriority.CRITICAL, RequestPriority.HIGH, 
                               RequestPriority.NORMAL, RequestPriority.LOW]:
                    queue = self.load_balancer.request_queue[priority]
                    
                    if not queue.empty():
                        request = await queue.get()
                        # Process request (implementation depends on queue structure)
                        pass
                
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                
            except Exception as e:
                logger.error(f"Error in request queue processing: {e}")
                await asyncio.sleep(1)
    
    async def _monitor_health(self):
        """Background task to monitor server health."""
        while True:
            try:
                # Check model instance health
                for model_name, instances in self.load_balancer.model_instances.items():
                    for instance in instances:
                        # Check if instance is responsive
                        if instance.status == ModelStatus.BUSY:
                            # Check if it's been busy too long
                            if time.time() - instance.last_used > 300:  # 5 minutes
                                instance.status = ModelStatus.ERROR
                                logger.warning(f"Instance {instance.model_name} marked as error due to timeout")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                await asyncio.sleep(60)
    
    def get_server_stats(self) -> Dict[str, Any]:
        """Get server statistics."""
        uptime = time.time() - self.server_stats["uptime"]
        
        return {
            "server_stats": {
                **self.server_stats,
                "uptime_seconds": uptime,
                "requests_per_second": self.server_stats["total_requests"] / max(uptime, 1)
            },
            "model_instances": {
                model_name: [asdict(instance) for instance in instances]
                for model_name, instances in self.load_balancer.model_instances.items()
            },
            "available_models": self.ollama_client.available_models,
            "default_model": self.ollama_client.default_model
        }
