"""Multi-agent LLM Inference with Load Balancing and Resource Management."""

import asyncio
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging
from concurrent.futures import ThreadPoolExecutor
import threading
import queue
import psutil
import torch

from ..ollama_client import OllamaClient, ModelType
from ..serving.enhanced_model_server import EnhancedModelServer, InferenceRequest, InferenceResponse

logger = logging.getLogger(__name__)

class AgentStatus(Enum):
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class LoadBalancingStrategy(Enum):
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    WEIGHTED = "weighted"
    PERFORMANCE_BASED = "performance_based"
    CAPABILITY_BASED = "capability_based"

@dataclass
class AgentMetrics:
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_latency: float = 0.0
    current_load: float = 0.0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    last_request_time: float = 0.0
    error_rate: float = 0.0

@dataclass
class AgentCapabilities:
    model_types: List[ModelType]
    max_context_length: int
    supports_streaming: bool
    supports_batch: bool
    specialized_tasks: List[str]
    performance_tier: str  # "high", "medium", "low"

@dataclass
class LLMAgent:
    agent_id: str
    name: str
    ollama_client: OllamaClient
    status: AgentStatus
    capabilities: AgentCapabilities
    metrics: AgentMetrics = field(default_factory=AgentMetrics)
    max_concurrent_requests: int = 5
    current_requests: int = 0
    weight: float = 1.0
    priority: int = 1

class MultiAgentInferenceEngine:
    """Multi-agent LLM inference engine with load balancing and resource management."""
    
    def __init__(self, max_agents: int = 10):
        self.agents: Dict[str, LLMAgent] = {}
        self.max_agents = max_agents
        self.load_balancer_strategy = LoadBalancingStrategy.PERFORMANCE_BASED
        self.request_queue = asyncio.Queue()
        self.result_cache: Dict[str, Any] = {}
        self.cache_ttl = 3600  # 1 hour
        
        # Resource management
        self.resource_monitor = ResourceMonitor()
        self.request_router = RequestRouter()
        
        # Performance tracking
        self.global_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_latency": 0.0,
            "agents_active": 0,
            "queue_size": 0
        }
        
        # Background tasks
        self._monitoring_task = None
        self._queue_processor_task = None
        
    async def initialize(self):
        """Initialize the multi-agent inference engine."""
        try:
            # Auto-discover and register agents
            await self._auto_discover_agents()
            
            # Start background tasks
            self._monitoring_task = asyncio.create_task(self._monitor_agents())
            self._queue_processor_task = asyncio.create_task(self._process_request_queue())
            
            logger.info(f"Multi-agent inference engine initialized with {len(self.agents)} agents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize multi-agent engine: {e}")
            return False
    
    async def _auto_discover_agents(self):
        """Auto-discover available agents and their capabilities."""
        try:
            # Create primary agent with main Ollama instance
            primary_client = OllamaClient()
            if primary_client.is_available():
                primary_agent = LLMAgent(
                    agent_id="primary_agent",
                    name="Primary Llama 3 Agent",
                    ollama_client=primary_client,
                    status=AgentStatus.IDLE,
                    capabilities=AgentCapabilities(
                        model_types=[ModelType.LLAMA3, ModelType.LLAMA2],
                        max_context_length=8192,
                        supports_streaming=True,
                        supports_batch=True,
                        specialized_tasks=["general", "reasoning", "conversation"],
                        performance_tier="high"
                    ),
                    max_concurrent_requests=10,
                    weight=1.0,
                    priority=1
                )
                self.agents[primary_agent.agent_id] = primary_agent
                logger.info("Registered primary Llama 3 agent")
            
            # Create specialized agents for different tasks
            specialized_configs = [
                {
                    "agent_id": "reasoning_agent",
                    "name": "Reasoning Specialist",
                    "specialized_tasks": ["reasoning", "analysis", "problem_solving"],
                    "performance_tier": "high",
                    "weight": 1.2
                },
                {
                    "agent_id": "conversation_agent", 
                    "name": "Conversation Specialist",
                    "specialized_tasks": ["conversation", "chat", "dialogue"],
                    "performance_tier": "medium",
                    "weight": 1.0
                },
                {
                    "agent_id": "code_agent",
                    "name": "Code Generation Specialist", 
                    "specialized_tasks": ["code_generation", "programming", "technical"],
                    "performance_tier": "high",
                    "weight": 1.1
                }
            ]
            
            for config in specialized_configs:
                if len(self.agents) < self.max_agents:
                    # Create agent with shared Ollama client but different specialization
                    agent = LLMAgent(
                        agent_id=config["agent_id"],
                        name=config["name"],
                        ollama_client=primary_client,  # Shared client
                        status=AgentStatus.IDLE,
                        capabilities=AgentCapabilities(
                            model_types=[ModelType.LLAMA3],
                            max_context_length=8192,
                            supports_streaming=True,
                            supports_batch=True,
                            specialized_tasks=config["specialized_tasks"],
                            performance_tier=config["performance_tier"]
                        ),
                        max_concurrent_requests=5,
                        weight=config["weight"],
                        priority=2
                    )
                    self.agents[agent.agent_id] = agent
                    logger.info(f"Registered specialized agent: {config['name']}")
            
        except Exception as e:
            logger.error(f"Failed to auto-discover agents: {e}")
    
    async def inference(self, request: InferenceRequest, 
                       agent_selection_criteria: Dict[str, Any] = None) -> InferenceResponse:
        """Perform inference using the best available agent."""
        request_id = request.request_id or str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(request)
            if cache_key in self.result_cache:
                cached_result = self.result_cache[cache_key]
                if time.time() - cached_result["timestamp"] < self.cache_ttl:
                    logger.info(f"Cache hit for request {request_id}")
                    return cached_result["response"]
            
            # Select best agent
            agent = await self._select_agent(request, agent_selection_criteria)
            if not agent:
                raise Exception("No available agents for this request")
            
            # Update agent status
            agent.status = AgentStatus.BUSY
            agent.current_requests += 1
            
            # Process request
            result = await self._process_with_agent(agent, request)
            
            # Update metrics
            latency = time.time() - start_time
            self._update_agent_metrics(agent, latency, True)
            self._update_global_metrics(latency, True)
            
            # Cache result
            if request.max_tokens <= 1000:  # Only cache smaller responses
                self.result_cache[cache_key] = {
                    "response": result,
                    "timestamp": time.time()
                }
            
            return result
            
        except Exception as e:
            # Update error metrics
            latency = time.time() - start_time
            if 'agent' in locals():
                self._update_agent_metrics(agent, latency, False)
            self._update_global_metrics(latency, False)
            
            logger.error(f"Inference failed for request {request_id}: {e}")
            raise
        
        finally:
            # Reset agent status
            if 'agent' in locals():
                agent.status = AgentStatus.IDLE
                agent.current_requests = max(0, agent.current_requests - 1)
    
    async def batch_inference(self, requests: List[InferenceRequest]) -> List[InferenceResponse]:
        """Process multiple inference requests in parallel."""
        if not requests:
            return []
        
        # Group requests by optimal agent
        agent_groups = await self._group_requests_by_agent(requests)
        
        # Process groups in parallel
        tasks = []
        for agent_id, agent_requests in agent_groups.items():
            agent = self.agents[agent_id]
            task = self._process_batch_with_agent(agent, agent_requests)
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Flatten results and handle exceptions
        all_responses = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Batch processing failed: {result}")
                # Create error responses
                error_response = InferenceResponse(
                    request_id=str(uuid.uuid4()),
                    response=f"Batch processing error: {result}",
                    model="unknown",
                    tokens_generated=0,
                    latency_ms=0.0,
                    status="error"
                )
                all_responses.append(error_response)
            else:
                all_responses.extend(result)
        
        return all_responses
    
    async def _select_agent(self, request: InferenceRequest, 
                           criteria: Dict[str, Any] = None) -> Optional[LLMAgent]:
        """Select the best agent for a request based on strategy and criteria."""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.status == AgentStatus.IDLE and 
            agent.current_requests < agent.max_concurrent_requests
        ]
        
        if not available_agents:
            return None
        
        # Apply capability filtering
        if criteria:
            task_type = criteria.get("task_type")
            if task_type:
                available_agents = [
                    agent for agent in available_agents
                    if task_type in agent.capabilities.specialized_tasks or
                    "general" in agent.capabilities.specialized_tasks
                ]
        
        if not available_agents:
            return None
        
        # Apply load balancing strategy
        if self.load_balancer_strategy == LoadBalancingStrategy.PERFORMANCE_BASED:
            # Select based on performance metrics
            return min(available_agents, key=lambda a: (
                a.metrics.error_rate * 10 +  # Penalize high error rates
                a.metrics.average_latency +   # Prefer lower latency
                a.current_requests / a.max_concurrent_requests  # Prefer less loaded
            ))
        
        elif self.load_balancer_strategy == LoadBalancingStrategy.LEAST_LOADED:
            return min(available_agents, key=lambda a: a.current_requests)
        
        elif self.load_balancer_strategy == LoadBalancingStrategy.WEIGHTED:
            # Weighted random selection
            import random
            weights = [agent.weight for agent in available_agents]
            return random.choices(available_agents, weights=weights)[0]
        
        else:  # ROUND_ROBIN
            # Simple round-robin (simplified implementation)
            return available_agents[self.global_metrics["total_requests"] % len(available_agents)]
    
    async def _process_with_agent(self, agent: LLMAgent, request: InferenceRequest) -> InferenceResponse:
        """Process a request with a specific agent."""
        try:
            # Use the agent's Ollama client
            result = await agent.ollama_client.generate_async(
                prompt=request.prompt,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                top_k=request.top_k,
                timeout=request.timeout,
                context_length=request.context_length
            )
            
            if "error" in result:
                raise Exception(result["error"])
            
            return InferenceResponse(
                request_id=request.request_id or str(uuid.uuid4()),
                response=result["response"],
                model=result.get("model", request.model or agent.ollama_client.default_model),
                tokens_generated=result.get("eval_count", 0),
                latency_ms=0.0,  # Will be calculated by caller
                status="success",
                metadata={
                    "agent_id": agent.agent_id,
                    "agent_name": agent.name,
                    "performance_metrics": result.get("performance_metrics", {})
                }
            )
            
        except Exception as e:
            logger.error(f"Agent {agent.agent_id} failed to process request: {e}")
            raise
    
    async def _process_batch_with_agent(self, agent: LLMAgent, 
                                       requests: List[InferenceRequest]) -> List[InferenceResponse]:
        """Process a batch of requests with a specific agent."""
        try:
            # Extract prompts
            prompts = [req.prompt for req in requests]
            
            # Use batch processing if available
            results = await agent.ollama_client.batch_generate(
                prompts=prompts,
                model=requests[0].model if requests else None,
                max_tokens=requests[0].max_tokens if requests else 500,
                temperature=requests[0].temperature if requests else 0.7,
                max_concurrent=min(len(requests), agent.max_concurrent_requests)
            )
            
            # Convert to response format
            responses = []
            for i, (request, result) in enumerate(zip(requests, results)):
                if "error" not in result:
                    response = InferenceResponse(
                        request_id=request.request_id or str(uuid.uuid4()),
                        response=result["response"],
                        model=result.get("model", request.model or agent.ollama_client.default_model),
                        tokens_generated=result.get("eval_count", 0),
                        latency_ms=0.0,
                        status="success",
                        metadata={
                            "agent_id": agent.agent_id,
                            "batch_index": i
                        }
                    )
                else:
                    response = InferenceResponse(
                        request_id=request.request_id or str(uuid.uuid4()),
                        response="",
                        model=request.model or agent.ollama_client.default_model,
                        tokens_generated=0,
                        latency_ms=0.0,
                        status="error",
                        metadata={
                            "agent_id": agent.agent_id,
                            "batch_index": i,
                            "error": result.get("error")
                        }
                    )
                responses.append(response)
            
            return responses
            
        except Exception as e:
            logger.error(f"Batch processing failed for agent {agent.agent_id}: {e}")
            raise
    
    def _update_agent_metrics(self, agent: LLMAgent, latency: float, success: bool):
        """Update metrics for an agent."""
        agent.metrics.total_requests += 1
        agent.metrics.last_request_time = time.time()
        
        if success:
            agent.metrics.successful_requests += 1
        else:
            agent.metrics.failed_requests += 1
        
        # Update average latency (exponential moving average)
        alpha = 0.1
        agent.metrics.average_latency = (
            alpha * latency + (1 - alpha) * agent.metrics.average_latency
        )
        
        # Update error rate
        agent.metrics.error_rate = (
            agent.metrics.failed_requests / agent.metrics.total_requests
        )
        
        # Update current load
        agent.metrics.current_load = agent.current_requests / agent.max_concurrent_requests
    
    def _update_global_metrics(self, latency: float, success: bool):
        """Update global metrics."""
        self.global_metrics["total_requests"] += 1
        
        if success:
            self.global_metrics["successful_requests"] += 1
        else:
            self.global_metrics["failed_requests"] += 1
        
        # Update average latency
        total = self.global_metrics["total_requests"]
        current_avg = self.global_metrics["average_latency"]
        self.global_metrics["average_latency"] = (
            (current_avg * (total - 1) + latency) / total
        )
        
        # Update active agents count
        self.global_metrics["agents_active"] = len([
            agent for agent in self.agents.values()
            if agent.status == AgentStatus.BUSY
        ])
    
    def _generate_cache_key(self, request: InferenceRequest) -> str:
        """Generate cache key for a request."""
        import hashlib
        key_data = f"{request.prompt}_{request.model}_{request.max_tokens}_{request.temperature}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def _group_requests_by_agent(self, requests: List[InferenceRequest]) -> Dict[str, List[InferenceRequest]]:
        """Group requests by optimal agent."""
        groups = {}
        
        for request in requests:
            agent = await self._select_agent(request)
            if agent:
                if agent.agent_id not in groups:
                    groups[agent.agent_id] = []
                groups[agent.agent_id].append(request)
        
        return groups
    
    async def _monitor_agents(self):
        """Background task to monitor agent health and performance."""
        while True:
            try:
                for agent in self.agents.values():
                    # Update resource usage
                    agent.metrics.memory_usage = psutil.virtual_memory().percent
                    agent.metrics.cpu_usage = psutil.cpu_percent()
                    
                    # Check for stuck agents
                    if (agent.status == AgentStatus.BUSY and 
                        time.time() - agent.metrics.last_request_time > 300):  # 5 minutes
                        logger.warning(f"Agent {agent.agent_id} appears stuck, resetting")
                        agent.status = AgentStatus.IDLE
                        agent.current_requests = 0
                
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in agent monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _process_request_queue(self):
        """Background task to process queued requests."""
        while True:
            try:
                # This would implement request queuing if needed
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error in request queue processing: {e}")
                await asyncio.sleep(5)
    
    def get_agent_stats(self) -> Dict[str, Any]:
        """Get statistics for all agents."""
        return {
            "global_metrics": self.global_metrics,
            "agents": {
                agent_id: {
                    "name": agent.name,
                    "status": agent.status.value,
                    "capabilities": agent.capabilities.__dict__,
                    "metrics": agent.metrics.__dict__,
                    "current_load": agent.current_requests / agent.max_concurrent_requests
                }
                for agent_id, agent in self.agents.items()
            },
            "total_agents": len(self.agents),
            "active_agents": len([a for a in self.agents.values() if a.status == AgentStatus.BUSY]),
            "load_balancing_strategy": self.load_balancer_strategy.value
        }

class ResourceMonitor:
    """Monitor system resources for optimal agent allocation."""
    
    def __init__(self):
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.gpu_threshold = 90.0
    
    def get_resource_status(self) -> Dict[str, Any]:
        """Get current resource status."""
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "gpu_available": torch.cuda.is_available(),
            "gpu_memory_percent": self._get_gpu_memory_usage() if torch.cuda.is_available() else 0
        }
    
    def _get_gpu_memory_usage(self) -> float:
        """Get GPU memory usage percentage."""
        if torch.cuda.is_available():
            total_memory = torch.cuda.get_device_properties(0).total_memory
            allocated_memory = torch.cuda.memory_allocated(0)
            return (allocated_memory / total_memory) * 100
        return 0.0

class RequestRouter:
    """Route requests to appropriate agents based on content analysis."""
    
    def __init__(self):
        self.task_keywords = {
            "reasoning": ["analyze", "reason", "logic", "solve", "problem", "think"],
            "conversation": ["chat", "talk", "discuss", "conversation", "dialogue"],
            "code_generation": ["code", "program", "function", "class", "script", "debug"],
            "creative": ["write", "story", "creative", "poem", "generate", "imagine"]
        }
    
    def analyze_request(self, request: InferenceRequest) -> Dict[str, Any]:
        """Analyze request to determine optimal routing."""
        prompt_lower = request.prompt.lower()
        
        task_scores = {}
        for task_type, keywords in self.task_keywords.items():
            score = sum(1 for keyword in keywords if keyword in prompt_lower)
            task_scores[task_type] = score
        
        # Determine primary task type
        primary_task = max(task_scores, key=task_scores.get) if task_scores else "general"
        
        return {
            "task_type": primary_task,
            "task_scores": task_scores,
            "complexity": len(request.prompt.split()),
            "requires_reasoning": any(word in prompt_lower for word in ["why", "how", "analyze", "explain"])
        }
