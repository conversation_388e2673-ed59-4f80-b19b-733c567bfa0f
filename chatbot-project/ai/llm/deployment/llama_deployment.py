import torch
import torch.nn as nn
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    BitsAndBytesConfig,
    LlamaTokenizer,
    LlamaForCausalLM,
    pipeline
)
from typing import Dict, List, Any, Optional, Union
import asyncio
import time
import psutil
import GPUtil
import os
import json
import requests
import shutil
from pathlib import Path
from huggingface_hub import snapshot_download, login
import logging

logger = logging.getLogger(__name__)

class LlamaDeployment:
    def __init__(self, model_name: str = "meta-llama/Llama-2-7b-chat-hf"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.device_map = None
        self.devices = []
        self.model_shards = {}
        self.performance_metrics = {}
        
    def configure_hardware(self) -> Dict[str, Any]:
        """Configure hardware for optimal inference"""
        config = {
            "gpu_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            "cpu_cores": psutil.cpu_count(),
            "memory_gb": psutil.virtual_memory().total / (1024**3),
            "recommended_setup": "cpu"
        }
        
        if config["gpu_available"]:
            try:
                gpus = GPUtil.getGPUs()
                config["gpu_info"] = [
                    {"id": gpu.id, "name": gpu.name, "memory_mb": gpu.memoryTotal}
                    for gpu in gpus
                ]
                config["recommended_setup"] = "gpu"
            except:
                config["gpu_info"] = []
        
        # Determine optimal device mapping
        if config["gpu_count"] > 1:
            config["recommended_setup"] = "multi_gpu"
            self.device_map = self.create_device_map(config["gpu_count"])
        elif config["gpu_count"] == 1:
            self.device_map = {"": 0}
        else:
            self.device_map = {"": "cpu"}
        
        return config
    
    def create_device_map(self, num_gpus: int) -> Dict[str, int]:
        """Create device mapping for model sharding"""
        # Simple layer-wise sharding
        device_map = {}
        
        # Distribute layers across GPUs
        layers_per_gpu = 32 // num_gpus  # Assuming 32 layers for Llama-2-7b
        
        for i in range(num_gpus):
            start_layer = i * layers_per_gpu
            end_layer = min((i + 1) * layers_per_gpu, 32)
            
            for layer in range(start_layer, end_layer):
                device_map[f"model.layers.{layer}"] = i
        
        # Place embedding and output layers
        device_map["model.embed_tokens"] = 0
        device_map["model.norm"] = num_gpus - 1
        device_map["lm_head"] = num_gpus - 1
        
        return device_map
    
    async def deploy_model(self, use_onnx: bool = False) -> bool:
        """Deploy Llama model with optimizations"""
        try:
            print(f"Deploying {self.model_name}...")
            
            # Configure hardware
            hw_config = self.configure_hardware()
            print(f"Hardware config: {hw_config['recommended_setup']}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model with optimizations
            if use_onnx:
                success = await self.deploy_with_onnx()
            else:
                success = await self.deploy_with_pytorch()
            
            if success:
                print("Model deployed successfully")
                return True
            else:
                print("Model deployment failed")
                return False
                
        except Exception as e:
            print(f"Deployment error: {e}")
            return False
    
    async def deploy_with_pytorch(self) -> bool:
        """Deploy with PyTorch optimizations"""
        try:
            # Load model with device mapping
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                device_map=self.device_map,
                torch_dtype=torch.float16,  # Use half precision
                low_cpu_mem_usage=True,
                trust_remote_code=True
            )
            
            # Apply optimizations
            if hasattr(self.model, 'half'):
                self.model = self.model.half()
            
            self.model.eval()
            
            # Compile model for faster inference (PyTorch 2.0+)
            try:
                self.model = torch.compile(self.model)
                print("Model compiled for optimization")
            except:
                print("Model compilation not available")
            
            return True
            
        except Exception as e:
            print(f"PyTorch deployment error: {e}")
            # Fallback to smaller model
            return await self.deploy_fallback_model()
    
    async def deploy_with_onnx(self) -> bool:
        """Deploy with ONNX Runtime optimization"""
        try:
            import onnxruntime as ort
            
            # For now, use PyTorch as ONNX conversion is complex
            print("ONNX optimization not implemented, using PyTorch")
            return await self.deploy_with_pytorch()
            
        except ImportError:
            print("ONNX Runtime not available, using PyTorch")
            return await self.deploy_with_pytorch()
    
    async def deploy_fallback_model(self) -> bool:
        """Deploy smaller fallback model"""
        try:
            fallback_model = "microsoft/DialoGPT-medium"
            print(f"Deploying fallback model: {fallback_model}")
            
            self.model_name = fallback_model
            self.tokenizer = AutoTokenizer.from_pretrained(fallback_model)
            self.model = AutoModelForCausalLM.from_pretrained(fallback_model)
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Move to available device
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.model.to(device)
            self.model.eval()
            
            return True
            
        except Exception as e:
            print(f"Fallback deployment error: {e}")
            return False
    
    def implement_model_sharding(self, num_shards: int) -> Dict[str, Any]:
        """Implement model sharding for large models"""
        if not self.model:
            return {"error": "Model not loaded"}
        
        sharding_info = {
            "num_shards": num_shards,
            "shard_devices": [],
            "memory_per_shard": 0
        }
        
        try:
            # Simple sharding by moving different layers to different devices
            if torch.cuda.device_count() >= num_shards:
                for i in range(num_shards):
                    device_id = i % torch.cuda.device_count()
                    sharding_info["shard_devices"].append(f"cuda:{device_id}")
                
                # Estimate memory per shard
                total_params = sum(p.numel() for p in self.model.parameters())
                sharding_info["memory_per_shard"] = total_params // num_shards
                
                print(f"Model sharded across {num_shards} devices")
            else:
                sharding_info["error"] = "Not enough GPUs for requested sharding"
        
        except Exception as e:
            sharding_info["error"] = str(e)
        
        return sharding_info
    
    def get_deployment_status(self) -> Dict[str, Any]:
        """Get current deployment status"""
        status = {
            "model_name": self.model_name,
            "model_loaded": self.model is not None,
            "tokenizer_loaded": self.tokenizer is not None,
            "device_map": self.device_map,
            "memory_usage": self.get_memory_usage(),
            "performance_metrics": self.performance_metrics
        }
        
        return status
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage"""
        memory_info = {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "gpu_memory": []
        }
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                gpu_memory = torch.cuda.memory_allocated(i) / (1024**3)  # GB
                gpu_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                memory_info["gpu_memory"].append({
                    "device": i,
                    "used_gb": gpu_memory,
                    "total_gb": gpu_total,
                    "utilization": gpu_memory / gpu_total * 100
                })
        
        return memory_info
    
    async def benchmark_performance(self, test_prompts: List[str] = None) -> Dict[str, Any]:
        """Benchmark model performance"""
        if not self.model or not self.tokenizer:
            return {"error": "Model not deployed"}
        
        if not test_prompts:
            test_prompts = [
                "What is artificial intelligence?",
                "Explain machine learning in simple terms.",
                "How does natural language processing work?"
            ]
        
        benchmark_results = {
            "total_prompts": len(test_prompts),
            "latencies": [],
            "throughput": 0,
            "avg_latency": 0,
            "tokens_per_second": 0
        }
        
        total_tokens = 0
        start_time = time.time()
        
        for prompt in test_prompts:
            prompt_start = time.time()
            
            # Tokenize
            inputs = self.tokenizer.encode(prompt, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = inputs.cuda()
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 50,
                    do_sample=True,
                    temperature=0.7
                )
            
            prompt_end = time.time()
            latency = prompt_end - prompt_start
            benchmark_results["latencies"].append(latency)
            
            total_tokens += outputs.shape[1]
        
        total_time = time.time() - start_time
        
        benchmark_results["avg_latency"] = sum(benchmark_results["latencies"]) / len(benchmark_results["latencies"])
        benchmark_results["throughput"] = len(test_prompts) / total_time
        benchmark_results["tokens_per_second"] = total_tokens / total_time
        
        self.performance_metrics = benchmark_results
        return benchmark_results