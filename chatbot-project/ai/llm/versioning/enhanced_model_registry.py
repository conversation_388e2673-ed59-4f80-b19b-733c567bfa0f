"""Enhanced Model Registry with Deployment Tracking, Rollback, and A/B Testing."""

import os
import json
import shutil
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict, field
from enum import Enum
from pathlib import Path
import logging
import uuid

logger = logging.getLogger(__name__)

class ModelStatus(Enum):
    REGISTERED = "registered"
    READY = "ready"
    DEPLOYED = "deployed"
    DEPRECATED = "deprecated"
    FAILED = "failed"
    TESTING = "testing"
    ROLLBACK = "rollback"

class DeploymentStrategy(Enum):
    BLUE_GREEN = "blue_green"
    CANARY = "canary"
    A_B_TEST = "a_b_test"
    ROLLING = "rolling"
    IMMEDIATE = "immediate"

@dataclass
class PerformanceMetrics:
    latency_p50: float = 0.0
    latency_p95: float = 0.0
    latency_p99: float = 0.0
    throughput: float = 0.0
    error_rate: float = 0.0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    gpu_usage: float = 0.0
    accuracy: Optional[float] = None
    f1_score: Optional[float] = None

@dataclass
class DeploymentConfig:
    strategy: DeploymentStrategy
    traffic_percentage: float = 100.0
    rollback_threshold: float = 0.05  # 5% error rate
    monitoring_duration: int = 3600  # 1 hour in seconds
    auto_rollback: bool = True
    health_check_interval: int = 60  # seconds
    success_criteria: Dict[str, float] = field(default_factory=dict)

@dataclass
class ABTestConfig:
    test_id: str
    control_model: str
    treatment_model: str
    traffic_split: float = 0.5  # 50/50 split
    duration_hours: int = 24
    success_metrics: List[str] = field(default_factory=list)
    minimum_sample_size: int = 1000

@dataclass
class ModelVersion:
    model_id: str
    version: str
    model_name: str
    model_path: str
    config_path: Optional[str]
    status: ModelStatus
    created_at: str
    deployed_at: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    checksum: str = ""
    performance_metrics: PerformanceMetrics = field(default_factory=PerformanceMetrics)
    deployment_config: Optional[DeploymentConfig] = None
    parent_version: Optional[str] = None
    tags: List[str] = field(default_factory=list)

@dataclass
class DeploymentRecord:
    deployment_id: str
    model_id: str
    version: str
    strategy: DeploymentStrategy
    started_at: str
    completed_at: Optional[str] = None
    status: str = "in_progress"
    traffic_percentage: float = 100.0
    rollback_reason: Optional[str] = None
    performance_before: Optional[PerformanceMetrics] = None
    performance_after: Optional[PerformanceMetrics] = None

class EnhancedModelRegistry:
    """Enhanced model registry with deployment tracking and A/B testing."""
    
    def __init__(self, registry_path: str = "./enhanced_model_registry"):
        self.registry_path = Path(registry_path)
        self.models_path = self.registry_path / "models"
        self.metadata_path = self.registry_path / "metadata"
        self.deployments_path = self.registry_path / "deployments"
        self.ab_tests_path = self.registry_path / "ab_tests"
        
        # Create directories
        for path in [self.models_path, self.metadata_path, self.deployments_path, self.ab_tests_path]:
            path.mkdir(parents=True, exist_ok=True)
        
        self.registry_file = self.registry_path / "registry.json"
        self.deployments_file = self.registry_path / "deployments.json"
        self.ab_tests_file = self.registry_path / "ab_tests.json"
        
        self.models = self._load_registry()
        self.deployments = self._load_deployments()
        self.ab_tests = self._load_ab_tests()
        self.active_deployments: Dict[str, DeploymentRecord] = {}
    
    def _load_registry(self) -> Dict[str, ModelVersion]:
        """Load model registry from file."""
        if self.registry_file.exists():
            try:
                with open(self.registry_file, 'r') as f:
                    data = json.load(f)
                    return {
                        model_id: ModelVersion(**model_data)
                        for model_id, model_data in data.items()
                    }
            except Exception as e:
                logger.error(f"Failed to load registry: {e}")
        return {}
    
    def _load_deployments(self) -> Dict[str, DeploymentRecord]:
        """Load deployment records from file."""
        if self.deployments_file.exists():
            try:
                with open(self.deployments_file, 'r') as f:
                    data = json.load(f)
                    return {
                        dep_id: DeploymentRecord(**dep_data)
                        for dep_id, dep_data in data.items()
                    }
            except Exception as e:
                logger.error(f"Failed to load deployments: {e}")
        return {}
    
    def _load_ab_tests(self) -> Dict[str, ABTestConfig]:
        """Load A/B test configurations from file."""
        if self.ab_tests_file.exists():
            try:
                with open(self.ab_tests_file, 'r') as f:
                    data = json.load(f)
                    return {
                        test_id: ABTestConfig(**test_data)
                        for test_id, test_data in data.items()
                    }
            except Exception as e:
                logger.error(f"Failed to load A/B tests: {e}")
        return {}
    
    def _save_registry(self):
        """Save model registry to file."""
        try:
            with open(self.registry_file, 'w') as f:
                json.dump({
                    model_id: asdict(model)
                    for model_id, model in self.models.items()
                }, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save registry: {e}")
    
    def _save_deployments(self):
        """Save deployment records to file."""
        try:
            with open(self.deployments_file, 'w') as f:
                json.dump({
                    dep_id: asdict(deployment)
                    for dep_id, deployment in self.deployments.items()
                }, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save deployments: {e}")
    
    def _save_ab_tests(self):
        """Save A/B test configurations to file."""
        try:
            with open(self.ab_tests_file, 'w') as f:
                json.dump({
                    test_id: asdict(test)
                    for test_id, test in self.ab_tests.items()
                }, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save A/B tests: {e}")
    
    def register_model(self, model_name: str, model_path: str, 
                      config_path: str = None, metadata: Dict[str, Any] = None,
                      tags: List[str] = None) -> str:
        """Register a new model version."""
        # Generate model ID and version
        model_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        version = self._generate_version(model_name)
        
        # Calculate checksum
        checksum = self._calculate_checksum(model_path)
        
        # Copy model files to registry
        registry_model_path = self.models_path / model_id
        registry_model_path.mkdir(exist_ok=True)
        
        # Copy model
        if Path(model_path).is_dir():
            shutil.copytree(model_path, registry_model_path / "model", dirs_exist_ok=True)
        else:
            shutil.copy2(model_path, registry_model_path / "model")
        
        # Copy config if provided
        registry_config_path = None
        if config_path and Path(config_path).exists():
            registry_config_path = str(registry_model_path / "config.json")
            shutil.copy2(config_path, registry_config_path)
        
        # Create model version
        model_version = ModelVersion(
            model_id=model_id,
            version=version,
            model_name=model_name,
            model_path=str(registry_model_path / "model"),
            config_path=registry_config_path,
            status=ModelStatus.REGISTERED,
            created_at=datetime.now().isoformat(),
            metadata=metadata or {},
            checksum=checksum,
            tags=tags or []
        )
        
        self.models[model_id] = model_version
        self._save_registry()
        
        logger.info(f"Registered model {model_name} version {version} with ID {model_id}")
        return model_id
    
    def deploy_model(self, model_id: str, deployment_config: DeploymentConfig) -> str:
        """Deploy a model with specified strategy."""
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not found")
        
        model = self.models[model_id]
        deployment_id = str(uuid.uuid4())
        
        # Create deployment record
        deployment_record = DeploymentRecord(
            deployment_id=deployment_id,
            model_id=model_id,
            version=model.version,
            strategy=deployment_config.strategy,
            started_at=datetime.now().isoformat(),
            traffic_percentage=deployment_config.traffic_percentage
        )
        
        # Update model status
        model.status = ModelStatus.DEPLOYED
        model.deployed_at = datetime.now().isoformat()
        model.deployment_config = deployment_config
        
        # Store deployment
        self.deployments[deployment_id] = deployment_record
        self.active_deployments[model_id] = deployment_record
        
        self._save_registry()
        self._save_deployments()
        
        logger.info(f"Started deployment {deployment_id} for model {model_id}")
        return deployment_id
    
    def rollback_model(self, model_id: str, reason: str = "Manual rollback") -> bool:
        """Rollback a model to its previous version."""
        if model_id not in self.models:
            logger.error(f"Model {model_id} not found")
            return False
        
        model = self.models[model_id]
        
        # Find previous version
        if not model.parent_version:
            logger.error(f"No parent version found for model {model_id}")
            return False
        
        parent_model = self.models.get(model.parent_version)
        if not parent_model:
            logger.error(f"Parent model {model.parent_version} not found")
            return False
        
        # Update deployment record
        if model_id in self.active_deployments:
            deployment = self.active_deployments[model_id]
            deployment.status = "rolled_back"
            deployment.rollback_reason = reason
            deployment.completed_at = datetime.now().isoformat()
        
        # Update model status
        model.status = ModelStatus.ROLLBACK
        parent_model.status = ModelStatus.DEPLOYED
        
        self._save_registry()
        self._save_deployments()
        
        logger.info(f"Rolled back model {model_id} to {model.parent_version}: {reason}")
        return True
    
    def start_ab_test(self, control_model_id: str, treatment_model_id: str, 
                     config: ABTestConfig) -> str:
        """Start an A/B test between two models."""
        if control_model_id not in self.models or treatment_model_id not in self.models:
            raise ValueError("One or both models not found")
        
        test_id = config.test_id or str(uuid.uuid4())
        config.test_id = test_id
        
        # Update model statuses
        self.models[control_model_id].status = ModelStatus.TESTING
        self.models[treatment_model_id].status = ModelStatus.TESTING
        
        # Store A/B test configuration
        self.ab_tests[test_id] = config
        
        self._save_registry()
        self._save_ab_tests()
        
        logger.info(f"Started A/B test {test_id}: {control_model_id} vs {treatment_model_id}")
        return test_id
    
    def _calculate_checksum(self, file_path: str) -> str:
        """Calculate MD5 checksum of a file or directory."""
        if Path(file_path).is_dir():
            # For directories, hash all files
            hash_md5 = hashlib.md5()
            for file_path in sorted(Path(file_path).rglob("*")):
                if file_path.is_file():
                    with open(file_path, "rb") as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            hash_md5.update(chunk)
            return hash_md5.hexdigest()
        else:
            # For single files
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
    
    def _generate_version(self, model_name: str) -> str:
        """Generate version number for model."""
        existing_versions = [
            m.version for m in self.models.values()
            if m.model_name == model_name
        ]
        
        if not existing_versions:
            return "1.0.0"
        
        # Simple version increment (major.minor.patch)
        latest_version = max(existing_versions)
        parts = latest_version.split('.')
        
        try:
            patch = int(parts[2]) + 1
            return f"{parts[0]}.{parts[1]}.{patch}"
        except:
            return f"{latest_version}.1"
