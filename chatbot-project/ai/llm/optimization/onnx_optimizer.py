"""ONNX Runtime Optimization for LLM Models."""

import os
import torch
import numpy as np
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import logging
from dataclasses import dataclass
from enum import Enum

try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True

    # Try to import optimum components
    try:
        from optimum.onnxruntime import ORTModelForCausalLM, ORTOptimizer, ORTQuantizer
        from optimum.onnxruntime.configuration import OptimizationConfig as OptimumOptimizationConfig, QuantizationConfig as OptimumQuantizationConfig
        OPTIMUM_AVAILABLE = True
    except ImportError:
        OPTIMUM_AVAILABLE = False
        # Create mock classes for when optimum is not available
        class ORTModelForCausalLM:
            pass
        class ORTOptimizer:
            pass
        class ORTQuantizer:
            pass
        class OptimumOptimizationConfig:
            pass
        class OptimumQuantizationConfig:
            pass

except ImportError:
    ONNX_AVAILABLE = False
    OPTIMUM_AVAILABLE = False

logger = logging.getLogger(__name__)

class OptimizationLevel(Enum):
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    AGGRESSIVE = "aggressive"

class QuantizationType(Enum):
    DYNAMIC = "dynamic"
    STATIC = "static"
    QAT = "qat"  # Quantization Aware Training

@dataclass
class OptimizationConfig:
    optimization_level: OptimizationLevel
    quantization_type: Optional[QuantizationType]
    target_device: str  # "cpu", "cuda", "tensorrt"
    precision: str  # "fp32", "fp16", "int8"
    batch_size: int
    sequence_length: int
    enable_graph_optimization: bool
    enable_memory_optimization: bool

class ONNXOptimizer:
    """ONNX Runtime optimizer for LLM models."""
    
    def __init__(self, cache_dir: str = "./models/onnx_cache"):
        if not ONNX_AVAILABLE:
            raise ImportError("ONNX Runtime not available. Install with: pip install onnxruntime")

        if not OPTIMUM_AVAILABLE:
            logger.warning("Optimum not available. Some features will be limited. Install with: pip install optimum")
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.optimization_configs = {
            OptimizationLevel.BASIC: {
                "graph_optimization_level": ort.GraphOptimizationLevel.ORT_ENABLE_BASIC,
                "optimizations": ["MatMulAddFusion", "AttentionFusion"]
            },
            OptimizationLevel.INTERMEDIATE: {
                "graph_optimization_level": ort.GraphOptimizationLevel.ORT_ENABLE_EXTENDED,
                "optimizations": ["MatMulAddFusion", "AttentionFusion", "EmbedLayerNormFusion", "SkipLayerNormFusion"]
            },
            OptimizationLevel.AGGRESSIVE: {
                "graph_optimization_level": ort.GraphOptimizationLevel.ORT_ENABLE_ALL,
                "optimizations": ["MatMulAddFusion", "AttentionFusion", "EmbedLayerNormFusion", 
                                "SkipLayerNormFusion", "BiasGeluFusion", "GeluFusion"]
            }
        }
        
        self.providers = self._get_available_providers()
        logger.info(f"Available ONNX providers: {self.providers}")
    
    def _get_available_providers(self) -> List[str]:
        """Get available ONNX Runtime providers."""
        available_providers = ort.get_available_providers()
        
        # Prioritize providers
        priority_order = [
            "TensorrtExecutionProvider",
            "CUDAExecutionProvider", 
            "CPUExecutionProvider"
        ]
        
        ordered_providers = []
        for provider in priority_order:
            if provider in available_providers:
                ordered_providers.append(provider)
        
        return ordered_providers
    
    def optimize_model(self, model_path: str, config: OptimizationConfig) -> Dict[str, Any]:
        """Optimize a model using ONNX Runtime."""
        try:
            model_name = Path(model_path).stem
            optimized_path = self.cache_dir / f"{model_name}_optimized"
            
            logger.info(f"Starting ONNX optimization for {model_name}")
            
            # Load model
            if config.target_device == "cuda" and "CUDAExecutionProvider" in self.providers:
                model = ORTModelForCausalLM.from_pretrained(
                    model_path,
                    provider=["CUDAExecutionProvider", "CPUExecutionProvider"]
                )
            else:
                model = ORTModelForCausalLM.from_pretrained(
                    model_path,
                    provider=["CPUExecutionProvider"]
                )
            
            # Apply optimizations
            optimization_result = self._apply_optimizations(model, config, optimized_path)
            
            # Apply quantization if specified
            if config.quantization_type:
                quantization_result = self._apply_quantization(model, config, optimized_path)
                optimization_result.update(quantization_result)
            
            # Save optimized model
            model.save_pretrained(optimized_path)
            
            # Benchmark performance
            performance_metrics = self._benchmark_model(model, config)
            
            result = {
                "status": "success",
                "optimized_model_path": str(optimized_path),
                "optimization_config": config.__dict__,
                "performance_metrics": performance_metrics,
                "optimization_details": optimization_result
            }
            
            logger.info(f"ONNX optimization completed for {model_name}")
            return result
            
        except Exception as e:
            logger.error(f"ONNX optimization failed: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "optimization_config": config.__dict__
            }
    
    def _apply_optimizations(self, model: ORTModelForCausalLM, config: OptimizationConfig, output_path: Path) -> Dict[str, Any]:
        """Apply graph optimizations to the model."""
        try:
            optimizer = ORTOptimizer.from_pretrained(model)
            
            # Create optimization configuration
            optimization_config = OptimizationConfig(
                optimization_level=self.optimization_configs[config.optimization_level]["graph_optimization_level"],
                optimize_for_gpu=(config.target_device == "cuda"),
                fp16=(config.precision == "fp16"),
                use_gpu=(config.target_device == "cuda")
            )
            
            # Apply optimizations
            optimizer.optimize(
                save_dir=output_path,
                optimization_config=optimization_config
            )
            
            return {
                "graph_optimizations_applied": self.optimization_configs[config.optimization_level]["optimizations"],
                "optimization_level": config.optimization_level.value,
                "fp16_enabled": config.precision == "fp16",
                "gpu_optimized": config.target_device == "cuda"
            }
            
        except Exception as e:
            logger.warning(f"Graph optimization failed: {e}")
            return {"graph_optimization_error": str(e)}
    
    def _apply_quantization(self, model: ORTModelForCausalLM, config: OptimizationConfig, output_path: Path) -> Dict[str, Any]:
        """Apply quantization to the model."""
        try:
            quantizer = ORTQuantizer.from_pretrained(model)
            
            if config.quantization_type == QuantizationType.DYNAMIC:
                quantization_config = QuantizationConfig(
                    is_static=False,
                    format="QOperator",
                    mode="IntegerOps",
                    activations_dtype="int8",
                    weights_dtype="int8"
                )
            elif config.quantization_type == QuantizationType.STATIC:
                quantization_config = QuantizationConfig(
                    is_static=True,
                    format="QDQ",
                    mode="QLinearOps",
                    activations_dtype="int8",
                    weights_dtype="int8"
                )
            else:
                return {"quantization_error": "QAT not implemented yet"}
            
            # Apply quantization
            quantizer.quantize(
                save_dir=output_path,
                quantization_config=quantization_config
            )
            
            return {
                "quantization_applied": config.quantization_type.value,
                "precision": "int8",
                "quantization_format": quantization_config.format
            }
            
        except Exception as e:
            logger.warning(f"Quantization failed: {e}")
            return {"quantization_error": str(e)}
    
    def _benchmark_model(self, model: ORTModelForCausalLM, config: OptimizationConfig) -> Dict[str, Any]:
        """Benchmark the optimized model performance."""
        try:
            import time
            
            # Create dummy input
            dummy_input = torch.randint(0, 1000, (config.batch_size, config.sequence_length))
            
            # Warmup
            for _ in range(3):
                _ = model.generate(dummy_input, max_length=config.sequence_length + 10)
            
            # Benchmark
            start_time = time.time()
            num_runs = 10
            
            for _ in range(num_runs):
                outputs = model.generate(dummy_input, max_length=config.sequence_length + 10)
            
            end_time = time.time()
            
            avg_latency = (end_time - start_time) / num_runs
            throughput = config.batch_size / avg_latency
            
            return {
                "average_latency_seconds": avg_latency,
                "throughput_samples_per_second": throughput,
                "batch_size": config.batch_size,
                "sequence_length": config.sequence_length,
                "num_benchmark_runs": num_runs
            }
            
        except Exception as e:
            logger.warning(f"Benchmarking failed: {e}")
            return {"benchmark_error": str(e)}
    
    def create_inference_session(self, model_path: str, config: OptimizationConfig) -> ort.InferenceSession:
        """Create an optimized ONNX Runtime inference session."""
        session_options = ort.SessionOptions()
        
        # Set optimization level
        session_options.graph_optimization_level = self.optimization_configs[config.optimization_level]["graph_optimization_level"]
        
        # Memory optimizations
        if config.enable_memory_optimization:
            session_options.enable_mem_pattern = True
            session_options.enable_cpu_mem_arena = True
        
        # Thread settings
        session_options.intra_op_num_threads = os.cpu_count()
        session_options.inter_op_num_threads = 1
        
        # Provider options
        provider_options = []
        if config.target_device == "cuda" and "CUDAExecutionProvider" in self.providers:
            provider_options = [
                ("CUDAExecutionProvider", {
                    "device_id": 0,
                    "arena_extend_strategy": "kNextPowerOfTwo",
                    "gpu_mem_limit": 2 * 1024 * 1024 * 1024,  # 2GB
                    "cudnn_conv_algo_search": "EXHAUSTIVE",
                    "do_copy_in_default_stream": True,
                }),
                ("CPUExecutionProvider", {})
            ]
        else:
            provider_options = [("CPUExecutionProvider", {})]
        
        # Create session
        session = ort.InferenceSession(
            model_path,
            sess_options=session_options,
            providers=provider_options
        )
        
        return session
    
    def get_optimization_recommendations(self, model_info: Dict[str, Any]) -> OptimizationConfig:
        """Get optimization recommendations based on model characteristics."""
        model_size = model_info.get("model_size_mb", 1000)
        target_device = "cuda" if torch.cuda.is_available() else "cpu"
        
        if model_size < 500:  # Small model
            return OptimizationConfig(
                optimization_level=OptimizationLevel.AGGRESSIVE,
                quantization_type=QuantizationType.DYNAMIC,
                target_device=target_device,
                precision="int8",
                batch_size=8,
                sequence_length=512,
                enable_graph_optimization=True,
                enable_memory_optimization=True
            )
        elif model_size < 2000:  # Medium model
            return OptimizationConfig(
                optimization_level=OptimizationLevel.INTERMEDIATE,
                quantization_type=QuantizationType.DYNAMIC,
                target_device=target_device,
                precision="fp16" if target_device == "cuda" else "fp32",
                batch_size=4,
                sequence_length=512,
                enable_graph_optimization=True,
                enable_memory_optimization=True
            )
        else:  # Large model
            return OptimizationConfig(
                optimization_level=OptimizationLevel.BASIC,
                quantization_type=None,
                target_device=target_device,
                precision="fp16" if target_device == "cuda" else "fp32",
                batch_size=1,
                sequence_length=512,
                enable_graph_optimization=True,
                enable_memory_optimization=True
            )
