"""Enhanced Ollama Client with Llama 3 Support and Advanced Features."""

import requests
import json
import asyncio
import time
from typing import Dict, Any, Optional, List, AsyncGenerator, Union
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ModelType(Enum):
    LLAMA3 = "llama3"
    LLAMA2 = "llama2"
    DEEPSEEK = "deepseek"
    CODELLAMA = "codellama"
    MISTRAL = "mistral"
    OTHER = "other"

@dataclass
class ModelInfo:
    name: str
    type: ModelType
    size: str
    capabilities: List[str]
    context_length: int
    is_instruct: bool

class OllamaClient:
    """Enhanced client for Ollama models with Llama 3 support."""

    def __init__(self, base_url: str = "http://10.10.110.25:11434"):
        self.base_url = base_url
        self.available_models = []
        self.model_info_cache = {}
        self.default_model = "llama3:latest"  # Default to Llama 3
        self.session = requests.Session()
        self.session.timeout = 30

        # Llama 3 model configurations
        self.llama3_models = {
            "llama3:8b": ModelInfo("llama3:8b", ModelType.LLAMA3, "8B", ["chat", "instruct"], 8192, True),
            "llama3:70b": ModelInfo("llama3:70b", ModelType.LLAMA3, "70B", ["chat", "instruct"], 8192, True),
            "llama3:latest": ModelInfo("llama3:latest", ModelType.LLAMA3, "8B", ["chat", "instruct"], 8192, True),
            "llama3-instruct": ModelInfo("llama3-instruct", ModelType.LLAMA3, "8B", ["chat", "instruct"], 8192, True)
        }

        self._check_connection()
    
    def _check_connection(self):
        """Check Ollama connection and available models with Llama 3 priority."""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.available_models = [model["name"] for model in data.get("models", [])]
                logger.info(f"✅ Connected to Ollama. Available models: {self.available_models}")

                # Priority order: Llama 3 > DeepSeek > Llama 2
                model_priority = [
                    "llama3:latest", "llama3:8b", "llama3:70b", "llama3-instruct",
                    "deepseek-r1:latest", "deepseek-coder:latest",
                    "llama2:latest", "llama2:7b-chat"
                ]

                for model in model_priority:
                    if model in self.available_models:
                        self.default_model = model
                        model_type = self._detect_model_type(model)
                        logger.info(f"✅ Using {model} ({model_type.value}) as default model")
                        break

                # Cache model information
                self._cache_model_info()

            else:
                logger.error(f"❌ Ollama connection failed: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ Cannot connect to Ollama: {e}")

    def _detect_model_type(self, model_name: str) -> ModelType:
        """Detect model type from name."""
        model_lower = model_name.lower()
        if "llama3" in model_lower or "llama-3" in model_lower:
            return ModelType.LLAMA3
        elif "llama2" in model_lower or "llama-2" in model_lower:
            return ModelType.LLAMA2
        elif "deepseek" in model_lower:
            return ModelType.DEEPSEEK
        elif "codellama" in model_lower:
            return ModelType.CODELLAMA
        elif "mistral" in model_lower:
            return ModelType.MISTRAL
        else:
            return ModelType.OTHER

    def _cache_model_info(self):
        """Cache detailed information about available models."""
        for model_name in self.available_models:
            try:
                info = self.get_model_info(model_name)
                if "error" not in info:
                    self.model_info_cache[model_name] = info
            except Exception as e:
                logger.warning(f"Failed to cache info for {model_name}: {e}")
    
    def generate(self, prompt: str, model: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Generate response using Ollama model with enhanced options."""
        model = model or self.default_model

        if model not in self.available_models:
            return {
                "response": f"Model {model} not available. Available: {self.available_models}",
                "error": "model_not_found"
            }

        try:
            # Enhanced payload with Llama 3 optimizations
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": kwargs.get("stream", False),
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "top_p": kwargs.get("top_p", 0.9),
                    "top_k": kwargs.get("top_k", 40),
                    "num_predict": kwargs.get("max_tokens", 500),
                    "repeat_penalty": kwargs.get("repeat_penalty", 1.1),
                    "seed": kwargs.get("seed", -1),
                    "stop": kwargs.get("stop", []),
                    "num_ctx": kwargs.get("context_length", 4096)
                },
                "format": kwargs.get("format", ""),
                "raw": kwargs.get("raw", False),
                "keep_alive": kwargs.get("keep_alive", "5m")
            }

            # Use session for connection pooling
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=kwargs.get("timeout", 60)
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "response": result.get("response", ""),
                    "model": model,
                    "done": result.get("done", True),
                    "context": result.get("context", []),
                    "total_duration": result.get("total_duration", 0),
                    "load_duration": result.get("load_duration", 0),
                    "prompt_eval_count": result.get("prompt_eval_count", 0),
                    "prompt_eval_duration": result.get("prompt_eval_duration", 0),
                    "eval_count": result.get("eval_count", 0),
                    "eval_duration": result.get("eval_duration", 0)
                }
            else:
                return {
                    "response": f"Error: {response.status_code} - {response.text}",
                    "error": "generation_failed"
                }

        except Exception as e:
            return {
                "response": f"Generation error: {str(e)}",
                "error": "exception"
            }

    async def generate_async(self, prompt: str, model: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Async version of generate method."""
        import aiohttp

        model = model or self.default_model

        if model not in self.available_models:
            return {
                "response": f"Model {model} not available. Available: {self.available_models}",
                "error": "model_not_found"
            }

        payload = {
            "model": model,
            "prompt": prompt,
            "stream": kwargs.get("stream", False),
            "options": {
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.9),
                "top_k": kwargs.get("top_k", 40),
                "num_predict": kwargs.get("max_tokens", 500),
                "repeat_penalty": kwargs.get("repeat_penalty", 1.1),
                "num_ctx": kwargs.get("context_length", 4096)
            }
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=kwargs.get("timeout", 60))
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "response": result.get("response", ""),
                            "model": model,
                            "done": result.get("done", True),
                            "context": result.get("context", []),
                            "performance_metrics": {
                                "total_duration": result.get("total_duration", 0),
                                "eval_count": result.get("eval_count", 0),
                                "eval_duration": result.get("eval_duration", 0)
                            }
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "response": f"Error: {response.status} - {error_text}",
                            "error": "generation_failed"
                        }
        except Exception as e:
            return {
                "response": f"Async generation error: {str(e)}",
                "error": "exception"
            }
    
    def chat(self, messages: List[Dict[str, str]], model: Optional[str] = None) -> Dict[str, Any]:
        """Chat with Ollama model using conversation format."""
        model = model or self.default_model
        
        # Convert messages to a single prompt for Ollama
        prompt = self._format_chat_prompt(messages)
        return self.generate(prompt, model)
    
    def _format_chat_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Format chat messages into a single prompt."""
        formatted = ""
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "system":
                formatted += f"System: {content}\n\n"
            elif role == "user":
                formatted += f"Human: {content}\n\n"
            elif role == "assistant":
                formatted += f"Assistant: {content}\n\n"
        
        formatted += "Assistant: "
        return formatted
    
    def get_model_info(self, model: Optional[str] = None) -> Dict[str, Any]:
        """Get information about a specific model."""
        model = model or self.default_model
        
        try:
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"name": model},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Failed to get model info: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Exception getting model info: {str(e)}"}
    
    def generate_stream(self, prompt: str, model: Optional[str] = None, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming response using Ollama model."""
        model = model or self.default_model

        if model not in self.available_models:
            yield {
                "response": f"Model {model} not available. Available: {self.available_models}",
                "error": "model_not_found",
                "done": True
            }
            return

        payload = {
            "model": model,
            "prompt": prompt,
            "stream": True,
            "options": {
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.9),
                "num_predict": kwargs.get("max_tokens", 500),
                "num_ctx": kwargs.get("context_length", 4096)
            }
        }

        try:
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                stream=True,
                timeout=kwargs.get("timeout", 120)
            )

            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk = json.loads(line.decode('utf-8'))
                            yield {
                                "response": chunk.get("response", ""),
                                "model": model,
                                "done": chunk.get("done", False),
                                "context": chunk.get("context", [])
                            }
                            if chunk.get("done", False):
                                break
                        except json.JSONDecodeError:
                            continue
            else:
                yield {
                    "response": f"Stream error: {response.status_code}",
                    "error": "stream_failed",
                    "done": True
                }
        except Exception as e:
            yield {
                "response": f"Stream error: {str(e)}",
                "error": "exception",
                "done": True
            }

    async def batch_generate(self, prompts: List[str], model: Optional[str] = None, **kwargs) -> List[Dict[str, Any]]:
        """Generate responses for multiple prompts in batch."""
        model = model or self.default_model

        if not prompts:
            return []

        # Process in parallel with concurrency limit
        max_concurrent = kwargs.get("max_concurrent", 5)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_single_prompt(prompt: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.generate_async(prompt, model, **kwargs)

        # Create tasks for all prompts
        tasks = [process_single_prompt(prompt) for prompt in prompts]

        # Execute all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "response": f"Batch error for prompt {i}: {str(result)}",
                    "error": "batch_exception",
                    "prompt_index": i
                })
            else:
                result["prompt_index"] = i
                processed_results.append(result)

        return processed_results

    def get_model_capabilities(self, model: Optional[str] = None) -> Dict[str, Any]:
        """Get detailed capabilities of a model."""
        model = model or self.default_model

        if model in self.llama3_models:
            model_info = self.llama3_models[model]
            return {
                "model": model,
                "type": model_info.type.value,
                "size": model_info.size,
                "capabilities": model_info.capabilities,
                "context_length": model_info.context_length,
                "is_instruct": model_info.is_instruct,
                "supports_streaming": True,
                "supports_batch": True,
                "optimal_temperature": 0.7,
                "recommended_use_cases": self._get_use_cases(model_info.type)
            }
        else:
            # Generic capabilities for other models
            model_type = self._detect_model_type(model)
            return {
                "model": model,
                "type": model_type.value,
                "capabilities": ["chat", "completion"],
                "supports_streaming": True,
                "supports_batch": True,
                "context_length": 4096
            }

    def _get_use_cases(self, model_type: ModelType) -> List[str]:
        """Get recommended use cases for model type."""
        use_cases = {
            ModelType.LLAMA3: [
                "conversational_ai", "instruction_following", "reasoning",
                "code_generation", "creative_writing", "analysis"
            ],
            ModelType.LLAMA2: [
                "conversational_ai", "text_completion", "basic_reasoning"
            ],
            ModelType.DEEPSEEK: [
                "code_generation", "technical_analysis", "reasoning",
                "problem_solving"
            ],
            ModelType.CODELLAMA: [
                "code_generation", "code_completion", "code_explanation"
            ],
            ModelType.MISTRAL: [
                "conversational_ai", "instruction_following", "reasoning"
            ]
        }
        return use_cases.get(model_type, ["general_purpose"])

    def is_available(self) -> bool:
        """Check if Ollama service is available."""
        return len(self.available_models) > 0

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the client."""
        return {
            "available_models": len(self.available_models),
            "default_model": self.default_model,
            "cached_model_info": len(self.model_info_cache),
            "llama3_models_available": [
                model for model in self.available_models
                if self._detect_model_type(model) == ModelType.LLAMA3
            ],
            "connection_status": "connected" if self.is_available() else "disconnected"
        }