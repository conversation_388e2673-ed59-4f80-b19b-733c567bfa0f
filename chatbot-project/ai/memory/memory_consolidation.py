"""
Memory Consolidation System for CHaBot.
Manages the transfer and consolidation of memories between different memory systems.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import uuid

# Import memory systems
from .episodic_memory import EpisodicMemorySystem, EpisodicMemory, EpisodeType, EpisodeStatus
from .semantic_memory import SemanticMemorySystem, SemanticConcept, ConceptType, RelationType
from .working_memory import WorkingMemorySystem, WorkingMemoryItem, WorkingMemoryType, Priority

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConsolidationType(Enum):
    """Types of memory consolidation."""
    WORKING_TO_EPISODIC = "working_to_episodic"
    WORKING_TO_SEMANTIC = "working_to_semantic"
    EPISODIC_TO_SEMANTIC = "episodic_to_semantic"
    CROSS_MODAL = "cross_modal"
    PATTERN_EXTRACTION = "pattern_extraction"
    KNOWLEDGE_INTEGRATION = "knowledge_integration"

class ConsolidationTrigger(Enum):
    """Triggers for memory consolidation."""
    TIME_BASED = "time_based"
    CAPACITY_BASED = "capacity_based"
    IMPORTANCE_BASED = "importance_based"
    PATTERN_BASED = "pattern_based"
    EXPLICIT_REQUEST = "explicit_request"

@dataclass
class ConsolidationRule:
    """Rule for memory consolidation."""
    id: str
    name: str
    consolidation_type: ConsolidationType
    trigger: ConsolidationTrigger
    conditions: Dict[str, Any]
    priority: int = 5
    enabled: bool = True
    
    # Thresholds
    min_importance: float = 0.5
    min_confidence: float = 0.6
    min_frequency: int = 1
    
    # Timing
    consolidation_delay: timedelta = timedelta(minutes=30)
    last_executed: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "name": self.name,
            "consolidation_type": self.consolidation_type.value,
            "trigger": self.trigger.value,
            "conditions": self.conditions,
            "priority": self.priority,
            "enabled": self.enabled,
            "min_importance": self.min_importance,
            "min_confidence": self.min_confidence,
            "min_frequency": self.min_frequency,
            "consolidation_delay": self.consolidation_delay.total_seconds(),
            "last_executed": self.last_executed.isoformat() if self.last_executed else None
        }

@dataclass
class ConsolidationResult:
    """Result of a consolidation operation."""
    success: bool
    consolidation_type: ConsolidationType
    source_items: List[str]
    target_items: List[str]
    patterns_found: List[Dict[str, Any]] = field(default_factory=list)
    knowledge_extracted: List[Dict[str, Any]] = field(default_factory=list)
    execution_time: float = 0.0
    error: Optional[str] = None

class MemoryConsolidationSystem:
    """
    Memory Consolidation System that manages transfer between memory systems.
    """
    
    def __init__(self,
                 episodic_memory: EpisodicMemorySystem,
                 semantic_memory: SemanticMemorySystem,
                 working_memory: WorkingMemorySystem):
        """Initialize the memory consolidation system."""
        self.episodic_memory = episodic_memory
        self.semantic_memory = semantic_memory
        self.working_memory = working_memory
        
        # Consolidation rules
        self.consolidation_rules: Dict[str, ConsolidationRule] = {}
        
        # Statistics
        self.total_consolidations = 0
        self.successful_consolidations = 0
        self.failed_consolidations = 0
        self.consolidations_by_type: Dict[ConsolidationType, int] = {}
        
        # Background task control
        self.consolidation_task = None
        self.running = False
        
        # Initialize default rules
        self._initialize_default_rules()
        
        logger.info("Memory Consolidation System initialized")
    
    def _initialize_default_rules(self):
        """Initialize default consolidation rules."""
        # Rule 1: Working memory to episodic (successful reasoning sessions)
        self.add_consolidation_rule(
            name="successful_reasoning_to_episodic",
            consolidation_type=ConsolidationType.WORKING_TO_EPISODIC,
            trigger=ConsolidationTrigger.PATTERN_BASED,
            conditions={
                "item_types": [WorkingMemoryType.REASONING_STEP.value],
                "min_confidence": 0.7,
                "session_success": True
            },
            priority=8,
            min_importance=0.6
        )
        
        # Rule 2: Frequent patterns to semantic memory
        self.add_consolidation_rule(
            name="frequent_patterns_to_semantic",
            consolidation_type=ConsolidationType.EPISODIC_TO_SEMANTIC,
            trigger=ConsolidationTrigger.PATTERN_BASED,
            conditions={
                "min_frequency": 3,
                "pattern_similarity": 0.8
            },
            priority=7,
            min_importance=0.7
        )
        
        # Rule 3: Time-based working memory cleanup
        self.add_consolidation_rule(
            name="working_memory_cleanup",
            consolidation_type=ConsolidationType.WORKING_TO_EPISODIC,
            trigger=ConsolidationTrigger.TIME_BASED,
            conditions={
                "max_age_hours": 2,
                "min_activation": 0.3
            },
            priority=5,
            consolidation_delay=timedelta(hours=1)
        )
        
        # Rule 4: Important concepts to semantic memory
        self.add_consolidation_rule(
            name="important_concepts_to_semantic",
            consolidation_type=ConsolidationType.WORKING_TO_SEMANTIC,
            trigger=ConsolidationTrigger.IMPORTANCE_BASED,
            conditions={
                "item_types": [WorkingMemoryType.CONTEXT.value, WorkingMemoryType.RESOURCE.value],
                "min_importance": 0.8
            },
            priority=9
        )
    
    def add_consolidation_rule(self,
                              name: str,
                              consolidation_type: ConsolidationType,
                              trigger: ConsolidationTrigger,
                              conditions: Dict[str, Any],
                              priority: int = 5,
                              min_importance: float = 0.5,
                              min_confidence: float = 0.6,
                              consolidation_delay: timedelta = timedelta(minutes=30)) -> str:
        """Add a new consolidation rule."""
        rule_id = str(uuid.uuid4())
        
        rule = ConsolidationRule(
            id=rule_id,
            name=name,
            consolidation_type=consolidation_type,
            trigger=trigger,
            conditions=conditions,
            priority=priority,
            min_importance=min_importance,
            min_confidence=min_confidence,
            consolidation_delay=consolidation_delay
        )
        
        self.consolidation_rules[rule_id] = rule
        
        logger.info(f"Added consolidation rule: {name} (ID: {rule_id})")
        return rule_id
    
    async def consolidate_working_to_episodic(self,
                                            session_id: Optional[str] = None,
                                            min_importance: float = 0.5) -> ConsolidationResult:
        """
        Consolidate working memory items to episodic memory.
        
        Args:
            session_id: Specific session to consolidate
            min_importance: Minimum importance threshold
            
        Returns:
            ConsolidationResult
        """
        start_time = datetime.now()
        
        try:
            # Get working memory items to consolidate
            items = await self.working_memory.find_items(
                session_id=session_id,
                active_only=False
            )
            
            # Filter by importance
            items = [item for item in items if item.activation_level >= min_importance]
            
            if not items:
                return ConsolidationResult(
                    success=True,
                    consolidation_type=ConsolidationType.WORKING_TO_EPISODIC,
                    source_items=[],
                    target_items=[]
                )
            
            # Group items by session/task
            sessions = {}
            for item in items:
                session_key = item.session_id or item.task_id or "default"
                if session_key not in sessions:
                    sessions[session_key] = []
                sessions[session_key].append(item)
            
            source_item_ids = []
            target_episode_ids = []
            
            # Create episodes for each session/task
            for session_key, session_items in sessions.items():
                # Determine episode type based on item types
                episode_type = self._determine_episode_type(session_items)
                
                # Create episode title and description
                title = self._generate_episode_title(session_items)
                description = self._generate_episode_description(session_items)
                
                # Consolidate content
                content = self._consolidate_working_memory_content(session_items)
                
                # Calculate importance
                importance = max(item.activation_level for item in session_items)
                
                # Create episode
                episode_id = await self.episodic_memory.store_episode(
                    episode_type=episode_type,
                    title=title,
                    description=description,
                    content=content,
                    importance=importance,
                    tags=self._extract_tags_from_items(session_items),
                    organization=self._extract_organization(session_items),
                    agent_id=self._extract_agent_id(session_items)
                )
                
                target_episode_ids.append(episode_id)
                source_item_ids.extend([item.id for item in session_items])
                
                # Remove consolidated items from working memory
                for item in session_items:
                    await self.working_memory.remove_item(item.id)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = ConsolidationResult(
                success=True,
                consolidation_type=ConsolidationType.WORKING_TO_EPISODIC,
                source_items=source_item_ids,
                target_items=target_episode_ids,
                execution_time=execution_time
            )
            
            self.successful_consolidations += 1
            logger.info(f"Consolidated {len(source_item_ids)} working memory items to {len(target_episode_ids)} episodes")
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.failed_consolidations += 1
            
            logger.error(f"Failed to consolidate working memory to episodic: {e}")
            
            return ConsolidationResult(
                success=False,
                consolidation_type=ConsolidationType.WORKING_TO_EPISODIC,
                source_items=[],
                target_items=[],
                execution_time=execution_time,
                error=str(e)
            )

    def _determine_episode_type(self, items: List[WorkingMemoryItem]) -> EpisodeType:
        """Determine episode type from working memory items."""
        # Count item types
        type_counts = {}
        for item in items:
            item_type = item.item_type
            type_counts[item_type] = type_counts.get(item_type, 0) + 1

        # Determine episode type based on dominant item types
        if WorkingMemoryType.REASONING_STEP in type_counts:
            return EpisodeType.PROBLEM_SOLVING
        elif WorkingMemoryType.CURRENT_GOAL in type_counts:
            return EpisodeType.PROBLEM_SOLVING
        elif WorkingMemoryType.CONTEXT in type_counts:
            return EpisodeType.USER_INTERACTION
        else:
            return EpisodeType.LEARNING

    def _generate_episode_title(self, items: List[WorkingMemoryItem]) -> str:
        """Generate episode title from working memory items."""
        # Look for goal items first
        for item in items:
            if item.item_type == WorkingMemoryType.CURRENT_GOAL:
                goal = item.content.get("goal", "")
                if goal:
                    return f"Session: {goal}"

        # Fallback to generic title
        return f"Memory Session ({len(items)} items)"

    def _generate_episode_description(self, items: List[WorkingMemoryItem]) -> str:
        """Generate episode description from working memory items."""
        item_types = [item.item_type.value for item in items]
        type_counts = {}
        for item_type in item_types:
            type_counts[item_type] = type_counts.get(item_type, 0) + 1

        type_summary = ", ".join([f"{count} {item_type}" for item_type, count in type_counts.items()])
        return f"Consolidated working memory session with {type_summary}"

    def _consolidate_working_memory_content(self, items: List[WorkingMemoryItem]) -> Dict[str, Any]:
        """Consolidate content from working memory items."""
        consolidated = {
            "items": [item.to_dict() for item in items],
            "item_count": len(items),
            "session_summary": {},
            "key_insights": []
        }

        # Extract session information
        sessions = set()
        tasks = set()
        agents = set()

        for item in items:
            if item.session_id:
                sessions.add(item.session_id)
            if item.task_id:
                tasks.add(item.task_id)
            if item.agent_id:
                agents.add(item.agent_id)

        consolidated["session_summary"] = {
            "sessions": list(sessions),
            "tasks": list(tasks),
            "agents": list(agents)
        }

        return consolidated

    def _extract_tags_from_items(self, items: List[WorkingMemoryItem]) -> List[str]:
        """Extract unique tags from working memory items."""
        all_tags = set()
        for item in items:
            all_tags.update(item.tags)
        return list(all_tags)

    def _extract_organization(self, items: List[WorkingMemoryItem]) -> Optional[str]:
        """Extract organization from working memory items."""
        for item in items:
            if hasattr(item, 'organization') and item.organization:
                return item.organization
        return None

    def _extract_agent_id(self, items: List[WorkingMemoryItem]) -> Optional[str]:
        """Extract agent ID from working memory items."""
        for item in items:
            if item.agent_id:
                return item.agent_id
        return None

    def _extract_patterns_from_episodes(self, episodes: List[EpisodicMemory], similarity_threshold: float) -> List[Dict[str, Any]]:
        """Extract patterns from episodic memories."""
        patterns = []

        # Group episodes by type and tags
        type_groups = {}
        for episode in episodes:
            episode_type = episode.episode_type
            if episode_type not in type_groups:
                type_groups[episode_type] = []
            type_groups[episode_type].append(episode)

        # Find patterns within each type group
        for episode_type, type_episodes in type_groups.items():
            if len(type_episodes) < 3:  # Need at least 3 episodes for pattern
                continue

            # Simple pattern detection based on tags and content similarity
            tag_patterns = self._find_tag_patterns(type_episodes)
            content_patterns = self._find_content_patterns(type_episodes, similarity_threshold)

            patterns.extend(tag_patterns)
            patterns.extend(content_patterns)

        return patterns

    def _find_tag_patterns(self, episodes: List[EpisodicMemory]) -> List[Dict[str, Any]]:
        """Find patterns based on tag co-occurrence."""
        patterns = []

        # Count tag combinations
        tag_combinations = {}
        for episode in episodes:
            if len(episode.tags) >= 2:
                # Create combinations of tags
                for i, tag1 in enumerate(episode.tags):
                    for tag2 in episode.tags[i+1:]:
                        combo = tuple(sorted([tag1, tag2]))
                        if combo not in tag_combinations:
                            tag_combinations[combo] = []
                        tag_combinations[combo].append(episode.id)

        # Find frequent combinations
        for combo, episode_ids in tag_combinations.items():
            if len(episode_ids) >= 3:  # Minimum frequency
                patterns.append({
                    "name": f"Pattern: {' + '.join(combo)}",
                    "description": f"Episodes frequently tagged with {' and '.join(combo)}",
                    "type": "tag_pattern",
                    "tags": list(combo),
                    "frequency": len(episode_ids),
                    "confidence": min(0.9, len(episode_ids) / len(episodes)),
                    "importance": 0.6,
                    "source_episodes": episode_ids,
                    "properties": {
                        "tag_combination": combo,
                        "occurrence_rate": len(episode_ids) / len(episodes)
                    }
                })

        return patterns

    def _find_content_patterns(self, episodes: List[EpisodicMemory], similarity_threshold: float) -> List[Dict[str, Any]]:
        """Find patterns based on content similarity."""
        patterns = []

        # Simple content pattern detection
        # Group episodes with similar content structure
        content_groups = {}

        for episode in episodes:
            content_keys = set(episode.content.keys())
            key_signature = tuple(sorted(content_keys))

            if key_signature not in content_groups:
                content_groups[key_signature] = []
            content_groups[key_signature].append(episode)

        # Create patterns for groups with sufficient size
        for key_signature, group_episodes in content_groups.items():
            if len(group_episodes) >= 3:
                patterns.append({
                    "name": f"Content Pattern: {', '.join(key_signature)}",
                    "description": f"Episodes with similar content structure: {', '.join(key_signature)}",
                    "type": "content_pattern",
                    "tags": ["content", "structure"],
                    "frequency": len(group_episodes),
                    "confidence": 0.7,
                    "importance": 0.5,
                    "source_episodes": [ep.id for ep in group_episodes],
                    "properties": {
                        "content_structure": key_signature,
                        "common_keys": list(key_signature)
                    }
                })

        return patterns

    def _extract_concepts_from_episodes(self, episodes: List[EpisodicMemory]) -> List[Dict[str, Any]]:
        """Extract semantic concepts from episodic memories."""
        concepts = []

        # Extract entities and concepts from episode content
        entity_counts = {}
        concept_counts = {}

        for episode in episodes:
            # Extract from title and description
            text_content = f"{episode.title} {episode.description}"
            words = text_content.lower().split()

            # Simple entity extraction (words that appear frequently)
            for word in words:
                if len(word) > 3:  # Filter short words
                    entity_counts[word] = entity_counts.get(word, 0) + 1

            # Extract from tags
            for tag in episode.tags:
                concept_counts[tag] = concept_counts.get(tag, 0) + 1

        # Create concepts from frequent entities
        for entity, count in entity_counts.items():
            if count >= 3:  # Minimum frequency
                concepts.append({
                    "name": entity.title(),
                    "type": ConceptType.ENTITY,
                    "description": f"Entity extracted from {count} episodes",
                    "properties": {
                        "frequency": count,
                        "extraction_method": "frequency_analysis"
                    },
                    "confidence": min(0.8, count / len(episodes)),
                    "importance": min(0.7, count / len(episodes)),
                    "tags": ["extracted", "entity"],
                    "source_episodes": [ep.id for ep in episodes if entity in f"{ep.title} {ep.description}".lower()]
                })

        # Create concepts from frequent tags
        for concept, count in concept_counts.items():
            if count >= 3:
                concepts.append({
                    "name": concept.title(),
                    "type": ConceptType.CONCEPT,
                    "description": f"Concept extracted from {count} episode tags",
                    "properties": {
                        "frequency": count,
                        "extraction_method": "tag_analysis"
                    },
                    "confidence": min(0.9, count / len(episodes)),
                    "importance": min(0.8, count / len(episodes)),
                    "tags": ["extracted", "concept"],
                    "source_episodes": [ep.id for ep in episodes if concept in ep.tags]
                })

        return concepts

    async def _create_concept_relationships(self, concept_ids: List[str], patterns: List[Dict[str, Any]]):
        """Create relationships between extracted concepts."""
        # Create relationships based on co-occurrence in patterns
        for pattern in patterns:
            if pattern["type"] == "tag_pattern" and len(pattern["tags"]) >= 2:
                # Find concepts that match the pattern tags
                matching_concepts = []
                for concept_id in concept_ids:
                    concept = await self.semantic_memory.get_concept_by_id(concept_id)
                    if concept and any(tag.lower() in concept.name.lower() for tag in pattern["tags"]):
                        matching_concepts.append(concept_id)

                # Create relationships between matching concepts
                for i, concept1_id in enumerate(matching_concepts):
                    for concept2_id in matching_concepts[i+1:]:
                        await self.semantic_memory.add_relation(
                            source_concept_id=concept1_id,
                            target_concept_id=concept2_id,
                            relation_type=RelationType.RELATED_TO,
                            strength=0.6,
                            confidence=pattern["confidence"],
                            bidirectional=True
                        )

    def _map_working_memory_type_to_concept_type(self, wm_type: WorkingMemoryType) -> ConceptType:
        """Map working memory type to semantic concept type."""
        mapping = {
            WorkingMemoryType.CURRENT_GOAL: ConceptType.CONCEPT,
            WorkingMemoryType.SUBGOAL: ConceptType.CONCEPT,
            WorkingMemoryType.CONTEXT: ConceptType.ENTITY,
            WorkingMemoryType.INTERMEDIATE_RESULT: ConceptType.FACT,
            WorkingMemoryType.REASONING_STEP: ConceptType.PROCEDURE,
            WorkingMemoryType.HYPOTHESIS: ConceptType.CONCEPT,
            WorkingMemoryType.CONSTRAINT: ConceptType.RULE,
            WorkingMemoryType.RESOURCE: ConceptType.ENTITY,
            WorkingMemoryType.ATTENTION_FOCUS: ConceptType.CONCEPT
        }
        return mapping.get(wm_type, ConceptType.CONCEPT)

    def _generate_concept_name(self, item: WorkingMemoryItem) -> str:
        """Generate concept name from working memory item."""
        if "name" in item.content:
            return item.content["name"]
        elif "goal" in item.content:
            return f"Goal: {item.content['goal']}"
        elif "type" in item.content:
            return f"{item.item_type.value.title()}: {item.content['type']}"
        else:
            return f"{item.item_type.value.title()} Item"

    def _generate_concept_description(self, item: WorkingMemoryItem) -> str:
        """Generate concept description from working memory item."""
        base_desc = f"Concept extracted from {item.item_type.value} in working memory"

        if "description" in item.content:
            return f"{base_desc}: {item.content['description']}"
        elif "content" in item.content:
            return f"{base_desc}: {item.content['content']}"
        else:
            return base_desc

    def get_statistics(self) -> Dict[str, Any]:
        """Get consolidation system statistics."""
        success_rate = (
            self.successful_consolidations / max(self.total_consolidations, 1)
        )

        return {
            "total_consolidations": self.total_consolidations,
            "successful_consolidations": self.successful_consolidations,
            "failed_consolidations": self.failed_consolidations,
            "success_rate": success_rate,
            "consolidations_by_type": dict(self.consolidations_by_type),
            "active_rules": len([r for r in self.consolidation_rules.values() if r.enabled]),
            "total_rules": len(self.consolidation_rules)
        }

    async def consolidate_episodic_to_semantic(self,
                                             min_frequency: int = 3,
                                             pattern_similarity: float = 0.8) -> ConsolidationResult:
        """
        Consolidate episodic memories to semantic knowledge.

        Args:
            min_frequency: Minimum frequency for pattern extraction
            pattern_similarity: Minimum similarity for pattern matching

        Returns:
            ConsolidationResult
        """
        start_time = datetime.now()

        try:
            # Get recent episodic memories
            recent_episodes = await self.episodic_memory.retrieve_episodes(
                time_range=(datetime.now() - timedelta(days=7), datetime.now()),
                limit=100
            )

            if len(recent_episodes) < min_frequency:
                return ConsolidationResult(
                    success=True,
                    consolidation_type=ConsolidationType.EPISODIC_TO_SEMANTIC,
                    source_items=[],
                    target_items=[]
                )

            # Extract patterns and concepts
            patterns = self._extract_patterns_from_episodes(recent_episodes, pattern_similarity)
            concepts = self._extract_concepts_from_episodes(recent_episodes)

            source_episode_ids = []
            target_concept_ids = []

            # Create semantic concepts from patterns
            for pattern in patterns:
                if pattern["frequency"] >= min_frequency:
                    concept_id = await self.semantic_memory.add_concept(
                        name=pattern["name"],
                        concept_type=ConceptType.PATTERN,
                        description=pattern["description"],
                        properties=pattern["properties"],
                        confidence=pattern["confidence"],
                        importance=pattern["importance"],
                        tags=pattern["tags"]
                    )

                    target_concept_ids.append(concept_id)
                    source_episode_ids.extend(pattern["source_episodes"])

            # Create semantic concepts from extracted entities
            for concept_data in concepts:
                concept_id = await self.semantic_memory.add_concept(
                    name=concept_data["name"],
                    concept_type=concept_data["type"],
                    description=concept_data["description"],
                    properties=concept_data["properties"],
                    confidence=concept_data["confidence"],
                    importance=concept_data["importance"],
                    tags=concept_data["tags"]
                )

                target_concept_ids.append(concept_id)
                source_episode_ids.extend(concept_data["source_episodes"])

            # Create relationships between concepts
            await self._create_concept_relationships(target_concept_ids, patterns)

            execution_time = (datetime.now() - start_time).total_seconds()

            result = ConsolidationResult(
                success=True,
                consolidation_type=ConsolidationType.EPISODIC_TO_SEMANTIC,
                source_items=source_episode_ids,
                target_items=target_concept_ids,
                patterns_found=patterns,
                knowledge_extracted=concepts,
                execution_time=execution_time
            )

            self.successful_consolidations += 1
            logger.info(f"Consolidated {len(set(source_episode_ids))} episodes to {len(target_concept_ids)} concepts")

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.failed_consolidations += 1

            logger.error(f"Failed to consolidate episodic to semantic: {e}")

            return ConsolidationResult(
                success=False,
                consolidation_type=ConsolidationType.EPISODIC_TO_SEMANTIC,
                source_items=[],
                target_items=[],
                execution_time=execution_time,
                error=str(e)
            )

    async def consolidate_working_to_semantic(self,
                                            item_types: Optional[List[WorkingMemoryType]] = None,
                                            min_importance: float = 0.8) -> ConsolidationResult:
        """
        Consolidate important working memory items directly to semantic memory.

        Args:
            item_types: Types of items to consolidate
            min_importance: Minimum importance threshold

        Returns:
            ConsolidationResult
        """
        start_time = datetime.now()

        try:
            # Get working memory items
            items = []

            if item_types:
                for item_type in item_types:
                    type_items = await self.working_memory.find_items(item_type=item_type)
                    items.extend(type_items)
            else:
                items = await self.working_memory.find_items()

            # Filter by importance
            items = [item for item in items if item.activation_level >= min_importance]

            if not items:
                return ConsolidationResult(
                    success=True,
                    consolidation_type=ConsolidationType.WORKING_TO_SEMANTIC,
                    source_items=[],
                    target_items=[]
                )

            source_item_ids = []
            target_concept_ids = []

            # Convert items to semantic concepts
            for item in items:
                concept_type = self._map_working_memory_type_to_concept_type(item.item_type)

                concept_id = await self.semantic_memory.add_concept(
                    name=self._generate_concept_name(item),
                    concept_type=concept_type,
                    description=self._generate_concept_description(item),
                    properties=item.content,
                    confidence=item.confidence,
                    importance=item.activation_level,
                    tags=item.tags,
                    organization=getattr(item, 'organization', None)
                )

                target_concept_ids.append(concept_id)
                source_item_ids.append(item.id)

                # Remove from working memory
                await self.working_memory.remove_item(item.id)

            execution_time = (datetime.now() - start_time).total_seconds()

            result = ConsolidationResult(
                success=True,
                consolidation_type=ConsolidationType.WORKING_TO_SEMANTIC,
                source_items=source_item_ids,
                target_items=target_concept_ids,
                execution_time=execution_time
            )

            self.successful_consolidations += 1
            logger.info(f"Consolidated {len(source_item_ids)} working memory items to {len(target_concept_ids)} concepts")

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.failed_consolidations += 1

            logger.error(f"Failed to consolidate working memory to semantic: {e}")

            return ConsolidationResult(
                success=False,
                consolidation_type=ConsolidationType.WORKING_TO_SEMANTIC,
                source_items=[],
                target_items=[],
                execution_time=execution_time,
                error=str(e)
            )
