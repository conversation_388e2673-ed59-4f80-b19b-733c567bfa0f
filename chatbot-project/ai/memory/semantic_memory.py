"""
Semantic Memory System for CHaBot.
Stores and manages structured knowledge, concepts, and their relationships.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import uuid
import sqlite3
import networkx as nx
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConceptType(Enum):
    """Types of semantic concepts."""
    ENTITY = "entity"
    CONCEPT = "concept"
    PROCEDURE = "procedure"
    FACT = "fact"
    RULE = "rule"
    PATTERN = "pattern"
    CATEGORY = "category"
    RELATIONSHIP = "relationship"

class RelationType(Enum):
    """Types of relationships between concepts."""
    IS_A = "is_a"
    PART_OF = "part_of"
    RELATED_TO = "related_to"
    CAUSES = "causes"
    ENABLES = "enables"
    REQUIRES = "requires"
    SIMILAR_TO = "similar_to"
    OPPOSITE_OF = "opposite_of"
    EXAMPLE_OF = "example_of"
    USED_FOR = "used_for"

@dataclass
class SemanticConcept:
    """Represents a semantic concept in memory."""
    id: str
    name: str
    concept_type: ConceptType
    description: str
    properties: Dict[str, Any] = field(default_factory=dict)
    
    # Knowledge attributes
    confidence: float = 0.8
    importance: float = 0.5
    frequency: int = 0
    last_updated: datetime = field(default_factory=datetime.now)
    
    # Context
    domain: Optional[str] = None
    organization: Optional[str] = None
    department: Optional[str] = None
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    sources: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "name": self.name,
            "concept_type": self.concept_type.value,
            "description": self.description,
            "properties": self.properties,
            "confidence": self.confidence,
            "importance": self.importance,
            "frequency": self.frequency,
            "last_updated": self.last_updated.isoformat(),
            "domain": self.domain,
            "organization": self.organization,
            "department": self.department,
            "tags": self.tags,
            "sources": self.sources
        }

@dataclass
class SemanticRelation:
    """Represents a relationship between semantic concepts."""
    id: str
    source_concept_id: str
    target_concept_id: str
    relation_type: RelationType
    strength: float = 0.5
    confidence: float = 0.8
    bidirectional: bool = False
    
    # Context
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "source_concept_id": self.source_concept_id,
            "target_concept_id": self.target_concept_id,
            "relation_type": self.relation_type.value,
            "strength": self.strength,
            "confidence": self.confidence,
            "bidirectional": self.bidirectional,
            "context": self.context,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat()
        }

class SemanticMemorySystem:
    """
    Semantic Memory System that manages structured knowledge and concepts.
    """
    
    def __init__(self, database_path: str = ":memory:"):
        """Initialize the semantic memory system."""
        self.database_path = database_path
        self.connection = None
        
        # Memory stores
        self.concepts: Dict[str, SemanticConcept] = {}
        self.relations: Dict[str, SemanticRelation] = {}
        
        # Knowledge graph
        self.knowledge_graph = nx.DiGraph()
        
        # Indexes
        self.name_index: Dict[str, str] = {}  # name -> concept_id
        self.type_index: Dict[ConceptType, Set[str]] = defaultdict(set)
        self.domain_index: Dict[str, Set[str]] = defaultdict(set)
        self.tag_index: Dict[str, Set[str]] = defaultdict(set)
        
        # Statistics
        self.total_concepts = 0
        self.total_relations = 0
        self.successful_queries = 0
        self.failed_queries = 0
        
        # Initialize database
        self._init_database()
        
        logger.info("Semantic Memory System initialized")
    
    def _init_database(self):
        """Initialize the SQLite database for persistent storage."""
        try:
            self.connection = sqlite3.connect(self.database_path)
            self.connection.row_factory = sqlite3.Row
            
            # Create concepts table
            self.connection.execute("""
                CREATE TABLE IF NOT EXISTS concepts (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    concept_type TEXT NOT NULL,
                    description TEXT,
                    properties TEXT,
                    confidence REAL DEFAULT 0.8,
                    importance REAL DEFAULT 0.5,
                    frequency INTEGER DEFAULT 0,
                    last_updated TEXT,
                    domain TEXT,
                    organization TEXT,
                    department TEXT,
                    tags TEXT,
                    sources TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create relations table
            self.connection.execute("""
                CREATE TABLE IF NOT EXISTS relations (
                    id TEXT PRIMARY KEY,
                    source_concept_id TEXT NOT NULL,
                    target_concept_id TEXT NOT NULL,
                    relation_type TEXT NOT NULL,
                    strength REAL DEFAULT 0.5,
                    confidence REAL DEFAULT 0.8,
                    bidirectional BOOLEAN DEFAULT FALSE,
                    context TEXT,
                    created_at TEXT,
                    last_updated TEXT,
                    FOREIGN KEY (source_concept_id) REFERENCES concepts (id),
                    FOREIGN KEY (target_concept_id) REFERENCES concepts (id)
                )
            """)
            
            # Create indexes
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_concept_name ON concepts(name)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_concept_type ON concepts(concept_type)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_concept_domain ON concepts(domain)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_relation_source ON relations(source_concept_id)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_relation_target ON relations(target_concept_id)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_relation_type ON relations(relation_type)")
            
            self.connection.commit()
            logger.info("Semantic memory database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize semantic memory database: {e}")
    
    async def add_concept(self,
                         name: str,
                         concept_type: ConceptType,
                         description: str,
                         properties: Optional[Dict[str, Any]] = None,
                         confidence: float = 0.8,
                         importance: float = 0.5,
                         domain: Optional[str] = None,
                         organization: Optional[str] = None,
                         department: Optional[str] = None,
                         tags: Optional[List[str]] = None,
                         sources: Optional[List[str]] = None) -> str:
        """
        Add a new semantic concept.
        
        Args:
            name: Name of the concept
            concept_type: Type of concept
            description: Description of the concept
            properties: Additional properties
            confidence: Confidence in the concept (0.0-1.0)
            importance: Importance of the concept (0.0-1.0)
            domain: Domain/category
            organization: Organization context
            department: Department context
            tags: Tags for categorization
            sources: Sources of information
            
        Returns:
            Concept ID
        """
        # Check if concept already exists
        if name.lower() in self.name_index:
            existing_id = self.name_index[name.lower()]
            logger.info(f"Concept '{name}' already exists with ID: {existing_id}")
            return existing_id
        
        concept_id = str(uuid.uuid4())
        
        concept = SemanticConcept(
            id=concept_id,
            name=name,
            concept_type=concept_type,
            description=description,
            properties=properties or {},
            confidence=confidence,
            importance=importance,
            domain=domain,
            organization=organization,
            department=department,
            tags=tags or [],
            sources=sources or []
        )
        
        # Store in memory
        self.concepts[concept_id] = concept
        
        # Update indexes
        self._update_concept_indexes(concept)
        
        # Add to knowledge graph
        self.knowledge_graph.add_node(concept_id, **concept.to_dict())
        
        # Store in database
        await self._store_concept_in_db(concept)
        
        self.total_concepts += 1
        
        logger.info(f"Added concept: {name} (ID: {concept_id})")
        return concept_id
    
    async def add_relation(self,
                          source_concept_id: str,
                          target_concept_id: str,
                          relation_type: RelationType,
                          strength: float = 0.5,
                          confidence: float = 0.8,
                          bidirectional: bool = False,
                          context: Optional[Dict[str, Any]] = None) -> str:
        """
        Add a relationship between two concepts.
        
        Args:
            source_concept_id: ID of source concept
            target_concept_id: ID of target concept
            relation_type: Type of relationship
            strength: Strength of relationship (0.0-1.0)
            confidence: Confidence in relationship (0.0-1.0)
            bidirectional: Whether relationship is bidirectional
            context: Additional context
            
        Returns:
            Relation ID
        """
        if source_concept_id not in self.concepts or target_concept_id not in self.concepts:
            logger.error("Cannot create relation: one or both concepts don't exist")
            return ""
        
        relation_id = str(uuid.uuid4())
        
        relation = SemanticRelation(
            id=relation_id,
            source_concept_id=source_concept_id,
            target_concept_id=target_concept_id,
            relation_type=relation_type,
            strength=strength,
            confidence=confidence,
            bidirectional=bidirectional,
            context=context or {}
        )
        
        # Store in memory
        self.relations[relation_id] = relation
        
        # Add to knowledge graph
        self.knowledge_graph.add_edge(
            source_concept_id,
            target_concept_id,
            relation_id=relation_id,
            relation_type=relation_type.value,
            strength=strength,
            confidence=confidence
        )
        
        # Add reverse edge if bidirectional
        if bidirectional:
            self.knowledge_graph.add_edge(
                target_concept_id,
                source_concept_id,
                relation_id=relation_id,
                relation_type=relation_type.value,
                strength=strength,
                confidence=confidence
            )
        
        # Store in database
        await self._store_relation_in_db(relation)
        
        self.total_relations += 1
        
        logger.info(f"Added relation: {relation_type.value} between {source_concept_id} and {target_concept_id}")
        return relation_id

    async def find_concepts(self,
                           query: Optional[str] = None,
                           concept_type: Optional[ConceptType] = None,
                           domain: Optional[str] = None,
                           tags: Optional[List[str]] = None,
                           organization: Optional[str] = None,
                           limit: int = 10) -> List[SemanticConcept]:
        """
        Find concepts based on search criteria.

        Args:
            query: Text query to search in name/description
            concept_type: Filter by concept type
            domain: Filter by domain
            tags: Filter by tags
            organization: Filter by organization
            limit: Maximum number of results

        Returns:
            List of matching concepts
        """
        try:
            candidates = list(self.concepts.values())

            # Apply filters
            if concept_type:
                candidates = [c for c in candidates if c.concept_type == concept_type]

            if domain:
                candidates = [c for c in candidates if c.domain == domain]

            if organization:
                candidates = [c for c in candidates if c.organization == organization]

            if tags:
                candidates = [c for c in candidates if any(tag in c.tags for tag in tags)]

            # Text search
            if query:
                query_lower = query.lower()
                candidates = [
                    c for c in candidates
                    if query_lower in c.name.lower() or query_lower in c.description.lower()
                ]

            # Sort by importance and frequency
            candidates.sort(key=lambda c: (c.importance, c.frequency), reverse=True)

            # Update frequency for accessed concepts
            results = candidates[:limit]
            for concept in results:
                concept.frequency += 1
                concept.last_updated = datetime.now()
                await self._update_concept_in_db(concept)

            self.successful_queries += 1
            return results

        except Exception as e:
            self.failed_queries += 1
            logger.error(f"Failed to find concepts: {e}")
            return []

    async def get_related_concepts(self,
                                  concept_id: str,
                                  relation_types: Optional[List[RelationType]] = None,
                                  max_depth: int = 2,
                                  limit: int = 10) -> List[Tuple[SemanticConcept, List[SemanticRelation]]]:
        """
        Get concepts related to the given concept.

        Args:
            concept_id: ID of the source concept
            relation_types: Filter by relation types
            max_depth: Maximum depth to traverse
            limit: Maximum number of results

        Returns:
            List of (concept, path_relations) tuples
        """
        if concept_id not in self.concepts:
            return []

        related = []
        visited = set()

        def traverse(current_id: str, depth: int, path: List[SemanticRelation]):
            if depth > max_depth or current_id in visited:
                return

            visited.add(current_id)

            # Get outgoing relations
            if current_id in self.knowledge_graph:
                for neighbor_id in self.knowledge_graph.neighbors(current_id):
                    edge_data = self.knowledge_graph.get_edge_data(current_id, neighbor_id)
                    relation_id = edge_data.get('relation_id')

                    if relation_id in self.relations:
                        relation = self.relations[relation_id]

                        # Filter by relation type if specified
                        if relation_types and relation.relation_type not in relation_types:
                            continue

                        new_path = path + [relation]

                        if neighbor_id in self.concepts:
                            related.append((self.concepts[neighbor_id], new_path))

                        # Continue traversal
                        if depth < max_depth:
                            traverse(neighbor_id, depth + 1, new_path)

        traverse(concept_id, 0, [])

        # Sort by relation strength and concept importance
        related.sort(key=lambda x: (
            sum(r.strength for r in x[1]) / len(x[1]) if x[1] else 0,
            x[0].importance
        ), reverse=True)

        return related[:limit]

    async def find_shortest_path(self, source_id: str, target_id: str) -> Optional[List[SemanticRelation]]:
        """Find the shortest path between two concepts."""
        if source_id not in self.knowledge_graph or target_id not in self.knowledge_graph:
            return None

        try:
            path = nx.shortest_path(self.knowledge_graph, source_id, target_id)
            relations = []

            for i in range(len(path) - 1):
                edge_data = self.knowledge_graph.get_edge_data(path[i], path[i + 1])
                relation_id = edge_data.get('relation_id')
                if relation_id in self.relations:
                    relations.append(self.relations[relation_id])

            return relations

        except nx.NetworkXNoPath:
            return None

    async def get_concept_clusters(self, min_cluster_size: int = 3) -> List[List[str]]:
        """Get clusters of related concepts."""
        # Use community detection algorithm
        try:
            import networkx.algorithms.community as nx_comm
            communities = nx_comm.greedy_modularity_communities(self.knowledge_graph.to_undirected())

            clusters = []
            for community in communities:
                if len(community) >= min_cluster_size:
                    clusters.append(list(community))

            return clusters

        except ImportError:
            logger.warning("NetworkX community detection not available")
            return []

    async def update_concept(self, concept_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing concept."""
        if concept_id not in self.concepts:
            return False

        concept = self.concepts[concept_id]

        # Update fields
        for key, value in updates.items():
            if hasattr(concept, key):
                setattr(concept, key, value)

        concept.last_updated = datetime.now()

        # Update indexes
        self._update_concept_indexes(concept)

        # Update knowledge graph
        self.knowledge_graph.nodes[concept_id].update(concept.to_dict())

        # Update in database
        await self._update_concept_in_db(concept)

        logger.info(f"Updated concept: {concept_id}")
        return True

    async def delete_concept(self, concept_id: str) -> bool:
        """Delete a concept and its relations."""
        if concept_id not in self.concepts:
            return False

        # Remove related relations
        relations_to_remove = []
        for relation_id, relation in self.relations.items():
            if relation.source_concept_id == concept_id or relation.target_concept_id == concept_id:
                relations_to_remove.append(relation_id)

        for relation_id in relations_to_remove:
            await self.delete_relation(relation_id)

        # Remove from memory
        concept = self.concepts[concept_id]
        del self.concepts[concept_id]

        # Remove from indexes
        self._remove_concept_from_indexes(concept)

        # Remove from knowledge graph
        if concept_id in self.knowledge_graph:
            self.knowledge_graph.remove_node(concept_id)

        # Remove from database
        try:
            self.connection.execute("DELETE FROM concepts WHERE id = ?", (concept_id,))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to delete concept from database: {e}")

        logger.info(f"Deleted concept: {concept_id}")
        return True

    async def delete_relation(self, relation_id: str) -> bool:
        """Delete a relation."""
        if relation_id not in self.relations:
            return False

        relation = self.relations[relation_id]

        # Remove from knowledge graph
        if self.knowledge_graph.has_edge(relation.source_concept_id, relation.target_concept_id):
            self.knowledge_graph.remove_edge(relation.source_concept_id, relation.target_concept_id)

        if relation.bidirectional and self.knowledge_graph.has_edge(relation.target_concept_id, relation.source_concept_id):
            self.knowledge_graph.remove_edge(relation.target_concept_id, relation.source_concept_id)

        # Remove from memory
        del self.relations[relation_id]

        # Remove from database
        try:
            self.connection.execute("DELETE FROM relations WHERE id = ?", (relation_id,))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to delete relation from database: {e}")

        logger.info(f"Deleted relation: {relation_id}")
        return True

    def _update_concept_indexes(self, concept: SemanticConcept):
        """Update indexes for a concept."""
        # Name index
        self.name_index[concept.name.lower()] = concept.id

        # Type index
        self.type_index[concept.concept_type].add(concept.id)

        # Domain index
        if concept.domain:
            self.domain_index[concept.domain].add(concept.id)

        # Tag index
        for tag in concept.tags:
            self.tag_index[tag].add(concept.id)

    def _remove_concept_from_indexes(self, concept: SemanticConcept):
        """Remove concept from indexes."""
        # Name index
        if concept.name.lower() in self.name_index:
            del self.name_index[concept.name.lower()]

        # Type index
        self.type_index[concept.concept_type].discard(concept.id)

        # Domain index
        if concept.domain:
            self.domain_index[concept.domain].discard(concept.id)

        # Tag index
        for tag in concept.tags:
            self.tag_index[tag].discard(concept.id)

    async def get_concept_by_id(self, concept_id: str) -> Optional[SemanticConcept]:
        """Get a concept by ID."""
        return self.concepts.get(concept_id)

    async def _store_concept_in_db(self, concept: SemanticConcept):
        """Store concept in database."""
        try:
            self.connection.execute("""
                INSERT OR REPLACE INTO concepts (
                    id, name, concept_type, description, properties,
                    confidence, importance, frequency, last_updated,
                    domain, organization, department, tags, sources
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                concept.id,
                concept.name,
                concept.concept_type.value,
                concept.description,
                json.dumps(concept.properties),
                concept.confidence,
                concept.importance,
                concept.frequency,
                concept.last_updated.isoformat(),
                concept.domain,
                concept.organization,
                concept.department,
                json.dumps(concept.tags),
                json.dumps(concept.sources)
            ))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to store concept in database: {e}")

    async def _update_concept_in_db(self, concept: SemanticConcept):
        """Update concept in database."""
        try:
            self.connection.execute("""
                UPDATE concepts SET
                    frequency = ?, last_updated = ?, importance = ?, confidence = ?
                WHERE id = ?
            """, (
                concept.frequency,
                concept.last_updated.isoformat(),
                concept.importance,
                concept.confidence,
                concept.id
            ))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to update concept in database: {e}")

    async def _store_relation_in_db(self, relation: SemanticRelation):
        """Store relation in database."""
        try:
            self.connection.execute("""
                INSERT OR REPLACE INTO relations (
                    id, source_concept_id, target_concept_id, relation_type,
                    strength, confidence, bidirectional, context,
                    created_at, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                relation.id,
                relation.source_concept_id,
                relation.target_concept_id,
                relation.relation_type.value,
                relation.strength,
                relation.confidence,
                relation.bidirectional,
                json.dumps(relation.context),
                relation.created_at.isoformat(),
                relation.last_updated.isoformat()
            ))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to store relation in database: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """Get semantic memory statistics."""
        # Concepts by type
        concepts_by_type = {}
        for concept_type in ConceptType:
            count = len(self.type_index[concept_type])
            concepts_by_type[concept_type.value] = count

        # Concepts by domain
        concepts_by_domain = {}
        for domain, concept_ids in self.domain_index.items():
            concepts_by_domain[domain] = len(concept_ids)

        # Relations by type
        relations_by_type = {}
        for relation in self.relations.values():
            rel_type = relation.relation_type.value
            relations_by_type[rel_type] = relations_by_type.get(rel_type, 0) + 1

        return {
            "total_concepts": self.total_concepts,
            "total_relations": self.total_relations,
            "successful_queries": self.successful_queries,
            "failed_queries": self.failed_queries,
            "concepts_by_type": concepts_by_type,
            "concepts_by_domain": concepts_by_domain,
            "relations_by_type": relations_by_type,
            "knowledge_graph_nodes": self.knowledge_graph.number_of_nodes(),
            "knowledge_graph_edges": self.knowledge_graph.number_of_edges()
        }

    def close(self):
        """Close the semantic memory system."""
        if self.connection:
            self.connection.close()
            self.connection = None
        logger.info("Semantic Memory System closed")
