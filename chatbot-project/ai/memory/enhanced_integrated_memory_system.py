"""
Enhanced Integrated Memory System for CHaBot.
Combines episodic, semantic, working memory, and consolidation systems.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field

# Import memory components
from .episodic_memory import EpisodicMemorySystem, EpisodeType, EpisodeStatus
from .semantic_memory import SemanticMemorySystem, ConceptType, RelationType
from .working_memory import WorkingMemorySystem, WorkingMemoryType, Priority
from .memory_consolidation import MemoryConsolidationSystem, ConsolidationType

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class MemoryQuery:
    """Query for memory retrieval."""
    query_text: str
    memory_types: List[str] = field(default_factory=lambda: ["episodic", "semantic", "working"])
    time_range: Optional[Tuple[datetime, datetime]] = None
    importance_threshold: float = 0.3
    limit: int = 10
    organization: Optional[str] = None
    department: Optional[str] = None
    user_id: Optional[str] = None
    agent_id: Optional[str] = None
    session_id: Optional[str] = None
    tags: Optional[List[str]] = None

@dataclass
class MemoryResult:
    """Result from memory query."""
    episodic_memories: List[Dict[str, Any]] = field(default_factory=list)
    semantic_concepts: List[Dict[str, Any]] = field(default_factory=list)
    working_memory_items: List[Dict[str, Any]] = field(default_factory=list)
    related_concepts: List[Dict[str, Any]] = field(default_factory=list)
    attention_focus: List[Dict[str, Any]] = field(default_factory=list)
    total_results: int = 0
    query_time: float = 0.0
    consolidation_suggestions: List[Dict[str, Any]] = field(default_factory=list)

class EnhancedIntegratedMemorySystem:
    """
    Enhanced Integrated Memory System that coordinates all memory components
    with advanced querying, consolidation, and reasoning support.
    """
    
    def __init__(self, 
                 database_path: str = ":memory:",
                 working_memory_capacity: int = 100,
                 auto_consolidation: bool = True):
        """Initialize the enhanced integrated memory system."""
        # Initialize individual memory systems
        db_prefix = database_path if database_path != ":memory:" else ":memory:"
        
        self.episodic_memory = EpisodicMemorySystem(
            f"{db_prefix}_episodic.db" if db_prefix != ":memory:" else ":memory:"
        )
        self.semantic_memory = SemanticMemorySystem(
            f"{db_prefix}_semantic.db" if db_prefix != ":memory:" else ":memory:"
        )
        self.working_memory = WorkingMemorySystem(capacity=working_memory_capacity)
        
        # Initialize consolidation system
        self.consolidation_system = MemoryConsolidationSystem(
            self.episodic_memory,
            self.semantic_memory,
            self.working_memory
        )
        
        # Configuration
        self.auto_consolidation = auto_consolidation
        
        # System state
        self.is_running = False
        self.background_tasks = []
        
        # Statistics
        self.total_queries = 0
        self.successful_queries = 0
        self.failed_queries = 0
        self.total_consolidations = 0
        
        # Performance tracking
        self.query_times = []
        self.consolidation_times = []
        
        logger.info("Enhanced Integrated Memory System initialized")
    
    async def start(self):
        """Start the integrated memory system."""
        if self.is_running:
            return
        
        self.is_running = True
        
        if self.auto_consolidation:
            # Start background consolidation task
            consolidation_task = asyncio.create_task(self._background_consolidation())
            self.background_tasks.append(consolidation_task)
        
        # Start working memory maintenance task
        maintenance_task = asyncio.create_task(self._background_maintenance())
        self.background_tasks.append(maintenance_task)
        
        logger.info("Enhanced Integrated Memory System started")
    
    async def stop(self):
        """Stop the integrated memory system."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.background_tasks, return_exceptions=True)
        self.background_tasks.clear()
        
        # Close memory systems
        self.episodic_memory.close()
        self.semantic_memory.close()
        self.working_memory.close()
        
        logger.info("Enhanced Integrated Memory System stopped")
    
    async def comprehensive_query(self, query: MemoryQuery) -> MemoryResult:
        """
        Comprehensive query across all memory systems with enhanced features.
        
        Args:
            query: Memory query parameters
            
        Returns:
            MemoryResult with comprehensive results
        """
        start_time = datetime.now()
        self.total_queries += 1
        
        try:
            result = MemoryResult()
            
            # Query episodic memory
            if "episodic" in query.memory_types:
                episodic_results = await self.episodic_memory.retrieve_episodes(
                    query=query.query_text,
                    time_range=query.time_range,
                    tags=query.tags,
                    organization=query.organization,
                    department=query.department,
                    user_id=query.user_id,
                    agent_id=query.agent_id,
                    limit=query.limit
                )
                
                result.episodic_memories = [
                    episode.to_dict() for episode in episodic_results
                    if episode.importance >= query.importance_threshold
                ]
            
            # Query semantic memory
            if "semantic" in query.memory_types:
                semantic_results = await self.semantic_memory.find_concepts(
                    query=query.query_text,
                    tags=query.tags,
                    organization=query.organization,
                    limit=query.limit
                )
                
                result.semantic_concepts = [
                    concept.to_dict() for concept in semantic_results
                    if concept.importance >= query.importance_threshold
                ]
                
                # Get related concepts for the first few results
                if semantic_results:
                    for concept in semantic_results[:3]:
                        related = await self.semantic_memory.get_related_concepts(
                            concept.id, max_depth=2, limit=5
                        )
                        for related_concept, relations in related:
                            result.related_concepts.append({
                                "concept": related_concept.to_dict(),
                                "relations": [r.to_dict() for r in relations],
                                "source_concept_id": concept.id
                            })
            
            # Query working memory
            if "working" in query.memory_types:
                working_results = await self.working_memory.find_items(
                    agent_id=query.agent_id,
                    session_id=query.session_id,
                    tags=query.tags,
                    limit=query.limit
                )
                
                result.working_memory_items = [
                    item.to_dict() for item in working_results
                    if item.activation_level >= query.importance_threshold
                ]
                
                # Get attention focus
                attention_items = await self.working_memory.get_attention_focus(query.session_id)
                result.attention_focus = [item.to_dict() for item in attention_items]
            
            # Generate consolidation suggestions
            result.consolidation_suggestions = await self._generate_consolidation_suggestions(query)
            
            # Calculate totals
            result.total_results = (
                len(result.episodic_memories) +
                len(result.semantic_concepts) +
                len(result.working_memory_items)
            )
            
            query_time = (datetime.now() - start_time).total_seconds()
            result.query_time = query_time
            self.query_times.append(query_time)
            
            self.successful_queries += 1
            logger.info(f"Comprehensive memory query completed: {result.total_results} results in {query_time:.3f}s")
            
            return result
            
        except Exception as e:
            self.failed_queries += 1
            logger.error(f"Comprehensive memory query failed: {e}")
            
            return MemoryResult(
                query_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def store_reasoning_session(self,
                                    goal: str,
                                    steps: List[Dict[str, Any]],
                                    outcome: Dict[str, Any],
                                    agent_id: Optional[str] = None,
                                    organization: Optional[str] = None) -> Tuple[str, str]:
        """
        Store a complete reasoning session in both working and episodic memory.
        
        Args:
            goal: The reasoning goal
            steps: List of reasoning steps
            outcome: Final outcome
            agent_id: Associated agent ID
            organization: Organization context
            
        Returns:
            Tuple of (working_memory_session_id, episodic_memory_id)
        """
        # Start working memory session
        session_id = await self.working_memory.start_reasoning_session(
            goal=goal,
            max_steps=len(steps) + 10
        )
        
        # Add reasoning steps to working memory
        for i, step in enumerate(steps):
            await self.working_memory.add_reasoning_step(
                session_id=session_id,
                step_type=step.get("type", "reasoning"),
                content=step,
                confidence=step.get("confidence", 0.8)
            )
        
        # Store outcome in working memory
        await self.working_memory.add_item(
            WorkingMemoryType.INTERMEDIATE_RESULT,
            outcome,
            Priority.HIGH,
            session_id=session_id,
            tags=["outcome", "result"]
        )
        
        # Create episodic memory
        episode_id = await self.episodic_memory.store_episode(
            episode_type=EpisodeType.PROBLEM_SOLVING,
            title=f"Reasoning: {goal}",
            description=f"Problem-solving session with {len(steps)} steps",
            content={
                "goal": goal,
                "steps": steps,
                "outcome": outcome,
                "session_id": session_id
            },
            importance=0.8 if outcome.get("success", False) else 0.6,
            tags=["reasoning", "problem_solving"],
            agent_id=agent_id,
            organization=organization
        )
        
        return session_id, episode_id
    
    async def get_reasoning_context(self, 
                                  current_goal: str,
                                  agent_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get relevant context for reasoning tasks.
        
        Args:
            current_goal: Current reasoning goal
            agent_id: Agent requesting context
            
        Returns:
            Dictionary with relevant context
        """
        # Query for similar past reasoning sessions
        query = MemoryQuery(
            query_text=current_goal,
            memory_types=["episodic", "semantic"],
            agent_id=agent_id,
            limit=5
        )
        
        results = await self.comprehensive_query(query)
        
        # Get current attention focus
        attention_items = await self.working_memory.get_attention_focus()
        
        context = {
            "similar_episodes": results.episodic_memories,
            "relevant_concepts": results.semantic_concepts,
            "related_concepts": results.related_concepts,
            "current_attention": [item.to_dict() for item in attention_items],
            "reasoning_patterns": self._extract_reasoning_patterns(results.episodic_memories)
        }
        
        return context

    async def _generate_consolidation_suggestions(self, query: MemoryQuery) -> List[Dict[str, Any]]:
        """Generate suggestions for memory consolidation."""
        suggestions = []

        # Check working memory utilization
        wm_stats = self.working_memory.get_statistics()
        if wm_stats["utilization"] > 0.8:
            suggestions.append({
                "type": "capacity_warning",
                "message": "Working memory is near capacity",
                "recommendation": "Consider consolidating to episodic memory",
                "priority": "high"
            })

        # Check for consolidation opportunities
        if wm_stats["current_items"] > 20:
            suggestions.append({
                "type": "consolidation_opportunity",
                "message": f"{wm_stats['current_items']} items in working memory",
                "recommendation": "Run consolidation to preserve important memories",
                "priority": "medium"
            })

        return suggestions

    def _extract_reasoning_patterns(self, episodes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract reasoning patterns from episodic memories."""
        patterns = []

        # Look for problem-solving episodes
        problem_solving_episodes = [
            ep for ep in episodes
            if ep.get("episode_type") == "problem_solving"
        ]

        if len(problem_solving_episodes) >= 2:
            patterns.append({
                "type": "problem_solving_pattern",
                "frequency": len(problem_solving_episodes),
                "description": "Recurring problem-solving approach",
                "episodes": [ep["id"] for ep in problem_solving_episodes]
            })

        return patterns

    async def _background_consolidation(self):
        """Background task for automatic memory consolidation."""
        while self.is_running:
            try:
                # Run consolidation every 30 minutes
                await asyncio.sleep(1800)  # 30 minutes

                if not self.is_running:
                    break

                logger.info("Running background memory consolidation...")

                # Consolidate working memory to episodic
                result = await self.consolidation_system.consolidate_working_to_episodic(
                    min_importance=0.4
                )

                if result.success:
                    self.total_consolidations += 1
                    consolidation_time = result.execution_time
                    self.consolidation_times.append(consolidation_time)

                    logger.info(f"Background consolidation completed: {len(result.source_items)} items consolidated in {consolidation_time:.3f}s")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Background consolidation failed: {e}")

    async def _background_maintenance(self):
        """Background task for memory system maintenance."""
        while self.is_running:
            try:
                # Run maintenance every 10 minutes
                await asyncio.sleep(600)  # 10 minutes

                if not self.is_running:
                    break

                # Working memory maintenance
                await self.working_memory.maintenance()

                # Episodic memory consolidation
                await self.episodic_memory.consolidate_episodes()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Background maintenance failed: {e}")

    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics from all memory systems."""
        episodic_stats = self.episodic_memory.get_statistics()
        semantic_stats = self.semantic_memory.get_statistics()
        working_stats = self.working_memory.get_statistics()
        consolidation_stats = self.consolidation_system.get_statistics()

        # Calculate performance metrics
        avg_query_time = sum(self.query_times) / max(len(self.query_times), 1)
        avg_consolidation_time = sum(self.consolidation_times) / max(len(self.consolidation_times), 1)

        return {
            "integrated_system": {
                "total_queries": self.total_queries,
                "successful_queries": self.successful_queries,
                "failed_queries": self.failed_queries,
                "query_success_rate": self.successful_queries / max(self.total_queries, 1),
                "average_query_time": avg_query_time,
                "total_consolidations": self.total_consolidations,
                "average_consolidation_time": avg_consolidation_time,
                "is_running": self.is_running,
                "auto_consolidation": self.auto_consolidation
            },
            "episodic_memory": episodic_stats,
            "semantic_memory": semantic_stats,
            "working_memory": working_stats,
            "consolidation_system": consolidation_stats
        }