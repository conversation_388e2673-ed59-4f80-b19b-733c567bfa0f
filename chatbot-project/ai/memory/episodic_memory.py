"""
Episodic Memory System for CHaBot.
Stores and retrieves specific experiences and events with temporal context.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import uuid
import sqlite3
from contextlib import asynccontextmanager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EpisodeType(Enum):
    """Types of episodic memories."""
    CONVERSATION = "conversation"
    PROBLEM_SOLVING = "problem_solving"
    LEARNING = "learning"
    ERROR_RECOVERY = "error_recovery"
    SUCCESS_PATTERN = "success_pattern"
    USER_INTERACTION = "user_interaction"
    AGENT_COLLABORATION = "agent_collaboration"

class EpisodeStatus(Enum):
    """Status of episodic memories."""
    ACTIVE = "active"
    CONSOLIDATED = "consolidated"
    ARCHIVED = "archived"
    FORGOTTEN = "forgotten"

@dataclass
class EpisodicContext:
    """Context information for an episode."""
    location: Optional[str] = None
    participants: List[str] = field(default_factory=list)
    environment: Dict[str, Any] = field(default_factory=dict)
    preconditions: List[str] = field(default_factory=list)
    postconditions: List[str] = field(default_factory=list)
    emotional_state: Optional[str] = None
    confidence_level: float = 0.5

@dataclass
class EpisodicMemory:
    """Represents a single episodic memory."""
    id: str
    episode_type: EpisodeType
    title: str
    description: str
    content: Dict[str, Any]
    context: EpisodicContext
    timestamp: datetime
    duration: Optional[timedelta] = None
    
    # Memory metadata
    importance: float = 0.5
    vividness: float = 0.5
    accuracy: float = 0.5
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    status: EpisodeStatus = EpisodeStatus.ACTIVE
    
    # Relationships
    related_episodes: List[str] = field(default_factory=list)
    causal_links: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    
    # Organization context
    organization: Optional[str] = None
    department: Optional[str] = None
    user_id: Optional[str] = None
    agent_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "episode_type": self.episode_type.value,
            "title": self.title,
            "description": self.description,
            "content": self.content,
            "context": asdict(self.context),
            "timestamp": self.timestamp.isoformat(),
            "duration": self.duration.total_seconds() if self.duration else None,
            "importance": self.importance,
            "vividness": self.vividness,
            "accuracy": self.accuracy,
            "access_count": self.access_count,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "status": self.status.value,
            "related_episodes": self.related_episodes,
            "causal_links": self.causal_links,
            "tags": self.tags,
            "organization": self.organization,
            "department": self.department,
            "user_id": self.user_id,
            "agent_id": self.agent_id
        }

class EpisodicMemorySystem:
    """
    Episodic Memory System that stores and retrieves specific experiences.
    """
    
    def __init__(self, database_path: str = ":memory:"):
        """Initialize the episodic memory system."""
        self.database_path = database_path
        self.connection = None
        
        # Memory stores
        self.episodes: Dict[str, EpisodicMemory] = {}
        self.temporal_index: Dict[str, List[str]] = {}  # Date -> episode IDs
        self.type_index: Dict[EpisodeType, List[str]] = {}  # Type -> episode IDs
        self.tag_index: Dict[str, List[str]] = {}  # Tag -> episode IDs
        
        # Statistics
        self.total_episodes = 0
        self.successful_retrievals = 0
        self.failed_retrievals = 0
        
        # Initialize database
        self._init_database()
        
        logger.info("Episodic Memory System initialized")
    
    def _init_database(self):
        """Initialize the SQLite database for persistent storage."""
        try:
            self.connection = sqlite3.connect(self.database_path)
            self.connection.row_factory = sqlite3.Row
            
            # Create episodes table
            self.connection.execute("""
                CREATE TABLE IF NOT EXISTS episodes (
                    id TEXT PRIMARY KEY,
                    episode_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    content TEXT,
                    context TEXT,
                    timestamp TEXT NOT NULL,
                    duration REAL,
                    importance REAL DEFAULT 0.5,
                    vividness REAL DEFAULT 0.5,
                    accuracy REAL DEFAULT 0.5,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TEXT,
                    status TEXT DEFAULT 'active',
                    related_episodes TEXT,
                    causal_links TEXT,
                    tags TEXT,
                    organization TEXT,
                    department TEXT,
                    user_id TEXT,
                    agent_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for faster queries
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_episode_type ON episodes(episode_type)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON episodes(timestamp)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_organization ON episodes(organization)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_user_id ON episodes(user_id)")
            self.connection.execute("CREATE INDEX IF NOT EXISTS idx_agent_id ON episodes(agent_id)")
            
            self.connection.commit()
            logger.info("Episodic memory database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize episodic memory database: {e}")
    
    async def store_episode(self, 
                           episode_type: EpisodeType,
                           title: str,
                           description: str,
                           content: Dict[str, Any],
                           context: Optional[EpisodicContext] = None,
                           duration: Optional[timedelta] = None,
                           importance: float = 0.5,
                           tags: Optional[List[str]] = None,
                           organization: Optional[str] = None,
                           department: Optional[str] = None,
                           user_id: Optional[str] = None,
                           agent_id: Optional[str] = None) -> str:
        """
        Store a new episodic memory.
        
        Args:
            episode_type: Type of episode
            title: Brief title of the episode
            description: Detailed description
            content: Episode content and data
            context: Contextual information
            duration: How long the episode lasted
            importance: Importance score (0.0-1.0)
            tags: Tags for categorization
            organization: Organization context
            department: Department context
            user_id: Associated user ID
            agent_id: Associated agent ID
            
        Returns:
            Episode ID
        """
        episode_id = str(uuid.uuid4())
        
        if context is None:
            context = EpisodicContext()
        
        episode = EpisodicMemory(
            id=episode_id,
            episode_type=episode_type,
            title=title,
            description=description,
            content=content,
            context=context,
            timestamp=datetime.now(),
            duration=duration,
            importance=importance,
            tags=tags or [],
            organization=organization,
            department=department,
            user_id=user_id,
            agent_id=agent_id
        )
        
        # Store in memory
        self.episodes[episode_id] = episode
        
        # Update indexes
        self._update_indexes(episode)
        
        # Store in database
        await self._store_episode_in_db(episode)
        
        self.total_episodes += 1
        
        logger.info(f"Stored episode: {title} (ID: {episode_id})")
        return episode_id
    
    async def retrieve_episodes(self,
                               query: Optional[str] = None,
                               episode_type: Optional[EpisodeType] = None,
                               time_range: Optional[Tuple[datetime, datetime]] = None,
                               tags: Optional[List[str]] = None,
                               organization: Optional[str] = None,
                               department: Optional[str] = None,
                               user_id: Optional[str] = None,
                               agent_id: Optional[str] = None,
                               limit: int = 10,
                               sort_by: str = "timestamp") -> List[EpisodicMemory]:
        """
        Retrieve episodic memories based on criteria.
        
        Args:
            query: Text query to search in title/description
            episode_type: Filter by episode type
            time_range: Tuple of (start_time, end_time)
            tags: Filter by tags
            organization: Filter by organization
            department: Filter by department
            user_id: Filter by user ID
            agent_id: Filter by agent ID
            limit: Maximum number of results
            sort_by: Sort criteria (timestamp, importance, access_count)
            
        Returns:
            List of matching episodic memories
        """
        try:
            # Start with all episodes
            candidates = list(self.episodes.values())
            
            # Apply filters
            if episode_type:
                candidates = [e for e in candidates if e.episode_type == episode_type]
            
            if time_range:
                start_time, end_time = time_range
                candidates = [e for e in candidates if start_time <= e.timestamp <= end_time]
            
            if tags:
                candidates = [e for e in candidates if any(tag in e.tags for tag in tags)]
            
            if organization:
                candidates = [e for e in candidates if e.organization == organization]
            
            if department:
                candidates = [e for e in candidates if e.department == department]
            
            if user_id:
                candidates = [e for e in candidates if e.user_id == user_id]
            
            if agent_id:
                candidates = [e for e in candidates if e.agent_id == agent_id]
            
            # Text search in title and description
            if query:
                query_lower = query.lower()
                candidates = [
                    e for e in candidates 
                    if query_lower in e.title.lower() or query_lower in e.description.lower()
                ]
            
            # Sort results
            if sort_by == "timestamp":
                candidates.sort(key=lambda e: e.timestamp, reverse=True)
            elif sort_by == "importance":
                candidates.sort(key=lambda e: e.importance, reverse=True)
            elif sort_by == "access_count":
                candidates.sort(key=lambda e: e.access_count, reverse=True)
            
            # Update access statistics
            results = candidates[:limit]
            for episode in results:
                episode.access_count += 1
                episode.last_accessed = datetime.now()
                await self._update_episode_in_db(episode)
            
            self.successful_retrievals += 1
            logger.info(f"Retrieved {len(results)} episodes for query: {query}")
            
            return results
            
        except Exception as e:
            self.failed_retrievals += 1
            logger.error(f"Failed to retrieve episodes: {e}")
            return []
    
    async def get_episode_by_id(self, episode_id: str) -> Optional[EpisodicMemory]:
        """Get a specific episode by ID."""
        episode = self.episodes.get(episode_id)
        if episode:
            episode.access_count += 1
            episode.last_accessed = datetime.now()
            await self._update_episode_in_db(episode)
        return episode
    
    async def update_episode(self, episode_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing episode."""
        if episode_id not in self.episodes:
            return False
        
        episode = self.episodes[episode_id]
        
        # Update fields
        for key, value in updates.items():
            if hasattr(episode, key):
                setattr(episode, key, value)
        
        # Update in database
        await self._update_episode_in_db(episode)
        
        logger.info(f"Updated episode: {episode_id}")
        return True
    
    async def delete_episode(self, episode_id: str) -> bool:
        """Delete an episode."""
        if episode_id not in self.episodes:
            return False
        
        # Remove from memory
        del self.episodes[episode_id]
        
        # Remove from database
        try:
            self.connection.execute("DELETE FROM episodes WHERE id = ?", (episode_id,))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to delete episode from database: {e}")
        
        # Update indexes
        self._remove_from_indexes(episode_id)
        
        logger.info(f"Deleted episode: {episode_id}")
        return True

    async def find_related_episodes(self, episode_id: str, max_results: int = 5) -> List[EpisodicMemory]:
        """Find episodes related to the given episode."""
        if episode_id not in self.episodes:
            return []

        target_episode = self.episodes[episode_id]
        related = []

        # Find episodes with similar tags
        for episode in self.episodes.values():
            if episode.id == episode_id:
                continue

            # Calculate similarity based on tags, context, and content
            similarity = self._calculate_episode_similarity(target_episode, episode)
            if similarity > 0.3:  # Threshold for relatedness
                related.append((episode, similarity))

        # Sort by similarity and return top results
        related.sort(key=lambda x: x[1], reverse=True)
        return [episode for episode, _ in related[:max_results]]

    async def consolidate_episodes(self, time_threshold: timedelta = timedelta(hours=24)) -> int:
        """Consolidate related episodes that occurred within a time window."""
        consolidated_count = 0
        current_time = datetime.now()

        # Find episodes that can be consolidated
        episodes_by_type = {}
        for episode in self.episodes.values():
            if episode.status == EpisodeStatus.ACTIVE:
                if episode.episode_type not in episodes_by_type:
                    episodes_by_type[episode.episode_type] = []
                episodes_by_type[episode.episode_type].append(episode)

        # Consolidate similar episodes within time threshold
        for episode_type, episodes in episodes_by_type.items():
            episodes.sort(key=lambda e: e.timestamp)

            i = 0
            while i < len(episodes) - 1:
                current_episode = episodes[i]
                next_episode = episodes[i + 1]

                # Check if episodes are within time threshold and similar
                time_diff = next_episode.timestamp - current_episode.timestamp
                if time_diff <= time_threshold:
                    similarity = self._calculate_episode_similarity(current_episode, next_episode)

                    if similarity > 0.7:  # High similarity threshold for consolidation
                        # Merge episodes
                        await self._merge_episodes(current_episode, next_episode)
                        consolidated_count += 1
                        episodes.pop(i + 1)  # Remove merged episode
                        continue

                i += 1

        logger.info(f"Consolidated {consolidated_count} episodes")
        return consolidated_count

    def _calculate_episode_similarity(self, episode1: EpisodicMemory, episode2: EpisodicMemory) -> float:
        """Calculate similarity between two episodes."""
        similarity = 0.0

        # Tag similarity
        if episode1.tags and episode2.tags:
            common_tags = set(episode1.tags) & set(episode2.tags)
            total_tags = set(episode1.tags) | set(episode2.tags)
            tag_similarity = len(common_tags) / len(total_tags) if total_tags else 0
            similarity += tag_similarity * 0.3

        # Context similarity
        if episode1.organization == episode2.organization:
            similarity += 0.2
        if episode1.department == episode2.department:
            similarity += 0.2
        if episode1.user_id == episode2.user_id:
            similarity += 0.1
        if episode1.agent_id == episode2.agent_id:
            similarity += 0.1

        # Content similarity (simple keyword matching)
        content1_str = json.dumps(episode1.content).lower()
        content2_str = json.dumps(episode2.content).lower()

        words1 = set(content1_str.split())
        words2 = set(content2_str.split())

        if words1 and words2:
            common_words = words1 & words2
            total_words = words1 | words2
            content_similarity = len(common_words) / len(total_words) if total_words else 0
            similarity += content_similarity * 0.1

        return min(similarity, 1.0)

    async def _merge_episodes(self, primary: EpisodicMemory, secondary: EpisodicMemory):
        """Merge two similar episodes."""
        # Update primary episode with merged content
        primary.description += f" [Merged with: {secondary.title}]"

        # Merge content
        if "merged_episodes" not in primary.content:
            primary.content["merged_episodes"] = []
        primary.content["merged_episodes"].append(secondary.to_dict())

        # Merge tags
        primary.tags = list(set(primary.tags + secondary.tags))

        # Update importance (take maximum)
        primary.importance = max(primary.importance, secondary.importance)

        # Update access count
        primary.access_count += secondary.access_count

        # Mark as consolidated
        primary.status = EpisodeStatus.CONSOLIDATED

        # Update in database
        await self._update_episode_in_db(primary)

        # Delete secondary episode
        await self.delete_episode(secondary.id)

    def _update_indexes(self, episode: EpisodicMemory):
        """Update memory indexes."""
        # Temporal index
        date_key = episode.timestamp.strftime("%Y-%m-%d")
        if date_key not in self.temporal_index:
            self.temporal_index[date_key] = []
        self.temporal_index[date_key].append(episode.id)

        # Type index
        if episode.episode_type not in self.type_index:
            self.type_index[episode.episode_type] = []
        self.type_index[episode.episode_type].append(episode.id)

        # Tag index
        for tag in episode.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = []
            self.tag_index[tag].append(episode.id)

    def _remove_from_indexes(self, episode_id: str):
        """Remove episode from indexes."""
        # Remove from temporal index
        for date_episodes in self.temporal_index.values():
            if episode_id in date_episodes:
                date_episodes.remove(episode_id)

        # Remove from type index
        for type_episodes in self.type_index.values():
            if episode_id in type_episodes:
                type_episodes.remove(episode_id)

        # Remove from tag index
        for tag_episodes in self.tag_index.values():
            if episode_id in tag_episodes:
                tag_episodes.remove(episode_id)

    async def _store_episode_in_db(self, episode: EpisodicMemory):
        """Store episode in database."""
        try:
            self.connection.execute("""
                INSERT OR REPLACE INTO episodes (
                    id, episode_type, title, description, content, context,
                    timestamp, duration, importance, vividness, accuracy,
                    access_count, last_accessed, status, related_episodes,
                    causal_links, tags, organization, department, user_id, agent_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                episode.id,
                episode.episode_type.value,
                episode.title,
                episode.description,
                json.dumps(episode.content),
                json.dumps(asdict(episode.context)),
                episode.timestamp.isoformat(),
                episode.duration.total_seconds() if episode.duration else None,
                episode.importance,
                episode.vividness,
                episode.accuracy,
                episode.access_count,
                episode.last_accessed.isoformat() if episode.last_accessed else None,
                episode.status.value,
                json.dumps(episode.related_episodes),
                json.dumps(episode.causal_links),
                json.dumps(episode.tags),
                episode.organization,
                episode.department,
                episode.user_id,
                episode.agent_id
            ))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to store episode in database: {e}")

    async def _update_episode_in_db(self, episode: EpisodicMemory):
        """Update episode in database."""
        try:
            self.connection.execute("""
                UPDATE episodes SET
                    access_count = ?, last_accessed = ?, status = ?,
                    importance = ?, vividness = ?, accuracy = ?
                WHERE id = ?
            """, (
                episode.access_count,
                episode.last_accessed.isoformat() if episode.last_accessed else None,
                episode.status.value,
                episode.importance,
                episode.vividness,
                episode.accuracy,
                episode.id
            ))
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to update episode in database: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """Get episodic memory statistics."""
        stats = {
            "total_episodes": self.total_episodes,
            "active_episodes": len([e for e in self.episodes.values() if e.status == EpisodeStatus.ACTIVE]),
            "consolidated_episodes": len([e for e in self.episodes.values() if e.status == EpisodeStatus.CONSOLIDATED]),
            "successful_retrievals": self.successful_retrievals,
            "failed_retrievals": self.failed_retrievals,
            "episodes_by_type": {},
            "episodes_by_organization": {},
            "most_accessed_episodes": []
        }

        # Episodes by type
        for episode_type in EpisodeType:
            count = len([e for e in self.episodes.values() if e.episode_type == episode_type])
            stats["episodes_by_type"][episode_type.value] = count

        # Episodes by organization
        orgs = {}
        for episode in self.episodes.values():
            if episode.organization:
                orgs[episode.organization] = orgs.get(episode.organization, 0) + 1
        stats["episodes_by_organization"] = orgs

        # Most accessed episodes
        sorted_episodes = sorted(self.episodes.values(), key=lambda e: e.access_count, reverse=True)
        stats["most_accessed_episodes"] = [
            {"id": e.id, "title": e.title, "access_count": e.access_count}
            for e in sorted_episodes[:10]
        ]

        return stats

    def close(self):
        """Close the episodic memory system."""
        if self.connection:
            self.connection.close()
            self.connection = None
        logger.info("Episodic Memory System closed")
