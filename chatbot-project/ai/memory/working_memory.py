"""
Working Memory System for CHaBot.
Manages short-term memory for active reasoning and problem-solving tasks.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import uuid
from collections import deque, defaultdict
import threading

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkingMemoryType(Enum):
    """Types of working memory items."""
    CURRENT_GOAL = "current_goal"
    SUBGOAL = "subgoal"
    CONTEXT = "context"
    INTERMEDIATE_RESULT = "intermediate_result"
    REASONING_STEP = "reasoning_step"
    HYPOTHESIS = "hypothesis"
    CONSTRAINT = "constraint"
    RESOURCE = "resource"
    ATTENTION_FOCUS = "attention_focus"

class Priority(Enum):
    """Priority levels for working memory items."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class WorkingMemoryItem:
    """Represents an item in working memory."""
    id: str
    item_type: WorkingMemoryType
    content: Dict[str, Any]
    priority: Priority = Priority.MEDIUM
    
    # Temporal attributes
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    
    # Usage tracking
    access_count: int = 0
    activation_level: float = 1.0  # Decreases over time
    
    # Context
    task_id: Optional[str] = None
    agent_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # Relationships
    depends_on: List[str] = field(default_factory=list)
    supports: List[str] = field(default_factory=list)
    conflicts_with: List[str] = field(default_factory=list)
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    confidence: float = 0.8
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "item_type": self.item_type.value,
            "content": self.content,
            "priority": self.priority.value,
            "created_at": self.created_at.isoformat(),
            "last_accessed": self.last_accessed.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "access_count": self.access_count,
            "activation_level": self.activation_level,
            "task_id": self.task_id,
            "agent_id": self.agent_id,
            "session_id": self.session_id,
            "depends_on": self.depends_on,
            "supports": self.supports,
            "conflicts_with": self.conflicts_with,
            "tags": self.tags,
            "confidence": self.confidence
        }

@dataclass
class ReasoningContext:
    """Context for a reasoning session."""
    session_id: str
    goal: str
    current_step: int = 0
    max_steps: int = 100
    start_time: datetime = field(default_factory=datetime.now)
    timeout: timedelta = field(default=timedelta(minutes=30))
    
    # State tracking
    active_hypotheses: List[str] = field(default_factory=list)
    completed_steps: List[str] = field(default_factory=list)
    failed_attempts: List[str] = field(default_factory=list)
    
    # Resources
    available_resources: Dict[str, Any] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)

class WorkingMemorySystem:
    """
    Working Memory System for managing short-term memory during reasoning.
    """
    
    def __init__(self, 
                 capacity: int = 100,
                 default_ttl: timedelta = timedelta(hours=1),
                 decay_rate: float = 0.1):
        """
        Initialize the working memory system.
        
        Args:
            capacity: Maximum number of items in working memory
            default_ttl: Default time-to-live for items
            decay_rate: Rate at which activation levels decay
        """
        self.capacity = capacity
        self.default_ttl = default_ttl
        self.decay_rate = decay_rate
        
        # Memory stores
        self.items: Dict[str, WorkingMemoryItem] = {}
        self.reasoning_contexts: Dict[str, ReasoningContext] = {}
        
        # Indexes for fast access
        self.type_index: Dict[WorkingMemoryType, Set[str]] = defaultdict(set)
        self.priority_index: Dict[Priority, Set[str]] = defaultdict(set)
        self.task_index: Dict[str, Set[str]] = defaultdict(set)
        self.agent_index: Dict[str, Set[str]] = defaultdict(set)
        self.session_index: Dict[str, Set[str]] = defaultdict(set)
        
        # Access patterns
        self.access_history: deque = deque(maxlen=1000)
        self.activation_updates: Dict[str, datetime] = {}
        
        # Statistics
        self.total_items_created = 0
        self.total_items_expired = 0
        self.total_accesses = 0
        
        # Background maintenance
        self._maintenance_lock = threading.Lock()
        self._last_maintenance = datetime.now()
        
        logger.info("Working Memory System initialized")
    
    async def add_item(self,
                      item_type: WorkingMemoryType,
                      content: Dict[str, Any],
                      priority: Priority = Priority.MEDIUM,
                      ttl: Optional[timedelta] = None,
                      task_id: Optional[str] = None,
                      agent_id: Optional[str] = None,
                      session_id: Optional[str] = None,
                      tags: Optional[List[str]] = None,
                      depends_on: Optional[List[str]] = None) -> str:
        """
        Add an item to working memory.
        
        Args:
            item_type: Type of working memory item
            content: Content of the item
            priority: Priority level
            ttl: Time-to-live (uses default if None)
            task_id: Associated task ID
            agent_id: Associated agent ID
            session_id: Associated session ID
            tags: Tags for categorization
            depends_on: List of item IDs this depends on
            
        Returns:
            Item ID
        """
        # Check capacity and make room if needed
        await self._ensure_capacity()
        
        item_id = str(uuid.uuid4())
        
        # Calculate expiration time
        expires_at = None
        if ttl or self.default_ttl:
            expires_at = datetime.now() + (ttl or self.default_ttl)
        
        item = WorkingMemoryItem(
            id=item_id,
            item_type=item_type,
            content=content,
            priority=priority,
            expires_at=expires_at,
            task_id=task_id,
            agent_id=agent_id,
            session_id=session_id,
            tags=tags or [],
            depends_on=depends_on or []
        )
        
        # Store item
        self.items[item_id] = item
        
        # Update indexes
        self._update_indexes(item)
        
        # Track creation
        self.total_items_created += 1
        
        logger.debug(f"Added working memory item: {item_type.value} (ID: {item_id})")
        return item_id
    
    async def get_item(self, item_id: str) -> Optional[WorkingMemoryItem]:
        """Get an item from working memory."""
        if item_id not in self.items:
            return None
        
        item = self.items[item_id]
        
        # Check if expired
        if item.expires_at and datetime.now() > item.expires_at:
            await self.remove_item(item_id)
            return None
        
        # Update access statistics
        item.access_count += 1
        item.last_accessed = datetime.now()
        item.activation_level = min(1.0, item.activation_level + 0.1)  # Boost activation
        
        # Track access
        self.access_history.append((item_id, datetime.now()))
        self.total_accesses += 1
        
        return item
    
    async def find_items(self,
                        item_type: Optional[WorkingMemoryType] = None,
                        priority: Optional[Priority] = None,
                        task_id: Optional[str] = None,
                        agent_id: Optional[str] = None,
                        session_id: Optional[str] = None,
                        tags: Optional[List[str]] = None,
                        active_only: bool = True,
                        limit: int = 50) -> List[WorkingMemoryItem]:
        """
        Find items in working memory based on criteria.
        
        Args:
            item_type: Filter by item type
            priority: Filter by priority
            task_id: Filter by task ID
            agent_id: Filter by agent ID
            session_id: Filter by session ID
            tags: Filter by tags
            active_only: Only return non-expired items
            limit: Maximum number of results
            
        Returns:
            List of matching items
        """
        candidates = set(self.items.keys())
        
        # Apply filters using indexes
        if item_type:
            candidates &= self.type_index[item_type]
        
        if priority:
            candidates &= self.priority_index[priority]
        
        if task_id:
            candidates &= self.task_index[task_id]
        
        if agent_id:
            candidates &= self.agent_index[agent_id]
        
        if session_id:
            candidates &= self.session_index[session_id]
        
        # Get items and apply additional filters
        results = []
        current_time = datetime.now()
        
        for item_id in candidates:
            if item_id not in self.items:
                continue
            
            item = self.items[item_id]
            
            # Check expiration
            if active_only and item.expires_at and current_time > item.expires_at:
                continue
            
            # Check tags
            if tags and not any(tag in item.tags for tag in tags):
                continue
            
            results.append(item)
        
        # Sort by activation level and priority
        results.sort(key=lambda x: (
            x.priority == Priority.CRITICAL,
            x.priority == Priority.HIGH,
            x.activation_level
        ), reverse=True)
        
        return results[:limit]
    
    async def update_item(self, item_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing item."""
        if item_id not in self.items:
            return False
        
        item = self.items[item_id]
        
        # Update fields
        for key, value in updates.items():
            if hasattr(item, key):
                setattr(item, key, value)
        
        item.last_accessed = datetime.now()
        
        # Update indexes
        self._update_indexes(item)
        
        logger.debug(f"Updated working memory item: {item_id}")
        return True
    
    async def remove_item(self, item_id: str) -> bool:
        """Remove an item from working memory."""
        if item_id not in self.items:
            return False
        
        item = self.items[item_id]
        
        # Remove from indexes
        self._remove_from_indexes(item)
        
        # Remove item
        del self.items[item_id]
        
        logger.debug(f"Removed working memory item: {item_id}")
        return True
    
    async def start_reasoning_session(self,
                                    goal: str,
                                    max_steps: int = 100,
                                    timeout: timedelta = timedelta(minutes=30),
                                    resources: Optional[Dict[str, Any]] = None,
                                    constraints: Optional[List[str]] = None) -> str:
        """
        Start a new reasoning session.
        
        Args:
            goal: The goal to achieve
            max_steps: Maximum reasoning steps
            timeout: Session timeout
            resources: Available resources
            constraints: Constraints to consider
            
        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())
        
        context = ReasoningContext(
            session_id=session_id,
            goal=goal,
            max_steps=max_steps,
            timeout=timeout,
            available_resources=resources or {},
            constraints=constraints or []
        )
        
        self.reasoning_contexts[session_id] = context
        
        # Add initial goal to working memory
        await self.add_item(
            WorkingMemoryType.CURRENT_GOAL,
            {"goal": goal, "status": "active"},
            Priority.CRITICAL,
            session_id=session_id,
            tags=["reasoning", "goal"]
        )
        
        logger.info(f"Started reasoning session: {goal} (ID: {session_id})")
        return session_id
    
    async def add_reasoning_step(self,
                               session_id: str,
                               step_type: str,
                               content: Dict[str, Any],
                               confidence: float = 0.8) -> str:
        """Add a reasoning step to a session."""
        if session_id not in self.reasoning_contexts:
            raise ValueError(f"Reasoning session {session_id} not found")
        
        context = self.reasoning_contexts[session_id]
        context.current_step += 1
        
        step_id = await self.add_item(
            WorkingMemoryType.REASONING_STEP,
            {
                "step_number": context.current_step,
                "step_type": step_type,
                "content": content,
                "confidence": confidence
            },
            Priority.HIGH,
            session_id=session_id,
            tags=["reasoning", "step", step_type]
        )
        
        context.completed_steps.append(step_id)

        return step_id

    async def end_reasoning_session(self, session_id: str) -> Dict[str, Any]:
        """End a reasoning session and return summary."""
        if session_id not in self.reasoning_contexts:
            return {}

        context = self.reasoning_contexts[session_id]

        # Get all items from this session
        session_items = await self.find_items(session_id=session_id, active_only=False)

        # Create summary
        summary = {
            "session_id": session_id,
            "goal": context.goal,
            "duration": datetime.now() - context.start_time,
            "steps_completed": context.current_step,
            "max_steps": context.max_steps,
            "items_created": len(session_items),
            "success": context.current_step < context.max_steps,
            "final_state": {}
        }

        # Categorize items by type
        for item in session_items:
            item_type = item.item_type.value
            if item_type not in summary["final_state"]:
                summary["final_state"][item_type] = []
            summary["final_state"][item_type].append(item.to_dict())

        # Clean up session items
        for item in session_items:
            await self.remove_item(item.id)

        # Remove context
        del self.reasoning_contexts[session_id]

        logger.info(f"Ended reasoning session: {session_id}")
        return summary

    async def get_attention_focus(self, session_id: Optional[str] = None) -> List[WorkingMemoryItem]:
        """Get items that should be in the attention focus."""
        # Get high-priority and recently accessed items
        focus_items = []

        # Critical and high priority items
        critical_items = await self.find_items(priority=Priority.CRITICAL, limit=5)
        high_items = await self.find_items(priority=Priority.HIGH, limit=10)

        focus_items.extend(critical_items)
        focus_items.extend(high_items)

        # Recently accessed items
        recent_accesses = list(self.access_history)[-20:]  # Last 20 accesses
        recent_item_ids = [access[0] for access in recent_accesses]

        for item_id in recent_item_ids:
            if item_id in self.items:
                item = self.items[item_id]
                if item not in focus_items:
                    focus_items.append(item)

        # Session-specific items if provided
        if session_id:
            session_items = await self.find_items(session_id=session_id, limit=10)
            for item in session_items:
                if item not in focus_items:
                    focus_items.append(item)

        # Sort by activation level
        focus_items.sort(key=lambda x: x.activation_level, reverse=True)

        return focus_items[:15]  # Limit attention focus

    async def decay_activation(self):
        """Decay activation levels of all items."""
        current_time = datetime.now()

        for item in self.items.values():
            # Calculate time since last access
            time_since_access = current_time - item.last_accessed
            hours_since_access = time_since_access.total_seconds() / 3600

            # Apply decay
            decay_factor = max(0.1, 1.0 - (self.decay_rate * hours_since_access))
            item.activation_level *= decay_factor

            # Update tracking
            self.activation_updates[item.id] = current_time

    async def cleanup_expired_items(self):
        """Remove expired items from working memory."""
        current_time = datetime.now()
        expired_items = []

        for item_id, item in self.items.items():
            if item.expires_at and current_time > item.expires_at:
                expired_items.append(item_id)

        for item_id in expired_items:
            await self.remove_item(item_id)
            self.total_items_expired += 1

        if expired_items:
            logger.info(f"Cleaned up {len(expired_items)} expired items")

    async def _ensure_capacity(self):
        """Ensure working memory doesn't exceed capacity."""
        if len(self.items) < self.capacity:
            return

        # Remove items with lowest activation levels
        items_by_activation = sorted(
            self.items.values(),
            key=lambda x: (x.priority != Priority.CRITICAL, x.activation_level)
        )

        items_to_remove = len(self.items) - self.capacity + 1

        for item in items_by_activation[:items_to_remove]:
            await self.remove_item(item.id)
            logger.debug(f"Removed item due to capacity: {item.id}")

    def _update_indexes(self, item: WorkingMemoryItem):
        """Update indexes for an item."""
        # Type index
        self.type_index[item.item_type].add(item.id)

        # Priority index
        self.priority_index[item.priority].add(item.id)

        # Context indexes
        if item.task_id:
            self.task_index[item.task_id].add(item.id)

        if item.agent_id:
            self.agent_index[item.agent_id].add(item.id)

        if item.session_id:
            self.session_index[item.session_id].add(item.id)

    def _remove_from_indexes(self, item: WorkingMemoryItem):
        """Remove item from indexes."""
        # Type index
        self.type_index[item.item_type].discard(item.id)

        # Priority index
        self.priority_index[item.priority].discard(item.id)

        # Context indexes
        if item.task_id:
            self.task_index[item.task_id].discard(item.id)

        if item.agent_id:
            self.agent_index[item.agent_id].discard(item.id)

        if item.session_id:
            self.session_index[item.session_id].discard(item.id)

    async def maintenance(self):
        """Perform background maintenance tasks."""
        with self._maintenance_lock:
            current_time = datetime.now()

            # Only run maintenance every 5 minutes
            if current_time - self._last_maintenance < timedelta(minutes=5):
                return

            await self.decay_activation()
            await self.cleanup_expired_items()

            self._last_maintenance = current_time
            logger.debug("Working memory maintenance completed")

    def get_statistics(self) -> Dict[str, Any]:
        """Get working memory statistics."""
        current_time = datetime.now()

        # Calculate average activation
        avg_activation = sum(item.activation_level for item in self.items.values()) / max(len(self.items), 1)

        # Items by type
        items_by_type = {}
        for item_type in WorkingMemoryType:
            count = len(self.type_index[item_type])
            items_by_type[item_type.value] = count

        # Items by priority
        items_by_priority = {}
        for priority in Priority:
            count = len(self.priority_index[priority])
            items_by_priority[priority.value] = count

        # Active sessions
        active_sessions = len(self.reasoning_contexts)

        return {
            "current_items": len(self.items),
            "capacity": self.capacity,
            "utilization": len(self.items) / self.capacity,
            "total_items_created": self.total_items_created,
            "total_items_expired": self.total_items_expired,
            "total_accesses": self.total_accesses,
            "average_activation": avg_activation,
            "items_by_type": items_by_type,
            "items_by_priority": items_by_priority,
            "active_sessions": active_sessions,
            "recent_accesses": len(self.access_history)
        }

    def close(self):
        """Close the working memory system."""
        # Clear all data
        self.items.clear()
        self.reasoning_contexts.clear()

        # Clear indexes
        self.type_index.clear()
        self.priority_index.clear()
        self.task_index.clear()
        self.agent_index.clear()
        self.session_index.clear()

        logger.info("Working Memory System closed")
