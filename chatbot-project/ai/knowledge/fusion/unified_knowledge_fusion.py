"""
Unified Knowledge Fusion System - Main Integration Module.
Provides a unified interface for all knowledge fusion components.
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime
import json
import logging
from collections import defaultdict

# Import all knowledge fusion components
from .multi_source_fusion import (
    MultiSourceFusion, KnowledgeItem, SourceType, SourceReliability, 
    FusionMethod, FusedKnowledgeResult
)
from .vector_graph_integration import (
    VectorGraphIntegration, SearchMode, IntegratedResult
)
from .conflict_resolution import (
    ConflictResolution, ConflictType, ResolutionStrategy, ConflictInstance, ResolutionResult
)
from .cross_document_coreference import (
    CrossDocumentCoreference, EntityType, EntityMention, EntityCluster
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProcessingMode(Enum):
    """Different processing modes for knowledge fusion."""
    FAST = "fast"
    BALANCED = "balanced"
    COMPREHENSIVE = "comprehensive"
    CUSTOM = "custom"

@dataclass
class FusionConfiguration:
    """Configuration for the unified knowledge fusion system."""
    processing_mode: ProcessingMode = ProcessingMode.BALANCED
    
    # Multi-source fusion settings
    fusion_method: FusionMethod = FusionMethod.ENSEMBLE
    source_weights: Dict[str, float] = field(default_factory=dict)
    
    # Vector-graph integration settings
    search_mode: SearchMode = SearchMode.HYBRID
    max_results: int = 20
    similarity_threshold: float = 0.7
    
    # Conflict resolution settings
    conflict_types: List[ConflictType] = field(default_factory=lambda: [
        ConflictType.FACTUAL, ConflictType.NUMERICAL, ConflictType.TEMPORAL
    ])
    resolution_strategy: ResolutionStrategy = ResolutionStrategy.HYBRID
    
    # Coreference resolution settings
    enable_coreference: bool = True
    clustering_threshold: float = 0.8
    entity_types: List[EntityType] = field(default_factory=lambda: [
        EntityType.PERSON, EntityType.ORGANIZATION, EntityType.LOCATION
    ])
    
    # Performance settings
    enable_parallel_processing: bool = True
    max_concurrent_tasks: int = 5
    timeout_seconds: int = 30

@dataclass
class UnifiedFusionResult:
    """Result from the unified knowledge fusion process."""
    query: str
    fused_content: str
    confidence: float
    
    # Component results
    search_results: List[IntegratedResult] = field(default_factory=list)
    entity_clusters: List[EntityCluster] = field(default_factory=list)
    conflicts_resolved: List[ResolutionResult] = field(default_factory=list)
    fusion_result: Optional[FusedKnowledgeResult] = None
    
    # Processing metadata
    processing_time: float = 0.0
    components_used: List[str] = field(default_factory=list)
    reasoning_trace: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "query": self.query,
            "fused_content": self.fused_content,
            "confidence": self.confidence,
            "search_results_count": len(self.search_results),
            "entity_clusters_count": len(self.entity_clusters),
            "conflicts_resolved_count": len(self.conflicts_resolved),
            "processing_time": self.processing_time,
            "components_used": self.components_used,
            "reasoning_trace": self.reasoning_trace,
            "metadata": self.metadata
        }

class UnifiedKnowledgeFusion:
    """
    Unified Knowledge Fusion System that integrates all components.
    Provides a single interface for comprehensive knowledge processing.
    """
    
    def __init__(self, 
                 config: Optional[FusionConfiguration] = None,
                 vector_client=None,
                 graph_client=None):
        """Initialize the unified knowledge fusion system."""
        self.config = config or FusionConfiguration()
        
        # Initialize all components
        self.multi_source_fusion = MultiSourceFusion(self.config.source_weights)
        self.vector_graph_integration = VectorGraphIntegration(
            vector_client, graph_client
        )
        self.conflict_resolution = ConflictResolution()
        self.cross_document_coreference = CrossDocumentCoreference(
            similarity_threshold=self.config.similarity_threshold,
            clustering_threshold=self.config.clustering_threshold
        )
        
        # Processing history
        self.processing_history: List[UnifiedFusionResult] = []
        
        # Performance metrics
        self.metrics = {
            "total_queries_processed": 0,
            "average_processing_time": 0.0,
            "average_confidence": 0.0,
            "component_usage": defaultdict(int)
        }
        
        logger.info("Unified Knowledge Fusion System initialized")
    
    async def process_query(self, 
                          query: str,
                          documents: Optional[List[Dict[str, Any]]] = None,
                          context: Optional[Dict[str, Any]] = None) -> UnifiedFusionResult:
        """
        Process a query through the complete knowledge fusion pipeline.
        
        Args:
            query: The query to process
            documents: Optional documents to process for entity extraction
            context: Optional context for processing
            
        Returns:
            UnifiedFusionResult with comprehensive processing results
        """
        start_time = datetime.now()
        reasoning_trace = []
        components_used = []
        
        logger.info(f"Processing query: {query}")
        reasoning_trace.append(f"Starting unified knowledge fusion for query: {query}")
        
        try:
            # Step 1: Extract entities from documents (if provided)
            entity_clusters = []
            if documents and self.config.enable_coreference:
                logger.info("Step 1: Extracting and clustering entities...")
                reasoning_trace.append("Extracting entities from provided documents")
                components_used.append("cross_document_coreference")
                
                # Extract entity mentions
                mentions = self.cross_document_coreference.extract_entity_mentions(documents)
                
                # Resolve coreferences and cluster entities
                if mentions:
                    links = self.cross_document_coreference.resolve_coreferences(mentions)
                    entity_clusters = self.cross_document_coreference.cluster_entities(links)
                    
                    reasoning_trace.append(f"Extracted {len(mentions)} mentions, "
                                         f"resolved {len(links)} links, "
                                         f"created {len(entity_clusters)} clusters")
            
            # Step 2: Perform integrated search
            logger.info("Step 2: Performing integrated vector-graph search...")
            reasoning_trace.append("Performing integrated search across vector and graph sources")
            components_used.append("vector_graph_integration")
            
            search_results = await self.vector_graph_integration.integrated_search(
                query=query,
                mode=self.config.search_mode,
                k=self.config.max_results,
                filters=context
            )
            
            reasoning_trace.append(f"Found {len(search_results)} integrated search results")
            
            # Step 3: Convert search results to knowledge items
            logger.info("Step 3: Converting search results to knowledge items...")
            knowledge_items = []
            
            for i, result in enumerate(search_results):
                # Determine source type based on result
                if result.source_type == "vector":
                    source_type = SourceType.VECTOR_SEARCH
                elif result.source_type == "graph":
                    source_type = SourceType.GRAPH_SEARCH
                else:
                    source_type = SourceType.VECTOR_SEARCH  # Default
                
                knowledge_item = KnowledgeItem(
                    content=result.content,
                    source_id=f"search_result_{i+1}",
                    source_type=source_type,
                    confidence=result.combined_score,
                    reliability=SourceReliability.MEDIUM,
                    metadata={
                        "vector_score": result.vector_score,
                        "graph_score": result.graph_score,
                        "relationships": len(result.relationships)
                    }
                )
                knowledge_items.append(knowledge_item)
            
            reasoning_trace.append(f"Created {len(knowledge_items)} knowledge items from search results")
            
            # Step 4: Detect and resolve conflicts
            logger.info("Step 4: Detecting and resolving conflicts...")
            reasoning_trace.append("Detecting conflicts between knowledge sources")
            components_used.append("conflict_resolution")
            
            conflicts = self.conflict_resolution.detect_conflicts(
                knowledge_items,
                conflict_types=self.config.conflict_types
            )
            
            conflicts_resolved = []
            for conflict in conflicts:
                resolution = self.conflict_resolution.resolve_conflict(
                    conflict,
                    strategy=self.config.resolution_strategy,
                    context=context or {}
                )
                conflicts_resolved.append(resolution)
            
            reasoning_trace.append(f"Detected {len(conflicts)} conflicts, resolved {len(conflicts_resolved)}")
            
            # Step 5: Fuse knowledge from all sources
            logger.info("Step 5: Fusing knowledge from all sources...")
            reasoning_trace.append("Fusing knowledge using multi-source fusion")
            components_used.append("multi_source_fusion")
            
            # Add resolved content from conflicts to knowledge items
            for resolution in conflicts_resolved:
                if resolution.resolved_content and resolution.confidence > 0.5:
                    resolved_item = KnowledgeItem(
                        content=resolution.resolved_content,
                        source_id="conflict_resolution",
                        source_type=SourceType.AGENT_REASONING,
                        confidence=resolution.confidence,
                        reliability=SourceReliability.HIGH,
                        metadata={"resolution_strategy": resolution.resolution_strategy.value}
                    )
                    knowledge_items.append(resolved_item)
            
            # Perform final fusion
            fusion_result = self.multi_source_fusion.fuse_knowledge(
                knowledge_items,
                method=self.config.fusion_method
            )
            
            reasoning_trace.append(f"Final fusion completed with confidence: {fusion_result.confidence:.3f}")
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create unified result
            unified_result = UnifiedFusionResult(
                query=query,
                fused_content=fusion_result.content,
                confidence=fusion_result.confidence,
                search_results=search_results,
                entity_clusters=entity_clusters,
                conflicts_resolved=conflicts_resolved,
                fusion_result=fusion_result,
                processing_time=processing_time,
                components_used=components_used,
                reasoning_trace=reasoning_trace,
                metadata={
                    "processing_mode": self.config.processing_mode.value,
                    "search_mode": self.config.search_mode.value,
                    "fusion_method": self.config.fusion_method.value,
                    "resolution_strategy": self.config.resolution_strategy.value
                }
            )
            
            # Update metrics
            self._update_metrics(unified_result)
            
            # Store in history
            self.processing_history.append(unified_result)
            
            logger.info(f"Query processing completed in {processing_time:.2f}s with confidence {fusion_result.confidence:.3f}")
            
            return unified_result
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            
            # Return error result
            processing_time = (datetime.now() - start_time).total_seconds()
            return UnifiedFusionResult(
                query=query,
                fused_content=f"Error processing query: {str(e)}",
                confidence=0.0,
                processing_time=processing_time,
                components_used=components_used,
                reasoning_trace=reasoning_trace + [f"Error occurred: {str(e)}"],
                metadata={"error": str(e)}
            )

    async def batch_process_queries(self,
                                  queries: List[str],
                                  documents: Optional[List[Dict[str, Any]]] = None,
                                  context: Optional[Dict[str, Any]] = None) -> List[UnifiedFusionResult]:
        """
        Process multiple queries in batch with optional parallel processing.

        Args:
            queries: List of queries to process
            documents: Optional documents for entity extraction
            context: Optional context for processing

        Returns:
            List of UnifiedFusionResult objects
        """
        logger.info(f"Processing {len(queries)} queries in batch")

        if self.config.enable_parallel_processing:
            # Process queries in parallel with concurrency limit
            semaphore = asyncio.Semaphore(self.config.max_concurrent_tasks)

            async def process_with_semaphore(query):
                async with semaphore:
                    return await self.process_query(query, documents, context)

            tasks = [process_with_semaphore(query) for query in queries]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle any exceptions
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing query {i}: {result}")
                    error_result = UnifiedFusionResult(
                        query=queries[i],
                        fused_content=f"Error: {str(result)}",
                        confidence=0.0,
                        reasoning_trace=[f"Batch processing error: {str(result)}"],
                        metadata={"batch_error": str(result)}
                    )
                    processed_results.append(error_result)
                else:
                    processed_results.append(result)

            return processed_results
        else:
            # Process queries sequentially
            results = []
            for query in queries:
                result = await self.process_query(query, documents, context)
                results.append(result)

            return results

    def update_configuration(self, new_config: FusionConfiguration):
        """Update the system configuration."""
        self.config = new_config

        # Update component configurations
        if new_config.source_weights:
            self.multi_source_fusion.update_source_weights(new_config.source_weights)

        self.vector_graph_integration.update_config({
            "max_vector_results": new_config.max_results * 2,
            "similarity_threshold": new_config.similarity_threshold
        })

        logger.info("Configuration updated successfully")

    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics."""
        if not self.processing_history:
            return {"total_queries": 0}

        # Calculate statistics
        total_queries = len(self.processing_history)
        total_time = sum(result.processing_time for result in self.processing_history)
        total_confidence = sum(result.confidence for result in self.processing_history)

        # Component usage statistics
        component_usage = defaultdict(int)
        for result in self.processing_history:
            for component in result.components_used:
                component_usage[component] += 1

        # Confidence distribution
        confidence_ranges = {
            "high (>0.8)": 0,
            "medium (0.5-0.8)": 0,
            "low (<0.5)": 0
        }

        for result in self.processing_history:
            if result.confidence > 0.8:
                confidence_ranges["high (>0.8)"] += 1
            elif result.confidence >= 0.5:
                confidence_ranges["medium (0.5-0.8)"] += 1
            else:
                confidence_ranges["low (<0.5)"] += 1

        return {
            "total_queries_processed": total_queries,
            "average_processing_time": total_time / total_queries,
            "average_confidence": total_confidence / total_queries,
            "component_usage": dict(component_usage),
            "confidence_distribution": confidence_ranges,
            "processing_modes_used": list(set(
                result.metadata.get("processing_mode", "unknown")
                for result in self.processing_history
            ))
        }

    def get_recent_results(self, limit: int = 10) -> List[UnifiedFusionResult]:
        """Get the most recent processing results."""
        return self.processing_history[-limit:] if self.processing_history else []

    def export_processing_history(self) -> Dict[str, Any]:
        """Export complete processing history for analysis."""
        return {
            "total_results": len(self.processing_history),
            "results": [result.to_dict() for result in self.processing_history],
            "statistics": self.get_processing_statistics(),
            "configuration": {
                "processing_mode": self.config.processing_mode.value,
                "fusion_method": self.config.fusion_method.value,
                "search_mode": self.config.search_mode.value,
                "resolution_strategy": self.config.resolution_strategy.value
            }
        }

    def clear_history(self):
        """Clear processing history to free memory."""
        self.processing_history.clear()
        logger.info("Processing history cleared")

    def _update_metrics(self, result: UnifiedFusionResult):
        """Update internal metrics based on processing result."""
        self.metrics["total_queries_processed"] += 1

        # Update average processing time
        total_time = (self.metrics["average_processing_time"] *
                     (self.metrics["total_queries_processed"] - 1) +
                     result.processing_time)
        self.metrics["average_processing_time"] = total_time / self.metrics["total_queries_processed"]

        # Update average confidence
        total_confidence = (self.metrics["average_confidence"] *
                           (self.metrics["total_queries_processed"] - 1) +
                           result.confidence)
        self.metrics["average_confidence"] = total_confidence / self.metrics["total_queries_processed"]

        # Update component usage
        for component in result.components_used:
            self.metrics["component_usage"][component] += 1

    # Convenience methods for specific processing modes

    async def fast_process(self, query: str, context: Optional[Dict[str, Any]] = None) -> UnifiedFusionResult:
        """Process query in fast mode with minimal components."""
        fast_config = FusionConfiguration(
            processing_mode=ProcessingMode.FAST,
            fusion_method=FusionMethod.WEIGHTED_AVERAGE,
            search_mode=SearchMode.VECTOR_FIRST,
            conflict_types=[ConflictType.FACTUAL],
            enable_coreference=False,
            max_results=10
        )

        original_config = self.config
        self.update_configuration(fast_config)

        try:
            result = await self.process_query(query, context=context)
            return result
        finally:
            self.update_configuration(original_config)

    async def comprehensive_process(self,
                                  query: str,
                                  documents: Optional[List[Dict[str, Any]]] = None,
                                  context: Optional[Dict[str, Any]] = None) -> UnifiedFusionResult:
        """Process query in comprehensive mode with all components."""
        comprehensive_config = FusionConfiguration(
            processing_mode=ProcessingMode.COMPREHENSIVE,
            fusion_method=FusionMethod.ENSEMBLE,
            search_mode=SearchMode.HYBRID,
            conflict_types=list(ConflictType),
            resolution_strategy=ResolutionStrategy.HYBRID,
            enable_coreference=True,
            max_results=50
        )

        original_config = self.config
        self.update_configuration(comprehensive_config)

        try:
            result = await self.process_query(query, documents, context)
            return result
        finally:
            self.update_configuration(original_config)

# Factory functions for common configurations

def create_fast_fusion_system(vector_client=None, graph_client=None) -> UnifiedKnowledgeFusion:
    """Create a knowledge fusion system optimized for speed."""
    config = FusionConfiguration(
        processing_mode=ProcessingMode.FAST,
        fusion_method=FusionMethod.WEIGHTED_AVERAGE,
        search_mode=SearchMode.VECTOR_FIRST,
        enable_coreference=False,
        max_results=10,
        enable_parallel_processing=True
    )
    return UnifiedKnowledgeFusion(config, vector_client, graph_client)

def create_balanced_fusion_system(vector_client=None, graph_client=None) -> UnifiedKnowledgeFusion:
    """Create a knowledge fusion system with balanced performance and accuracy."""
    config = FusionConfiguration(
        processing_mode=ProcessingMode.BALANCED,
        fusion_method=FusionMethod.ENSEMBLE,
        search_mode=SearchMode.HYBRID,
        enable_coreference=True,
        max_results=20,
        enable_parallel_processing=True
    )
    return UnifiedKnowledgeFusion(config, vector_client, graph_client)

def create_comprehensive_fusion_system(vector_client=None, graph_client=None) -> UnifiedKnowledgeFusion:
    """Create a knowledge fusion system optimized for maximum accuracy."""
    config = FusionConfiguration(
        processing_mode=ProcessingMode.COMPREHENSIVE,
        fusion_method=FusionMethod.ENSEMBLE,
        search_mode=SearchMode.HYBRID,
        conflict_types=list(ConflictType),
        resolution_strategy=ResolutionStrategy.HYBRID,
        enable_coreference=True,
        max_results=50,
        enable_parallel_processing=True
    )
    return UnifiedKnowledgeFusion(config, vector_client, graph_client)
