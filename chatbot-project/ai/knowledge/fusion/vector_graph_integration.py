"""
Vector + Graph Search Integration Component for the Knowledge Fusion System.
Combines vector similarity search with graph-based relationship exploration.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime
import json
import logging
from collections import defaultdict
import asyncio

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SearchMode(Enum):
    """Different modes for vector-graph integration."""
    VECTOR_FIRST = "vector_first"
    GRAPH_FIRST = "graph_first"
    PARALLEL = "parallel"
    ITERATIVE = "iterative"
    HYBRID = "hybrid"

class RelationshipType(Enum):
    """Types of relationships in the knowledge graph."""
    SEMANTIC = "semantic"
    STRUCTURAL = "structural"
    TEMPORAL = "temporal"
    CAUSAL = "causal"
    HIERARCHICAL = "hierarchical"
    ASSOCIATIVE = "associative"

@dataclass
class VectorResult:
    """Result from vector similarity search."""
    content: str
    score: float
    document_id: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[List[float]] = None

@dataclass
class GraphResult:
    """Result from graph-based search."""
    content: str
    node_id: str
    relationships: List[Dict[str, Any]] = field(default_factory=list)
    path_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class IntegratedResult:
    """Result from integrated vector-graph search."""
    content: str
    vector_score: float
    graph_score: float
    combined_score: float
    source_type: str  # "vector", "graph", or "both"
    vector_result: Optional[VectorResult] = None
    graph_result: Optional[GraphResult] = None
    relationships: List[Dict[str, Any]] = field(default_factory=list)
    reasoning: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class VectorGraphIntegration:
    """
    Integrates vector similarity search with graph-based relationship exploration.
    Provides unified search across both vector embeddings and knowledge graphs.
    """
    
    def __init__(self, 
                 vector_search_client=None,
                 graph_search_client=None,
                 integration_weights: Optional[Dict[str, float]] = None):
        """Initialize the vector-graph integration component."""
        self.vector_client = vector_search_client
        self.graph_client = graph_search_client
        
        # Default integration weights
        self.weights = integration_weights or {
            "vector_weight": 0.6,
            "graph_weight": 0.4,
            "relationship_boost": 0.2,
            "semantic_boost": 0.15,
            "structural_boost": 0.1
        }
        
        # Search configuration
        self.config = {
            "max_vector_results": 50,
            "max_graph_results": 30,
            "similarity_threshold": 0.7,
            "relationship_depth": 2,
            "enable_expansion": True,
            "expansion_factor": 1.5
        }
        
        logger.info("Vector-Graph Integration component initialized")
    
    async def integrated_search(self,
                              query: str,
                              mode: SearchMode = SearchMode.HYBRID,
                              k: int = 10,
                              filters: Optional[Dict[str, Any]] = None) -> List[IntegratedResult]:
        """
        Perform integrated vector-graph search.
        
        Args:
            query: Search query
            mode: Integration mode to use
            k: Number of results to return
            filters: Optional filters for search
            
        Returns:
            List of integrated search results
        """
        logger.info(f"Starting integrated search with mode: {mode.value}")
        
        # Choose search strategy based on mode
        if mode == SearchMode.VECTOR_FIRST:
            return await self._vector_first_search(query, k, filters)
        elif mode == SearchMode.GRAPH_FIRST:
            return await self._graph_first_search(query, k, filters)
        elif mode == SearchMode.PARALLEL:
            return await self._parallel_search(query, k, filters)
        elif mode == SearchMode.ITERATIVE:
            return await self._iterative_search(query, k, filters)
        else:  # HYBRID
            return await self._hybrid_search(query, k, filters)
    
    async def _vector_first_search(self,
                                 query: str,
                                 k: int,
                                 filters: Optional[Dict[str, Any]]) -> List[IntegratedResult]:
        """
        Vector-first search: Start with vector search, then enhance with graph.
        """
        results = []
        reasoning = ["Using vector-first search strategy"]
        
        # Step 1: Vector search
        vector_results = await self._perform_vector_search(query, k * 2, filters)
        reasoning.append(f"Found {len(vector_results)} vector results")
        
        # Step 2: Enhance with graph relationships
        for vector_result in vector_results[:k]:
            # Find related graph nodes
            graph_enhancements = await self._find_graph_relationships(
                vector_result.content, vector_result.document_id
            )
            
            # Calculate combined score
            combined_score = self._calculate_combined_score(
                vector_result.score, 
                graph_enhancements.get("graph_score", 0.0),
                graph_enhancements.get("relationships", [])
            )
            
            # Create integrated result
            integrated_result = IntegratedResult(
                content=vector_result.content,
                vector_score=vector_result.score,
                graph_score=graph_enhancements.get("graph_score", 0.0),
                combined_score=combined_score,
                source_type="vector",
                vector_result=vector_result,
                relationships=graph_enhancements.get("relationships", []),
                reasoning=reasoning + [f"Enhanced vector result with {len(graph_enhancements.get('relationships', []))} relationships"],
                metadata=vector_result.metadata
            )
            
            results.append(integrated_result)
        
        # Sort by combined score
        results.sort(key=lambda x: x.combined_score, reverse=True)
        return results[:k]
    
    async def _graph_first_search(self,
                                query: str,
                                k: int,
                                filters: Optional[Dict[str, Any]]) -> List[IntegratedResult]:
        """
        Graph-first search: Start with graph traversal, then enhance with vector similarity.
        """
        results = []
        reasoning = ["Using graph-first search strategy"]
        
        # Step 1: Graph search
        graph_results = await self._perform_graph_search(query, k * 2, filters)
        reasoning.append(f"Found {len(graph_results)} graph results")
        
        # Step 2: Enhance with vector similarity
        for graph_result in graph_results[:k]:
            # Calculate vector similarity
            vector_score = await self._calculate_vector_similarity(query, graph_result.content)
            
            # Calculate combined score
            combined_score = self._calculate_combined_score(
                vector_score,
                graph_result.path_score,
                graph_result.relationships
            )
            
            # Create integrated result
            integrated_result = IntegratedResult(
                content=graph_result.content,
                vector_score=vector_score,
                graph_score=graph_result.path_score,
                combined_score=combined_score,
                source_type="graph",
                graph_result=graph_result,
                relationships=graph_result.relationships,
                reasoning=reasoning + [f"Enhanced graph result with vector similarity {vector_score:.3f}"],
                metadata=graph_result.metadata
            )
            
            results.append(integrated_result)
        
        # Sort by combined score
        results.sort(key=lambda x: x.combined_score, reverse=True)
        return results[:k]
    
    async def _parallel_search(self,
                             query: str,
                             k: int,
                             filters: Optional[Dict[str, Any]]) -> List[IntegratedResult]:
        """
        Parallel search: Perform vector and graph searches simultaneously.
        """
        reasoning = ["Using parallel search strategy"]
        
        # Perform both searches in parallel
        vector_task = asyncio.create_task(self._perform_vector_search(query, k, filters))
        graph_task = asyncio.create_task(self._perform_graph_search(query, k, filters))
        
        vector_results, graph_results = await asyncio.gather(vector_task, graph_task)
        
        reasoning.append(f"Found {len(vector_results)} vector and {len(graph_results)} graph results")
        
        # Combine and deduplicate results
        combined_results = await self._combine_results(vector_results, graph_results, reasoning)
        
        # Sort by combined score and return top k
        combined_results.sort(key=lambda x: x.combined_score, reverse=True)
        return combined_results[:k]
    
    async def _iterative_search(self,
                              query: str,
                              k: int,
                              filters: Optional[Dict[str, Any]]) -> List[IntegratedResult]:
        """
        Iterative search: Alternate between vector and graph searches to refine results.
        """
        results = []
        reasoning = ["Using iterative search strategy"]
        current_query = query
        
        for iteration in range(3):  # Maximum 3 iterations
            reasoning.append(f"Iteration {iteration + 1}")
            
            # Alternate between vector and graph
            if iteration % 2 == 0:
                # Vector search
                vector_results = await self._perform_vector_search(current_query, k, filters)
                
                # Extract key terms for next iteration
                if vector_results:
                    key_terms = self._extract_key_terms([r.content for r in vector_results])
                    current_query = f"{query} {' '.join(key_terms[:5])}"
                    
                    # Convert to integrated results
                    for vr in vector_results:
                        integrated_result = IntegratedResult(
                            content=vr.content,
                            vector_score=vr.score,
                            graph_score=0.0,
                            combined_score=vr.score * self.weights["vector_weight"],
                            source_type="vector",
                            vector_result=vr,
                            reasoning=reasoning + [f"Vector result from iteration {iteration + 1}"],
                            metadata=vr.metadata
                        )
                        results.append(integrated_result)
            else:
                # Graph search
                graph_results = await self._perform_graph_search(current_query, k, filters)
                
                # Convert to integrated results
                for gr in graph_results:
                    integrated_result = IntegratedResult(
                        content=gr.content,
                        vector_score=0.0,
                        graph_score=gr.path_score,
                        combined_score=gr.path_score * self.weights["graph_weight"],
                        source_type="graph",
                        graph_result=gr,
                        relationships=gr.relationships,
                        reasoning=reasoning + [f"Graph result from iteration {iteration + 1}"],
                        metadata=gr.metadata
                    )
                    results.append(integrated_result)
        
        # Deduplicate and sort
        unique_results = self._deduplicate_results(results)
        unique_results.sort(key=lambda x: x.combined_score, reverse=True)
        return unique_results[:k]
    
    async def _hybrid_search(self,
                           query: str,
                           k: int,
                           filters: Optional[Dict[str, Any]]) -> List[IntegratedResult]:
        """
        Hybrid search: Intelligently combine vector and graph approaches.
        """
        reasoning = ["Using hybrid search strategy"]
        
        # Analyze query to determine optimal strategy
        query_analysis = self._analyze_query(query)
        reasoning.append(f"Query analysis: {query_analysis}")
        
        # Adjust weights based on query type
        adjusted_weights = self._adjust_weights_for_query(query_analysis)
        
        # Perform both searches
        vector_results = await self._perform_vector_search(query, k * 2, filters)
        graph_results = await self._perform_graph_search(query, k * 2, filters)
        
        reasoning.append(f"Found {len(vector_results)} vector and {len(graph_results)} graph results")
        
        # Create integrated results with adjusted weights
        integrated_results = []
        
        # Process vector results
        for vr in vector_results:
            graph_enhancements = await self._find_graph_relationships(vr.content, vr.document_id)
            
            combined_score = (
                vr.score * adjusted_weights["vector_weight"] +
                graph_enhancements.get("graph_score", 0.0) * adjusted_weights["graph_weight"] +
                len(graph_enhancements.get("relationships", [])) * adjusted_weights["relationship_boost"]
            )
            
            integrated_result = IntegratedResult(
                content=vr.content,
                vector_score=vr.score,
                graph_score=graph_enhancements.get("graph_score", 0.0),
                combined_score=combined_score,
                source_type="both" if graph_enhancements.get("relationships") else "vector",
                vector_result=vr,
                relationships=graph_enhancements.get("relationships", []),
                reasoning=reasoning + ["Hybrid vector result with graph enhancement"],
                metadata=vr.metadata
            )
            integrated_results.append(integrated_result)
        
        # Process graph results not already covered
        covered_content = {r.content for r in integrated_results}
        for gr in graph_results:
            if gr.content not in covered_content:
                vector_score = await self._calculate_vector_similarity(query, gr.content)
                
                combined_score = (
                    vector_score * adjusted_weights["vector_weight"] +
                    gr.path_score * adjusted_weights["graph_weight"] +
                    len(gr.relationships) * adjusted_weights["relationship_boost"]
                )
                
                integrated_result = IntegratedResult(
                    content=gr.content,
                    vector_score=vector_score,
                    graph_score=gr.path_score,
                    combined_score=combined_score,
                    source_type="both" if vector_score > 0.5 else "graph",
                    graph_result=gr,
                    relationships=gr.relationships,
                    reasoning=reasoning + ["Hybrid graph result with vector enhancement"],
                    metadata=gr.metadata
                )
                integrated_results.append(integrated_result)
        
        # Sort and return top results
        integrated_results.sort(key=lambda x: x.combined_score, reverse=True)
        return integrated_results[:k]

    # Helper methods for search operations

    async def _perform_vector_search(self,
                                   query: str,
                                   k: int,
                                   filters: Optional[Dict[str, Any]]) -> List[VectorResult]:
        """Perform vector similarity search."""
        if not self.vector_client:
            logger.warning("Vector search client not available, returning mock results")
            return self._generate_mock_vector_results(query, k)

        try:
            # This would call the actual vector search client
            # For now, return mock results
            return self._generate_mock_vector_results(query, k)
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []

    async def _perform_graph_search(self,
                                  query: str,
                                  k: int,
                                  filters: Optional[Dict[str, Any]]) -> List[GraphResult]:
        """Perform graph-based search."""
        if not self.graph_client:
            logger.warning("Graph search client not available, returning mock results")
            return self._generate_mock_graph_results(query, k)

        try:
            # This would call the actual graph search client
            # For now, return mock results
            return self._generate_mock_graph_results(query, k)
        except Exception as e:
            logger.error(f"Graph search failed: {e}")
            return []

    async def _find_graph_relationships(self,
                                      content: str,
                                      document_id: str) -> Dict[str, Any]:
        """Find graph relationships for a given content."""
        # Mock implementation - in real system, this would query the knowledge graph
        relationships = [
            {
                "type": RelationshipType.SEMANTIC.value,
                "target": "related_concept_1",
                "strength": 0.8,
                "metadata": {"source": "knowledge_graph"}
            },
            {
                "type": RelationshipType.HIERARCHICAL.value,
                "target": "parent_concept",
                "strength": 0.9,
                "metadata": {"source": "knowledge_graph"}
            }
        ]

        return {
            "graph_score": 0.7,
            "relationships": relationships
        }

    async def _calculate_vector_similarity(self, query: str, content: str) -> float:
        """Calculate vector similarity between query and content."""
        # Mock implementation - in real system, this would use embeddings
        # Simple word overlap similarity
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())

        if not query_words or not content_words:
            return 0.0

        intersection = len(query_words.intersection(content_words))
        union = len(query_words.union(content_words))

        return intersection / union if union > 0 else 0.0

    def _calculate_combined_score(self,
                                vector_score: float,
                                graph_score: float,
                                relationships: List[Dict[str, Any]]) -> float:
        """Calculate combined score from vector and graph components."""
        # Base combined score
        combined = (
            vector_score * self.weights["vector_weight"] +
            graph_score * self.weights["graph_weight"]
        )

        # Add relationship bonuses
        relationship_bonus = 0.0
        for rel in relationships:
            rel_type = rel.get("type", "")
            strength = rel.get("strength", 0.0)

            if rel_type == RelationshipType.SEMANTIC.value:
                relationship_bonus += strength * self.weights["semantic_boost"]
            elif rel_type == RelationshipType.STRUCTURAL.value:
                relationship_bonus += strength * self.weights["structural_boost"]
            else:
                relationship_bonus += strength * self.weights["relationship_boost"]

        return min(combined + relationship_bonus, 1.0)  # Cap at 1.0

    async def _combine_results(self,
                             vector_results: List[VectorResult],
                             graph_results: List[GraphResult],
                             reasoning: List[str]) -> List[IntegratedResult]:
        """Combine vector and graph results into integrated results."""
        integrated_results = []

        # Create a mapping of content to avoid duplicates
        content_map = {}

        # Process vector results
        for vr in vector_results:
            if vr.content not in content_map:
                graph_enhancements = await self._find_graph_relationships(vr.content, vr.document_id)

                combined_score = self._calculate_combined_score(
                    vr.score,
                    graph_enhancements.get("graph_score", 0.0),
                    graph_enhancements.get("relationships", [])
                )

                integrated_result = IntegratedResult(
                    content=vr.content,
                    vector_score=vr.score,
                    graph_score=graph_enhancements.get("graph_score", 0.0),
                    combined_score=combined_score,
                    source_type="both" if graph_enhancements.get("relationships") else "vector",
                    vector_result=vr,
                    relationships=graph_enhancements.get("relationships", []),
                    reasoning=reasoning + ["Combined vector result with graph enhancement"],
                    metadata=vr.metadata
                )

                integrated_results.append(integrated_result)
                content_map[vr.content] = integrated_result

        # Process graph results not already included
        for gr in graph_results:
            if gr.content not in content_map:
                vector_score = await self._calculate_vector_similarity("", gr.content)  # Empty query for now

                combined_score = self._calculate_combined_score(
                    vector_score,
                    gr.path_score,
                    gr.relationships
                )

                integrated_result = IntegratedResult(
                    content=gr.content,
                    vector_score=vector_score,
                    graph_score=gr.path_score,
                    combined_score=combined_score,
                    source_type="both" if vector_score > 0.3 else "graph",
                    graph_result=gr,
                    relationships=gr.relationships,
                    reasoning=reasoning + ["Combined graph result with vector enhancement"],
                    metadata=gr.metadata
                )

                integrated_results.append(integrated_result)
                content_map[gr.content] = integrated_result

        return integrated_results

    def _analyze_query(self, query: str) -> Dict[str, Any]:
        """Analyze query to determine optimal search strategy."""
        query_lower = query.lower()

        # Simple heuristics for query analysis
        analysis = {
            "is_factual": any(word in query_lower for word in ["what", "when", "where", "who", "how"]),
            "is_relational": any(word in query_lower for word in ["related", "connected", "similar", "associated"]),
            "is_conceptual": any(word in query_lower for word in ["concept", "idea", "theory", "principle"]),
            "has_entities": len([word for word in query.split() if word[0].isupper()]) > 0,
            "complexity": len(query.split()),
            "query_type": "factual" if "what" in query_lower else "exploratory"
        }

        return analysis

    def _adjust_weights_for_query(self, query_analysis: Dict[str, Any]) -> Dict[str, float]:
        """Adjust integration weights based on query analysis."""
        adjusted_weights = self.weights.copy()

        # Adjust based on query characteristics
        if query_analysis.get("is_relational", False):
            adjusted_weights["graph_weight"] += 0.2
            adjusted_weights["vector_weight"] -= 0.1
            adjusted_weights["relationship_boost"] += 0.1

        if query_analysis.get("is_factual", False):
            adjusted_weights["vector_weight"] += 0.1
            adjusted_weights["graph_weight"] -= 0.05

        if query_analysis.get("complexity", 0) > 10:
            adjusted_weights["graph_weight"] += 0.15
            adjusted_weights["relationship_boost"] += 0.05

        # Normalize weights
        total_weight = adjusted_weights["vector_weight"] + adjusted_weights["graph_weight"]
        if total_weight > 1.0:
            adjusted_weights["vector_weight"] /= total_weight
            adjusted_weights["graph_weight"] /= total_weight

        return adjusted_weights

    def _extract_key_terms(self, contents: List[str]) -> List[str]:
        """Extract key terms from content for query expansion."""
        # Simple key term extraction
        word_freq = defaultdict(int)

        for content in contents:
            words = content.lower().split()
            for word in words:
                if len(word) > 3 and word.isalpha():  # Filter short words and non-alphabetic
                    word_freq[word] += 1

        # Return most frequent terms
        sorted_terms = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [term for term, freq in sorted_terms if freq > 1]

    def _deduplicate_results(self, results: List[IntegratedResult]) -> List[IntegratedResult]:
        """Remove duplicate results based on content similarity."""
        unique_results = []
        seen_content = set()

        for result in results:
            # Simple deduplication based on exact content match
            if result.content not in seen_content:
                unique_results.append(result)
                seen_content.add(result.content)

        return unique_results

    # Mock data generation methods for testing

    def _generate_mock_vector_results(self, query: str, k: int) -> List[VectorResult]:
        """Generate mock vector search results for testing."""
        mock_results = []

        for i in range(min(k, 5)):  # Generate up to 5 mock results
            score = max(0.1, 1.0 - (i * 0.15))  # Decreasing scores
            content = f"Vector search result {i+1} for query '{query}'. This content is relevant to the search query and contains information about the requested topic."

            mock_result = VectorResult(
                content=content,
                score=score,
                document_id=f"doc_vector_{i+1}",
                metadata={
                    "source": "vector_search",
                    "index": i,
                    "query": query
                },
                embedding=[0.1 * j for j in range(10)]  # Mock embedding
            )
            mock_results.append(mock_result)

        return mock_results

    def _generate_mock_graph_results(self, query: str, k: int) -> List[GraphResult]:
        """Generate mock graph search results for testing."""
        mock_results = []

        for i in range(min(k, 4)):  # Generate up to 4 mock results
            path_score = max(0.1, 0.9 - (i * 0.2))  # Decreasing scores
            content = f"Graph search result {i+1} for query '{query}'. This content was found through graph traversal and relationship analysis."

            relationships = [
                {
                    "type": RelationshipType.SEMANTIC.value,
                    "target": f"concept_{i+1}",
                    "strength": 0.8 - (i * 0.1),
                    "metadata": {"source": "knowledge_graph"}
                },
                {
                    "type": RelationshipType.HIERARCHICAL.value,
                    "target": f"parent_concept_{i+1}",
                    "strength": 0.7 - (i * 0.1),
                    "metadata": {"source": "knowledge_graph"}
                }
            ]

            mock_result = GraphResult(
                content=content,
                node_id=f"node_graph_{i+1}",
                relationships=relationships,
                path_score=path_score,
                metadata={
                    "source": "graph_search",
                    "index": i,
                    "query": query,
                    "path_length": i + 1
                }
            )
            mock_results.append(mock_result)

        return mock_results

    # Configuration and utility methods

    def update_weights(self, new_weights: Dict[str, float]):
        """Update integration weights."""
        self.weights.update(new_weights)
        logger.info(f"Updated integration weights: {new_weights}")

    def update_config(self, new_config: Dict[str, Any]):
        """Update search configuration."""
        self.config.update(new_config)
        logger.info(f"Updated search configuration: {new_config}")

    def get_search_statistics(self, results: List[IntegratedResult]) -> Dict[str, Any]:
        """Get statistics about search results."""
        if not results:
            return {"total_results": 0}

        source_counts = defaultdict(int)
        vector_scores = []
        graph_scores = []
        combined_scores = []
        relationship_counts = []

        for result in results:
            source_counts[result.source_type] += 1
            vector_scores.append(result.vector_score)
            graph_scores.append(result.graph_score)
            combined_scores.append(result.combined_score)
            relationship_counts.append(len(result.relationships))

        return {
            "total_results": len(results),
            "source_distribution": dict(source_counts),
            "average_vector_score": np.mean(vector_scores) if vector_scores else 0.0,
            "average_graph_score": np.mean(graph_scores) if graph_scores else 0.0,
            "average_combined_score": np.mean(combined_scores) if combined_scores else 0.0,
            "average_relationships": np.mean(relationship_counts) if relationship_counts else 0.0,
            "max_combined_score": max(combined_scores) if combined_scores else 0.0,
            "min_combined_score": min(combined_scores) if combined_scores else 0.0
        }
