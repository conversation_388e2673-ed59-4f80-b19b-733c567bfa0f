"""
Multi-source Knowledge Fusion Component for the Knowledge Fusion System.
Enables fusion of knowledge from diverse sources with different formats and reliability.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime
import json
import logging
import hashlib
from collections import defaultdict
import re

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SourceType(Enum):
    """Types of knowledge sources that can be fused."""
    DOCUMENT = "document"
    DATABASE = "database"
    API = "api"
    VECTOR_SEARCH = "vector_search"
    GRAPH_SEARCH = "graph_search"
    AGENT_REASONING = "agent_reasoning"
    TOOL_EXECUTION = "tool_execution"
    USER_INPUT = "user_input"
    MEMORY = "memory"
    WEB = "web"

class SourceReliability(Enum):
    """Reliability levels for knowledge sources."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    UNKNOWN = "unknown"

class FusionMethod(Enum):
    """Methods for fusing knowledge from multiple sources."""
    WEIGHTED_AVERAGE = "weighted_average"
    MAJORITY_VOTING = "majority_voting"
    BAYESIAN_INTEGRATION = "bayesian_integration"
    EVIDENCE_ACCUMULATION = "evidence_accumulation"
    HIERARCHICAL = "hierarchical"
    ENSEMBLE = "ensemble"

@dataclass
class KnowledgeItem:
    """Represents a single piece of knowledge from a specific source."""
    content: str
    source_id: str
    source_type: SourceType
    confidence: float = 0.5
    reliability: SourceReliability = SourceReliability.MEDIUM
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    entities: List[Dict[str, Any]] = field(default_factory=list)
    
    def __post_init__(self):
        """Generate a unique hash for this knowledge item."""
        if isinstance(self.source_type, SourceType):
            self.source_type_str = self.source_type.value
        else:
            self.source_type_str = str(self.source_type)
            self.source_type = SourceType(self.source_type_str)
            
        if isinstance(self.reliability, SourceReliability):
            self.reliability_str = self.reliability.value
        else:
            self.reliability_str = str(self.reliability)
            self.reliability = SourceReliability(self.reliability_str)
        
        # Create a hash of the content for comparison
        self.content_hash = hashlib.md5(self.content.encode()).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "content": self.content,
            "source_id": self.source_id,
            "source_type": self.source_type_str,
            "confidence": self.confidence,
            "reliability": self.reliability_str,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata,
            "entities": self.entities,
            "content_hash": self.content_hash
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgeItem':
        """Create a KnowledgeItem from a dictionary."""
        timestamp = datetime.fromisoformat(data["timestamp"]) if isinstance(data["timestamp"], str) else data["timestamp"]
        return cls(
            content=data["content"],
            source_id=data["source_id"],
            source_type=data["source_type"],
            confidence=data["confidence"],
            reliability=data["reliability"],
            timestamp=timestamp,
            metadata=data.get("metadata", {}),
            entities=data.get("entities", [])
        )

@dataclass
class FusedKnowledgeResult:
    """Result of a knowledge fusion operation."""
    content: str
    confidence: float
    source_items: List[KnowledgeItem]
    fusion_method: FusionMethod
    reasoning: List[str] = field(default_factory=list)
    conflicts: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.fusion_method, FusionMethod):
            self.fusion_method_str = self.fusion_method.value
        else:
            self.fusion_method_str = str(self.fusion_method)
            self.fusion_method = FusionMethod(self.fusion_method_str)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "content": self.content,
            "confidence": self.confidence,
            "sources": [item.to_dict() for item in self.source_items],
            "fusion_method": self.fusion_method_str,
            "reasoning": self.reasoning,
            "conflicts": self.conflicts,
            "metadata": self.metadata
        }

class MultiSourceFusion:
    """
    Implements multi-source knowledge fusion capabilities.
    Combines information from diverse sources with different formats and reliability.
    """
    
    def __init__(self, default_weights: Optional[Dict[str, float]] = None):
        """Initialize the multi-source fusion component."""
        # Default source type weights
        self.source_weights = default_weights or {
            SourceType.DOCUMENT.value: 0.7,
            SourceType.DATABASE.value: 0.8,
            SourceType.API.value: 0.75,
            SourceType.VECTOR_SEARCH.value: 0.65,
            SourceType.GRAPH_SEARCH.value: 0.85,
            SourceType.AGENT_REASONING.value: 0.6,
            SourceType.TOOL_EXECUTION.value: 0.8,
            SourceType.USER_INPUT.value: 0.9,
            SourceType.MEMORY.value: 0.7,
            SourceType.WEB.value: 0.5
        }
        
        # Default reliability weights
        self.reliability_weights = {
            SourceReliability.HIGH.value: 1.0,
            SourceReliability.MEDIUM.value: 0.7,
            SourceReliability.LOW.value: 0.4,
            SourceReliability.UNKNOWN.value: 0.3
        }
        
        # Available fusion methods
        self.fusion_methods = {
            FusionMethod.WEIGHTED_AVERAGE: self._weighted_average_fusion,
            FusionMethod.MAJORITY_VOTING: self._majority_voting_fusion,
            FusionMethod.BAYESIAN_INTEGRATION: self._bayesian_integration_fusion,
            FusionMethod.EVIDENCE_ACCUMULATION: self._evidence_accumulation_fusion,
            FusionMethod.HIERARCHICAL: self._hierarchical_fusion,
            FusionMethod.ENSEMBLE: self._ensemble_fusion
        }
        
        logger.info("Multi-source Knowledge Fusion component initialized")
    
    def fuse_knowledge(self, 
                      knowledge_items: List[KnowledgeItem], 
                      method: FusionMethod = FusionMethod.WEIGHTED_AVERAGE,
                      custom_weights: Optional[Dict[str, float]] = None) -> FusedKnowledgeResult:
        """
        Fuse knowledge from multiple sources using the specified method.
        
        Args:
            knowledge_items: List of knowledge items to fuse
            method: Fusion method to use
            custom_weights: Optional custom weights for sources
            
        Returns:
            FusedKnowledgeResult containing the fused knowledge
        """
        if not knowledge_items:
            logger.warning("No knowledge items provided for fusion")
            return FusedKnowledgeResult(
                content="",
                confidence=0.0,
                source_items=[],
                fusion_method=method,
                reasoning=["No knowledge items provided for fusion"]
            )
        
        # If only one item, return it directly
        if len(knowledge_items) == 1:
            return FusedKnowledgeResult(
                content=knowledge_items[0].content,
                confidence=knowledge_items[0].confidence,
                source_items=knowledge_items,
                fusion_method=method,
                reasoning=["Single knowledge item, no fusion needed"]
            )
        
        # Apply the selected fusion method
        fusion_func = self.fusion_methods.get(method, self._weighted_average_fusion)
        
        # Use custom weights if provided
        weights = custom_weights or self.source_weights
        
        # Perform fusion
        return fusion_func(knowledge_items, weights)
    
    def _weighted_average_fusion(self, 
                               knowledge_items: List[KnowledgeItem],
                               weights: Dict[str, float]) -> FusedKnowledgeResult:
        """
        Fuse knowledge using weighted average based on source reliability and confidence.
        
        This method is best for numerical data or when items can be combined proportionally.
        """
        # Group similar content to avoid duplication
        grouped_items = self._group_similar_items(knowledge_items)
        
        # Calculate weights for each group
        group_weights = []
        group_contents = []
        reasoning = []
        
        for group in grouped_items:
            # Calculate combined weight for this group
            group_weight = 0
            for item in group:
                source_weight = weights.get(item.source_type_str, 0.5)
                reliability_weight = self.reliability_weights.get(item.reliability_str, 0.5)
                item_weight = item.confidence * source_weight * reliability_weight
                group_weight += item_weight
            
            # Normalize by number of items
            group_weight = group_weight / len(group)
            group_weights.append(group_weight)
            
            # Use the content from the highest confidence item in the group
            best_item = max(group, key=lambda x: x.confidence)
            group_contents.append(best_item.content)
            
            # Add reasoning
            reasoning.append(f"Group with {len(group)} similar items from sources "
                           f"{[item.source_id for item in group]} has weight {group_weight:.2f}")
        
        # Normalize weights
        total_weight = sum(group_weights)
        if total_weight > 0:
            normalized_weights = [w/total_weight for w in group_weights]
        else:
            normalized_weights = [1.0/len(group_weights) for _ in group_weights]
        
        # Combine contents based on weights
        # For text, we select the highest weighted content
        if group_weights:
            best_idx = normalized_weights.index(max(normalized_weights))
            fused_content = group_contents[best_idx]
            confidence = normalized_weights[best_idx]
        else:
            fused_content = ""
            confidence = 0.0
        
        # Create result
        return FusedKnowledgeResult(
            content=fused_content,
            confidence=confidence,
            source_items=knowledge_items,
            fusion_method=FusionMethod.WEIGHTED_AVERAGE,
            reasoning=reasoning
        )
    
    def _majority_voting_fusion(self,
                              knowledge_items: List[KnowledgeItem],
                              weights: Dict[str, float]) -> FusedKnowledgeResult:
        """
        Fuse knowledge using majority voting with weighted votes.
        
        This method is best for categorical data or when consensus is important.
        """
        # Group similar content
        grouped_items = self._group_similar_items(knowledge_items)
        
        # Count weighted votes for each group
        group_votes = []
        group_contents = []
        reasoning = []
        
        for group in grouped_items:
            # Calculate votes for this group
            votes = 0
            for item in group:
                source_weight = weights.get(item.source_type_str, 0.5)
                reliability_weight = self.reliability_weights.get(item.reliability_str, 0.5)
                item_vote = item.confidence * source_weight * reliability_weight
                votes += item_vote
            
            group_votes.append(votes)
            
            # Use the content from the highest confidence item in the group
            best_item = max(group, key=lambda x: x.confidence)
            group_contents.append(best_item.content)
            
            # Add reasoning
            reasoning.append(f"Group with {len(group)} similar items received {votes:.2f} votes")
        
        # Select the group with the most votes
        if group_votes:
            winner_idx = group_votes.index(max(group_votes))
            fused_content = group_contents[winner_idx]
            
            # Calculate confidence based on vote margin
            total_votes = sum(group_votes)
            if total_votes > 0:
                confidence = group_votes[winner_idx] / total_votes
            else:
                confidence = 0.0
                
            reasoning.append(f"Selected group {winner_idx} with {group_votes[winner_idx]:.2f} votes "
                           f"({confidence:.2%} of total)")
        else:
            fused_content = ""
            confidence = 0.0
        
        return FusedKnowledgeResult(
            content=fused_content,
            confidence=confidence,
            source_items=knowledge_items,
            fusion_method=FusionMethod.MAJORITY_VOTING,
            reasoning=reasoning
        )

    def _bayesian_integration_fusion(self,
                                   knowledge_items: List[KnowledgeItem],
                                   weights: Dict[str, float]) -> FusedKnowledgeResult:
        """
        Fuse knowledge using Bayesian integration.

        This method updates beliefs based on evidence from multiple sources.
        """
        # Start with uniform prior
        prior_confidence = 0.5

        # Update belief with each piece of evidence
        posterior_confidence = prior_confidence
        reasoning = [f"Starting with prior confidence: {prior_confidence:.2f}"]

        for item in knowledge_items:
            # Calculate likelihood based on source reliability
            source_weight = weights.get(item.source_type_str, 0.5)
            reliability_weight = self.reliability_weights.get(item.reliability_str, 0.5)
            likelihood = item.confidence * source_weight * reliability_weight

            # Bayesian update
            # P(H|E) = P(E|H) * P(H) / P(E)
            # Simplified: new_confidence = likelihood * old_confidence / normalization
            posterior_confidence = (likelihood * posterior_confidence) / \
                                 (likelihood * posterior_confidence + (1 - likelihood) * (1 - posterior_confidence))

            reasoning.append(f"Updated with evidence from {item.source_id} "
                           f"(likelihood: {likelihood:.2f}) -> confidence: {posterior_confidence:.2f}")

        # Select the highest confidence content
        best_item = max(knowledge_items, key=lambda x: x.confidence)

        return FusedKnowledgeResult(
            content=best_item.content,
            confidence=posterior_confidence,
            source_items=knowledge_items,
            fusion_method=FusionMethod.BAYESIAN_INTEGRATION,
            reasoning=reasoning
        )

    def _evidence_accumulation_fusion(self,
                                    knowledge_items: List[KnowledgeItem],
                                    weights: Dict[str, float]) -> FusedKnowledgeResult:
        """
        Fuse knowledge by accumulating evidence from multiple sources.

        This method combines evidence strength from all sources.
        """
        # Group similar content
        grouped_items = self._group_similar_items(knowledge_items)

        # Accumulate evidence for each group
        group_evidence = []
        group_contents = []
        reasoning = []

        for group in grouped_items:
            # Accumulate evidence for this group
            evidence_strength = 0
            for item in group:
                source_weight = weights.get(item.source_type_str, 0.5)
                reliability_weight = self.reliability_weights.get(item.reliability_str, 0.5)
                item_evidence = item.confidence * source_weight * reliability_weight
                evidence_strength += item_evidence

            group_evidence.append(evidence_strength)

            # Use the content from the highest confidence item in the group
            best_item = max(group, key=lambda x: x.confidence)
            group_contents.append(best_item.content)

            reasoning.append(f"Group with {len(group)} items accumulated evidence: {evidence_strength:.2f}")

        # Select the group with the strongest evidence
        if group_evidence:
            best_idx = group_evidence.index(max(group_evidence))
            fused_content = group_contents[best_idx]

            # Normalize confidence
            max_evidence = max(group_evidence)
            total_evidence = sum(group_evidence)
            confidence = max_evidence / total_evidence if total_evidence > 0 else 0.0

            reasoning.append(f"Selected group with strongest evidence: {max_evidence:.2f}")
        else:
            fused_content = ""
            confidence = 0.0

        return FusedKnowledgeResult(
            content=fused_content,
            confidence=confidence,
            source_items=knowledge_items,
            fusion_method=FusionMethod.EVIDENCE_ACCUMULATION,
            reasoning=reasoning
        )

    def _hierarchical_fusion(self,
                           knowledge_items: List[KnowledgeItem],
                           weights: Dict[str, float]) -> FusedKnowledgeResult:
        """
        Fuse knowledge using hierarchical approach based on source priority.

        This method prioritizes sources in a hierarchical order.
        """
        # Define source hierarchy (higher values = higher priority)
        source_hierarchy = {
            SourceType.DATABASE.value: 10,
            SourceType.TOOL_EXECUTION.value: 9,
            SourceType.USER_INPUT.value: 8,
            SourceType.GRAPH_SEARCH.value: 7,
            SourceType.API.value: 6,
            SourceType.DOCUMENT.value: 5,
            SourceType.VECTOR_SEARCH.value: 4,
            SourceType.MEMORY.value: 3,
            SourceType.AGENT_REASONING.value: 2,
            SourceType.WEB.value: 1
        }

        # Sort items by hierarchy and confidence
        sorted_items = sorted(knowledge_items,
                            key=lambda x: (source_hierarchy.get(x.source_type_str, 0), x.confidence),
                            reverse=True)

        reasoning = []
        for i, item in enumerate(sorted_items):
            hierarchy_level = source_hierarchy.get(item.source_type_str, 0)
            reasoning.append(f"Rank {i+1}: {item.source_id} (hierarchy: {hierarchy_level}, "
                           f"confidence: {item.confidence:.2f})")

        # Select the highest priority item
        if sorted_items:
            best_item = sorted_items[0]
            fused_content = best_item.content
            confidence = best_item.confidence
            reasoning.append(f"Selected highest priority source: {best_item.source_id}")
        else:
            fused_content = ""
            confidence = 0.0

        return FusedKnowledgeResult(
            content=fused_content,
            confidence=confidence,
            source_items=knowledge_items,
            fusion_method=FusionMethod.HIERARCHICAL,
            reasoning=reasoning
        )

    def _ensemble_fusion(self,
                       knowledge_items: List[KnowledgeItem],
                       weights: Dict[str, float]) -> FusedKnowledgeResult:
        """
        Fuse knowledge using ensemble of multiple fusion methods.

        This method combines results from multiple fusion approaches.
        """
        # Apply multiple fusion methods
        methods = [
            FusionMethod.WEIGHTED_AVERAGE,
            FusionMethod.MAJORITY_VOTING,
            FusionMethod.EVIDENCE_ACCUMULATION
        ]

        results = []
        reasoning = ["Ensemble fusion using multiple methods:"]

        for method in methods:
            fusion_func = self.fusion_methods[method]
            result = fusion_func(knowledge_items, weights)
            results.append(result)
            reasoning.append(f"  {method.value}: confidence {result.confidence:.2f}")

        # Combine results using weighted average of confidences
        if results:
            # Weight by confidence
            total_confidence = sum(r.confidence for r in results)
            if total_confidence > 0:
                weights_norm = [r.confidence / total_confidence for r in results]
            else:
                weights_norm = [1.0 / len(results) for _ in results]

            # Select the result with highest weighted confidence
            best_idx = weights_norm.index(max(weights_norm))
            best_result = results[best_idx]

            # Calculate ensemble confidence
            ensemble_confidence = sum(w * r.confidence for w, r in zip(weights_norm, results))

            reasoning.append(f"Selected result from {methods[best_idx].value} "
                           f"with ensemble confidence: {ensemble_confidence:.2f}")
        else:
            best_result = FusedKnowledgeResult("", 0.0, [], FusionMethod.ENSEMBLE)
            ensemble_confidence = 0.0

        return FusedKnowledgeResult(
            content=best_result.content,
            confidence=ensemble_confidence,
            source_items=knowledge_items,
            fusion_method=FusionMethod.ENSEMBLE,
            reasoning=reasoning
        )

    def _group_similar_items(self, knowledge_items: List[KnowledgeItem]) -> List[List[KnowledgeItem]]:
        """
        Group similar knowledge items together.

        This is a simplified similarity grouping based on content overlap.
        """
        groups = []

        for item in knowledge_items:
            # Find if this item belongs to an existing group
            placed = False
            for group in groups:
                # Check similarity with group representative (first item)
                if self._are_similar(item, group[0]):
                    group.append(item)
                    placed = True
                    break

            # If not placed in any group, create a new group
            if not placed:
                groups.append([item])

        return groups

    def _are_similar(self, item1: KnowledgeItem, item2: KnowledgeItem, threshold: float = 0.7) -> bool:
        """
        Check if two knowledge items are similar.

        This is a simplified similarity check based on content overlap.
        """
        # Simple word overlap similarity
        words1 = set(re.findall(r'\w+', item1.content.lower()))
        words2 = set(re.findall(r'\w+', item2.content.lower()))

        if not words1 or not words2:
            return False

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        similarity = intersection / union if union > 0 else 0.0
        return similarity >= threshold

    def update_source_weights(self, new_weights: Dict[str, float]):
        """Update the weights for different source types."""
        self.source_weights.update(new_weights)
        logger.info(f"Updated source weights: {new_weights}")

    def update_reliability_weights(self, new_weights: Dict[str, float]):
        """Update the weights for different reliability levels."""
        self.reliability_weights.update(new_weights)
        logger.info(f"Updated reliability weights: {new_weights}")

    def get_fusion_statistics(self, knowledge_items: List[KnowledgeItem]) -> Dict[str, Any]:
        """Get statistics about the knowledge items for fusion analysis."""
        if not knowledge_items:
            return {"total_items": 0}

        # Count by source type
        source_counts = defaultdict(int)
        reliability_counts = defaultdict(int)
        confidence_sum = 0

        for item in knowledge_items:
            source_counts[item.source_type_str] += 1
            reliability_counts[item.reliability_str] += 1
            confidence_sum += item.confidence

        return {
            "total_items": len(knowledge_items),
            "source_distribution": dict(source_counts),
            "reliability_distribution": dict(reliability_counts),
            "average_confidence": confidence_sum / len(knowledge_items),
            "unique_sources": len(set(item.source_id for item in knowledge_items))
        }
