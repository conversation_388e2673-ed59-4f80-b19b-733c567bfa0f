"""
Knowledge Conflict Resolution Component for the Knowledge Fusion System.
Detects and resolves conflicts between knowledge from different sources.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime
import json
import logging
import re
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConflictType(Enum):
    """Types of knowledge conflicts."""
    FACTUAL = "factual"
    TEMPORAL = "temporal"
    LOGICAL = "logical"
    SEMANTIC = "semantic"
    NUMERICAL = "numerical"
    CATEGORICAL = "categorical"
    PROCEDURAL = "procedural"
    POLICY = "policy"

class ResolutionStrategy(Enum):
    """Strategies for resolving knowledge conflicts."""
    RECENCY = "recency"
    AUTHORITY = "authority"
    MAJORITY = "majority"
    CONFIDENCE = "confidence"
    EVIDENCE = "evidence"
    CONTEXT = "context"
    HYBRID = "hybrid"
    ESCALATION = "escalation"

@dataclass
class KnowledgeItem:
    """Represents a single piece of knowledge from a specific source."""
    content: str
    source_id: str
    source_type: str
    confidence: float = 0.5
    reliability: str = "medium"
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    entities: List[Dict[str, Any]] = field(default_factory=list)
    content_hash: str = ""
    
    def __post_init__(self):
        """Generate a unique hash for this knowledge item if not provided."""
        if not self.content_hash:
            import hashlib
            self.content_hash = hashlib.md5(self.content.encode()).hexdigest()

@dataclass
class ConflictInstance:
    """Represents a detected conflict between knowledge items."""
    conflict_id: str
    conflict_type: ConflictType
    items: List[KnowledgeItem]
    description: str
    severity: float  # 0.0 to 1.0
    detected_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.conflict_type, ConflictType):
            self.conflict_type_str = self.conflict_type.value
        else:
            self.conflict_type_str = str(self.conflict_type)
            self.conflict_type = ConflictType(self.conflict_type_str)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "conflict_id": self.conflict_id,
            "conflict_type": self.conflict_type_str,
            "items": [self._item_to_dict(item) for item in self.items],
            "description": self.description,
            "severity": self.severity,
            "detected_at": self.detected_at.isoformat(),
            "metadata": self.metadata
        }
    
    def _item_to_dict(self, item: KnowledgeItem) -> Dict[str, Any]:
        """Convert a knowledge item to dictionary."""
        return {
            "content": item.content,
            "source_id": item.source_id,
            "source_type": item.source_type,
            "confidence": item.confidence,
            "reliability": item.reliability,
            "timestamp": item.timestamp.isoformat() if isinstance(item.timestamp, datetime) else item.timestamp,
            "content_hash": item.content_hash
        }

@dataclass
class ResolutionResult:
    """Result of a conflict resolution operation."""
    conflict: ConflictInstance
    resolved_content: str
    resolution_strategy: ResolutionStrategy
    confidence: float
    reasoning: List[str]
    selected_items: List[KnowledgeItem] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.resolution_strategy, ResolutionStrategy):
            self.resolution_strategy_str = self.resolution_strategy.value
        else:
            self.resolution_strategy_str = str(self.resolution_strategy)
            self.resolution_strategy = ResolutionStrategy(self.resolution_strategy_str)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "conflict": self.conflict.to_dict(),
            "resolved_content": self.resolved_content,
            "resolution_strategy": self.resolution_strategy_str,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "selected_items": [item.content_hash for item in self.selected_items],
            "metadata": self.metadata
        }

class ConflictResolution:
    """
    Implements knowledge conflict detection and resolution capabilities.
    Identifies and resolves conflicts between knowledge from different sources.
    """
    
    def __init__(self, 
                 source_authority: Optional[Dict[str, float]] = None,
                 default_strategy: ResolutionStrategy = ResolutionStrategy.HYBRID):
        """Initialize the conflict resolution component."""
        # Source authority ratings (higher = more authoritative)
        self.source_authority = source_authority or {
            "database": 0.9,
            "api": 0.85,
            "document": 0.7,
            "graph_search": 0.8,
            "vector_search": 0.6,
            "agent_reasoning": 0.5,
            "tool_execution": 0.8,
            "user_input": 0.95,
            "memory": 0.7,
            "web": 0.4
        }
        
        # Default resolution strategy
        self.default_strategy = default_strategy
        
        # Resolution strategies
        self.resolution_strategies = {
            ResolutionStrategy.RECENCY: self._resolve_by_recency,
            ResolutionStrategy.AUTHORITY: self._resolve_by_authority,
            ResolutionStrategy.MAJORITY: self._resolve_by_majority,
            ResolutionStrategy.CONFIDENCE: self._resolve_by_confidence,
            ResolutionStrategy.EVIDENCE: self._resolve_by_evidence,
            ResolutionStrategy.CONTEXT: self._resolve_by_context,
            ResolutionStrategy.HYBRID: self._resolve_by_hybrid,
            ResolutionStrategy.ESCALATION: self._resolve_by_escalation
        }
        
        # Conflict detection patterns
        self.conflict_patterns = {
            ConflictType.FACTUAL: self._detect_factual_conflicts,
            ConflictType.TEMPORAL: self._detect_temporal_conflicts,
            ConflictType.LOGICAL: self._detect_logical_conflicts,
            ConflictType.NUMERICAL: self._detect_numerical_conflicts,
            ConflictType.CATEGORICAL: self._detect_categorical_conflicts
        }
        
        # Conflict history
        self.conflict_history: List[ConflictInstance] = []
        self.resolution_history: List[ResolutionResult] = []
        
        logger.info("Knowledge Conflict Resolution component initialized")
    
    def detect_conflicts(self, 
                       knowledge_items: List[KnowledgeItem],
                       conflict_types: Optional[List[ConflictType]] = None) -> List[ConflictInstance]:
        """
        Detect conflicts among knowledge items.
        
        Args:
            knowledge_items: List of knowledge items to check for conflicts
            conflict_types: Optional list of conflict types to detect
            
        Returns:
            List of detected conflict instances
        """
        if not knowledge_items or len(knowledge_items) < 2:
            logger.info("Not enough knowledge items to detect conflicts")
            return []
        
        # Use all conflict types if none specified
        if not conflict_types:
            conflict_types = list(ConflictType)
        
        detected_conflicts = []
        
        # Apply each conflict detection pattern
        for conflict_type in conflict_types:
            if conflict_type in self.conflict_patterns:
                detector = self.conflict_patterns[conflict_type]
                conflicts = detector(knowledge_items)
                detected_conflicts.extend(conflicts)
                logger.info(f"Detected {len(conflicts)} {conflict_type.value} conflicts")
            else:
                logger.warning(f"No detector for conflict type: {conflict_type}")
        
        # Update conflict history
        self.conflict_history.extend(detected_conflicts)
        
        return detected_conflicts
    
    def resolve_conflict(self,
                       conflict: ConflictInstance,
                       strategy: Optional[ResolutionStrategy] = None,
                       context: Optional[Dict[str, Any]] = None) -> ResolutionResult:
        """
        Resolve a knowledge conflict using the specified strategy.
        
        Args:
            conflict: The conflict instance to resolve
            strategy: Resolution strategy to use (default: self.default_strategy)
            context: Optional context information for resolution
            
        Returns:
            Resolution result with resolved content and reasoning
        """
        # Use default strategy if none specified
        if not strategy:
            strategy = self.default_strategy
        
        # Get resolution function
        resolver = self.resolution_strategies.get(strategy, self._resolve_by_hybrid)
        
        # Apply resolution strategy
        result = resolver(conflict, context or {})
        
        # Update resolution history
        self.resolution_history.append(result)
        
        logger.info(f"Resolved {conflict.conflict_type_str} conflict using {strategy.value} strategy")

        return result

    # Conflict detection methods

    def _detect_factual_conflicts(self, knowledge_items: List[KnowledgeItem]) -> List[ConflictInstance]:
        """
        Detect factual conflicts between knowledge items.

        Factual conflicts occur when items make contradictory factual claims.
        """
        conflicts = []

        # Group items by entity mentions to find potential conflicts
        entity_groups = self._group_by_entities(knowledge_items)

        for entity, items in entity_groups.items():
            if len(items) < 2:
                continue

            # Look for contradictory statements about the same entity
            for i in range(len(items)):
                for j in range(i+1, len(items)):
                    item1, item2 = items[i], items[j]

                    # Check for contradictions
                    contradiction = self._check_factual_contradiction(item1.content, item2.content)

                    if contradiction:
                        conflict_id = f"factual_{entity}_{item1.content_hash[:6]}_{item2.content_hash[:6]}"

                        conflict = ConflictInstance(
                            conflict_id=conflict_id,
                            conflict_type=ConflictType.FACTUAL,
                            items=[item1, item2],
                            description=f"Factual contradiction about {entity}: {contradiction}",
                            severity=0.8,
                            metadata={"entity": entity, "contradiction": contradiction}
                        )

                        conflicts.append(conflict)

        return conflicts

    def _detect_temporal_conflicts(self, knowledge_items: List[KnowledgeItem]) -> List[ConflictInstance]:
        """
        Detect temporal conflicts between knowledge items.

        Temporal conflicts occur when items make contradictory claims about timing or sequence.
        """
        conflicts = []

        # Extract date/time patterns
        date_pattern = r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b'
        time_pattern = r'\b\d{1,2}:\d{2}(:\d{2})?\s*(am|pm|AM|PM)?\b'

        # Group items by temporal references
        temporal_groups = defaultdict(list)

        for item in knowledge_items:
            # Extract dates and times
            dates = re.findall(date_pattern, item.content)
            times = re.findall(time_pattern, item.content)

            # Add to relevant groups
            for date in dates:
                temporal_groups[f"date_{date}"].append(item)

            for time in times:
                temporal_groups[f"time_{time[0]}"].append(item)

        # Check for conflicts within each temporal group
        for temporal_ref, items in temporal_groups.items():
            if len(items) < 2:
                continue

            for i in range(len(items)):
                for j in range(i+1, len(items)):
                    item1, item2 = items[i], items[j]

                    # Check for temporal contradictions
                    contradiction = self._check_temporal_contradiction(item1.content, item2.content)

                    if contradiction:
                        conflict_id = f"temporal_{temporal_ref}_{item1.content_hash[:6]}_{item2.content_hash[:6]}"

                        conflict = ConflictInstance(
                            conflict_id=conflict_id,
                            conflict_type=ConflictType.TEMPORAL,
                            items=[item1, item2],
                            description=f"Temporal contradiction about {temporal_ref}: {contradiction}",
                            severity=0.7,
                            metadata={"temporal_reference": temporal_ref, "contradiction": contradiction}
                        )

                        conflicts.append(conflict)

        return conflicts

    def _detect_numerical_conflicts(self, knowledge_items: List[KnowledgeItem]) -> List[ConflictInstance]:
        """
        Detect numerical conflicts between knowledge items.

        Numerical conflicts occur when items provide different numerical values for the same attribute.
        """
        conflicts = []

        # Extract numerical patterns with context
        number_pattern = r'(\w+(?:\s+\w+){0,3})\s+(?:is|are|was|were|equals?|amounts? to)\s+(\d+(?:\.\d+)?)'

        # Group items by numerical attributes
        numerical_groups = defaultdict(list)

        for item in knowledge_items:
            # Extract numerical statements
            matches = re.findall(number_pattern, item.content, re.IGNORECASE)

            # Add to relevant groups
            for attribute, value in matches:
                attribute = attribute.strip().lower()
                numerical_groups[attribute].append((item, float(value)))

        # Check for conflicts within each attribute group
        for attribute, item_values in numerical_groups.items():
            if len(item_values) < 2:
                continue

            # Check for significant differences in values
            for i in range(len(item_values)):
                for j in range(i+1, len(item_values)):
                    item1, value1 = item_values[i]
                    item2, value2 = item_values[j]

                    # Calculate relative difference
                    max_val = max(abs(value1), abs(value2))
                    if max_val > 0:
                        rel_diff = abs(value1 - value2) / max_val
                    else:
                        rel_diff = 0

                    # If difference is significant, create conflict
                    if rel_diff > 0.1:  # 10% threshold
                        conflict_id = f"numerical_{attribute}_{item1.content_hash[:6]}_{item2.content_hash[:6]}"

                        conflict = ConflictInstance(
                            conflict_id=conflict_id,
                            conflict_type=ConflictType.NUMERICAL,
                            items=[item1, item2],
                            description=f"Numerical contradiction for {attribute}: {value1} vs {value2}",
                            severity=min(0.9, rel_diff),
                            metadata={
                                "attribute": attribute,
                                "value1": value1,
                                "value2": value2,
                                "relative_difference": rel_diff
                            }
                        )

                        conflicts.append(conflict)

        return conflicts

    def _detect_logical_conflicts(self, knowledge_items: List[KnowledgeItem]) -> List[ConflictInstance]:
        """
        Detect logical conflicts between knowledge items.

        Logical conflicts occur when items make logically incompatible claims.
        """
        conflicts = []

        # Extract logical patterns
        affirmation_pattern = r'((?:\w+\s+){1,5})\s+(?:is|are|does|do|has|have|can|will|should)\s+'
        negation_pattern = r'((?:\w+\s+){1,5})\s+(?:is not|are not|isn\'t|aren\'t|does not|doesn\'t|do not|don\'t|has not|hasn\'t|have not|haven\'t|cannot|can\'t|will not|won\'t|should not|shouldn\'t)\s+'

        # Group items by logical statements
        logical_groups = defaultdict(list)

        for item in knowledge_items:
            # Extract affirmations
            affirmations = re.findall(affirmation_pattern, item.content, re.IGNORECASE)
            for statement in affirmations:
                statement = statement.strip().lower()
                logical_groups[statement].append((item, True))  # True for affirmation

            # Extract negations
            negations = re.findall(negation_pattern, item.content, re.IGNORECASE)
            for statement in negations:
                statement = statement.strip().lower()
                logical_groups[statement].append((item, False))  # False for negation

        # Check for conflicts within each logical group
        for statement, item_values in logical_groups.items():
            if len(item_values) < 2:
                continue

            # Check for logical contradictions (affirmation vs negation)
            affirmations = [item for item, is_affirmation in item_values if is_affirmation]
            negations = [item for item, is_affirmation in item_values if not is_affirmation]

            if affirmations and negations:
                for aff_item in affirmations:
                    for neg_item in negations:
                        conflict_id = f"logical_{statement[:10]}_{aff_item.content_hash[:6]}_{neg_item.content_hash[:6]}"

                        conflict = ConflictInstance(
                            conflict_id=conflict_id,
                            conflict_type=ConflictType.LOGICAL,
                            items=[aff_item, neg_item],
                            description=f"Logical contradiction about '{statement}': affirmation vs negation",
                            severity=0.85,
                            metadata={"statement": statement}
                        )

                        conflicts.append(conflict)

        return conflicts

    def _detect_categorical_conflicts(self, knowledge_items: List[KnowledgeItem]) -> List[ConflictInstance]:
        """
        Detect categorical conflicts between knowledge items.

        Categorical conflicts occur when items assign different categories to the same entity.
        """
        conflicts = []

        # Extract categorical patterns
        category_pattern = r'(\w+(?:\s+\w+){0,2})\s+(?:is|are)\s+(?:a|an)\s+(\w+(?:\s+\w+){0,2})'

        # Group items by entity-category assignments
        category_groups = defaultdict(list)

        for item in knowledge_items:
            # Extract categorical statements
            matches = re.findall(category_pattern, item.content, re.IGNORECASE)

            # Add to relevant groups
            for entity, category in matches:
                entity = entity.strip().lower()
                category = category.strip().lower()
                category_groups[entity].append((item, category))

        # Check for conflicts within each entity group
        for entity, item_categories in category_groups.items():
            if len(item_categories) < 2:
                continue

            # Check for different categories assigned to the same entity
            categories = set(category for _, category in item_categories)

            if len(categories) > 1:
                # Create conflict for each pair of different categories
                items_by_category = defaultdict(list)
                for item, category in item_categories:
                    items_by_category[category].append(item)

                category_list = list(categories)
                for i in range(len(category_list)):
                    for j in range(i+1, len(category_list)):
                        cat1, cat2 = category_list[i], category_list[j]

                        # Get representative items for each category
                        item1 = items_by_category[cat1][0]
                        item2 = items_by_category[cat2][0]

                        conflict_id = f"categorical_{entity}_{cat1}_{cat2}_{item1.content_hash[:6]}"

                        conflict = ConflictInstance(
                            conflict_id=conflict_id,
                            conflict_type=ConflictType.CATEGORICAL,
                            items=[item1, item2],
                            description=f"Categorical contradiction for {entity}: {cat1} vs {cat2}",
                            severity=0.6,
                            metadata={
                                "entity": entity,
                                "category1": cat1,
                                "category2": cat2
                            }
                        )

                        conflicts.append(conflict)

        return conflicts

    # Resolution strategy methods

    def _resolve_by_recency(self,
                          conflict: ConflictInstance,
                          context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict by selecting the most recent information.
        """
        # Sort items by timestamp (most recent first)
        sorted_items = sorted(conflict.items, key=lambda x: x.timestamp, reverse=True)

        selected_item = sorted_items[0]
        reasoning = [
            f"Selected most recent information from {selected_item.source_id}",
            f"Timestamp: {selected_item.timestamp}",
            f"Rejected {len(conflict.items) - 1} older items"
        ]

        return ResolutionResult(
            conflict=conflict,
            resolved_content=selected_item.content,
            resolution_strategy=ResolutionStrategy.RECENCY,
            confidence=0.7,
            reasoning=reasoning,
            selected_items=[selected_item],
            metadata={"selection_criterion": "most_recent"}
        )

    def _resolve_by_authority(self,
                            conflict: ConflictInstance,
                            context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict by selecting information from the most authoritative source.
        """
        # Sort items by source authority (highest first)
        sorted_items = sorted(
            conflict.items,
            key=lambda x: self.source_authority.get(x.source_type, 0.5),
            reverse=True
        )

        selected_item = sorted_items[0]
        authority_score = self.source_authority.get(selected_item.source_type, 0.5)

        reasoning = [
            f"Selected information from most authoritative source: {selected_item.source_type}",
            f"Authority score: {authority_score}",
            f"Source: {selected_item.source_id}",
            f"Rejected {len(conflict.items) - 1} items from less authoritative sources"
        ]

        return ResolutionResult(
            conflict=conflict,
            resolved_content=selected_item.content,
            resolution_strategy=ResolutionStrategy.AUTHORITY,
            confidence=authority_score,
            reasoning=reasoning,
            selected_items=[selected_item],
            metadata={"authority_score": authority_score}
        )

    def _resolve_by_majority(self,
                           conflict: ConflictInstance,
                           context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict by selecting the information supported by the majority.
        """
        # Group similar content
        content_groups = defaultdict(list)

        for item in conflict.items:
            # Simple grouping by exact content match
            # In a more sophisticated implementation, use semantic similarity
            content_groups[item.content].append(item)

        # Find the group with the most items
        majority_group = max(content_groups.values(), key=len)
        majority_content = majority_group[0].content

        # Calculate confidence based on majority size
        total_items = len(conflict.items)
        majority_size = len(majority_group)
        confidence = majority_size / total_items

        reasoning = [
            f"Selected information supported by majority: {majority_size}/{total_items} sources",
            f"Majority sources: {[item.source_id for item in majority_group]}",
            f"Confidence based on majority: {confidence:.2f}"
        ]

        return ResolutionResult(
            conflict=conflict,
            resolved_content=majority_content,
            resolution_strategy=ResolutionStrategy.MAJORITY,
            confidence=confidence,
            reasoning=reasoning,
            selected_items=majority_group,
            metadata={"majority_size": majority_size, "total_items": total_items}
        )

    def _resolve_by_confidence(self,
                             conflict: ConflictInstance,
                             context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict by selecting the information with the highest confidence.
        """
        # Sort items by confidence (highest first)
        sorted_items = sorted(conflict.items, key=lambda x: x.confidence, reverse=True)

        selected_item = sorted_items[0]

        reasoning = [
            f"Selected information with highest confidence: {selected_item.confidence}",
            f"Source: {selected_item.source_id}",
            f"Rejected {len(conflict.items) - 1} items with lower confidence"
        ]

        return ResolutionResult(
            conflict=conflict,
            resolved_content=selected_item.content,
            resolution_strategy=ResolutionStrategy.CONFIDENCE,
            confidence=selected_item.confidence,
            reasoning=reasoning,
            selected_items=[selected_item],
            metadata={"selected_confidence": selected_item.confidence}
        )

    def _resolve_by_evidence(self,
                           conflict: ConflictInstance,
                           context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict by selecting the information with the strongest evidence.
        """
        # Calculate evidence strength for each item
        evidence_scores = []

        for item in conflict.items:
            # Evidence strength based on multiple factors
            authority = self.source_authority.get(item.source_type, 0.5)
            confidence = item.confidence
            reliability_weight = {"high": 1.0, "medium": 0.7, "low": 0.4}.get(item.reliability, 0.5)

            # Bonus for metadata richness
            metadata_bonus = min(0.2, len(item.metadata) * 0.05)

            evidence_score = (authority * 0.4 + confidence * 0.3 + reliability_weight * 0.3) + metadata_bonus
            evidence_scores.append((item, evidence_score))

        # Select item with highest evidence score
        selected_item, best_score = max(evidence_scores, key=lambda x: x[1])

        reasoning = [
            f"Selected information with strongest evidence score: {best_score:.3f}",
            f"Evidence factors: authority={self.source_authority.get(selected_item.source_type, 0.5):.2f}, "
            f"confidence={selected_item.confidence:.2f}, reliability={selected_item.reliability}",
            f"Source: {selected_item.source_id}"
        ]

        return ResolutionResult(
            conflict=conflict,
            resolved_content=selected_item.content,
            resolution_strategy=ResolutionStrategy.EVIDENCE,
            confidence=best_score,
            reasoning=reasoning,
            selected_items=[selected_item],
            metadata={"evidence_score": best_score}
        )

    def _resolve_by_context(self,
                          conflict: ConflictInstance,
                          context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict by selecting the information most relevant to the context.
        """
        # Calculate context relevance for each item
        context_scores = []

        for item in conflict.items:
            # Simple context matching based on keyword overlap
            context_score = self._calculate_context_relevance(item, context)
            context_scores.append((item, context_score))

        # Select item with highest context relevance
        selected_item, best_score = max(context_scores, key=lambda x: x[1])

        reasoning = [
            f"Selected information most relevant to context: {best_score:.3f}",
            f"Context factors considered: {list(context.keys())}",
            f"Source: {selected_item.source_id}"
        ]

        return ResolutionResult(
            conflict=conflict,
            resolved_content=selected_item.content,
            resolution_strategy=ResolutionStrategy.CONTEXT,
            confidence=best_score,
            reasoning=reasoning,
            selected_items=[selected_item],
            metadata={"context_score": best_score}
        )

    def _resolve_by_hybrid(self,
                         conflict: ConflictInstance,
                         context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict using a hybrid approach combining multiple strategies.
        """
        # Apply multiple resolution strategies
        strategies = [
            ResolutionStrategy.AUTHORITY,
            ResolutionStrategy.CONFIDENCE,
            ResolutionStrategy.EVIDENCE,
            ResolutionStrategy.RECENCY
        ]

        strategy_results = []
        for strategy in strategies:
            resolver = self.resolution_strategies[strategy]
            result = resolver(conflict, context)
            strategy_results.append((strategy, result))

        # Score each item based on how often it's selected
        item_scores = defaultdict(float)
        reasoning = ["Hybrid resolution using multiple strategies:"]

        for strategy, result in strategy_results:
            for selected_item in result.selected_items:
                item_scores[selected_item.content_hash] += result.confidence
                reasoning.append(f"  {strategy.value}: selected {selected_item.source_id} "
                               f"(confidence: {result.confidence:.3f})")

        # Find the item with the highest combined score
        best_item = None
        best_score = 0

        for item in conflict.items:
            score = item_scores.get(item.content_hash, 0)
            if score > best_score:
                best_score = score
                best_item = item

        # Normalize confidence
        max_possible_score = len(strategies)
        normalized_confidence = min(best_score / max_possible_score, 1.0)

        reasoning.append(f"Selected item with highest combined score: {best_score:.3f}")
        reasoning.append(f"Normalized confidence: {normalized_confidence:.3f}")

        return ResolutionResult(
            conflict=conflict,
            resolved_content=best_item.content if best_item else "",
            resolution_strategy=ResolutionStrategy.HYBRID,
            confidence=normalized_confidence,
            reasoning=reasoning,
            selected_items=[best_item] if best_item else [],
            metadata={"combined_score": best_score, "strategies_used": len(strategies)}
        )

    def _resolve_by_escalation(self,
                             conflict: ConflictInstance,
                             context: Dict[str, Any]) -> ResolutionResult:
        """
        Resolve conflict by escalating to human review.
        """
        reasoning = [
            "Conflict requires human review due to:",
            f"  - High severity: {conflict.severity}",
            f"  - Complex conflict type: {conflict.conflict_type_str}",
            f"  - Multiple conflicting sources: {len(conflict.items)}",
            "Escalating to human expert for resolution"
        ]

        return ResolutionResult(
            conflict=conflict,
            resolved_content="[ESCALATED TO HUMAN REVIEW]",
            resolution_strategy=ResolutionStrategy.ESCALATION,
            confidence=0.0,
            reasoning=reasoning,
            selected_items=[],
            metadata={"escalation_reason": "complex_conflict", "requires_human_review": True}
        )

    # Helper methods

    def _group_by_entities(self, knowledge_items: List[KnowledgeItem]) -> Dict[str, List[KnowledgeItem]]:
        """Group knowledge items by entities they mention."""
        entity_groups = defaultdict(list)

        for item in knowledge_items:
            # Extract entities from the item
            entities = self._extract_entities(item.content)

            # Add item to each entity group
            for entity in entities:
                entity_groups[entity].append(item)

        return entity_groups

    def _extract_entities(self, content: str) -> List[str]:
        """Extract entities from content (simplified implementation)."""
        # Simple entity extraction based on capitalized words
        words = content.split()
        entities = []

        for word in words:
            # Consider capitalized words as potential entities
            if word[0].isupper() and len(word) > 2 and word.isalpha():
                entities.append(word.lower())

        return list(set(entities))  # Remove duplicates

    def _check_factual_contradiction(self, content1: str, content2: str) -> Optional[str]:
        """Check if two content pieces contain factual contradictions."""
        # Simplified contradiction detection
        # Look for opposite statements

        # Extract key phrases
        phrases1 = self._extract_key_phrases(content1)
        phrases2 = self._extract_key_phrases(content2)

        # Look for contradictory patterns
        for phrase1 in phrases1:
            for phrase2 in phrases2:
                if self._are_contradictory(phrase1, phrase2):
                    return f"'{phrase1}' contradicts '{phrase2}'"

        return None

    def _check_temporal_contradiction(self, content1: str, content2: str) -> Optional[str]:
        """Check if two content pieces contain temporal contradictions."""
        # Simplified temporal contradiction detection
        # This would be more sophisticated in a real implementation

        # Look for conflicting temporal indicators
        temporal_indicators = ["before", "after", "during", "while", "when", "then"]

        for indicator in temporal_indicators:
            if indicator in content1.lower() and indicator in content2.lower():
                # Simple check for potential contradiction
                if "not" in content1.lower() or "not" in content2.lower():
                    return f"Temporal contradiction involving '{indicator}'"

        return None

    def _extract_key_phrases(self, content: str) -> List[str]:
        """Extract key phrases from content."""
        # Simple phrase extraction
        sentences = content.split('.')
        phrases = []

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # Only consider substantial sentences
                phrases.append(sentence.lower())

        return phrases

    def _are_contradictory(self, phrase1: str, phrase2: str) -> bool:
        """Check if two phrases are contradictory."""
        # Simple contradiction detection
        # Look for negation patterns

        # Remove common words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}

        words1 = set(phrase1.split()) - stop_words
        words2 = set(phrase2.split()) - stop_words

        # Check for negation patterns
        negation_words = {"not", "no", "never", "none", "nothing", "isn't", "aren't", "doesn't", "don't", "won't", "can't"}

        has_negation1 = bool(words1.intersection(negation_words))
        has_negation2 = bool(words2.intersection(negation_words))

        # If one has negation and they share common words, might be contradictory
        if has_negation1 != has_negation2:  # XOR - one has negation, other doesn't
            common_words = words1.intersection(words2)
            if len(common_words) > 1:  # Significant overlap
                return True

        return False

    def _calculate_context_relevance(self, item: KnowledgeItem, context: Dict[str, Any]) -> float:
        """Calculate how relevant an item is to the given context."""
        if not context:
            return 0.5  # Neutral relevance if no context

        relevance_score = 0.0
        total_factors = 0

        # Check context keywords in content
        for key, value in context.items():
            if isinstance(value, str):
                if value.lower() in item.content.lower():
                    relevance_score += 1.0
                total_factors += 1
            elif isinstance(value, list):
                for v in value:
                    if str(v).lower() in item.content.lower():
                        relevance_score += 0.5
                    total_factors += 0.5

        # Check metadata relevance
        for key, value in context.items():
            if key in item.metadata:
                if str(item.metadata[key]).lower() == str(value).lower():
                    relevance_score += 0.5
                total_factors += 0.5

        return relevance_score / max(total_factors, 1.0)

    # Public utility methods

    def get_conflict_statistics(self) -> Dict[str, Any]:
        """Get statistics about detected conflicts."""
        if not self.conflict_history:
            return {"total_conflicts": 0}

        conflict_types = defaultdict(int)
        severity_sum = 0

        for conflict in self.conflict_history:
            conflict_types[conflict.conflict_type_str] += 1
            severity_sum += conflict.severity

        return {
            "total_conflicts": len(self.conflict_history),
            "conflict_types": dict(conflict_types),
            "average_severity": severity_sum / len(self.conflict_history),
            "resolutions_performed": len(self.resolution_history)
        }

    def get_resolution_statistics(self) -> Dict[str, Any]:
        """Get statistics about conflict resolutions."""
        if not self.resolution_history:
            return {"total_resolutions": 0}

        strategy_counts = defaultdict(int)
        confidence_sum = 0

        for resolution in self.resolution_history:
            strategy_counts[resolution.resolution_strategy_str] += 1
            confidence_sum += resolution.confidence

        return {
            "total_resolutions": len(self.resolution_history),
            "strategy_distribution": dict(strategy_counts),
            "average_confidence": confidence_sum / len(self.resolution_history),
            "escalations": strategy_counts.get("escalation", 0)
        }
