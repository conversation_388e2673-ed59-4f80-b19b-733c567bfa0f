"""
Cross-document Coreference Component for the Knowledge Fusion System.
Identifies and resolves entity references across multiple documents.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime
import json
import logging
import re
from collections import defaultdict, Counter
import hashlib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EntityType(Enum):
    """Types of entities that can be identified."""
    PERSON = "person"
    ORGANIZATION = "organization"
    LOCATION = "location"
    DATE = "date"
    TIME = "time"
    MONEY = "money"
    PERCENTAGE = "percentage"
    PRODUCT = "product"
    EVENT = "event"
    CONCEPT = "concept"
    POLICY = "policy"
    PROCEDURE = "procedure"

class CoreferenceType(Enum):
    """Types of coreference relationships."""
    EXACT_MATCH = "exact_match"
    ALIAS = "alias"
    ABBREVIATION = "abbreviation"
    PARTIAL_MATCH = "partial_match"
    SEMANTIC_EQUIVALENT = "semantic_equivalent"
    HIERARCHICAL = "hierarchical"
    TEMPORAL_VARIANT = "temporal_variant"

@dataclass
class EntityMention:
    """Represents a mention of an entity in a document."""
    text: str
    entity_type: EntityType
    document_id: str
    start_pos: int
    end_pos: int
    context: str = ""
    confidence: float = 0.5
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.entity_type, EntityType):
            self.entity_type_str = self.entity_type.value
        else:
            self.entity_type_str = str(self.entity_type)
            self.entity_type = EntityType(self.entity_type_str)
        
        # Generate a unique ID for this mention
        self.mention_id = hashlib.md5(
            f"{self.document_id}_{self.start_pos}_{self.end_pos}_{self.text}".encode()
        ).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "mention_id": self.mention_id,
            "text": self.text,
            "entity_type": self.entity_type_str,
            "document_id": self.document_id,
            "start_pos": self.start_pos,
            "end_pos": self.end_pos,
            "context": self.context,
            "confidence": self.confidence,
            "metadata": self.metadata
        }

@dataclass
class EntityCluster:
    """Represents a cluster of coreferent entity mentions."""
    cluster_id: str
    canonical_name: str
    entity_type: EntityType
    mentions: List[EntityMention] = field(default_factory=list)
    confidence: float = 0.5
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.entity_type, EntityType):
            self.entity_type_str = self.entity_type.value
        else:
            self.entity_type_str = str(self.entity_type)
            self.entity_type = EntityType(self.entity_type_str)
    
    def add_mention(self, mention: EntityMention):
        """Add a mention to this cluster."""
        if mention not in self.mentions:
            self.mentions.append(mention)
            # Update confidence based on number of mentions
            self.confidence = min(0.95, 0.5 + (len(self.mentions) * 0.1))
    
    def get_document_coverage(self) -> Set[str]:
        """Get the set of documents covered by this cluster."""
        return set(mention.document_id for mention in self.mentions)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "cluster_id": self.cluster_id,
            "canonical_name": self.canonical_name,
            "entity_type": self.entity_type_str,
            "mentions": [mention.to_dict() for mention in self.mentions],
            "confidence": self.confidence,
            "document_coverage": list(self.get_document_coverage()),
            "metadata": self.metadata
        }

@dataclass
class CoreferenceLink:
    """Represents a coreference link between two entity mentions."""
    mention1: EntityMention
    mention2: EntityMention
    coreference_type: CoreferenceType
    confidence: float
    reasoning: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.coreference_type, CoreferenceType):
            self.coreference_type_str = self.coreference_type.value
        else:
            self.coreference_type_str = str(self.coreference_type)
            self.coreference_type = CoreferenceType(self.coreference_type_str)
        
        # Generate a unique ID for this link
        self.link_id = hashlib.md5(
            f"{self.mention1.mention_id}_{self.mention2.mention_id}".encode()
        ).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "link_id": self.link_id,
            "mention1": self.mention1.to_dict(),
            "mention2": self.mention2.to_dict(),
            "coreference_type": self.coreference_type_str,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "metadata": self.metadata
        }

class CrossDocumentCoreference:
    """
    Implements cross-document coreference resolution.
    Identifies and links entity mentions across multiple documents.
    """
    
    def __init__(self, 
                 similarity_threshold: float = 0.7,
                 clustering_threshold: float = 0.8):
        """Initialize the cross-document coreference component."""
        self.similarity_threshold = similarity_threshold
        self.clustering_threshold = clustering_threshold
        
        # Entity patterns for different types
        self.entity_patterns = {
            EntityType.PERSON: [
                r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',  # First Last
                r'\b(?:Mr|Mrs|Ms|Dr|Prof)\.?\s+[A-Z][a-z]+\b',  # Title Name
            ],
            EntityType.ORGANIZATION: [
                r'\b[A-Z][A-Za-z\s]+(?:Inc|Corp|Ltd|LLC|Company|Organization|Pvt Ltd)\b',
                r'\b[A-Z]{2,}(?:\s+[A-Z]{2,})*\b',  # Acronyms
            ],
            EntityType.LOCATION: [
                r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+(?:City|State|Country|Street|Avenue|Road))\b',
            ],
            EntityType.DATE: [
                r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',
                r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b',
            ],
            EntityType.MONEY: [
                r'\$\d+(?:,\d{3})*(?:\.\d{2})?\b',
                r'\b\d+(?:,\d{3})*(?:\.\d{2})?\s+(?:dollars?|USD|INR|rupees?)\b',
            ],
            EntityType.PERCENTAGE: [
                r'\b\d+(?:\.\d+)?%\b',
                r'\b\d+(?:\.\d+)?\s+percent\b',
            ]
        }
        
        # Coreference resolution strategies
        self.resolution_strategies = {
            CoreferenceType.EXACT_MATCH: self._resolve_exact_match,
            CoreferenceType.ALIAS: self._resolve_alias,
            CoreferenceType.ABBREVIATION: self._resolve_abbreviation,
            CoreferenceType.PARTIAL_MATCH: self._resolve_partial_match,
            CoreferenceType.SEMANTIC_EQUIVALENT: self._resolve_semantic_equivalent
        }
        
        # Storage for mentions and clusters
        self.entity_mentions: List[EntityMention] = []
        self.entity_clusters: List[EntityCluster] = []
        self.coreference_links: List[CoreferenceLink] = []
        
        logger.info("Cross-document Coreference component initialized")
    
    def extract_entity_mentions(self, 
                               documents: List[Dict[str, Any]]) -> List[EntityMention]:
        """
        Extract entity mentions from a list of documents.
        
        Args:
            documents: List of documents with 'id' and 'content' fields
            
        Returns:
            List of extracted entity mentions
        """
        all_mentions = []
        
        for doc in documents:
            doc_id = doc.get('id', 'unknown')
            content = doc.get('content', '')
            
            # Extract mentions for each entity type
            for entity_type, patterns in self.entity_patterns.items():
                mentions = self._extract_mentions_by_type(content, doc_id, entity_type, patterns)
                all_mentions.extend(mentions)
        
        # Store mentions
        self.entity_mentions = all_mentions
        
        logger.info(f"Extracted {len(all_mentions)} entity mentions from {len(documents)} documents")
        return all_mentions
    
    def resolve_coreferences(self, 
                           mentions: Optional[List[EntityMention]] = None) -> List[CoreferenceLink]:
        """
        Resolve coreferences between entity mentions.
        
        Args:
            mentions: Optional list of mentions to process (uses stored mentions if None)
            
        Returns:
            List of coreference links
        """
        if mentions is None:
            mentions = self.entity_mentions
        
        if not mentions:
            logger.warning("No entity mentions provided for coreference resolution")
            return []
        
        coreference_links = []
        
        # Group mentions by entity type for efficiency
        mentions_by_type = defaultdict(list)
        for mention in mentions:
            mentions_by_type[mention.entity_type].append(mention)
        
        # Resolve coreferences within each entity type
        for entity_type, type_mentions in mentions_by_type.items():
            logger.info(f"Resolving coreferences for {len(type_mentions)} {entity_type.value} mentions")
            
            # Compare all pairs of mentions
            for i in range(len(type_mentions)):
                for j in range(i + 1, len(type_mentions)):
                    mention1, mention2 = type_mentions[i], type_mentions[j]
                    
                    # Skip if from the same document (intra-document coreference)
                    if mention1.document_id == mention2.document_id:
                        continue
                    
                    # Check for coreference
                    link = self._check_coreference(mention1, mention2)
                    if link:
                        coreference_links.append(link)
        
        # Store links
        self.coreference_links = coreference_links
        
        logger.info(f"Resolved {len(coreference_links)} coreference links")
        return coreference_links
    
    def cluster_entities(self, 
                        links: Optional[List[CoreferenceLink]] = None) -> List[EntityCluster]:
        """
        Cluster entity mentions based on coreference links.
        
        Args:
            links: Optional list of coreference links (uses stored links if None)
            
        Returns:
            List of entity clusters
        """
        if links is None:
            links = self.coreference_links
        
        if not links:
            logger.warning("No coreference links provided for clustering")
            return []
        
        # Build a graph of mentions connected by coreference links
        mention_graph = defaultdict(set)
        all_mentions = set()
        
        for link in links:
            if link.confidence >= self.clustering_threshold:
                mention1_id = link.mention1.mention_id
                mention2_id = link.mention2.mention_id
                
                mention_graph[mention1_id].add(mention2_id)
                mention_graph[mention2_id].add(mention1_id)
                
                all_mentions.add(mention1_id)
                all_mentions.add(mention2_id)
        
        # Find connected components (clusters)
        visited = set()
        clusters = []
        
        # Create a mapping from mention_id to mention object
        mention_map = {mention.mention_id: mention for mention in self.entity_mentions}
        
        for mention_id in all_mentions:
            if mention_id not in visited:
                # Find all mentions in this connected component
                cluster_mention_ids = self._find_connected_component(mention_id, mention_graph, visited)
                
                if cluster_mention_ids:
                    # Create cluster
                    cluster_mentions = [mention_map[mid] for mid in cluster_mention_ids if mid in mention_map]
                    
                    if cluster_mentions:
                        # Determine canonical name (most frequent or longest)
                        canonical_name = self._determine_canonical_name(cluster_mentions)
                        
                        # Create cluster
                        cluster_id = f"cluster_{len(clusters) + 1:04d}"
                        cluster = EntityCluster(
                            cluster_id=cluster_id,
                            canonical_name=canonical_name,
                            entity_type=cluster_mentions[0].entity_type,
                            mentions=cluster_mentions,
                            confidence=min(0.95, 0.5 + (len(cluster_mentions) * 0.1)),
                            metadata={
                                "creation_method": "coreference_clustering",
                                "document_count": len(set(m.document_id for m in cluster_mentions))
                            }
                        )
                        
                        clusters.append(cluster)
        
        # Store clusters
        self.entity_clusters = clusters
        
        logger.info(f"Created {len(clusters)} entity clusters")
        return clusters

    # Helper methods for entity extraction and coreference resolution

    def _extract_mentions_by_type(self,
                                content: str,
                                doc_id: str,
                                entity_type: EntityType,
                                patterns: List[str]) -> List[EntityMention]:
        """Extract entity mentions of a specific type from content."""
        mentions = []

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)

            for match in matches:
                text = match.group().strip()
                start_pos = match.start()
                end_pos = match.end()

                # Extract context (surrounding text)
                context_start = max(0, start_pos - 50)
                context_end = min(len(content), end_pos + 50)
                context = content[context_start:context_end].strip()

                # Calculate confidence based on pattern specificity
                confidence = self._calculate_extraction_confidence(text, entity_type, pattern)

                mention = EntityMention(
                    text=text,
                    entity_type=entity_type,
                    document_id=doc_id,
                    start_pos=start_pos,
                    end_pos=end_pos,
                    context=context,
                    confidence=confidence,
                    metadata={
                        "extraction_pattern": pattern,
                        "context_length": len(context)
                    }
                )

                mentions.append(mention)

        return mentions

    def _calculate_extraction_confidence(self,
                                       text: str,
                                       entity_type: EntityType,
                                       pattern: str) -> float:
        """Calculate confidence for an extracted entity mention."""
        base_confidence = 0.6

        # Boost confidence for longer entities
        if len(text) > 10:
            base_confidence += 0.1

        # Boost confidence for specific entity types
        if entity_type in [EntityType.DATE, EntityType.MONEY, EntityType.PERCENTAGE]:
            base_confidence += 0.2  # These have more specific patterns

        # Boost confidence for capitalized entities
        if text[0].isupper():
            base_confidence += 0.1

        return min(0.95, base_confidence)

    def _check_coreference(self,
                         mention1: EntityMention,
                         mention2: EntityMention) -> Optional[CoreferenceLink]:
        """Check if two mentions are coreferent."""
        # Must be the same entity type
        if mention1.entity_type != mention2.entity_type:
            return None

        # Try different coreference resolution strategies
        for coreference_type, resolver in self.resolution_strategies.items():
            result = resolver(mention1, mention2)

            if result and result.confidence >= self.similarity_threshold:
                return result

        return None

    def _resolve_exact_match(self,
                           mention1: EntityMention,
                           mention2: EntityMention) -> Optional[CoreferenceLink]:
        """Resolve coreference based on exact text match."""
        if mention1.text.lower() == mention2.text.lower():
            return CoreferenceLink(
                mention1=mention1,
                mention2=mention2,
                coreference_type=CoreferenceType.EXACT_MATCH,
                confidence=0.95,
                reasoning=["Exact text match"],
                metadata={"match_type": "exact"}
            )

        return None

    def _resolve_alias(self,
                     mention1: EntityMention,
                     mention2: EntityMention) -> Optional[CoreferenceLink]:
        """Resolve coreference based on alias relationships."""
        text1, text2 = mention1.text.lower(), mention2.text.lower()

        # Check for common alias patterns
        alias_patterns = [
            # Organization aliases
            (r'(.+)\s+(?:inc|corp|ltd|llc|company|pvt ltd)', r'\1'),
            # Person aliases (first name vs full name)
            (r'([a-z]+)\s+[a-z]+', r'\1'),
            # Abbreviations
            (r'([a-z])[a-z]*(?:\s+([a-z])[a-z]*)*', r'\1\2')
        ]

        for pattern, replacement in alias_patterns:
            # Try both directions
            if re.match(pattern, text1) and re.match(pattern, text2):
                normalized1 = re.sub(pattern, replacement, text1)
                normalized2 = re.sub(pattern, replacement, text2)

                if normalized1 == normalized2:
                    return CoreferenceLink(
                        mention1=mention1,
                        mention2=mention2,
                        coreference_type=CoreferenceType.ALIAS,
                        confidence=0.8,
                        reasoning=[f"Alias match: {normalized1}"],
                        metadata={"alias_pattern": pattern}
                    )

        return None

    def _resolve_abbreviation(self,
                            mention1: EntityMention,
                            mention2: EntityMention) -> Optional[CoreferenceLink]:
        """Resolve coreference based on abbreviation relationships."""
        text1, text2 = mention1.text, mention2.text

        # Check if one is an abbreviation of the other
        if self._is_abbreviation(text1, text2):
            return CoreferenceLink(
                mention1=mention1,
                mention2=mention2,
                coreference_type=CoreferenceType.ABBREVIATION,
                confidence=0.85,
                reasoning=[f"'{text1}' is abbreviation of '{text2}'"],
                metadata={"abbreviation_direction": "1_to_2"}
            )
        elif self._is_abbreviation(text2, text1):
            return CoreferenceLink(
                mention1=mention1,
                mention2=mention2,
                coreference_type=CoreferenceType.ABBREVIATION,
                confidence=0.85,
                reasoning=[f"'{text2}' is abbreviation of '{text1}'"],
                metadata={"abbreviation_direction": "2_to_1"}
            )

        return None

    def _resolve_partial_match(self,
                             mention1: EntityMention,
                             mention2: EntityMention) -> Optional[CoreferenceLink]:
        """Resolve coreference based on partial text match."""
        text1, text2 = mention1.text.lower(), mention2.text.lower()

        # Calculate word overlap
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return None

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        overlap_ratio = len(intersection) / len(union)

        if overlap_ratio >= 0.6:  # 60% word overlap threshold
            return CoreferenceLink(
                mention1=mention1,
                mention2=mention2,
                coreference_type=CoreferenceType.PARTIAL_MATCH,
                confidence=min(0.8, overlap_ratio + 0.1),
                reasoning=[f"Partial match: {overlap_ratio:.2f} word overlap"],
                metadata={"overlap_ratio": overlap_ratio, "common_words": list(intersection)}
            )

        return None

    def _resolve_semantic_equivalent(self,
                                   mention1: EntityMention,
                                   mention2: EntityMention) -> Optional[CoreferenceLink]:
        """Resolve coreference based on semantic equivalence."""
        # This is a simplified implementation
        # In a real system, this would use semantic embeddings or knowledge bases

        text1, text2 = mention1.text.lower(), mention2.text.lower()

        # Define some semantic equivalence patterns
        semantic_equivalents = {
            # Organizations
            "company": ["corporation", "corp", "inc", "business", "firm"],
            "university": ["college", "institute", "school"],
            # Locations
            "city": ["town", "municipality"],
            "country": ["nation", "state"],
            # Time
            "morning": ["am", "a.m."],
            "evening": ["pm", "p.m."],
        }

        # Check for semantic equivalence
        for base_term, equivalents in semantic_equivalents.items():
            if base_term in text1 and any(equiv in text2 for equiv in equivalents):
                return CoreferenceLink(
                    mention1=mention1,
                    mention2=mention2,
                    coreference_type=CoreferenceType.SEMANTIC_EQUIVALENT,
                    confidence=0.7,
                    reasoning=[f"Semantic equivalence: {base_term} ~ {text2}"],
                    metadata={"base_term": base_term}
                )
            elif base_term in text2 and any(equiv in text1 for equiv in equivalents):
                return CoreferenceLink(
                    mention1=mention1,
                    mention2=mention2,
                    coreference_type=CoreferenceType.SEMANTIC_EQUIVALENT,
                    confidence=0.7,
                    reasoning=[f"Semantic equivalence: {base_term} ~ {text1}"],
                    metadata={"base_term": base_term}
                )

        return None

    def _is_abbreviation(self, short_text: str, long_text: str) -> bool:
        """Check if short_text is an abbreviation of long_text."""
        if len(short_text) >= len(long_text):
            return False

        # Simple abbreviation check: first letters of words
        long_words = long_text.split()
        if len(short_text.replace('.', '')) == len(long_words):
            # Check if short_text matches first letters
            first_letters = ''.join(word[0].upper() for word in long_words)
            return short_text.upper().replace('.', '') == first_letters

        return False

    def _find_connected_component(self,
                                start_id: str,
                                graph: Dict[str, Set[str]],
                                visited: Set[str]) -> Set[str]:
        """Find all nodes in the connected component containing start_id."""
        component = set()
        stack = [start_id]

        while stack:
            node_id = stack.pop()
            if node_id not in visited:
                visited.add(node_id)
                component.add(node_id)

                # Add neighbors to stack
                for neighbor in graph.get(node_id, set()):
                    if neighbor not in visited:
                        stack.append(neighbor)

        return component

    def _determine_canonical_name(self, mentions: List[EntityMention]) -> str:
        """Determine the canonical name for a cluster of mentions."""
        # Count frequency of each text
        text_counts = Counter(mention.text for mention in mentions)

        # Get the most frequent text
        most_frequent = text_counts.most_common(1)[0][0]

        # If there's a tie, choose the longest one
        tied_texts = [text for text, count in text_counts.items() if count == text_counts[most_frequent]]

        if len(tied_texts) > 1:
            return max(tied_texts, key=len)
        else:
            return most_frequent

    # Public utility methods

    def get_entity_statistics(self) -> Dict[str, Any]:
        """Get statistics about extracted entities and coreferences."""
        if not self.entity_mentions:
            return {"total_mentions": 0}

        # Count by entity type
        type_counts = Counter(mention.entity_type_str for mention in self.entity_mentions)

        # Count by document
        doc_counts = Counter(mention.document_id for mention in self.entity_mentions)

        # Coreference statistics
        coreference_type_counts = Counter(link.coreference_type_str for link in self.coreference_links)

        return {
            "total_mentions": len(self.entity_mentions),
            "entity_type_distribution": dict(type_counts),
            "document_distribution": dict(doc_counts),
            "total_coreference_links": len(self.coreference_links),
            "coreference_type_distribution": dict(coreference_type_counts),
            "total_clusters": len(self.entity_clusters),
            "average_cluster_size": np.mean([len(cluster.mentions) for cluster in self.entity_clusters]) if self.entity_clusters else 0.0
        }

    def get_cluster_by_canonical_name(self, canonical_name: str) -> Optional[EntityCluster]:
        """Get an entity cluster by its canonical name."""
        for cluster in self.entity_clusters:
            if cluster.canonical_name.lower() == canonical_name.lower():
                return cluster
        return None

    def get_mentions_by_document(self, document_id: str) -> List[EntityMention]:
        """Get all entity mentions from a specific document."""
        return [mention for mention in self.entity_mentions if mention.document_id == document_id]

    def export_coreference_data(self) -> Dict[str, Any]:
        """Export all coreference data for serialization."""
        return {
            "mentions": [mention.to_dict() for mention in self.entity_mentions],
            "links": [link.to_dict() for link in self.coreference_links],
            "clusters": [cluster.to_dict() for cluster in self.entity_clusters],
            "statistics": self.get_entity_statistics(),
            "configuration": {
                "similarity_threshold": self.similarity_threshold,
                "clustering_threshold": self.clustering_threshold
            }
        }
