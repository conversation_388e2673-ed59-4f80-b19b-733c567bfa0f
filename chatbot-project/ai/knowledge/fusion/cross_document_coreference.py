"""
Cross-document Coreference Component for the Knowledge Fusion System.
Identifies and resolves entity references across multiple documents.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime
import json
import logging
import re
from collections import defaultdict, Counter
import hashlib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EntityType(Enum):
    """Types of entities that can be identified."""
    PERSON = "person"
    ORGANIZATION = "organization"
    LOCATION = "location"
    DATE = "date"
    TIME = "time"
    MONEY = "money"
    PERCENTAGE = "percentage"
    PRODUCT = "product"
    EVENT = "event"
    CONCEPT = "concept"
    POLICY = "policy"
    PROCEDURE = "procedure"

class CoreferenceType(Enum):
    """Types of coreference relationships."""
    EXACT_MATCH = "exact_match"
    ALIAS = "alias"
    ABBREVIATION = "abbreviation"
    PARTIAL_MATCH = "partial_match"
    SEMANTIC_EQUIVALENT = "semantic_equivalent"
    HIERARCHICAL = "hierarchical"
    TEMPORAL_VARIANT = "temporal_variant"

@dataclass
class EntityMention:
    """Represents a mention of an entity in a document."""
    text: str
    entity_type: EntityType
    document_id: str
    start_pos: int
    end_pos: int
    context: str = ""
    confidence: float = 0.5
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.entity_type, EntityType):
            self.entity_type_str = self.entity_type.value
        else:
            self.entity_type_str = str(self.entity_type)
            self.entity_type = EntityType(self.entity_type_str)
        
        # Generate a unique ID for this mention
        self.mention_id = hashlib.md5(
            f"{self.document_id}_{self.start_pos}_{self.end_pos}_{self.text}".encode()
        ).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "mention_id": self.mention_id,
            "text": self.text,
            "entity_type": self.entity_type_str,
            "document_id": self.document_id,
            "start_pos": self.start_pos,
            "end_pos": self.end_pos,
            "context": self.context,
            "confidence": self.confidence,
            "metadata": self.metadata
        }

@dataclass
class EntityCluster:
    """Represents a cluster of coreferent entity mentions."""
    cluster_id: str
    canonical_name: str
    entity_type: EntityType
    mentions: List[EntityMention] = field(default_factory=list)
    confidence: float = 0.5
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.entity_type, EntityType):
            self.entity_type_str = self.entity_type.value
        else:
            self.entity_type_str = str(self.entity_type)
            self.entity_type = EntityType(self.entity_type_str)
    
    def add_mention(self, mention: EntityMention):
        """Add a mention to this cluster."""
        if mention not in self.mentions:
            self.mentions.append(mention)
            # Update confidence based on number of mentions
            self.confidence = min(0.95, 0.5 + (len(self.mentions) * 0.1))
    
    def get_document_coverage(self) -> Set[str]:
        """Get the set of documents covered by this cluster."""
        return set(mention.document_id for mention in self.mentions)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "cluster_id": self.cluster_id,
            "canonical_name": self.canonical_name,
            "entity_type": self.entity_type_str,
            "mentions": [mention.to_dict() for mention in self.mentions],
            "confidence": self.confidence,
            "document_coverage": list(self.get_document_coverage()),
            "metadata": self.metadata
        }

@dataclass
class CoreferenceLink:
    """Represents a coreference link between two entity mentions."""
    mention1: EntityMention
    mention2: EntityMention
    coreference_type: CoreferenceType
    confidence: float
    reasoning: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Process after initialization."""
        if isinstance(self.coreference_type, CoreferenceType):
            self.coreference_type_str = self.coreference_type.value
        else:
            self.coreference_type_str = str(self.coreference_type)
            self.coreference_type = CoreferenceType(self.coreference_type_str)
        
        # Generate a unique ID for this link
        self.link_id = hashlib.md5(
            f"{self.mention1.mention_id}_{self.mention2.mention_id}".encode()
        ).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "link_id": self.link_id,
            "mention1": self.mention1.to_dict(),
            "mention2": self.mention2.to_dict(),
            "coreference_type": self.coreference_type_str,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "metadata": self.metadata
        }

class CrossDocumentCoreference:
    """
    Implements cross-document coreference resolution.
    Identifies and links entity mentions across multiple documents.
    """
    
    def __init__(self, 
                 similarity_threshold: float = 0.7,
                 clustering_threshold: float = 0.8):
        """Initialize the cross-document coreference component."""
        self.similarity_threshold = similarity_threshold
        self.clustering_threshold = clustering_threshold
        
        # Entity patterns for different types
        self.entity_patterns = {
            EntityType.PERSON: [
                r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',  # First Last
                r'\b(?:Mr|Mrs|Ms|Dr|Prof)\.?\s+[A-Z][a-z]+\b',  # Title Name
            ],
            EntityType.ORGANIZATION: [
                r'\b[A-Z][A-Za-z\s]+(?:Inc|Corp|Ltd|LLC|Company|Organization|Pvt Ltd)\b',
                r'\b[A-Z]{2,}(?:\s+[A-Z]{2,})*\b',  # Acronyms
            ],
            EntityType.LOCATION: [
                r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+(?:City|State|Country|Street|Avenue|Road))\b',
            ],
            EntityType.DATE: [
                r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',
                r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b',
            ],
            EntityType.MONEY: [
                r'\$\d+(?:,\d{3})*(?:\.\d{2})?\b',
                r'\b\d+(?:,\d{3})*(?:\.\d{2})?\s+(?:dollars?|USD|INR|rupees?)\b',
            ],
            EntityType.PERCENTAGE: [
                r'\b\d+(?:\.\d+)?%\b',
                r'\b\d+(?:\.\d+)?\s+percent\b',
            ]
        }
        
        # Coreference resolution strategies
        self.resolution_strategies = {
            CoreferenceType.EXACT_MATCH: self._resolve_exact_match,
            CoreferenceType.ALIAS: self._resolve_alias,
            CoreferenceType.ABBREVIATION: self._resolve_abbreviation,
            CoreferenceType.PARTIAL_MATCH: self._resolve_partial_match,
            CoreferenceType.SEMANTIC_EQUIVALENT: self._resolve_semantic_equivalent
        }
        
        # Storage for mentions and clusters
        self.entity_mentions: List[EntityMention] = []
        self.entity_clusters: List[EntityCluster] = []
        self.coreference_links: List[CoreferenceLink] = []
        
        logger.info("Cross-document Coreference component initialized")
    
    def extract_entity_mentions(self, 
                               documents: List[Dict[str, Any]]) -> List[EntityMention]:
        """
        Extract entity mentions from a list of documents.
        
        Args:
            documents: List of documents with 'id' and 'content' fields
            
        Returns:
            List of extracted entity mentions
        """
        all_mentions = []
        
        for doc in documents:
            doc_id = doc.get('id', 'unknown')
            content = doc.get('content', '')
            
            # Extract mentions for each entity type
            for entity_type, patterns in self.entity_patterns.items():
                mentions = self._extract_mentions_by_type(content, doc_id, entity_type, patterns)
                all_mentions.extend(mentions)
        
        # Store mentions
        self.entity_mentions = all_mentions
        
        logger.info(f"Extracted {len(all_mentions)} entity mentions from {len(documents)} documents")
        return all_mentions
    
    def resolve_coreferences(self, 
                           mentions: Optional[List[EntityMention]] = None) -> List[CoreferenceLink]:
        """
        Resolve coreferences between entity mentions.
        
        Args:
            mentions: Optional list of mentions to process (uses stored mentions if None)
            
        Returns:
            List of coreference links
        """
        if mentions is None:
            mentions = self.entity_mentions
        
        if not mentions:
            logger.warning("No entity mentions provided for coreference resolution")
            return []
        
        coreference_links = []
        
        # Group mentions by entity type for efficiency
        mentions_by_type = defaultdict(list)
        for mention in mentions:
            mentions_by_type[mention.entity_type].append(mention)
        
        # Resolve coreferences within each entity type
        for entity_type, type_mentions in mentions_by_type.items():
            logger.info(f"Resolving coreferences for {len(type_mentions)} {entity_type.value} mentions")
            
            # Compare all pairs of mentions
            for i in range(len(type_mentions)):
                for j in range(i + 1, len(type_mentions)):
                    mention1, mention2 = type_mentions[i], type_mentions[j]
                    
                    # Skip if from the same document (intra-document coreference)
                    if mention1.document_id == mention2.document_id:
                        continue
                    
                    # Check for coreference
                    link = self._check_coreference(mention1, mention2)
                    if link:
                        coreference_links.append(link)
        
        # Store links
        self.coreference_links = coreference_links
        
        logger.info(f"Resolved {len(coreference_links)} coreference links")
        return coreference_links
    
    def cluster_entities(self, 
                        links: Optional[List[CoreferenceLink]] = None) -> List[EntityCluster]:
        """
        Cluster entity mentions based on coreference links.
        
        Args:
            links: Optional list of coreference links (uses stored links if None)
            
        Returns:
            List of entity clusters
        """
        if links is None:
            links = self.coreference_links
        
        if not links:
            logger.warning("No coreference links provided for clustering")
            return []
        
        # Build a graph of mentions connected by coreference links
        mention_graph = defaultdict(set)
        all_mentions = set()
        
        for link in links:
            if link.confidence >= self.clustering_threshold:
                mention1_id = link.mention1.mention_id
                mention2_id = link.mention2.mention_id
                
                mention_graph[mention1_id].add(mention2_id)
                mention_graph[mention2_id].add(mention1_id)
                
                all_mentions.add(mention1_id)
                all_mentions.add(mention2_id)
        
        # Find connected components (clusters)
        visited = set()
        clusters = []
        
        # Create a mapping from mention_id to mention object
        mention_map = {mention.mention_id: mention for mention in self.entity_mentions}
        
        for mention_id in all_mentions:
            if mention_id not in visited:
                # Find all mentions in this connected component
                cluster_mention_ids = self._find_connected_component(mention_id, mention_graph, visited)
                
                if cluster_mention_ids:
                    # Create cluster
                    cluster_mentions = [mention_map[mid] for mid in cluster_mention_ids if mid in mention_map]
                    
                    if cluster_mentions:
                        # Determine canonical name (most frequent or longest)
                        canonical_name = self._determine_canonical_name(cluster_mentions)
                        
                        # Create cluster
                        cluster_id = f"cluster_{len(clusters) + 1:04d}"
                        cluster = EntityCluster(
                            cluster_id=cluster_id,
                            canonical_name=canonical_name,
                            entity_type=cluster_mentions[0].entity_type,
                            mentions=cluster_mentions,
                            confidence=min(0.95, 0.5 + (len(cluster_mentions) * 0.1)),
                            metadata={
                                "creation_method": "coreference_clustering",
                                "document_count": len(set(m.document_id for m in cluster_mentions))
                            }
                        )
                        
                        clusters.append(cluster)
        
        # Store clusters
        self.entity_clusters = clusters
        
        logger.info(f"Created {len(clusters)} entity clusters")
        return clusters
