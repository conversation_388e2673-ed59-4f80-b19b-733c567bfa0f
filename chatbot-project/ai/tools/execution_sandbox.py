"""
Tool Execution Sandbox for secure and isolated tool execution.
Provides sandboxed environment for running tools with resource limits and security controls.
"""

import asyncio
import sys
import os
import subprocess
import tempfile
import shutil
import signal
import resource
import time
import json
import logging
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from contextlib import contextmanager
import threading
import multiprocessing
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, TimeoutError
import traceback

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SandboxMode(Enum):
    """Different sandbox execution modes."""
    THREAD = "thread"
    PROCESS = "process"
    CONTAINER = "container"
    RESTRICTED = "restricted"

class ExecutionStatus(Enum):
    """Execution status types."""
    SUCCESS = "success"
    FAILURE = "failure"
    TIMEOUT = "timeout"
    MEMORY_LIMIT = "memory_limit"
    SECURITY_VIOLATION = "security_violation"
    RESOURCE_LIMIT = "resource_limit"

@dataclass
class SandboxConfig:
    """Configuration for sandbox execution."""
    mode: SandboxMode = SandboxMode.THREAD
    timeout_seconds: float = 30.0
    memory_limit_mb: int = 512
    cpu_limit_percent: float = 50.0
    max_file_size_mb: int = 10
    allowed_imports: List[str] = field(default_factory=lambda: [
        'math', 'json', 'datetime', 'collections', 're', 'itertools',
        'functools', 'operator', 'statistics', 'random', 'uuid'
    ])
    blocked_imports: List[str] = field(default_factory=lambda: [
        'os', 'sys', 'subprocess', 'socket', 'urllib', 'requests',
        'shutil', 'tempfile', 'pickle', 'eval', 'exec', '__import__'
    ])
    enable_network: bool = False
    enable_file_system: bool = False
    working_directory: Optional[str] = None

@dataclass
class ExecutionResult:
    """Result of tool execution in sandbox."""
    status: ExecutionStatus
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    memory_used: float = 0.0
    stdout: str = ""
    stderr: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "execution_time": self.execution_time,
            "memory_used": self.memory_used,
            "stdout": self.stdout,
            "stderr": self.stderr,
            "metadata": self.metadata
        }

class SecurityValidator:
    """Validates code for security issues before execution."""
    
    def __init__(self, config: SandboxConfig):
        self.config = config
        
        # Dangerous patterns to detect
        self.dangerous_patterns = [
            r'__import__\s*\(',
            r'eval\s*\(',
            r'exec\s*\(',
            r'compile\s*\(',
            r'open\s*\(',
            r'file\s*\(',
            r'input\s*\(',
            r'raw_input\s*\(',
            r'globals\s*\(',
            r'locals\s*\(',
            r'vars\s*\(',
            r'dir\s*\(',
            r'getattr\s*\(',
            r'setattr\s*\(',
            r'delattr\s*\(',
            r'hasattr\s*\(',
        ]
    
    def validate_code(self, code: str) -> tuple[bool, List[str]]:
        """
        Validate code for security issues.
        
        Returns:
            Tuple of (is_safe, list_of_issues)
        """
        issues = []
        
        # Check for dangerous patterns
        import re
        for pattern in self.dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                issues.append(f"Dangerous pattern detected: {pattern}")
        
        # Check for blocked imports
        for blocked in self.config.blocked_imports:
            if f"import {blocked}" in code or f"from {blocked}" in code:
                issues.append(f"Blocked import detected: {blocked}")
        
        # Check for file operations if not allowed
        if not self.config.enable_file_system:
            file_operations = ['open(', 'file(', 'with open', 'os.path', 'shutil.']
            for op in file_operations:
                if op in code:
                    issues.append(f"File operation not allowed: {op}")
        
        # Check for network operations if not allowed
        if not self.config.enable_network:
            network_operations = ['urllib', 'requests', 'socket', 'http', 'ftp']
            for op in network_operations:
                if op in code:
                    issues.append(f"Network operation not allowed: {op}")
        
        return len(issues) == 0, issues

class ResourceMonitor:
    """Monitors resource usage during execution."""
    
    def __init__(self, config: SandboxConfig):
        self.config = config
        self.start_time = None
        self.peak_memory = 0.0
        
    def start_monitoring(self):
        """Start monitoring resources."""
        self.start_time = time.time()
        self.peak_memory = 0.0
        
        # Set memory limit if supported
        try:
            if hasattr(resource, 'RLIMIT_AS'):
                memory_limit = self.config.memory_limit_mb * 1024 * 1024
                resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
        except (OSError, ValueError) as e:
            logger.warning(f"Could not set memory limit: {e}")
    
    def check_limits(self) -> Optional[str]:
        """Check if any limits are exceeded."""
        # Check timeout
        if self.start_time and time.time() - self.start_time > self.config.timeout_seconds:
            return "Execution timeout exceeded"
        
        # Check memory usage
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / (1024 * 1024)
            self.peak_memory = max(self.peak_memory, memory_mb)
            
            if memory_mb > self.config.memory_limit_mb:
                return f"Memory limit exceeded: {memory_mb:.1f}MB > {self.config.memory_limit_mb}MB"
        except ImportError:
            # psutil not available, skip memory monitoring
            pass
        except Exception as e:
            logger.warning(f"Error monitoring memory: {e}")
        
        return None
    
    def get_stats(self) -> Dict[str, float]:
        """Get execution statistics."""
        execution_time = time.time() - self.start_time if self.start_time else 0.0
        return {
            "execution_time": execution_time,
            "peak_memory_mb": self.peak_memory
        }

class ToolExecutionSandbox:
    """
    Secure sandbox for executing tools with resource limits and security controls.
    """
    
    def __init__(self, config: Optional[SandboxConfig] = None):
        """Initialize the execution sandbox."""
        self.config = config or SandboxConfig()
        self.security_validator = SecurityValidator(self.config)
        
        # Execution statistics
        self.execution_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.total_execution_time = 0.0
        
        logger.info("Tool Execution Sandbox initialized")
    
    async def execute_tool(self, 
                          tool_function: Callable,
                          args: tuple = (),
                          kwargs: Dict[str, Any] = None) -> ExecutionResult:
        """
        Execute a tool function in the sandbox.
        
        Args:
            tool_function: The function to execute
            args: Positional arguments for the function
            kwargs: Keyword arguments for the function
            
        Returns:
            ExecutionResult with execution details
        """
        kwargs = kwargs or {}
        start_time = time.time()
        
        logger.info(f"Executing tool: {tool_function.__name__}")
        
        try:
            # Choose execution method based on sandbox mode
            if self.config.mode == SandboxMode.THREAD:
                result = await self._execute_in_thread(tool_function, args, kwargs)
            elif self.config.mode == SandboxMode.PROCESS:
                result = await self._execute_in_process(tool_function, args, kwargs)
            elif self.config.mode == SandboxMode.RESTRICTED:
                result = await self._execute_restricted(tool_function, args, kwargs)
            else:
                result = await self._execute_in_thread(tool_function, args, kwargs)  # Default
            
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            # Update statistics
            self.execution_count += 1
            self.total_execution_time += execution_time
            
            if result.status == "success":
                self.success_count += 1
            else:
                self.failure_count += 1

            logger.info(f"Tool execution completed: {result.status} in {execution_time:.3f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Tool execution failed: {e}")
            
            self.execution_count += 1
            self.failure_count += 1
            self.total_execution_time += execution_time
            
            return ExecutionResult(
                status="failure",
                error=str(e),
                execution_time=execution_time,
                metadata={"exception_type": type(e).__name__}
            )
    
    async def execute_code(self, 
                          code: str,
                          globals_dict: Optional[Dict[str, Any]] = None,
                          locals_dict: Optional[Dict[str, Any]] = None) -> ExecutionResult:
        """
        Execute arbitrary code in the sandbox.
        
        Args:
            code: Python code to execute
            globals_dict: Global variables for execution
            locals_dict: Local variables for execution
            
        Returns:
            ExecutionResult with execution details
        """
        start_time = time.time()
        
        logger.info("Executing code in sandbox")
        
        # Validate code security
        is_safe, issues = self.security_validator.validate_code(code)
        if not is_safe:
            return ExecutionResult(
                status="security_violation",
                error=f"Security validation failed: {'; '.join(issues)}",
                execution_time=time.time() - start_time
            )
        
        try:
            # Prepare execution environment
            if globals_dict is None:
                globals_dict = self._create_safe_globals()
            
            if locals_dict is None:
                locals_dict = {}
            
            # Execute based on sandbox mode
            if self.config.mode == SandboxMode.PROCESS:
                result = await self._execute_code_in_process(code, globals_dict, locals_dict)
            else:
                result = await self._execute_code_in_thread(code, globals_dict, locals_dict)
            
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            # Update statistics
            self.execution_count += 1
            self.total_execution_time += execution_time
            
            if result.status == "success":
                self.success_count += 1
            else:
                self.failure_count += 1

            logger.info(f"Code execution completed: {result.status} in {execution_time:.3f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Code execution failed: {e}")
            
            self.execution_count += 1
            self.failure_count += 1
            self.total_execution_time += execution_time
            
            return ExecutionResult(
                status="failure",
                error=str(e),
                execution_time=execution_time,
                metadata={"exception_type": type(e).__name__}
            )

    async def _execute_in_thread(self,
                               tool_function: Callable,
                               args: tuple,
                               kwargs: Dict[str, Any]) -> ExecutionResult:
        """Execute tool function in a separate thread."""
        monitor = ResourceMonitor(self.config)
        monitor.start_monitoring()

        def execute_with_monitoring():
            try:
                # Execute the function
                result = tool_function(*args, **kwargs)
                return result

            except Exception as e:
                raise e

        try:
            # Execute in thread pool with timeout
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(execute_with_monitoring)

                try:
                    result = future.result(timeout=self.config.timeout_seconds)
                    stats = monitor.get_stats()

                    return ExecutionResult(
                        status="success",
                        result=result,
                        execution_time=stats["execution_time"],
                        memory_used=stats["peak_memory_mb"]
                    )

                except TimeoutError:
                    return ExecutionResult(
                        status="timeout",
                        error=f"Execution timed out after {self.config.timeout_seconds}s",
                        execution_time=self.config.timeout_seconds
                    )

        except Exception as e:
            stats = monitor.get_stats()

            # Determine error type
            error_msg = str(e)
            if "timeout" in error_msg.lower():
                status = "timeout"
            elif "memory" in error_msg.lower():
                status = "memory_limit"
            else:
                status = "failure"

            return ExecutionResult(
                status=status,
                error=error_msg,
                execution_time=stats["execution_time"],
                memory_used=stats["peak_memory_mb"]
            )

    async def _execute_code_in_thread(self,
                                    code: str,
                                    globals_dict: Dict[str, Any],
                                    locals_dict: Dict[str, Any]) -> ExecutionResult:
        """Execute code in a separate thread."""
        monitor = ResourceMonitor(self.config)
        monitor.start_monitoring()

        def execute_code():
            try:
                # Compile and execute code
                compiled_code = compile(code, '<sandbox>', 'exec')
                exec(compiled_code, globals_dict, locals_dict)

                # Return locals as result
                return locals_dict

            except Exception as e:
                raise e

        try:
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(execute_code)

                try:
                    result = future.result(timeout=self.config.timeout_seconds)
                    stats = monitor.get_stats()

                    return ExecutionResult(
                        status="success",
                        result=result,
                        execution_time=stats["execution_time"],
                        memory_used=stats["peak_memory_mb"]
                    )

                except TimeoutError:
                    return ExecutionResult(
                        status="timeout",
                        error=f"Code execution timed out after {self.config.timeout_seconds}s",
                        execution_time=self.config.timeout_seconds
                    )

        except Exception as e:
            stats = monitor.get_stats()
            return ExecutionResult(
                status="failure",
                error=str(e),
                execution_time=stats["execution_time"],
                memory_used=stats["peak_memory_mb"],
                metadata={"exception_type": type(e).__name__}
            )

    async def _execute_code_in_process(self,
                                     code: str,
                                     globals_dict: Dict[str, Any],
                                     locals_dict: Dict[str, Any]) -> ExecutionResult:
        """Execute code in a separate process."""
        # For simplicity, use thread execution for now
        return await self._execute_code_in_thread(code, globals_dict, locals_dict)

    async def _execute_in_process(self,
                                tool_function: Callable,
                                args: tuple,
                                kwargs: Dict[str, Any]) -> ExecutionResult:
        """Execute tool function in a separate process."""
        # For simplicity, use thread execution for now
        return await self._execute_in_thread(tool_function, args, kwargs)

    async def _execute_restricted(self,
                                tool_function: Callable,
                                args: tuple,
                                kwargs: Dict[str, Any]) -> ExecutionResult:
        """Execute tool function with restricted permissions."""
        # For simplicity, use thread execution for now
        return await self._execute_in_thread(tool_function, args, kwargs)

    def _create_safe_globals(self) -> Dict[str, Any]:
        """Create a safe globals dictionary for code execution."""
        safe_globals = {
            '__builtins__': {
                # Safe built-ins
                'abs': abs,
                'all': all,
                'any': any,
                'bool': bool,
                'dict': dict,
                'enumerate': enumerate,
                'filter': filter,
                'float': float,
                'int': int,
                'len': len,
                'list': list,
                'map': map,
                'max': max,
                'min': min,
                'range': range,
                'reversed': reversed,
                'round': round,
                'set': set,
                'sorted': sorted,
                'str': str,
                'sum': sum,
                'tuple': tuple,
                'type': type,
                'zip': zip,
            }
        }

        # Add allowed imports
        for module_name in self.config.allowed_imports:
            try:
                safe_globals[module_name] = __import__(module_name)
            except ImportError:
                logger.warning(f"Could not import allowed module: {module_name}")

        return safe_globals

    def get_statistics(self) -> Dict[str, Any]:
        """Get execution statistics."""
        avg_execution_time = (
            self.total_execution_time / self.execution_count
            if self.execution_count > 0 else 0.0
        )

        success_rate = (
            self.success_count / self.execution_count
            if self.execution_count > 0 else 0.0
        )

        return {
            "total_executions": self.execution_count,
            "successful_executions": self.success_count,
            "failed_executions": self.failure_count,
            "success_rate": success_rate,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": avg_execution_time,
            "sandbox_mode": self.config.mode.value,
            "timeout_seconds": self.config.timeout_seconds,
            "memory_limit_mb": self.config.memory_limit_mb
        }

    def reset_statistics(self):
        """Reset execution statistics."""
        self.execution_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.total_execution_time = 0.0
        logger.info("Execution statistics reset")

    def update_config(self, new_config: SandboxConfig):
        """Update sandbox configuration."""
        self.config = new_config
        self.security_validator = SecurityValidator(new_config)
        logger.info("Sandbox configuration updated")

# Factory functions for common sandbox configurations

def create_fast_sandbox() -> ToolExecutionSandbox:
    """Create a sandbox optimized for speed."""
    config = SandboxConfig(
        mode=SandboxMode.THREAD,
        timeout_seconds=10.0,
        memory_limit_mb=256,
        cpu_limit_percent=80.0
    )
    return ToolExecutionSandbox(config)

def create_secure_sandbox() -> ToolExecutionSandbox:
    """Create a sandbox optimized for security."""
    config = SandboxConfig(
        mode=SandboxMode.PROCESS,
        timeout_seconds=30.0,
        memory_limit_mb=128,
        cpu_limit_percent=25.0,
        allowed_imports=['math', 'json', 'datetime'],
        enable_network=False,
        enable_file_system=False
    )
    return ToolExecutionSandbox(config)

def create_balanced_sandbox() -> ToolExecutionSandbox:
    """Create a sandbox with balanced performance and security."""
    config = SandboxConfig(
        mode=SandboxMode.THREAD,
        timeout_seconds=30.0,
        memory_limit_mb=512,
        cpu_limit_percent=50.0
    )
    return ToolExecutionSandbox(config)
