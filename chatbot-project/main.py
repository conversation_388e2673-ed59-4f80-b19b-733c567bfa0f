"""
Main entry point for the chatbot application.
"""

import asyncio
import os
from dotenv import load_dotenv

from agents.core.agent_registry import AgentRegistry
from agents.core.lifecycle import AgentLifecycle


async def main():
    """Main application entry point."""
    # Load environment variables
    load_dotenv()
    
    print("🤖 Starting Multi-Organization Chatbot with Advanced Agentic RAG")
    print("=" * 60)
    
    # Initialize agent registry and lifecycle
    registry = AgentRegistry()
    lifecycle = AgentLifecycle(registry)
    
    print("✅ Agent registry initialized")
    print("✅ Agent lifecycle manager initialized")
    
    # Start monitoring
    await lifecycle.start_monitoring()
    print("✅ Agent monitoring started")
    
    # Get system status
    status = await lifecycle.get_system_status()
    print(f"📊 System Status: {status}")
    
    print("\n🎯 Phase 1: Core Infrastructure Setup - COMPLETED")
    print("Next steps:")
    print("  - Run tests: pytest tests/")
    print("  - Start databases: docker-compose up -d")
    print("  - Proceed to Phase 2: Agent Framework Foundation")
    
    # Keep running for a short time to demonstrate
    print("\n⏳ Running for 10 seconds to demonstrate...")
    await asyncio.sleep(10)
    
    # Cleanup
    await lifecycle.stop_monitoring()
    print("🛑 System shutdown complete")


if __name__ == "__main__":
    asyncio.run(main())