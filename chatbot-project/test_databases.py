#!/usr/bin/env python3
"""
Comprehensive Database Testing Script for CHaBot
Tests all database connections and basic operations.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Settings
from infrastructure.databases.database_manager import DatabaseManager, DatabaseConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseTester:
    """Comprehensive database testing suite."""
    
    def __init__(self):
        self.settings = Settings()
        self.db_config = DatabaseConfig(
            postgres_url=self.settings.database_url,
            neo4j_uri=self.settings.neo4j_uri,
            neo4j_user=self.settings.neo4j_user,
            neo4j_password=self.settings.neo4j_password,
            neo4j_database=self.settings.neo4j_database,
            milvus_host=self.settings.milvus_host,
            milvus_port=self.settings.milvus_port,
            milvus_collection=self.settings.milvus_collection_name,
            memgraph_host=self.settings.memgraph_host,
            memgraph_port=self.settings.memgraph_port,
            memgraph_user=self.settings.memgraph_user,
            memgraph_password=self.settings.memgraph_password,
            chroma_host=self.settings.chroma_host,
            chroma_port=self.settings.chroma_port,
            chroma_collection=self.settings.chroma_collection_name,
            chroma_persist_dir=self.settings.chroma_persist_directory
        )
        self.db_manager = DatabaseManager(self.db_config)
        self.test_results = {}
    
    async def run_all_tests(self):
        """Run comprehensive database tests."""
        logger.info("🚀 Starting comprehensive database tests...")
        
        try:
            # Initialize all databases
            await self.test_database_initialization()
            
            # Test individual database operations
            await self.test_postgresql_operations()
            await self.test_neo4j_operations()
            await self.test_milvus_operations()
            await self.test_memgraph_operations()
            await self.test_chroma_operations()
            
            # Test integrated operations
            await self.test_integrated_operations()
            
            # Health checks
            await self.test_health_checks()
            
            # Generate report
            self.generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            self.test_results["overall"] = {"status": "FAILED", "error": str(e)}
        
        finally:
            await self.db_manager.close_all()
    
    async def test_database_initialization(self):
        """Test database initialization."""
        logger.info("📊 Testing database initialization...")
        
        try:
            connection_status = await self.db_manager.initialize_all()
            
            self.test_results["initialization"] = {
                "status": "PASSED" if any(connection_status.values()) else "FAILED",
                "connections": connection_status,
                "summary": self.db_manager.get_connection_summary()
            }
            
            logger.info(f"✅ Database initialization test completed")
            logger.info(f"Connected: {list(k for k, v in connection_status.items() if v)}")
            logger.info(f"Failed: {list(k for k, v in connection_status.items() if not v)}")
            
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            self.test_results["initialization"] = {"status": "FAILED", "error": str(e)}
    
    async def test_postgresql_operations(self):
        """Test PostgreSQL operations."""
        logger.info("🐘 Testing PostgreSQL operations...")
        
        try:
            if not self.db_manager.postgres or not self.db_manager.connection_status["postgres"]:
                self.test_results["postgresql"] = {"status": "SKIPPED", "reason": "Not connected"}
                return
            
            # Test basic query
            result = await self.db_manager.postgres.execute_query("SELECT 1 as test")
            assert result[0]["test"] == 1
            
            # Test table creation (already done in initialization)
            tables = await self.db_manager.postgres.execute_query(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
            )
            table_names = [t["table_name"] for t in tables]
            
            required_tables = ["users", "conversations", "messages", "organizations", "departments"]
            missing_tables = [t for t in required_tables if t not in table_names]
            
            self.test_results["postgresql"] = {
                "status": "PASSED" if not missing_tables else "PARTIAL",
                "tables_created": table_names,
                "missing_tables": missing_tables,
                "basic_query": "PASSED"
            }
            
            logger.info(f"✅ PostgreSQL tests completed - Tables: {len(table_names)}")
            
        except Exception as e:
            logger.error(f"❌ PostgreSQL tests failed: {e}")
            self.test_results["postgresql"] = {"status": "FAILED", "error": str(e)}
    
    async def test_neo4j_operations(self):
        """Test Neo4j operations."""
        logger.info("🕸️ Testing Neo4j operations...")
        
        try:
            if not self.db_manager.neo4j or not self.db_manager.connection_status["neo4j"]:
                self.test_results["neo4j"] = {"status": "SKIPPED", "reason": "Not connected"}
                return
            
            # Test basic query
            result = await self.db_manager.neo4j.execute_query("RETURN 1 as test")
            assert result[0]["test"] == 1
            
            # Test node creation
            await self.db_manager.neo4j.execute_write_query(
                "CREATE (t:TestNode {name: 'test', created_at: datetime()})"
            )
            
            # Test node retrieval
            nodes = await self.db_manager.neo4j.execute_query(
                "MATCH (t:TestNode {name: 'test'}) RETURN t"
            )
            
            # Cleanup test node
            await self.db_manager.neo4j.execute_write_query(
                "MATCH (t:TestNode {name: 'test'}) DELETE t"
            )
            
            self.test_results["neo4j"] = {
                "status": "PASSED",
                "basic_query": "PASSED",
                "node_operations": "PASSED",
                "test_nodes_found": len(nodes)
            }
            
            logger.info(f"✅ Neo4j tests completed")
            
        except Exception as e:
            logger.error(f"❌ Neo4j tests failed: {e}")
            self.test_results["neo4j"] = {"status": "FAILED", "error": str(e)}
    
    async def test_milvus_operations(self):
        """Test Milvus operations."""
        logger.info("🔍 Testing Milvus operations...")
        
        try:
            if not self.db_manager.milvus or not self.db_manager.connection_status["milvus"]:
                self.test_results["milvus"] = {"status": "SKIPPED", "reason": "Not connected"}
                return
            
            # Test collection stats
            stats = await self.db_manager.milvus.get_collection_stats()
            
            # Test embedding insertion
            test_data = [{
                "document_id": "test_doc_1",
                "chunk_id": "test_chunk_1",
                "content": "This is a test document for Milvus",
                "embedding": [0.1] * 384,  # 384-dimensional test embedding
                "organization": "TestOrg",
                "department": "TestDept",
                "document_type": "test",
                "metadata": {"test": True},
                "created_at": int(datetime.now().timestamp())
            }]
            
            insert_success = await self.db_manager.milvus.insert_embeddings(test_data)
            
            # Test search
            search_results = await self.db_manager.milvus.search_similar(
                query_embedding=[0.1] * 384,
                top_k=5,
                organization="TestOrg"
            )
            
            self.test_results["milvus"] = {
                "status": "PASSED" if insert_success else "PARTIAL",
                "collection_stats": stats,
                "insert_test": "PASSED" if insert_success else "FAILED",
                "search_test": "PASSED" if search_results else "FAILED",
                "search_results_count": len(search_results)
            }
            
            logger.info(f"✅ Milvus tests completed - Collection entities: {stats.get('num_entities', 0)}")
            
        except Exception as e:
            logger.error(f"❌ Milvus tests failed: {e}")
            self.test_results["milvus"] = {"status": "FAILED", "error": str(e)}
    
    async def test_memgraph_operations(self):
        """Test Memgraph operations."""
        logger.info("⚡ Testing Memgraph operations...")
        
        try:
            if not self.db_manager.memgraph or not self.db_manager.connection_status["memgraph"]:
                self.test_results["memgraph"] = {"status": "SKIPPED", "reason": "Not connected"}
                return
            
            # Test basic query
            result = await self.db_manager.memgraph.execute_query("RETURN 1 as test")
            assert result[0]["test"] == 1
            
            # Test reasoning session creation
            session_id = f"test_session_{int(datetime.now().timestamp())}"
            session_created = await self.db_manager.memgraph.create_reasoning_session(
                session_id=session_id,
                query="Test reasoning query",
                user_context={"user": "test", "org": "TestOrg"}
            )
            
            # Test reasoning step addition
            step_added = await self.db_manager.memgraph.add_reasoning_step(
                session_id=session_id,
                step_id=f"{session_id}_step_1",
                agent_id="test_agent",
                step_type="analysis",
                input_data={"query": "test"},
                output_data={"result": "test_result"}
            )
            
            # Test reasoning trace retrieval
            trace = await self.db_manager.memgraph.get_reasoning_trace(session_id)
            
            self.test_results["memgraph"] = {
                "status": "PASSED",
                "basic_query": "PASSED",
                "session_creation": "PASSED" if session_created else "FAILED",
                "step_addition": "PASSED" if step_added else "FAILED",
                "trace_retrieval": "PASSED" if trace else "FAILED",
                "trace_length": len(trace)
            }
            
            logger.info(f"✅ Memgraph tests completed")
            
        except Exception as e:
            logger.error(f"❌ Memgraph tests failed: {e}")
            self.test_results["memgraph"] = {"status": "FAILED", "error": str(e)}
    
    async def test_chroma_operations(self):
        """Test Chroma operations."""
        logger.info("🧠 Testing Chroma operations...")
        
        try:
            if not self.db_manager.chroma or not self.db_manager.connection_status["chroma"]:
                self.test_results["chroma"] = {"status": "SKIPPED", "reason": "Not connected"}
                return
            
            # Test collection stats
            stats = await self.db_manager.chroma.get_collection_stats()
            
            # Test memory addition
            test_memories = [{
                "id": f"test_memory_{int(datetime.now().timestamp())}",
                "content": "This is a test memory for Chroma",
                "session_id": "test_session",
                "user_id": "test_user",
                "organization": "TestOrg",
                "department": "TestDept",
                "memory_type": "test",
                "importance": 0.8,
                "created_at": datetime.now().isoformat(),
                "context": {"test": True},
                "agent_id": "test_agent"
            }]
            
            memory_added = await self.db_manager.chroma.add_memory(test_memories)
            
            # Test memory search
            search_results = await self.db_manager.chroma.search_memories(
                query="test memory",
                n_results=5,
                organization="TestOrg"
            )
            
            self.test_results["chroma"] = {
                "status": "PASSED" if memory_added else "PARTIAL",
                "collection_stats": stats,
                "memory_addition": "PASSED" if memory_added else "FAILED",
                "search_test": "PASSED" if search_results else "FAILED",
                "search_results_count": len(search_results)
            }
            
            logger.info(f"✅ Chroma tests completed - Total memories: {stats.get('total_memories', 0)}")
            
        except Exception as e:
            logger.error(f"❌ Chroma tests failed: {e}")
            self.test_results["chroma"] = {"status": "FAILED", "error": str(e)}
    
    async def test_integrated_operations(self):
        """Test integrated database operations."""
        logger.info("🔗 Testing integrated operations...")
        
        try:
            # Setup initial data
            await self.db_manager.setup_initial_data()
            
            self.test_results["integration"] = {
                "status": "PASSED",
                "initial_data_setup": "PASSED"
            }
            
            logger.info(f"✅ Integration tests completed")
            
        except Exception as e:
            logger.error(f"❌ Integration tests failed: {e}")
            self.test_results["integration"] = {"status": "FAILED", "error": str(e)}
    
    async def test_health_checks(self):
        """Test health checks for all databases."""
        logger.info("🏥 Testing health checks...")
        
        try:
            health_status = await self.db_manager.health_check_all()
            
            self.test_results["health_checks"] = {
                "status": "PASSED" if any(health_status.values()) else "FAILED",
                "individual_health": health_status,
                "healthy_databases": [db for db, healthy in health_status.items() if healthy],
                "unhealthy_databases": [db for db, healthy in health_status.items() if not healthy]
            }
            
            logger.info(f"✅ Health check tests completed")
            
        except Exception as e:
            logger.error(f"❌ Health check tests failed: {e}")
            self.test_results["health_checks"] = {"status": "FAILED", "error": str(e)}
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("📋 Generating test report...")
        
        # Calculate overall status
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get("status") == "PASSED")
        total_tests = len(self.test_results)
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "success_rate": f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
                "overall_status": "PASSED" if passed_tests >= total_tests * 0.7 else "FAILED"
            },
            "database_status": self.db_manager.get_connection_summary(),
            "available_features": self.db_manager.get_available_features(),
            "detailed_results": self.test_results
        }
        
        # Save report
        with open("database_test_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print("\n" + "="*80)
        print("🎯 DATABASE TEST REPORT SUMMARY")
        print("="*80)
        print(f"📊 Tests: {passed_tests}/{total_tests} passed ({report['summary']['success_rate']})")
        print(f"🔗 Connected DBs: {report['database_status']['total_connected']}/{report['database_status']['total_databases']}")
        print(f"✨ Available Features: {len(report['available_features'])}")
        print(f"📈 Overall Status: {report['summary']['overall_status']}")
        print("="*80)
        
        if report['summary']['overall_status'] == "PASSED":
            print("🎉 Database infrastructure is ready for CHaBot!")
        else:
            print("⚠️  Some database components need attention.")
        
        print(f"📄 Detailed report saved to: database_test_report.json")

async def main():
    """Main test execution."""
    tester = DatabaseTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
