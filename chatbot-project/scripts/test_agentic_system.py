#!/usr/bin/env python3

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from agents.orchestrator.orchestrator_agent import OrchestratorAgent
from agents.specialized.knowledge_agent import KnowledgeAgent
from agents.specialized.reasoning_agent import ReasoningAgent
from agents.communication.agent_coordinator import AgentCoordinator
from agents.memory.conversation_memory import ConversationMemory
from ai.knowledge.document_processor import DocumentProcessor

async def test_agentic_system():
    print("🤖 Testing Advanced Agentic RAG System")
    
    # Initialize components
    coordinator = AgentCoordinator()
    memory = ConversationMemory()
    doc_processor = DocumentProcessor()
    
    print("\n1. Loading Documents:")
    documents = doc_processor.load_documents()
    print(f"   Documents loaded: {len(documents)}")
    
    print("\n2. Initializing Agents:")
    
    # Create agents
    orchestrator = OrchestratorAgent()
    knowledge_agent = KnowledgeAgent()
    reasoning_agent = ReasoningAgent()
    
    # Initialize knowledge agent
    await knowledge_agent.initialize(documents)
    
    # Register agents
    coordinator.register_agent("OrchestratorAgent", orchestrator)
    coordinator.register_agent("KnowledgeAgent", knowledge_agent)
    coordinator.register_agent("ReasoningAgent", reasoning_agent)
    
    print(f"   Agents registered: {len(coordinator.agents)}")
    
    print("\n3. Testing Multi-Agent Coordination:")
    
    test_queries = [
        {
            "query": "What is the privilege leave policy at NUVO AI?",
            "user_context": {"organization": "NUVO AI", "user_id": "test_user_1"}
        },
        {
            "query": "Compare maternity leave policies between NUVO AI and Meril",
            "user_context": {"organization": "Multi", "user_id": "test_user_2"}
        },
        {
            "query": "Who is Anita Nagar and what is her role?",
            "user_context": {"organization": "Meril", "user_id": "test_user_3"}
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n   Query {i}: {test_case['query']}")
        
        # Test orchestrator
        orchestrator_result = await orchestrator.process_task(test_case)
        print(f"   Orchestrator Result: {orchestrator_result.get('subtasks_completed', 0)} subtasks")
        
        # Test multi-agent coordination
        coordination_result = await coordinator.coordinate_multi_agent_task(test_case)
        print(f"   Agents Used: {coordination_result.get('agents_used', [])}")
        print(f"   Overall Confidence: {coordination_result.get('overall_confidence', 0):.3f}")
        print(f"   Results Count: {len(coordination_result.get('combined_results', []))}")
        
        # Test memory
        memory.store_interaction(
            test_case['user_context']['user_id'],
            test_case['query'],
            "Test response",
            {"agents": coordination_result.get('agents_used', [])}
        )
    
    print("\n4. Testing Memory System:")
    for user_id in ["test_user_1", "test_user_2", "test_user_3"]:
        history = memory.get_conversation_history(user_id)
        context = memory.get_context_for_query(user_id, "follow up question")
        print(f"   User {user_id}: {len(history)} interactions, topics: {context.get('recent_topics', [])}")
    
    print("\n5. Testing Individual Agent Capabilities:")
    
    # Test Knowledge Agent
    knowledge_task = {
        "query": "NUVO AI leave policies",
        "context": {"organization": "NUVO AI"},
        "k": 3
    }
    knowledge_result = await knowledge_agent.process_task(knowledge_task)
    print(f"   Knowledge Agent: {len(knowledge_result.get('results', []))} results, confidence: {knowledge_result.get('confidence', 0):.3f}")
    
    # Test Reasoning Agent
    reasoning_task = {
        "type": "tree_of_thoughts",
        "query": "Why do companies have different leave policies?",
        "context": {}
    }
    reasoning_result = await reasoning_agent.process_task(reasoning_task)
    print(f"   Reasoning Agent: {len(reasoning_result.get('reasoning_paths', []))} paths, confidence: {reasoning_result.get('confidence', 0):.3f}")
    
    print("\n6. Testing Agent Status and Coordination:")
    status = coordinator.get_coordination_status()
    print(f"   Registered Agents: {status['registered_agents']}")
    print(f"   Agent Statuses: {status['agent_statuses']}")
    print(f"   Recent Coordinations: {len(status['recent_coordination'])}")
    
    print("\n✅ Advanced Agentic RAG System Testing Complete!")
    print("\n📈 Key Features Tested:")
    print("   • Multi-agent task coordination")
    print("   • Orchestrator-based task planning")
    print("   • Knowledge agent with fusion search")
    print("   • Reasoning agent with Tree of Thoughts")
    print("   • Conversation memory system")
    print("   • Agent status monitoring")
    print("   • Cross-agent result aggregation")

if __name__ == "__main__":
    asyncio.run(test_agentic_system())