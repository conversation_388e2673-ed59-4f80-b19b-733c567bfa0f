#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from ai.nlp.intent.models.intent_classifier import IntentClassifier

def test_enhanced_intents():
    print("🧪 Testing Enhanced Intent Classification with Real Document Data")
    
    classifier = IntentClassifier()
    
    # Test queries based on actual document content
    test_queries = [
        # NuvoAi specific queries
        "What is NuvoAi's privilege leave policy?",
        "How many days of PL do I get at NuvoAi?",
        "What is the LTA policy at NuvoAi?",
        "Tell me about NuvoAi's mediclaim coverage",
        "What are NuvoAi's travel expense limits?",
        
        # Meril specific queries  
        "What is Meril Life Sciences leave encashment policy?",
        "How do I book travel through Meril travel portal?",
        "Who is on Meril's sexual harassment committee?",
        "What is Me<PERSON>'s domestic travel policy?",
        "Tell me about Me<PERSON>'s ethical code of conduct",
        
        # Cross-organization queries
        "What are the differences between NuvoAi and Meril leave policies?",
        "How do travel policies compare between NuvoAi and Meril?",
        
        # Complex queries requiring decomposition
        "What is the maternity leave policy and how do I apply for it at Meril?",
        "I need to know about LTA benefits and also want to understand mediclaim coverage at NuvoAi",
        
        # Safety and compliance
        "How do I report sexual harassment at Meril?",
        "What are the emergency evacuation procedures for pregnant employees?",
        
        # Administrative queries
        "How do I submit expense reports and get approval from HOD?",
        "What is the process for mobile phone reimbursement?"
    ]
    
    print("\n📊 Enhanced Intent Classification Results:")
    print("=" * 80)
    
    for query in test_queries:
        result = classifier.analyze_query(query)
        print(f"\nQuery: '{query}'")
        print(f"Intent: {result['primary_intent']} (confidence: {result['confidence']:.2f})")
        print(f"Keywords: {result['keywords']}")
        print(f"Organizations: {result['entities']['organizations']}")
        print(f"Departments: {result['entities']['departments']}")
        print(f"Complexity: {result['complexity']}")
        print(f"Needs decomposition: {result['requires_decomposition']}")
        print("-" * 40)
    
    print("\n✅ Enhanced intent classification testing completed!")
    print("\n📈 Key Improvements:")
    print("   • Organization-specific intent detection (NuvoAi vs Meril)")
    print("   • Enhanced keyword matching based on actual documents")
    print("   • Better entity extraction for organizations and departments")
    print("   • Improved confidence scoring")
    print("   • More accurate complexity assessment")

if __name__ == "__main__":
    test_enhanced_intents()