#!/bin/bash

# Local Kubernetes Setup (using kind or minikube)

set -e

echo "🚀 Setting up local Kubernetes cluster..."

# Check if kind is available
if command -v kind &> /dev/null; then
    echo "📦 Using kind for local cluster..."
    
    # Create kind cluster if it doesn't exist
    if ! kind get clusters | grep -q "chatbot-cluster"; then
        echo "🔧 Creating kind cluster..."
        cat <<EOF | kind create cluster --name chatbot-cluster --config=-
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  - containerPort: 80
    hostPort: 8080
    protocol: TCP
  - containerPort: 443
    hostPort: 8443
    protocol: TCP
- role: worker
- role: worker
EOF
    else
        echo "✅ Kind cluster already exists"
    fi
    
    # Set kubectl context
    kubectl config use-context kind-chatbot-cluster
    
elif command -v minikube &> /dev/null; then
    echo "📦 Using minikube for local cluster..."
    
    # Start minikube if not running
    if ! minikube status | grep -q "Running"; then
        echo "🔧 Starting minikube..."
        minikube start --cpus=4 --memory=8192 --disk-size=20g
        minikube addons enable ingress
    else
        echo "✅ Minikube already running"
    fi
    
else
    echo "❌ Neither kind nor minikube found. Please install one of them."
    echo "   kind: https://kind.sigs.k8s.io/docs/user/quick-start/"
    echo "   minikube: https://minikube.sigs.k8s.io/docs/start/"
    exit 1
fi

echo "✅ Local Kubernetes cluster is ready!"
echo "🔧 Now run: ./scripts/k8s-setup.sh"