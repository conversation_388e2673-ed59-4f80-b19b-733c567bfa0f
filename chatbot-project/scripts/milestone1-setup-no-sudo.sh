#!/bin/bash

# Milestone 1: Infrastructure Ready Setup Script (No Sudo Version)
# This script sets up:
# 1. Kubernetes cluster operational
# 2. All databases deployed and configured
# 3. Agent communication infrastructure active
# 4. Monitoring system operational

set -e

echo "🚀 Starting Milestone 1: Infrastructure Ready Setup..."
echo "=================================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo "🔍 Checking dependencies..."

# Check for Docker
if ! command_exists docker; then
    echo "❌ Docker is not installed or not in PATH"
    echo "📋 Please install Docker manually:"
    echo "   sudo apt update"
    echo "   sudo apt install docker.io"
    echo "   sudo systemctl enable docker"
    echo "   sudo systemctl start docker"
    echo "   sudo usermod -aG docker \$USER"
    echo "   # Then log out and log back in"
    echo ""
    echo "   Or install via snap:"
    echo "   sudo snap install docker"
    echo ""
    exit 1
else
    echo "✅ Docker is available"
fi

# Check for kubectl
if ! command_exists kubectl; then
    echo "❌ kubectl is not installed or not in PATH"
    echo "📋 Please install kubectl manually:"
    echo "   sudo snap install kubectl --classic"
    echo "   # Or download binary:"
    echo "   curl -LO \"https://dl.k8s.io/release/\$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl\""
    echo "   sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl"
    echo ""
    exit 1
else
    echo "✅ kubectl is available"
fi

# Check for kind
if ! command_exists kind; then
    echo "❌ kind is not installed or not in PATH"
    echo "📋 Please install kind manually:"
    echo "   # For AMD64 / x86_64"
    echo "   [ \$(uname -m) = x86_64 ] && curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64"
    echo "   # For ARM64"
    echo "   [ \$(uname -m) = aarch64 ] && curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-arm64"
    echo "   chmod +x ./kind"
    echo "   sudo mv ./kind /usr/local/bin/kind"
    echo ""
    exit 1
else
    echo "✅ kind is available"
fi

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running or current user doesn't have permission"
    echo "📋 Please ensure Docker is running:"
    echo "   sudo systemctl start docker"
    echo "   # And add your user to docker group:"
    echo "   sudo usermod -aG docker \$USER"
    echo "   # Then log out and log back in, or run:"
    echo "   newgrp docker"
    echo ""
    exit 1
else
    echo "✅ Docker is running and accessible"
fi

echo ""
echo "🚀 Setting up Kubernetes cluster..."
echo "=================================="

# Run the local Kubernetes setup
chmod +x ./scripts/local-k8s-setup.sh
./scripts/local-k8s-setup.sh

echo ""
echo "🗄️ Setting up databases and infrastructure..."
echo "============================================="

# Make sure we're in the right context
kubectl config use-context kind-chatbot-cluster

# Run the main Kubernetes setup
chmod +x ./scripts/k8s-setup.sh
./scripts/k8s-setup.sh

echo ""
echo "🔗 Setting up Agent Communication Infrastructure..."
echo "=================================================="

# Run communication setup
chmod +x ./scripts/communication-setup.sh
./scripts/communication-setup.sh

echo ""
echo "📊 Setting up Monitoring System..."
echo "================================="

# Run monitoring setup
chmod +x ./scripts/complete-monitoring-setup.sh
./scripts/complete-monitoring-setup.sh

echo ""
echo "🎉 Milestone 1: Infrastructure Ready - COMPLETED!"
echo "================================================"
echo ""
echo "📋 Summary of what was set up:"
echo "✅ Kubernetes cluster operational (kind cluster)"
echo "✅ All databases deployed and configured"
echo "✅ Agent communication infrastructure active"
echo "✅ Monitoring system operational"
echo ""
echo "🔍 Verification commands:"
echo "  kubectl get pods -A"
echo "  kubectl get services -A"
echo "  kubectl get namespaces"
echo ""
echo "🌐 Access points:"
echo "  Application: kubectl port-forward service/chatbot-service 8080:80 -n chatbot-dev"
echo "  Monitoring: kubectl port-forward service/grafana 3000:3000 -n monitoring"
echo ""