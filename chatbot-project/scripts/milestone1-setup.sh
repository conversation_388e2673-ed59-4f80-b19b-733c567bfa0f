#!/bin/bash

# Milestone 1: Infrastructure Ready Setup Script
# This script sets up:
# 1. Kubernetes cluster operational
# 2. All databases deployed and configured
# 3. Agent communication infrastructure active
# 4. Monitoring system operational

set -e

echo "🚀 Starting Milestone 1: Infrastructure Ready Setup..."
echo "=================================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Docker
install_docker() {
    echo "🐳 Installing Docker..."
    sudo apt update
    sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Set up the stable repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker Engine
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Add current user to docker group
    sudo usermod -aG docker $USER
    
    # Enable and start Docker
    sudo systemctl enable docker
    sudo systemctl start docker
    
    echo "✅ Docker installed successfully"
}

# Function to install kubectl
install_kubectl() {
    echo "⚙️ Installing kubectl..."
    curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
    rm kubectl
    echo "✅ kubectl installed successfully"
}

# Function to install kind
install_kind() {
    echo "🔧 Installing kind..."
    # For AMD64 / x86_64
    [ $(uname -m) = x86_64 ] && curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64
    # For ARM64
    [ $(uname -m) = aarch64 ] && curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-arm64
    chmod +x ./kind
    sudo mv ./kind /usr/local/bin/kind
    echo "✅ kind installed successfully"
}

# Check and install dependencies
echo "🔍 Checking dependencies..."

if ! command_exists docker; then
    install_docker
    echo "⚠️ Docker was just installed. Please log out and log back in, then run this script again."
    echo "   Or run: newgrp docker"
    exit 1
else
    echo "✅ Docker is already installed"
fi

if ! command_exists kubectl; then
    install_kubectl
else
    echo "✅ kubectl is already installed"
fi

if ! command_exists kind; then
    install_kind
else
    echo "✅ kind is already installed"
fi

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running or current user doesn't have permission"
    echo "   Try: sudo systemctl start docker"
    echo "   Or: newgrp docker"
    exit 1
fi

echo ""
echo "🚀 Setting up Kubernetes cluster..."
echo "=================================="

# Run the local Kubernetes setup
chmod +x ./scripts/local-k8s-setup.sh
./scripts/local-k8s-setup.sh

echo ""
echo "🗄️ Setting up databases and infrastructure..."
echo "============================================="

# Make sure we're in the right context
kubectl config use-context kind-chatbot-cluster

# Run the main Kubernetes setup
chmod +x ./scripts/k8s-setup.sh
./scripts/k8s-setup.sh

echo ""
echo "🔗 Setting up Agent Communication Infrastructure..."
echo "=================================================="

# Run communication setup
chmod +x ./scripts/communication-setup.sh
./scripts/communication-setup.sh

echo ""
echo "📊 Setting up Monitoring System..."
echo "================================="

# Run monitoring setup
chmod +x ./scripts/complete-monitoring-setup.sh
./scripts/complete-monitoring-setup.sh

echo ""
echo "🎉 Milestone 1: Infrastructure Ready - COMPLETED!"
echo "================================================"
echo ""
echo "📋 Summary of what was set up:"
echo "✅ Kubernetes cluster operational (kind cluster)"
echo "✅ All databases deployed and configured"
echo "✅ Agent communication infrastructure active"
echo "✅ Monitoring system operational"
echo ""
echo "🔍 Verification commands:"
echo "  kubectl get pods -A"
echo "  kubectl get services -A"
echo "  kubectl get namespaces"
echo ""
echo "🌐 Access points:"
echo "  Application: kubectl port-forward service/chatbot-service 8080:80 -n chatbot-dev"
echo "  Monitoring: kubectl port-forward service/grafana 3000:3000 -n monitoring"
echo ""