#!/bin/bash

# Agent Communication Infrastructure Setup Script

set -e

echo "🔗 Setting up Agent Communication Infrastructure..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed"
    exit 1
fi

# Deploy Zookeeper first
echo "🐘 Deploying Zookeeper cluster..."
kubectl apply -f infrastructure/kubernetes/deployments/kafka.yaml

# Wait for Zookeeper to be ready
echo "⏳ Waiting for Zookeeper to be ready..."
kubectl wait --for=condition=ready pod -l app=zookeeper -n chatbot-dev --timeout=300s

# Deploy Kafka
echo "📨 Deploying Kafka cluster..."
sleep 30

# Wait for Kafka to be ready
echo "⏳ Waiting for Kafka to be ready..."
kubectl wait --for=condition=ready pod -l app=kafka -n chatbot-dev --timeout=300s

# Create Kafka services
echo "🌐 Creating Kafka services..."
kubectl apply -f infrastructure/kubernetes/services/kafka-services.yaml

# Deploy Redis cluster
echo "🔴 Deploying Redis cluster..."
kubectl apply -f infrastructure/kubernetes/configmaps/communication-config.yaml
kubectl apply -f infrastructure/kubernetes/deployments/redis-cluster.yaml
kubectl apply -f infrastructure/kubernetes/services/redis-cluster-service.yaml

# Wait for Redis to be ready
echo "⏳ Waiting for Redis cluster to be ready..."
kubectl wait --for=condition=ready pod -l app=redis-cluster -n chatbot-dev --timeout=300s

# Setup Kafka topics and Redis cluster
echo "⚙️ Setting up Kafka topics and Redis cluster..."
kubectl apply -f infrastructure/kubernetes/jobs/kafka-setup.yaml

# Wait for setup jobs to complete
echo "⏳ Waiting for setup jobs to complete..."
kubectl wait --for=condition=complete job/kafka-topics-setup -n chatbot-dev --timeout=180s
kubectl wait --for=condition=complete job/redis-cluster-setup -n chatbot-dev --timeout=180s

# Deploy agent communication services
echo "🤖 Deploying agent gateway and registry..."
kubectl apply -f infrastructure/kubernetes/configmaps/agent-gateway-code.yaml
kubectl apply -f infrastructure/kubernetes/deployments/agent-gateway.yaml
kubectl apply -f infrastructure/kubernetes/services/agent-services.yaml

# Wait for agent services to be ready
echo "⏳ Waiting for agent services to be ready..."
kubectl wait --for=condition=available deployment/agent-gateway -n chatbot-dev --timeout=300s
kubectl wait --for=condition=available deployment/agent-registry -n chatbot-dev --timeout=300s

echo "✅ Agent Communication Infrastructure setup completed!"
echo ""
echo "📊 Infrastructure Status:"
kubectl get pods -l app=zookeeper -n chatbot-dev
kubectl get pods -l app=kafka -n chatbot-dev
kubectl get pods -l app=redis-cluster -n chatbot-dev
kubectl get pods -l app=agent-gateway -n chatbot-dev
kubectl get pods -l app=agent-registry -n chatbot-dev
echo ""
echo "🌐 Services:"
kubectl get services -l app=kafka -n chatbot-dev
kubectl get services -l app=redis-cluster -n chatbot-dev
kubectl get services -l app=agent-gateway -n chatbot-dev
kubectl get services -l app=agent-registry -n chatbot-dev
echo ""
echo "🔗 Connection Info:"
echo "  Kafka:          kafka-lb-service:9092"
echo "  Redis Cluster:  redis-cluster-lb-service:6379"
echo "  Agent Gateway:  agent-gateway-service:8080"
echo "  Agent Registry: agent-registry-service:8082"
echo ""
echo "📋 Kafka Topics Created:"
echo "  - agent-requests (12 partitions)"
echo "  - agent-responses (12 partitions)"
echo "  - agent-events (12 partitions)"
echo "  - agent-heartbeat (6 partitions)"
echo "  - reasoning-steps (12 partitions)"
echo "  - decision-events (6 partitions)"
echo "  - knowledge-updates (6 partitions)"
echo "  - user-queries (12 partitions)"
echo "  - user-responses (12 partitions)"
echo "  - conversation-events (6 partitions)"
echo "  - system-metrics (6 partitions)"
echo "  - error-events (6 partitions)"
echo ""
# Deploy enhanced communication components
echo "🔧 Deploying enhanced communication components..."
kubectl apply -f infrastructure/kubernetes/configmaps/grpc-proto.yaml
kubectl apply -f infrastructure/kubernetes/deployments/grpc-gateway.yaml
kubectl apply -f infrastructure/kubernetes/deployments/temporal.yaml
kubectl apply -f infrastructure/kubernetes/deployments/redis-streams.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/enhanced-communication-code.yaml
kubectl apply -f infrastructure/kubernetes/deployments/message-broker.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/message-broker-code.yaml
kubectl apply -f infrastructure/kubernetes/services/enhanced-communication-services.yaml

# Wait for enhanced services to be ready
echo "⏳ Waiting for enhanced services to be ready..."
kubectl wait --for=condition=available deployment/grpc-gateway -n chatbot-dev --timeout=300s
kubectl wait --for=condition=available deployment/temporal-server -n chatbot-dev --timeout=300s
kubectl wait --for=condition=available deployment/temporal-worker -n chatbot-dev --timeout=300s
kubectl wait --for=condition=available deployment/redis-streams-processor -n chatbot-dev --timeout=300s
kubectl wait --for=condition=available deployment/enhanced-message-broker -n chatbot-dev --timeout=300s

echo "🧪 Test Commands:"
echo "  kubectl port-forward service/agent-gateway-service 8080:8080 -n chatbot-dev"
echo "  kubectl port-forward service/agent-registry-service 8082:8082 -n chatbot-dev"
echo "  kubectl port-forward service/grpc-gateway-service 50051:50051 -n chatbot-dev"
echo "  kubectl port-forward service/temporal-web-service 8080:8080 -n chatbot-dev"
echo "  kubectl port-forward service/enhanced-message-broker-service 8080:8080 -n chatbot-dev"
echo "  kubectl exec -it kafka-0 -n chatbot-dev -- kafka-topics --bootstrap-server localhost:9092 --list"