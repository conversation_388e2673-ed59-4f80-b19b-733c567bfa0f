#!/bin/bash

echo "Setting up Phase 2: AI Foundation"

# Create necessary directories
mkdir -p ai/nlp/entity/models
mkdir -p ai/knowledge/graph_search
mkdir -p ai/knowledge/fusion

# Install additional requirements for Phase 2
pip install sentence-transformers==2.2.2
pip install spacy==3.7.2
pip install networkx==3.2.1

# Download spaCy model
python -m spacy download en_core_web_sm

# Initialize knowledge base
python -c "
from ai.knowledge.vector_search.embeddings import EmbeddingGenerator
print('Testing embedding generation...')
embedder = EmbeddingGenerator()
test_embeddings = embedder.generate_embeddings(['test document'])
print(f'Embedding dimension: {test_embeddings.shape}')
print('Phase 2 setup complete!')
"

echo "Phase 2 AI Foundation setup completed successfully!"