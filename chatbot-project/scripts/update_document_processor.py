#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from ai.knowledge.document_processor import DocumentProcessor

def update_document_processor():
    print("🔄 Updating Document Processor for Organization-wise Structure")
    
    processor = DocumentProcessor()
    
    # Update the load_documents method to handle new structure
    print("\n📁 New Document Structure:")
    print("   NUVO_AI/")
    print("     ├── NUVO_AI_Complete_Policy_Manual.txt")
    print("     ├── Nuvoai_Formatted.txt")
    print("     └── Nuvoai.txt")
    print("   MERIL_GROUP/")
    print("     ├── Meril_Life_Sciences/")
    print("     │   ├── MERIL_LIFE_SCIENCES_Complete_Policy_Manual.txt")
    print("     │   └── [8 other files]")
    print("     ├── Meril_Healthcare/")
    print("     │   ├── MERIL_HEALTHCARE_Policy_Manual.txt")
    print("     │   └── MerilHealthcare.txt")
    print("     ├── Meril_Diagnostics/")
    print("     │   ├── MERIL_DIAGNOSTICS_Policy_Manual.txt")
    print("     │   └── MerilDiagnostics.txt")
    print("     └── Meril_Endo_Surgery/")
    print("         ├── MERIL_ENDO_SURGERY_Policy_Manual.txt")
    print("         └── MerilEndo-Surgery.txt")
    
    # Test loading documents with new structure
    print("\n🧪 Testing Document Loading:")
    try:
        documents = processor.load_documents()
        print(f"   Total documents loaded: {len(documents)}")
        
        # Count by organization
        org_counts = {}
        for doc in documents:
            org = doc.get('organization', 'Unknown')
            org_counts[org] = org_counts.get(org, 0) + 1
        
        print("\n📊 Documents by Organization:")
        for org, count in org_counts.items():
            print(f"   {org}: {count} documents")
        
        # Show sample document structure
        if documents:
            sample_doc = documents[0]
            print(f"\n📄 Sample Document Structure:")
            print(f"   Filename: {sample_doc.get('filename', 'N/A')}")
            print(f"   Organization: {sample_doc.get('organization', 'N/A')}")
            print(f"   Content length: {len(sample_doc.get('content', ''))}")
            print(f"   Content preview: {sample_doc.get('content', '')[:100]}...")
        
        print("\n✅ Document processor updated successfully!")
        
    except Exception as e:
        print(f"❌ Error loading documents: {str(e)}")

if __name__ == "__main__":
    update_document_processor()