#!/bin/bash

# PostgreSQL Cluster Setup Script

set -e

echo "🐘 Setting up PostgreSQL cluster with high availability..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed"
    exit 1
fi

# Create PostgreSQL secrets
echo "🔐 Creating PostgreSQL secrets..."
kubectl apply -f infrastructure/kubernetes/secrets/postgres-secrets.yaml

# Create PostgreSQL configuration
echo "⚙️ Creating PostgreSQL configuration..."
kubectl apply -f infrastructure/kubernetes/configmaps/postgres-config.yaml

# Deploy PostgreSQL primary
echo "🚀 Deploying PostgreSQL primary..."
kubectl apply -f infrastructure/kubernetes/deployments/postgres-primary.yaml

# Create primary service
echo "🌐 Creating primary service..."
kubectl apply -f infrastructure/kubernetes/services/postgres-primary-service.yaml

# Wait for primary to be ready
echo "⏳ Waiting for PostgreSQL primary to be ready..."
kubectl wait --for=condition=ready pod -l app=postgres-primary -n chatbot-dev --timeout=300s

# Deploy PostgreSQL replicas
echo "🔄 Deploying PostgreSQL replicas..."
kubectl apply -f infrastructure/kubernetes/deployments/postgres-replica.yaml

# Wait for replicas to be ready
echo "⏳ Waiting for PostgreSQL replicas to be ready..."
sleep 30
kubectl wait --for=condition=ready pod -l app=postgres-replica -n chatbot-dev --timeout=300s

# Setup backup job
echo "💾 Setting up backup job..."
kubectl apply -f infrastructure/kubernetes/jobs/postgres-backup.yaml

echo "✅ PostgreSQL cluster setup completed!"
echo ""
echo "📊 Cluster Status:"
kubectl get pods -l app=postgres-primary -n chatbot-dev
kubectl get pods -l app=postgres-replica -n chatbot-dev
echo ""
echo "🌐 Services:"
kubectl get services -l app=postgres -n chatbot-dev
echo ""
echo "🔗 Connection Info:"
echo "  Primary (Write): postgres-primary-service:5432"
echo "  Replica (Read):  postgres-read-service:5432"
echo ""
echo "🧪 Test Connection:"
echo "  kubectl exec -it postgres-primary-0 -n chatbot-dev -- psql -U chatbot_user -d chatbot_db"