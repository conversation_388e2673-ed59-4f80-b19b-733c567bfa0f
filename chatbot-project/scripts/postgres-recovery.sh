#!/bin/bash

# PostgreSQL Recovery Script

set -e

BACKUP_FILE=$1
TARGET_DB=${2:-chatbot_db}

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file> [target_db]"
    echo "Example: $0 /backup/chatbot_db_20240101_020000.sql.gz"
    exit 1
fi

echo "🔄 Starting PostgreSQL recovery process..."
echo "Backup file: $BACKUP_FILE"
echo "Target database: $TARGET_DB"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Create recovery pod
echo "🚀 Creating recovery pod..."
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: postgres-recovery
  namespace: chatbot-dev
spec:
  containers:
  - name: postgres-recovery
    image: postgres:15
    command: ["sleep", "3600"]
    env:
    - name: POSTGRES_USER
      valueFrom:
        secretKeyRef:
          name: chatbot-secrets
          key: POSTGRES_USER
    - name: POSTGRES_DB
      value: "$TARGET_DB"
    - name: PGPASSWORD
      valueFrom:
        secretKeyRef:
          name: chatbot-secrets
          key: POSTGRES_PASSWORD
    volumeMounts:
    - name: backup-storage
      mountPath: /backup
  volumes:
  - name: backup-storage
    persistentVolumeClaim:
      claimName: postgres-backup-pvc
  restartPolicy: Never
EOF

# Wait for recovery pod to be ready
echo "⏳ Waiting for recovery pod to be ready..."
kubectl wait --for=condition=ready pod/postgres-recovery -n chatbot-dev --timeout=60s

# Perform recovery
echo "🔄 Performing database recovery..."
if [[ "$BACKUP_FILE" == *.gz ]]; then
    kubectl exec postgres-recovery -n chatbot-dev -- bash -c "
        gunzip -c $BACKUP_FILE | psql -h postgres-primary-service -U \$POSTGRES_USER -d \$POSTGRES_DB
    "
else
    kubectl exec postgres-recovery -n chatbot-dev -- bash -c "
        psql -h postgres-primary-service -U \$POSTGRES_USER -d \$POSTGRES_DB < $BACKUP_FILE
    "
fi

# Cleanup recovery pod
echo "🧹 Cleaning up recovery pod..."
kubectl delete pod postgres-recovery -n chatbot-dev

echo "✅ PostgreSQL recovery completed successfully!"
echo "🔗 Verify recovery:"
echo "  kubectl exec -it postgres-primary-0 -n chatbot-dev -- psql -U chatbot_user -d $TARGET_DB -c '\\dt'"