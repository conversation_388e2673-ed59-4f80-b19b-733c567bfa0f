#!/bin/bash

# Kubernetes Cleanup Script for Chatbot Project

set -e

echo "🧹 Cleaning up Kubernetes resources for Chatbot project..."

# Delete application resources
echo "🗑️ Removing application resources..."
kubectl delete -f infrastructure/kubernetes/ingress/chatbot-ingress.yaml --ignore-not-found=true
kubectl delete -f infrastructure/kubernetes/services/chatbot-service.yaml --ignore-not-found=true
kubectl delete -f infrastructure/kubernetes/deployments/chatbot-app.yaml --ignore-not-found=true

# Delete database resources
echo "🗑️ Removing database resources..."
kubectl delete -f infrastructure/kubernetes/services/redis-service.yaml --ignore-not-found=true
kubectl delete -f infrastructure/kubernetes/services/postgres-service.yaml --ignore-not-found=true
kubectl delete -f infrastructure/kubernetes/deployments/redis.yaml --ignore-not-found=true
kubectl delete -f infrastructure/kubernetes/deployments/postgres.yaml --ignore-not-found=true

# Delete configuration
echo "🗑️ Removing configuration..."
kubectl delete -f infrastructure/kubernetes/secrets/app-secrets.yaml --ignore-not-found=true
kubectl delete -f infrastructure/kubernetes/configmaps/app-config.yaml --ignore-not-found=true

# Delete persistent volumes
echo "🗑️ Removing persistent volumes..."
kubectl delete -f infrastructure/kubernetes/persistent-volumes.yaml --ignore-not-found=true

# Delete namespaces (this will delete everything in them)
echo "🗑️ Removing namespaces..."
kubectl delete namespace chatbot-dev --ignore-not-found=true

echo "✅ Cleanup completed!"