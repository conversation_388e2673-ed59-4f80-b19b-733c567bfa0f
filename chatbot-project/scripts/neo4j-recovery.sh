#!/bin/bash

# Neo4j Recovery Script

set -e

BACKUP_FILE=$1
TARGET_DB=${2:-neo4j}

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file> [target_db]"
    echo "Example: $0 /backup/neo4j_20240101_030000.tar.gz"
    exit 1
fi

echo "🔄 Starting Neo4j recovery process..."
echo "Backup file: $BACKUP_FILE"
echo "Target database: $TARGET_DB"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Create recovery pod
echo "🚀 Creating recovery pod..."
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: neo4j-recovery
  namespace: chatbot-dev
spec:
  containers:
  - name: neo4j-recovery
    image: neo4j:5.15-community
    command: ["sleep", "3600"]
    env:
    - name: NEO4J_AUTH
      valueFrom:
        secretKeyRef:
          name: neo4j-secrets
          key: NEO4J_AUTH
    volumeMounts:
    - name: backup-storage
      mountPath: /backup
    - name: recovery-data
      mountPath: /data
  volumes:
  - name: backup-storage
    persistentVolumeClaim:
      claimName: neo4j-backup-pvc
  - name: recovery-data
    emptyDir: {}
  restartPolicy: Never
EOF

# Wait for recovery pod to be ready
echo "⏳ Waiting for recovery pod to be ready..."
kubectl wait --for=condition=ready pod/neo4j-recovery -n chatbot-dev --timeout=60s

# Extract and restore backup
echo "🔄 Performing database recovery..."
kubectl exec neo4j-recovery -n chatbot-dev -- bash -c "
    cd /backup
    tar -xzf $(basename $BACKUP_FILE)
    BACKUP_DIR=\$(basename $BACKUP_FILE .tar.gz)
    
    # Stop Neo4j core instances for recovery
    echo 'Stopping Neo4j instances for recovery...'
    
    # Restore from backup
    neo4j-admin database restore --from-path=/backup/\$BACKUP_DIR $TARGET_DB --overwrite-destination=true
    
    echo 'Recovery completed successfully'
"

# Restart Neo4j cluster
echo "🔄 Restarting Neo4j cluster..."
kubectl rollout restart statefulset/neo4j-core -n chatbot-dev
kubectl rollout restart statefulset/neo4j-replica -n chatbot-dev

# Wait for cluster to be ready
echo "⏳ Waiting for cluster to be ready after recovery..."
kubectl rollout status statefulset/neo4j-core -n chatbot-dev --timeout=300s
kubectl rollout status statefulset/neo4j-replica -n chatbot-dev --timeout=300s

# Cleanup recovery pod
echo "🧹 Cleaning up recovery pod..."
kubectl delete pod neo4j-recovery -n chatbot-dev

echo "✅ Neo4j recovery completed successfully!"
echo "🔗 Verify recovery:"
echo "  kubectl exec -it neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass -c 'MATCH (n) RETURN count(n) as total_nodes'"