#!/bin/bash

# Kubernetes Setup Script for Chatbot Project

set -e

echo "🚀 Setting up Kubernetes cluster for Chatbot project..."

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed. Please install kubectl first."
    exit 1
fi

# Check if cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Cannot connect to Kubernetes cluster. Please ensure cluster is running."
    exit 1
fi

echo "✅ Kubernetes cluster is accessible"

# Create namespaces
echo "📁 Creating namespaces..."
kubectl apply -f infrastructure/kubernetes/namespaces/namespaces.yaml

# Create persistent volumes
echo "💾 Creating persistent volumes..."
kubectl apply -f infrastructure/kubernetes/persistent-volumes.yaml

# Create config maps and secrets
echo "🔧 Creating configuration..."
kubectl apply -f infrastructure/kubernetes/configmaps/app-config.yaml
kubectl apply -f infrastructure/kubernetes/secrets/app-secrets.yaml

# Deploy databases
echo "🗄️ Deploying databases..."
kubectl apply -f infrastructure/kubernetes/deployments/postgres.yaml
kubectl apply -f infrastructure/kubernetes/deployments/redis.yaml

# Create services
echo "🌐 Creating services..."
kubectl apply -f infrastructure/kubernetes/services/postgres-service.yaml
kubectl apply -f infrastructure/kubernetes/services/redis-service.yaml

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/postgres -n chatbot-dev
kubectl wait --for=condition=available --timeout=300s deployment/redis -n chatbot-dev

# Deploy application (if image exists)
if docker images | grep -q "chatbot-app"; then
    echo "🤖 Deploying chatbot application..."
    kubectl apply -f infrastructure/kubernetes/deployments/chatbot-app.yaml
    kubectl apply -f infrastructure/kubernetes/services/chatbot-service.yaml
    kubectl apply -f infrastructure/kubernetes/ingress/chatbot-ingress.yaml
else
    echo "⚠️ Chatbot application image not found. Skipping application deployment."
    echo "   Build the image first: docker build -t chatbot-app ."
fi

echo "✅ Kubernetes setup completed!"
echo ""
echo "📊 Cluster Status:"
kubectl get pods -n chatbot-dev
echo ""
echo "🌐 Services:"
kubectl get services -n chatbot-dev
echo ""
echo "🔗 To access the application:"
echo "   kubectl port-forward service/chatbot-service 8080:80 -n chatbot-dev"
echo "   Then visit: http://localhost:8080"