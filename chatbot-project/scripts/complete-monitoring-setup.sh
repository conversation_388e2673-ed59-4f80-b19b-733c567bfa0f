#!/bin/bash

# Complete Monitoring and Logging Setup Script

set -e

echo "📊 Setting up complete monitoring and logging infrastructure..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed"
    exit 1
fi

# Deploy OpenSearch cluster
echo "🔍 Deploying OpenSearch cluster..."
kubectl apply -f infrastructure/kubernetes/deployments/opensearch.yaml
kubectl apply -f infrastructure/kubernetes/services/opensearch-services.yaml

# Wait for OpenSearch to be ready
echo "⏳ Waiting for OpenSearch cluster to be ready..."
kubectl wait --for=condition=ready pod -l app=opensearch -n chatbot-dev --timeout=300s

# Deploy Fluent Bit for log shipping
echo "📋 Deploying Fluent Bit log shipper..."
kubectl apply -f infrastructure/kubernetes/deployments/fluent-bit.yaml

# Wait for Fluent Bit to be ready
echo "⏳ Waiting for Fluent Bit to be ready..."
kubectl wait --for=condition=ready pod -l app=fluent-bit -n chatbot-dev --timeout=180s

# Deploy enhanced monitoring
echo "📈 Deploying enhanced monitoring stack..."
kubectl apply -f infrastructure/kubernetes/monitoring/prometheus.yaml
kubectl apply -f infrastructure/kubernetes/monitoring/grafana.yaml

# Wait for monitoring to be ready
echo "⏳ Waiting for monitoring stack to be ready..."
kubectl wait --for=condition=available deployment/prometheus -n chatbot-dev --timeout=180s
kubectl wait --for=condition=available deployment/grafana -n chatbot-dev --timeout=180s
kubectl wait --for=condition=available deployment/opensearch-dashboards -n chatbot-dev --timeout=180s

# Initialize OpenSearch indices and dashboards
echo "📊 Initializing OpenSearch indices..."
sleep 60

kubectl exec opensearch-0 -n chatbot-dev -- curl -X PUT "localhost:9200/chatbot-logs" -H 'Content-Type: application/json' -d'
{
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "index.refresh_interval": "30s"
  },
  "mappings": {
    "properties": {
      "@timestamp": {"type": "date"},
      "kubernetes": {
        "properties": {
          "pod_name": {"type": "keyword"},
          "namespace_name": {"type": "keyword"},
          "container_name": {"type": "keyword"}
        }
      },
      "log": {"type": "text"},
      "level": {"type": "keyword"},
      "agent_id": {"type": "keyword"},
      "agent_type": {"type": "keyword"},
      "user_id": {"type": "keyword"},
      "organization_id": {"type": "keyword"},
      "department_id": {"type": "keyword"}
    }
  }
}'

# Create agent activity index
kubectl exec opensearch-0 -n chatbot-dev -- curl -X PUT "localhost:9200/agent-activity" -H 'Content-Type: application/json' -d'
{
  "settings": {
    "number_of_shards": 2,
    "number_of_replicas": 1
  },
  "mappings": {
    "properties": {
      "@timestamp": {"type": "date"},
      "agent_id": {"type": "keyword"},
      "agent_type": {"type": "keyword"},
      "action": {"type": "keyword"},
      "user_id": {"type": "keyword"},
      "session_id": {"type": "keyword"},
      "processing_time_ms": {"type": "integer"},
      "confidence_score": {"type": "float"},
      "input_tokens": {"type": "integer"},
      "output_tokens": {"type": "integer"},
      "success": {"type": "boolean"},
      "error_message": {"type": "text"}
    }
  }
}'

echo "✅ Complete monitoring and logging setup completed!"
echo ""
echo "📊 Infrastructure Status:"
kubectl get pods -l app=opensearch -n chatbot-dev
kubectl get pods -l app=opensearch-dashboards -n chatbot-dev
kubectl get pods -l app=fluent-bit -n chatbot-dev
kubectl get pods -l app=prometheus -n chatbot-dev
kubectl get pods -l app=grafana -n chatbot-dev
echo ""
echo "🌐 Services:"
kubectl get services -l app=opensearch -n chatbot-dev
kubectl get services -l app=prometheus -n chatbot-dev
kubectl get services -l app=grafana -n chatbot-dev
echo ""
echo "🔗 Access Information:"
echo "  OpenSearch:         opensearch-lb-service:9200"
echo "  OpenSearch Dashboards: opensearch-dashboards-service:5601"
echo "  Prometheus:         prometheus-service:9090"
echo "  Grafana:           grafana-service:3000 (admin/admin123)"
echo "  Fluent Bit:        fluent-bit-service:2020"
echo ""
echo "🧪 Port Forward Commands:"
echo "  kubectl port-forward service/opensearch-dashboards-service 5601:5601 -n chatbot-dev"
echo "  kubectl port-forward service/prometheus-service 9090:9090 -n chatbot-dev"
echo "  kubectl port-forward service/grafana-service 3000:3000 -n chatbot-dev"
echo ""
echo "📋 Log Indices Created:"
echo "  - chatbot-logs: Application and system logs"
echo "  - agent-activity: Agent-specific activity tracking"