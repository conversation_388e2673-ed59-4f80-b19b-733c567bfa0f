#!/bin/bash

# Neo4j Cluster Setup Script

set -e

echo "🔗 Setting up Neo4j cluster with high availability..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed"
    exit 1
fi

# Create Neo4j secrets
echo "🔐 Creating Neo4j secrets..."
kubectl apply -f infrastructure/kubernetes/secrets/neo4j-secrets.yaml

# Create Neo4j configuration
echo "⚙️ Creating Neo4j configuration..."
kubectl apply -f infrastructure/kubernetes/configmaps/neo4j-config.yaml

# Deploy Neo4j core cluster
echo "🚀 Deploying Neo4j core cluster..."
kubectl apply -f infrastructure/kubernetes/deployments/neo4j-core.yaml

# Create core services
echo "🌐 Creating core services..."
kubectl apply -f infrastructure/kubernetes/services/neo4j-services.yaml

# Wait for core cluster to be ready
echo "⏳ Waiting for Neo4j core cluster to be ready..."
kubectl wait --for=condition=ready pod -l app=neo4j-core -n chatbot-dev --timeout=600s

# Initialize schema
echo "📊 Initializing graph schema..."
sleep 30
kubectl exec neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass -f /var/lib/neo4j/init/01-schema-setup.cypher
kubectl exec neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass -f /var/lib/neo4j/init/02-initial-data.cypher
kubectl exec neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass -f /var/lib/neo4j/init/03-agent-partitions.cypher
kubectl exec neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass -f /var/lib/neo4j/init/04-knowledge-graph.cypher

# Deploy Neo4j read replicas
echo "🔄 Deploying Neo4j read replicas..."
kubectl apply -f infrastructure/kubernetes/deployments/neo4j-replica.yaml

# Wait for replicas to be ready
echo "⏳ Waiting for Neo4j replicas to be ready..."
sleep 60
kubectl wait --for=condition=ready pod -l app=neo4j-replica -n chatbot-dev --timeout=300s

# Setup backup job
echo "💾 Setting up backup job..."
kubectl apply -f infrastructure/kubernetes/jobs/neo4j-backup.yaml

echo "✅ Neo4j cluster setup completed!"
echo ""
echo "📊 Cluster Status:"
kubectl get pods -l app=neo4j-core -n chatbot-dev
kubectl get pods -l app=neo4j-replica -n chatbot-dev
echo ""
echo "🌐 Services:"
kubectl get services -l app=neo4j -n chatbot-dev
echo ""
echo "🔗 Connection Info:"
echo "  Write (Core):  neo4j-write-service:7687"
echo "  Read (Replica): neo4j-read-service:7687"
echo "  Browser:       neo4j-write-service:7474"
echo ""
echo "🧪 Test Connection:"
echo "  kubectl exec -it neo4j-core-0 -n chatbot-dev -- cypher-shell -u neo4j -p chatbot_neo4j_pass"