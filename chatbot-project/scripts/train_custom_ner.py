#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from ai.nlp.entity.training.ner_trainer import NERTrainer

def train_custom_ner():
    print("🚀 Training Custom NER Model for Organizational Entities")
    
    trainer = NERTrainer()
    
    print("\n📊 Training Data Overview:")
    training_data = trainer.get_training_data()
    print(f"   Total training examples: {len(training_data)}")
    
    entity_types = set()
    for _, annotations in training_data:
        for entity in annotations.get("entities", []):
            entity_types.add(entity[2])
    
    print(f"   Entity types: {', '.join(entity_types)}")
    
    print("\n🔄 Starting training process...")
    trained_model = trainer.train_model(iterations=50)
    
    print("\n💾 Saving trained model...")
    model_path = "./models/custom_ner_model"
    trainer.save_model(model_path)
    
    print("\n🧪 Evaluating model performance...")
    test_data = [
        ("<PERSON><PERSON><PERSON> works at Meril Diagnostics", {
            "entities": [(0, 12, "PERSON"), (22, 39, "ORG")]
        }),
        ("The LTA policy is managed by HR department", {
            "entities": [(4, 14, "POLICY"), (31, 44, "DEPT")]
        })
    ]
    
    evaluation_results = trainer.evaluate_model(test_data)
    print(f"   Accuracy: {evaluation_results['accuracy']:.2f}")
    print(f"   Correct predictions: {evaluation_results['correct']}/{evaluation_results['total']}")
    
    print("\n✅ Custom NER model training completed!")
    print(f"   Model saved to: {model_path}")

if __name__ == "__main__":
    train_custom_ner()