import sys
import os
import time
import signal
import argparse

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from agents.communication.communication_service import CommunicationService
from agents.core.agent_registry import AgentRegistry

def signal_handler(sig, frame):
    print('Shutting down communication service...')
    if 'comm_service' in globals():
        comm_service.stop()
    sys.exit(0)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Run the agent communication service')
    parser.add_argument('--redis-host', default='localhost', help='Redis host')
    parser.add_argument('--redis-port', type=int, default=6379, help='Redis port')
    parser.add_argument('--grpc-port', type=int, default=50051, help='gRPC port')
    args = parser.parse_args()
    
    # Register signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create agent registry
    agent_registry = AgentRegistry()
    
    # Create and start communication service
    comm_service = CommunicationService(
        redis_host=args.redis_host,
        redis_port=args.redis_port,
        grpc_port=args.grpc_port
    )
    
    # Start the service
    comm_service.start(agent_registry)
    
    print(f"Communication service running on port {args.grpc_port}")
    print("Press Ctrl+C to stop")
    
    # Keep the script running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print('Shutting down communication service...')
        comm_service.stop()