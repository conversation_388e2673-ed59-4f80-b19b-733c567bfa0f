#!/usr/bin/env python3
"""
Comprehensive Test Script for the Knowledge Fusion System.
Tests the unified knowledge fusion interface with all components.
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any
import time

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

# Import knowledge fusion components
from ai.knowledge.fusion.unified_knowledge_fusion import (
    UnifiedKnowledgeFusion, FusionConfiguration, ProcessingMode,
    create_fast_fusion_system, create_balanced_fusion_system, create_comprehensive_fusion_system
)
from ai.knowledge.fusion.multi_source_fusion import FusionMethod, SourceType, SourceReliability
from ai.knowledge.fusion.vector_graph_integration import SearchMode
from ai.knowledge.fusion.conflict_resolution import ConflictType, ResolutionStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Sample test data
SAMPLE_DOCUMENTS = [
    {
        "id": "doc1",
        "content": "NUVO AI is a leading technology company with over 500 employees. "
                  "The company was founded in 2015 by <PERSON>. <PERSON> and has offices "
                  "in San Francisco, New York, and London. NUVO's leave policy allows "
                  "employees to take 20 days of paid time off per year."
    },
    {
        "id": "doc2",
        "content": "According to the annual report, NUVO Artificial Intelligence Corp. "
                  "currently employs approximately 450 people across its global offices. "
                  "Dr. Johnson, the CEO, announced new benefits including flexible work "
                  "arrangements and competitive leave policies."
    },
    {
        "id": "doc3",
        "content": "The HR handbook at NUVO states that staff members receive 15 days "
                  "of annual leave plus 5 sick days. The company's headquarters is located "
                  "in San Francisco, with satellite offices in NYC and London."
    }
]

SAMPLE_QUERIES = [
    "What is NUVO AI's leave policy?",
    "How many employees work at NUVO AI?",
    "Where are NUVO AI's offices located?",
    "Who is the CEO of NUVO AI?",
    "When was NUVO AI founded?"
]

SAMPLE_CONTEXT = {
    "organization": "NUVO AI",
    "department": "HR",
    "user_role": "employee"
}

async def test_unified_knowledge_fusion():
    """Test the unified knowledge fusion system."""
    logger.info("🚀 Starting Unified Knowledge Fusion System Test")
    logger.info("=" * 80)
    
    # Test different system configurations
    configurations = [
        ("Fast", create_fast_fusion_system()),
        ("Balanced", create_balanced_fusion_system()),
        ("Comprehensive", create_comprehensive_fusion_system())
    ]
    
    all_results = {}
    
    for config_name, fusion_system in configurations:
        logger.info(f"\n🔍 Testing {config_name} Knowledge Fusion Configuration")
        
        # Process a single query
        logger.info(f"Processing single query with {config_name} configuration...")
        start_time = time.time()
        
        result = await fusion_system.process_query(
            query=SAMPLE_QUERIES[0],
            documents=SAMPLE_DOCUMENTS,
            context=SAMPLE_CONTEXT
        )
        
        processing_time = time.time() - start_time
        
        logger.info(f"✅ Query processed in {processing_time:.2f}s with confidence {result.confidence:.3f}")
        logger.info(f"Result: {result.fused_content[:100]}...")
        logger.info(f"Components used: {', '.join(result.components_used)}")
        
        # Process batch queries
        logger.info(f"Processing batch queries with {config_name} configuration...")
        start_time = time.time()
        
        batch_results = await fusion_system.batch_process_queries(
            queries=SAMPLE_QUERIES,
            documents=SAMPLE_DOCUMENTS,
            context=SAMPLE_CONTEXT
        )
        
        batch_time = time.time() - start_time
        avg_confidence = sum(r.confidence for r in batch_results) / len(batch_results)
        
        logger.info(f"✅ {len(batch_results)} queries processed in {batch_time:.2f}s "
                   f"(avg: {batch_time/len(batch_results):.2f}s per query)")
        logger.info(f"Average confidence: {avg_confidence:.3f}")
        
        # Get processing statistics
        stats = fusion_system.get_processing_statistics()
        logger.info(f"Processing statistics: {stats['total_queries_processed']} queries, "
                   f"avg time: {stats['average_processing_time']:.2f}s, "
                   f"avg confidence: {stats['average_confidence']:.3f}")
        
        # Store results for comparison
        all_results[config_name] = {
            "single_query": {
                "time": processing_time,
                "confidence": result.confidence,
                "content_length": len(result.fused_content),
                "components_used": result.components_used
            },
            "batch_queries": {
                "total_time": batch_time,
                "avg_time_per_query": batch_time / len(batch_results),
                "avg_confidence": avg_confidence,
                "results_count": len(batch_results)
            },
            "statistics": stats
        }
    
    # Compare configurations
    logger.info("\n📊 Configuration Comparison")
    logger.info("=" * 80)
    
    logger.info("Single Query Performance:")
    for config_name, results in all_results.items():
        single_query = results["single_query"]
        logger.info(f"  {config_name}: {single_query['time']:.2f}s, "
                   f"confidence: {single_query['confidence']:.3f}, "
                   f"components: {len(single_query['components_used'])}")
    
    logger.info("\nBatch Processing Performance:")
    for config_name, results in all_results.items():
        batch = results["batch_queries"]
        logger.info(f"  {config_name}: {batch['avg_time_per_query']:.2f}s per query, "
                   f"confidence: {batch['avg_confidence']:.3f}")
    
    # Test custom configuration
    logger.info("\n🔧 Testing Custom Configuration")
    
    custom_config = FusionConfiguration(
        processing_mode=ProcessingMode.CUSTOM,
        fusion_method=FusionMethod.BAYESIAN_INTEGRATION,
        search_mode=SearchMode.ITERATIVE,
        conflict_types=[ConflictType.FACTUAL, ConflictType.NUMERICAL],
        resolution_strategy=ResolutionStrategy.EVIDENCE,
        enable_coreference=True,
        max_results=15
    )
    
    custom_system = UnifiedKnowledgeFusion(custom_config)
    
    result = await custom_system.process_query(
        query="What is the company's policy on remote work?",
        documents=SAMPLE_DOCUMENTS,
        context=SAMPLE_CONTEXT
    )
    
    logger.info(f"✅ Custom configuration result: {result.fused_content[:100]}...")
    logger.info(f"Confidence: {result.confidence:.3f}, Components: {', '.join(result.components_used)}")
    
    # Export results
    results_file = "../knowledge_fusion_unified_test_results.json"
    with open(results_file, 'w') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "configuration_results": {k: v for k, v in all_results.items()},
            "custom_configuration": {
                "confidence": result.confidence,
                "components_used": result.components_used,
                "processing_time": result.processing_time
            }
        }, f, indent=2)
    
    logger.info(f"\n📄 Detailed results saved to {results_file}")
    logger.info("\n🎉 Knowledge Fusion System tests completed successfully!")
    logger.info("✅ All components are working together perfectly")
    logger.info("🚀 The Knowledge Fusion System is ready for production use")

async def main():
    """Main test execution function."""
    try:
        await test_unified_knowledge_fusion()
        return True
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
