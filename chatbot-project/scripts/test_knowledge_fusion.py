#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))

from ai.knowledge.fusion.knowledge_fusion import KnowledgeFusion
from ai.knowledge.document_processor import DocumentProcessor

def test_knowledge_fusion():
    print("🧪 Testing Knowledge Fusion Engine")
    
    # Initialize fusion engine
    fusion = KnowledgeFusion()
    doc_processor = DocumentProcessor()
    
    print("\n1. Loading Documents:")
    documents = doc_processor.load_documents()
    print(f"   Documents loaded: {len(documents)}")
    
    print("\n2. Initializing Knowledge Fusion:")
    fusion.initialize(documents)
    print("   ✅ Fusion engine initialized")
    
    # Test queries
    test_queries = [
        {
            "query": "What is the privilege leave policy at NUVO AI?",
            "context": {"organization": "NUVO AI Pvt Ltd", "department": "HR"}
        },
        {
            "query": "Who is <PERSON> and what is her role?",
            "context": {"organization": "Meril Life Sciences"}
        },
        {
            "query": "Sexual harassment committee members at Meril companies",
            "context": {"organization": "Meril Group"}
        },
        {
            "query": "Travel policy differences between NUVO AI and Meril",
            "context": {}
        }
    ]
    
    print("\n3. Testing Fused Search:")
    for i, test in enumerate(test_queries, 1):
        print(f"\n   Query {i}: {test['query']}")
        
        results = fusion.fused_search(
            test['query'], 
            3, 
            test['context']
        )
        
        print(f"   Results: {len(results)}")
        
        if results:
            top_result = results[0]
            print(f"   Top Score: {top_result.get('fusion_score', 0):.3f}")
            print(f"   Vector Score: {top_result.get('score_breakdown', {}).get('vector_score', 0):.3f}")
            print(f"   Graph Context: {len(top_result.get('graph_context', {}))}")
            print(f"   Entity Connections: {len(top_result.get('entity_connections', []))}")
            print(f"   Text Preview: {top_result['text'][:100]}...")
    
    print("\n4. Testing Knowledge Summary:")
    summary_query = "NUVO AI leave policies and key personnel"
    summary = fusion.get_knowledge_summary(
        summary_query,
        {"organization": "NUVO AI Pvt Ltd"}
    )
    
    print(f"   Query: {summary['query']}")
    print(f"   Total Results: {summary['total_results']}")
    print(f"   Key Entities: {summary['key_entities']}")
    print(f"   Organizations: {summary['organizations_involved']}")
    print(f"   Policies: {summary['policies_referenced']}")
    print(f"   Top Score: {summary['top_result_score']:.3f}")
    print(f"   Knowledge Sources: {summary['knowledge_sources']}")
    
    print("\n5. Testing Cross-Organization Query:")
    cross_org_results = fusion.fused_search(
        "Compare maternity leave policies between NUVO AI and Meril",
        5,
        {"query_type": "comparison"}
    )
    
    print(f"   Cross-org results: {len(cross_org_results)}")
    if cross_org_results:
        for i, result in enumerate(cross_org_results[:2], 1):
            print(f"   Result {i} Score: {result.get('fusion_score', 0):.3f}")
            print(f"   Organization: {result.get('metadata', {}).get('organization', 'N/A')}")
    
    print("\n✅ Knowledge Fusion testing completed!")
    print("\n📈 Key Features Tested:")
    print("   • Vector + Graph knowledge fusion")
    print("   • Multi-factor scoring system")
    print("   • Entity extraction and linking")
    print("   • Cross-validation and contradiction detection")
    print("   • Knowledge summarization")
    print("   • Cross-organizational queries")

if __name__ == "__main__":
    test_knowledge_fusion()