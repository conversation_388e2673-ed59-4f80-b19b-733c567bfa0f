#!/bin/bash

# Memgraph Setup Script for Real-time Agent Reasoning

set -e

echo "🧠 Setting up Memgraph for real-time agent reasoning graphs..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed"
    exit 1
fi

# Create Memgraph secrets
echo "🔐 Creating Memgraph secrets..."
kubectl apply -f infrastructure/kubernetes/secrets/memgraph-secrets.yaml

# Create Memgraph configuration
echo "⚙️ Creating Memgraph configuration..."
kubectl apply -f infrastructure/kubernetes/configmaps/memgraph-config.yaml
kubectl apply -f infrastructure/kubernetes/configmaps/streaming-config.yaml

# Deploy Memgraph cluster
echo "🚀 Deploying Memgraph cluster..."
kubectl apply -f infrastructure/kubernetes/deployments/memgraph.yaml

# Create Memgraph services
echo "🌐 Creating Memgraph services..."
kubectl apply -f infrastructure/kubernetes/services/memgraph-service.yaml

# Wait for Memgraph cluster to be ready
echo "⏳ Waiting for Memgraph cluster to be ready..."
kubectl wait --for=condition=ready pod -l app=memgraph -n chatbot-dev --timeout=300s

# Initialize reasoning graph procedures
echo "📊 Initializing reasoning graph procedures..."
sleep 30

# Load custom procedures
kubectl exec memgraph-0 -n chatbot-dev -- bash -c "
    echo 'Loading reasoning graph procedures...'
    # Procedures are loaded from the mounted volume automatically
"

# Deploy streaming components
echo "🔄 Deploying streaming components..."
kubectl apply -f infrastructure/kubernetes/deployments/memgraph-streaming.yaml

# Wait for streaming components
echo "⏳ Waiting for streaming components to be ready..."
kubectl wait --for=condition=available deployment/memgraph-streaming -n chatbot-dev --timeout=180s

# Test connection and procedures
echo "🧪 Testing Memgraph connection and procedures..."
kubectl exec memgraph-0 -n chatbot-dev -- bash -c "
    echo 'Testing connection...'
    echo 'RETURN \"Memgraph is ready for real-time reasoning!\" as status;' | mgconsole --host localhost --port 7687 --username memgraph --password chatbot_memgraph_pass
"

echo "✅ Memgraph setup completed!"
echo ""
echo "📊 Cluster Status:"
kubectl get pods -l app=memgraph -n chatbot-dev
kubectl get pods -l app=memgraph-streaming -n chatbot-dev
echo ""
echo "🌐 Services:"
kubectl get services -l app=memgraph -n chatbot-dev
echo ""
echo "🔗 Connection Info:"
echo "  Bolt Protocol: memgraph-lb-service:7687"
echo "  Monitoring:    memgraph-lb-service:7444"
echo "  Username:      memgraph"
echo "  Password:      chatbot_memgraph_pass"
echo ""
echo "🧪 Test Connection:"
echo "  kubectl exec -it memgraph-0 -n chatbot-dev -- mgconsole --host localhost --port 7687 --username memgraph --password chatbot_memgraph_pass"
echo ""
echo "📈 Available Procedures:"
echo "  - get_agent_reasoning_path(agent_id, session_id)"
echo "  - add_reasoning_step(agent_id, session_id, step_type, content, confidence)"
echo "  - analyze_reasoning_patterns(agent_type, time_window_hours)"
echo "  - get_hot_reasoning_paths(time_window_minutes)"
echo "  - cleanup_old_reasoning_data(retention_hours)"
echo "  - get_agent_performance_metrics(agent_id)"