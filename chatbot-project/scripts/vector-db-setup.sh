#!/bin/bash

# Vector Database Setup Script

set -e

echo "🔍 Setting up Milvus vector database and monitoring..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed"
    exit 1
fi

# Create Milvus secrets
echo "🔐 Creating Milvus secrets..."
kubectl apply -f infrastructure/kubernetes/secrets/milvus-secrets.yaml

# Create Milvus configuration
echo "⚙️ Creating Milvus configuration..."
kubectl apply -f infrastructure/kubernetes/configmaps/milvus-config.yaml

# Deploy etcd cluster first
echo "🗄️ Deploying etcd cluster..."
kubectl apply -f infrastructure/kubernetes/deployments/milvus.yaml

# Wait for etcd to be ready
echo "⏳ Waiting for etcd cluster to be ready..."
kubectl wait --for=condition=ready pod -l app=etcd -n chatbot-dev --timeout=300s

# Deploy MinIO
echo "📦 Deploying MinIO object storage..."
sleep 30

# Wait for MinIO to be ready
echo "⏳ Waiting for MinIO to be ready..."
kubectl wait --for=condition=ready pod -l app=minio -n chatbot-dev --timeout=180s

# Create services
echo "🌐 Creating services..."
kubectl apply -f infrastructure/kubernetes/services/milvus-services.yaml

# Deploy Milvus
echo "🚀 Deploying Milvus vector database..."
sleep 30

# Wait for Milvus to be ready
echo "⏳ Waiting for Milvus to be ready..."
kubectl wait --for=condition=ready pod -l app=milvus -n chatbot-dev --timeout=300s

# Initialize Milvus collections
echo "📊 Initializing Milvus collections..."
sleep 60

# Test Milvus connection
kubectl exec milvus-0 -n chatbot-dev -- python3 -c "
import pymilvus
try:
    connections = pymilvus.connections
    connections.connect('default', host='localhost', port='19530')
    print('✅ Milvus connection successful')
    
    # Create collections for different embedding types
    from pymilvus import Collection, CollectionSchema, FieldSchema, DataType
    
    # Document embeddings collection
    doc_fields = [
        FieldSchema(name='id', dtype=DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema(name='document_id', dtype=DataType.VARCHAR, max_length=255),
        FieldSchema(name='organization_id', dtype=DataType.INT64),
        FieldSchema(name='department_id', dtype=DataType.INT64),
        FieldSchema(name='embedding', dtype=DataType.FLOAT_VECTOR, dim=384),
        FieldSchema(name='content', dtype=DataType.VARCHAR, max_length=65535),
        FieldSchema(name='metadata', dtype=DataType.JSON)
    ]
    doc_schema = CollectionSchema(doc_fields, 'Document embeddings for RAG')
    doc_collection = Collection('document_embeddings', doc_schema)
    
    # Query embeddings collection
    query_fields = [
        FieldSchema(name='id', dtype=DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema(name='query_id', dtype=DataType.VARCHAR, max_length=255),
        FieldSchema(name='user_id', dtype=DataType.INT64),
        FieldSchema(name='embedding', dtype=DataType.FLOAT_VECTOR, dim=384),
        FieldSchema(name='query_text', dtype=DataType.VARCHAR, max_length=65535),
        FieldSchema(name='timestamp', dtype=DataType.VARCHAR, max_length=50)
    ]
    query_schema = CollectionSchema(query_fields, 'Query embeddings for similarity search')
    query_collection = Collection('query_embeddings', query_schema)
    
    print('✅ Collections created successfully')
    
except Exception as e:
    print(f'❌ Error: {e}')
"

# Deploy monitoring stack
echo "📈 Deploying monitoring stack..."
kubectl apply -f infrastructure/kubernetes/monitoring/prometheus.yaml
kubectl apply -f infrastructure/kubernetes/monitoring/grafana.yaml

# Wait for monitoring to be ready
echo "⏳ Waiting for monitoring stack to be ready..."
kubectl wait --for=condition=available deployment/prometheus -n chatbot-dev --timeout=180s
kubectl wait --for=condition=available deployment/grafana -n chatbot-dev --timeout=180s

echo "✅ Vector database and monitoring setup completed!"
echo ""
echo "📊 Cluster Status:"
kubectl get pods -l app=milvus -n chatbot-dev
kubectl get pods -l app=etcd -n chatbot-dev
kubectl get pods -l app=minio -n chatbot-dev
kubectl get pods -l app=prometheus -n chatbot-dev
kubectl get pods -l app=grafana -n chatbot-dev
echo ""
echo "🌐 Services:"
kubectl get services -l app=milvus -n chatbot-dev
kubectl get services -l app=prometheus -n chatbot-dev
kubectl get services -l app=grafana -n chatbot-dev
echo ""
echo "🔗 Connection Info:"
echo "  Milvus gRPC:    milvus-service:19530"
echo "  Milvus Metrics: milvus-service:9091"
echo "  Prometheus:     prometheus-service:9090"
echo "  Grafana:        grafana-service:3000 (admin/admin123)"
echo ""
echo "🧪 Test Connections:"
echo "  kubectl port-forward service/milvus-service 19530:19530 -n chatbot-dev"
echo "  kubectl port-forward service/prometheus-service 9090:9090 -n chatbot-dev"
echo "  kubectl port-forward service/grafana-service 3000:3000 -n chatbot-dev"