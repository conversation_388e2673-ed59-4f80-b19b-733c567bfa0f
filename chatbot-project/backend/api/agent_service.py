"""
Agent Service for CHaBot Integrated System
Handles agent management and coordination
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class AgentService:
    """Service for managing agents and their coordination."""
    
    def __init__(self, 
                 agent_orchestrator=None,
                 communication_infrastructure=None):
        """Initialize the agent service."""
        self.agent_orchestrator = agent_orchestrator
        self.communication_infrastructure = communication_infrastructure
        
        # Mock agent registry for when orchestrator is not available
        self.mock_agents = {
            "research": {
                "id": "research-001",
                "name": "Research Agent",
                "type": "research",
                "status": "active",
                "capabilities": ["web_search", "document_analysis", "fact_checking"],
                "created_at": datetime.now(),
                "last_activity": datetime.now()
            },
            "analysis": {
                "id": "analysis-001", 
                "name": "Analysis Agent",
                "type": "analysis",
                "status": "active",
                "capabilities": ["data_analysis", "pattern_recognition", "statistical_analysis"],
                "created_at": datetime.now(),
                "last_activity": datetime.now()
            },
            "creative": {
                "id": "creative-001",
                "name": "Creative Agent", 
                "type": "creative",
                "status": "active",
                "capabilities": ["content_generation", "brainstorming", "creative_writing"],
                "created_at": datetime.now(),
                "last_activity": datetime.now()
            },
            "technical": {
                "id": "technical-001",
                "name": "Technical Agent",
                "type": "technical", 
                "status": "active",
                "capabilities": ["code_analysis", "technical_documentation", "system_design"],
                "created_at": datetime.now(),
                "last_activity": datetime.now()
            }
        }
        
        logger.info("Agent Service initialized")
    
    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all available agents."""
        try:
            if self.agent_orchestrator:
                # Use real orchestrator if available
                return await self.agent_orchestrator.list_agents()
            else:
                # Return mock agents
                agents = []
                for agent_id, agent_data in self.mock_agents.items():
                    agent_info = agent_data.copy()
                    agent_info["created_at"] = agent_info["created_at"].isoformat()
                    agent_info["last_activity"] = agent_info["last_activity"].isoformat()
                    agents.append(agent_info)
                return agents
                
        except Exception as e:
            logger.error(f"Failed to list agents: {e}")
            return []
    
    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific agent."""
        try:
            if self.agent_orchestrator:
                return await self.agent_orchestrator.get_agent_status(agent_id)
            else:
                # Check mock agents
                for mock_id, agent_data in self.mock_agents.items():
                    if agent_data["id"] == agent_id or mock_id == agent_id:
                        agent_info = agent_data.copy()
                        agent_info["created_at"] = agent_info["created_at"].isoformat()
                        agent_info["last_activity"] = agent_info["last_activity"].isoformat()
                        return agent_info
                return None
                
        except Exception as e:
            logger.error(f"Failed to get agent status for {agent_id}: {e}")
            return None
    
    async def assign_task(self, agent_id: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """Assign a task to a specific agent."""
        try:
            task_id = str(uuid.uuid4())
            
            if self.agent_orchestrator:
                # Use real orchestrator
                result = await self.agent_orchestrator.assign_task(agent_id, task)
                return {
                    "success": True,
                    "task_id": task_id,
                    "agent_id": agent_id,
                    "result": result,
                    "assigned_at": datetime.now().isoformat()
                }
            else:
                # Mock task assignment
                agent = await self.get_agent_status(agent_id)
                if not agent:
                    return {
                        "success": False,
                        "error": f"Agent {agent_id} not found",
                        "task_id": task_id
                    }
                
                # Simulate task processing
                await asyncio.sleep(0.1)  # Simulate processing time
                
                # Update mock agent activity
                for mock_id, agent_data in self.mock_agents.items():
                    if agent_data["id"] == agent_id or mock_id == agent_id:
                        agent_data["last_activity"] = datetime.now()
                        break
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "agent_id": agent_id,
                    "agent_name": agent["name"],
                    "task": task,
                    "status": "completed",
                    "result": f"Mock result for task: {task.get('description', 'No description')}",
                    "assigned_at": datetime.now().isoformat(),
                    "completed_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Failed to assign task to agent {agent_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "task_id": task_id if 'task_id' in locals() else None
            }
    
    async def get_agent_capabilities(self, agent_type: str) -> List[str]:
        """Get capabilities of agents by type."""
        try:
            capabilities = []
            
            if self.agent_orchestrator:
                # Use real orchestrator
                agents = await self.list_agents()
                for agent in agents:
                    if agent.get("type") == agent_type:
                        capabilities.extend(agent.get("capabilities", []))
            else:
                # Use mock data
                for agent_data in self.mock_agents.values():
                    if agent_data["type"] == agent_type:
                        capabilities.extend(agent_data["capabilities"])
            
            return list(set(capabilities))  # Remove duplicates
            
        except Exception as e:
            logger.error(f"Failed to get capabilities for agent type {agent_type}: {e}")
            return []
    
    async def coordinate_agents(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate multiple agents for a complex task."""
        try:
            coordination_id = str(uuid.uuid4())
            
            if self.agent_orchestrator:
                # Use real orchestrator
                result = await self.agent_orchestrator.coordinate_task(task)
                return {
                    "success": True,
                    "coordination_id": coordination_id,
                    "result": result,
                    "started_at": datetime.now().isoformat()
                }
            else:
                # Mock coordination
                task_type = task.get("type", "general")
                description = task.get("description", "No description")
                
                # Simulate agent selection based on task type
                selected_agents = []
                if "research" in description.lower() or "search" in description.lower():
                    selected_agents.append("research")
                if "analyze" in description.lower() or "analysis" in description.lower():
                    selected_agents.append("analysis")
                if "create" in description.lower() or "generate" in description.lower():
                    selected_agents.append("creative")
                if "code" in description.lower() or "technical" in description.lower():
                    selected_agents.append("technical")
                
                if not selected_agents:
                    selected_agents = ["research"]  # Default agent
                
                # Simulate coordination
                await asyncio.sleep(0.2)  # Simulate processing time
                
                results = {}
                for agent_type in selected_agents:
                    agent_task = {
                        "type": task_type,
                        "description": f"{agent_type.title()} subtask: {description}",
                        "coordination_id": coordination_id
                    }
                    
                    agent_result = await self.assign_task(f"{agent_type}-001", agent_task)
                    results[agent_type] = agent_result
                
                return {
                    "success": True,
                    "coordination_id": coordination_id,
                    "task": task,
                    "selected_agents": selected_agents,
                    "agent_results": results,
                    "started_at": datetime.now().isoformat(),
                    "completed_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Failed to coordinate agents: {e}")
            return {
                "success": False,
                "error": str(e),
                "coordination_id": coordination_id if 'coordination_id' in locals() else None
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get agent service statistics."""
        try:
            active_agents = len([a for a in self.mock_agents.values() if a["status"] == "active"])
            
            return {
                "total_agents": len(self.mock_agents),
                "active_agents": active_agents,
                "agent_types": list(set(a["type"] for a in self.mock_agents.values())),
                "orchestrator_available": self.agent_orchestrator is not None,
                "communication_available": self.communication_infrastructure is not None,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get agent statistics: {e}")
            return {
                "error": str(e),
                "last_updated": datetime.now().isoformat()
            }
