"""
Chat Service for CHaBot Integrated System
Handles chat interactions and message processing
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class ChatService:
    """Service for handling chat interactions."""
    
    def __init__(self, 
                 reasoning_engine=None,
                 memory_system=None,
                 agent_orchestrator=None):
        """Initialize the chat service."""
        self.reasoning_engine = reasoning_engine
        self.memory_system = memory_system
        self.agent_orchestrator = agent_orchestrator
        
        # Chat session management
        self.active_sessions = {}
        
        logger.info("Chat Service initialized")
    
    async def process_message(self, message: str, user_id: str = "default", session_id: Optional[str] = None) -> Dict[str, Any]:
        """Process a chat message and return response."""
        try:
            # Generate session ID if not provided
            if not session_id:
                session_id = str(uuid.uuid4())
            
            # Create or update session
            if session_id not in self.active_sessions:
                self.active_sessions[session_id] = {
                    "user_id": user_id,
                    "created_at": datetime.now(),
                    "message_count": 0,
                    "context": {}
                }
            
            session = self.active_sessions[session_id]
            session["message_count"] += 1
            session["last_activity"] = datetime.now()
            
            # Store message in memory if available
            if self.memory_system:
                try:
                    from ai.memory.enhanced_integrated_memory_system import MemoryQuery
                    
                    # Store user message
                    await self.memory_system.episodic_memory.store_episode(
                        episode_type="conversation",
                        title=f"User Message - {session_id}",
                        description=f"User message in session {session_id}",
                        content={
                            "message": message,
                            "user_id": user_id,
                            "session_id": session_id,
                            "timestamp": datetime.now().isoformat()
                        },
                        importance=0.7,
                        tags=["chat", "user_message"],
                        user_id=user_id
                    )
                    
                    # Query relevant context
                    query = MemoryQuery(
                        query_text=message,
                        memory_types=["episodic", "semantic"],
                        limit=5,
                        user_id=user_id
                    )
                    
                    context = await self.memory_system.comprehensive_query(query)
                    session["context"] = {
                        "relevant_memories": len(context.episodic_memories),
                        "relevant_concepts": len(context.semantic_concepts)
                    }
                    
                except Exception as e:
                    logger.warning(f"Memory system error: {e}")
                    context = None
            
            # Generate response
            response = await self._generate_response(message, session, context)
            
            # Store response in memory if available
            if self.memory_system:
                try:
                    await self.memory_system.episodic_memory.store_episode(
                        episode_type="conversation",
                        title=f"Assistant Response - {session_id}",
                        description=f"Assistant response in session {session_id}",
                        content={
                            "response": response["message"],
                            "user_id": user_id,
                            "session_id": session_id,
                            "timestamp": datetime.now().isoformat(),
                            "processing_time": response.get("processing_time", 0)
                        },
                        importance=0.6,
                        tags=["chat", "assistant_response"],
                        user_id=user_id
                    )
                except Exception as e:
                    logger.warning(f"Failed to store response in memory: {e}")
            
            return {
                "success": True,
                "response": response,
                "session_id": session_id,
                "message_count": session["message_count"]
            }
            
        except Exception as e:
            logger.error(f"Chat processing error: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": {
                    "message": "I apologize, but I encountered an error processing your message. Please try again.",
                    "type": "error"
                }
            }
    
    async def _generate_response(self, message: str, session: Dict[str, Any], context: Any = None) -> Dict[str, Any]:
        """Generate a response to the user message."""
        start_time = datetime.now()
        
        try:
            # Simple response generation for now
            # In production, this would use the reasoning engine and agents
            
            response_message = f"I received your message: '{message}'. "
            
            if context and hasattr(context, 'episodic_memories') and context.episodic_memories:
                response_message += f"I found {len(context.episodic_memories)} relevant memories. "
            
            if context and hasattr(context, 'semantic_concepts') and context.semantic_concepts:
                response_message += f"I also found {len(context.semantic_concepts)} related concepts. "
            
            response_message += f"This is message #{session['message_count']} in our conversation."
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "message": response_message,
                "type": "text",
                "processing_time": processing_time,
                "context_used": context is not None,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Response generation error: {e}")
            return {
                "message": "I apologize, but I'm having trouble generating a response right now.",
                "type": "error",
                "processing_time": (datetime.now() - start_time).total_seconds()
            }
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a chat session."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id].copy()
            session["created_at"] = session["created_at"].isoformat()
            session["last_activity"] = session.get("last_activity", datetime.now()).isoformat()
            return session
        return None
    
    async def list_active_sessions(self) -> List[Dict[str, Any]]:
        """List all active chat sessions."""
        sessions = []
        for session_id, session_data in self.active_sessions.items():
            session_info = {
                "session_id": session_id,
                "user_id": session_data["user_id"],
                "message_count": session_data["message_count"],
                "created_at": session_data["created_at"].isoformat(),
                "last_activity": session_data.get("last_activity", datetime.now()).isoformat()
            }
            sessions.append(session_info)
        return sessions
    
    async def end_session(self, session_id: str) -> bool:
        """End a chat session."""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            logger.info(f"Ended chat session: {session_id}")
            return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get chat service statistics."""
        total_messages = sum(session["message_count"] for session in self.active_sessions.values())
        
        return {
            "active_sessions": len(self.active_sessions),
            "total_messages": total_messages,
            "average_messages_per_session": total_messages / max(len(self.active_sessions), 1),
            "memory_system_available": self.memory_system is not None,
            "reasoning_engine_available": self.reasoning_engine is not None,
            "agent_orchestrator_available": self.agent_orchestrator is not None
        }
