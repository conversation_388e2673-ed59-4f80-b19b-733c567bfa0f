"""
Memory Service for CHaBot Integrated System
Handles memory operations and queries
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class MemoryService:
    """Service for managing memory operations."""
    
    def __init__(self, memory_system=None):
        """Initialize the memory service."""
        self.memory_system = memory_system
        
        logger.info("Memory Service initialized")
    
    async def query_memory(self, query: str, limit: int = 10, memory_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Query memory systems for relevant information."""
        try:
            if not self.memory_system:
                return {
                    "success": False,
                    "error": "Memory system not available",
                    "results": []
                }
            
            # Import memory query
            from ai.memory.enhanced_integrated_memory_system import MemoryQuery
            
            # Create memory query
            memory_query = MemoryQuery(
                query_text=query,
                memory_types=memory_types or ["episodic", "semantic", "working"],
                limit=limit
            )
            
            # Execute query
            result = await self.memory_system.comprehensive_query(memory_query)
            
            return {
                "success": True,
                "query": query,
                "results": {
                    "episodic_memories": [mem for mem in result.episodic_memories],
                    "semantic_concepts": [concept for concept in result.semantic_concepts],
                    "working_memory_items": [item for item in result.working_memory_items],
                    "related_concepts": [rel for rel in result.related_concepts],
                    "attention_focus": [focus for focus in result.attention_focus]
                },
                "total_results": result.total_results,
                "query_time": result.query_time,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Memory query failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "results": [],
                "timestamp": datetime.now().isoformat()
            }
    
    async def store_memory(self, memory_type: str, content: Dict[str, Any]) -> Dict[str, Any]:
        """Store information in memory."""
        try:
            if not self.memory_system:
                return {
                    "success": False,
                    "error": "Memory system not available"
                }
            
            memory_id = None
            
            if memory_type == "episodic":
                # Store episodic memory
                memory_id = await self.memory_system.episodic_memory.store_episode(
                    episode_type=content.get("episode_type", "user_interaction"),
                    title=content.get("title", "User Memory"),
                    description=content.get("description", ""),
                    content=content.get("content", {}),
                    importance=content.get("importance", 0.5),
                    tags=content.get("tags", []),
                    user_id=content.get("user_id"),
                    agent_id=content.get("agent_id"),
                    organization=content.get("organization")
                )
                
            elif memory_type == "semantic":
                # Store semantic concept
                from ai.memory.semantic_memory import ConceptType
                
                concept_type_map = {
                    "entity": ConceptType.ENTITY,
                    "concept": ConceptType.CONCEPT,
                    "procedure": ConceptType.PROCEDURE,
                    "fact": ConceptType.FACT,
                    "rule": ConceptType.RULE,
                    "pattern": ConceptType.PATTERN,
                    "category": ConceptType.CATEGORY
                }
                
                concept_type = concept_type_map.get(
                    content.get("concept_type", "concept"),
                    ConceptType.CONCEPT
                )
                
                memory_id = await self.memory_system.semantic_memory.add_concept(
                    name=content.get("name", "Unnamed Concept"),
                    concept_type=concept_type,
                    description=content.get("description", ""),
                    properties=content.get("properties", {}),
                    confidence=content.get("confidence", 0.8),
                    importance=content.get("importance", 0.5),
                    domain=content.get("domain"),
                    tags=content.get("tags", [])
                )
                
            elif memory_type == "working":
                # Store working memory item
                from ai.memory.working_memory import WorkingMemoryType, Priority
                
                type_map = {
                    "goal": WorkingMemoryType.CURRENT_GOAL,
                    "context": WorkingMemoryType.CONTEXT,
                    "result": WorkingMemoryType.INTERMEDIATE_RESULT,
                    "reasoning": WorkingMemoryType.REASONING_STEP,
                    "hypothesis": WorkingMemoryType.HYPOTHESIS,
                    "constraint": WorkingMemoryType.CONSTRAINT,
                    "resource": WorkingMemoryType.RESOURCE
                }
                
                priority_map = {
                    "critical": Priority.CRITICAL,
                    "high": Priority.HIGH,
                    "medium": Priority.MEDIUM,
                    "low": Priority.LOW
                }
                
                item_type = type_map.get(
                    content.get("item_type", "context"),
                    WorkingMemoryType.CONTEXT
                )
                
                priority = priority_map.get(
                    content.get("priority", "medium"),
                    Priority.MEDIUM
                )
                
                memory_id = await self.memory_system.working_memory.add_item(
                    item_type=item_type,
                    content=content.get("content", {}),
                    priority=priority,
                    tags=content.get("tags", []),
                    agent_id=content.get("agent_id"),
                    session_id=content.get("session_id")
                )
            
            else:
                return {
                    "success": False,
                    "error": f"Unknown memory type: {memory_type}"
                }
            
            return {
                "success": True,
                "memory_id": memory_id,
                "memory_type": memory_type,
                "stored_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Memory storage failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "memory_type": memory_type
            }
    
    async def get_memory_statistics(self) -> Dict[str, Any]:
        """Get comprehensive memory system statistics."""
        try:
            if not self.memory_system:
                return {
                    "success": False,
                    "error": "Memory system not available"
                }
            
            stats = self.memory_system.get_comprehensive_statistics()
            
            return {
                "success": True,
                "statistics": stats,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get memory statistics: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def consolidate_memory(self) -> Dict[str, Any]:
        """Trigger memory consolidation process."""
        try:
            if not self.memory_system:
                return {
                    "success": False,
                    "error": "Memory system not available"
                }
            
            # Trigger consolidation
            result = await self.memory_system.consolidation_system.consolidate_working_to_episodic()
            
            return {
                "success": result.success,
                "consolidation_type": result.consolidation_type.value if hasattr(result.consolidation_type, 'value') else str(result.consolidation_type),
                "source_items": len(result.source_items),
                "target_items": len(result.target_items),
                "execution_time": result.execution_time,
                "error": result.error,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Memory consolidation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def search_episodes(self, query: str, limit: int = 10, episode_type: Optional[str] = None) -> Dict[str, Any]:
        """Search episodic memories."""
        try:
            if not self.memory_system:
                return {
                    "success": False,
                    "error": "Memory system not available",
                    "episodes": []
                }
            
            # Convert episode type if provided
            episode_type_filter = None
            if episode_type:
                from ai.memory.episodic_memory import EpisodeType
                type_map = {
                    "conversation": EpisodeType.CONVERSATION,
                    "problem_solving": EpisodeType.PROBLEM_SOLVING,
                    "learning": EpisodeType.LEARNING,
                    "user_interaction": EpisodeType.USER_INTERACTION,
                    "system_event": EpisodeType.SYSTEM_EVENT
                }
                episode_type_filter = type_map.get(episode_type.lower())
            
            # Search episodes
            episodes = await self.memory_system.episodic_memory.retrieve_episodes(
                query=query,
                episode_type=episode_type_filter,
                limit=limit
            )
            
            return {
                "success": True,
                "query": query,
                "episodes": [episode.to_dict() for episode in episodes],
                "count": len(episodes),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Episode search failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "episodes": [],
                "timestamp": datetime.now().isoformat()
            }
    
    async def search_concepts(self, query: str, limit: int = 10, concept_type: Optional[str] = None) -> Dict[str, Any]:
        """Search semantic concepts."""
        try:
            if not self.memory_system:
                return {
                    "success": False,
                    "error": "Memory system not available",
                    "concepts": []
                }
            
            # Convert concept type if provided
            concept_type_filter = None
            if concept_type:
                from ai.memory.semantic_memory import ConceptType
                type_map = {
                    "entity": ConceptType.ENTITY,
                    "concept": ConceptType.CONCEPT,
                    "procedure": ConceptType.PROCEDURE,
                    "fact": ConceptType.FACT,
                    "rule": ConceptType.RULE,
                    "pattern": ConceptType.PATTERN,
                    "category": ConceptType.CATEGORY
                }
                concept_type_filter = type_map.get(concept_type.lower())
            
            # Search concepts
            concepts = await self.memory_system.semantic_memory.find_concepts(
                query=query,
                concept_type=concept_type_filter,
                limit=limit
            )
            
            return {
                "success": True,
                "query": query,
                "concepts": [concept.to_dict() for concept in concepts],
                "count": len(concepts),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Concept search failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "concepts": [],
                "timestamp": datetime.now().isoformat()
            }
