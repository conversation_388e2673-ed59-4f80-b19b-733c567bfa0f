"""
Tool Service for CHaBot Integrated System
Handles tool management and execution
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ToolService:
    """Service for managing tools and their execution."""
    
    def __init__(self, tool_registry=None):
        """Initialize the tool service."""
        self.tool_registry = tool_registry
        
        # Mock tools when registry is not available
        self.mock_tools = {
            "calculate": {
                "name": "calculate",
                "description": "Perform mathematical calculations",
                "category": "calculation",
                "parameters": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression to evaluate",
                        "required": True
                    }
                },
                "examples": ["2 + 2", "sqrt(16)", "sin(pi/2)"]
            },
            "search": {
                "name": "search",
                "description": "Search for information",
                "category": "search",
                "parameters": {
                    "query": {
                        "type": "string", 
                        "description": "Search query",
                        "required": True
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results",
                        "required": False,
                        "default": 10
                    }
                },
                "examples": ["artificial intelligence", "python programming"]
            },
            "query_database": {
                "name": "query_database",
                "description": "Execute database queries",
                "category": "data",
                "parameters": {
                    "query": {
                        "type": "string",
                        "description": "SQL query to execute",
                        "required": True
                    },
                    "database": {
                        "type": "string",
                        "description": "Database to query",
                        "required": False,
                        "default": "default"
                    }
                },
                "examples": ["SELECT * FROM users LIMIT 10", "SELECT COUNT(*) FROM orders"]
            },
            "create_tool": {
                "name": "create_tool",
                "description": "Create new tools dynamically",
                "category": "meta",
                "parameters": {
                    "name": {
                        "type": "string",
                        "description": "Name of the new tool",
                        "required": True
                    },
                    "code": {
                        "type": "string",
                        "description": "Python code for the tool",
                        "required": True
                    },
                    "description": {
                        "type": "string",
                        "description": "Description of what the tool does",
                        "required": True
                    }
                },
                "examples": ["def hello_world(): return 'Hello, World!'"]
            }
        }
        
        logger.info("Tool Service initialized")
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List all available tools."""
        try:
            if self.tool_registry:
                # Use real tool registry
                return await self.tool_registry.list_tools()
            else:
                # Return mock tools
                tools = []
                for tool_name, tool_info in self.mock_tools.items():
                    tools.append({
                        "name": tool_name,
                        "description": tool_info["description"],
                        "category": tool_info["category"],
                        "parameters": tool_info["parameters"],
                        "examples": tool_info["examples"],
                        "available": True,
                        "last_used": None
                    })
                return tools
                
        except Exception as e:
            logger.error(f"Failed to list tools: {e}")
            return []
    
    async def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific tool."""
        try:
            if self.tool_registry:
                return await self.tool_registry.get_tool_info(tool_name)
            else:
                # Check mock tools
                if tool_name in self.mock_tools:
                    tool_info = self.mock_tools[tool_name].copy()
                    tool_info["available"] = True
                    tool_info["last_used"] = None
                    return tool_info
                return None
                
        except Exception as e:
            logger.error(f"Failed to get tool info for {tool_name}: {e}")
            return None
    
    async def execute_tool(self, tool_name: str, *args, **kwargs) -> Dict[str, Any]:
        """Execute a tool with given parameters."""
        try:
            execution_id = f"exec_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            start_time = datetime.now()
            
            if self.tool_registry:
                # Use real tool registry
                result = await self.tool_registry.execute_tool(tool_name, *args, **kwargs)
                
                return {
                    "success": True,
                    "execution_id": execution_id,
                    "tool_name": tool_name,
                    "result": result,
                    "execution_time": (datetime.now() - start_time).total_seconds(),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                # Mock tool execution
                if tool_name not in self.mock_tools:
                    return {
                        "success": False,
                        "execution_id": execution_id,
                        "error": f"Tool '{tool_name}' not found",
                        "available_tools": list(self.mock_tools.keys())
                    }
                
                # Simulate tool execution
                await asyncio.sleep(0.1)  # Simulate processing time
                
                # Generate mock results based on tool type
                if tool_name == "calculate":
                    expression = args[0] if args else kwargs.get("expression", "2+2")
                    try:
                        # Simple evaluation for demo (in production, use safe evaluation)
                        result = eval(expression) if expression.replace(" ", "").replace(".", "").replace("+", "").replace("-", "").replace("*", "").replace("/", "").replace("(", "").replace(")", "").isdigit() or "sqrt" in expression or "sin" in expression or "cos" in expression or "pi" in expression else f"Result of {expression}"
                        if "sqrt" in expression:
                            result = f"Square root calculation: {expression}"
                        elif "sin" in expression or "cos" in expression:
                            result = f"Trigonometric calculation: {expression}"
                        elif "pi" in expression:
                            result = f"Pi-based calculation: {expression}"
                    except:
                        result = f"Calculated result for: {expression}"
                        
                elif tool_name == "search":
                    query = args[0] if args else kwargs.get("query", "default query")
                    limit = kwargs.get("limit", 10)
                    result = {
                        "query": query,
                        "results": [
                            {"title": f"Result {i+1} for '{query}'", "url": f"https://example.com/result{i+1}", "snippet": f"This is a mock search result {i+1} for the query '{query}'"}
                            for i in range(min(limit, 5))
                        ],
                        "total_found": limit
                    }
                    
                elif tool_name == "query_database":
                    query = args[0] if args else kwargs.get("query", "SELECT 1")
                    database = kwargs.get("database", "default")
                    result = {
                        "query": query,
                        "database": database,
                        "rows": [
                            {"id": 1, "name": "Sample Data 1", "value": "Mock Value 1"},
                            {"id": 2, "name": "Sample Data 2", "value": "Mock Value 2"}
                        ],
                        "row_count": 2,
                        "execution_time_ms": 15
                    }
                    
                elif tool_name == "create_tool":
                    name = kwargs.get("name", "new_tool")
                    code = kwargs.get("code", "def new_tool(): return 'Hello from new tool'")
                    description = kwargs.get("description", "A dynamically created tool")
                    result = {
                        "tool_name": name,
                        "code": code,
                        "description": description,
                        "created": True,
                        "message": f"Tool '{name}' created successfully (mock)"
                    }
                    
                else:
                    result = f"Mock result for tool '{tool_name}' with args: {args}, kwargs: {kwargs}"
                
                return {
                    "success": True,
                    "execution_id": execution_id,
                    "tool_name": tool_name,
                    "parameters": {"args": args, "kwargs": kwargs},
                    "result": result,
                    "execution_time": (datetime.now() - start_time).total_seconds(),
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Tool execution failed for {tool_name}: {e}")
            return {
                "success": False,
                "execution_id": execution_id if 'execution_id' in locals() else None,
                "tool_name": tool_name,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def create_dynamic_tool(self, name: str, code: str, description: str, category: str = "custom") -> Dict[str, Any]:
        """Create a new tool dynamically."""
        try:
            if self.tool_registry:
                # Use real tool registry
                result = await self.tool_registry.create_dynamic_tool(name, code, description, category)
                return {
                    "success": True,
                    "tool_name": name,
                    "result": result,
                    "created_at": datetime.now().isoformat()
                }
            else:
                # Mock tool creation
                self.mock_tools[name] = {
                    "name": name,
                    "description": description,
                    "category": category,
                    "code": code,
                    "parameters": {},
                    "examples": [],
                    "created_at": datetime.now().isoformat(),
                    "dynamic": True
                }
                
                return {
                    "success": True,
                    "tool_name": name,
                    "description": description,
                    "category": category,
                    "message": f"Dynamic tool '{name}' created successfully (mock)",
                    "created_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Failed to create dynamic tool {name}: {e}")
            return {
                "success": False,
                "tool_name": name,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_tool_categories(self) -> List[str]:
        """Get all available tool categories."""
        try:
            if self.tool_registry:
                return await self.tool_registry.get_categories()
            else:
                # Return mock categories
                categories = set()
                for tool_info in self.mock_tools.values():
                    categories.add(tool_info["category"])
                return list(categories)
                
        except Exception as e:
            logger.error(f"Failed to get tool categories: {e}")
            return []
    
    async def get_tools_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Get all tools in a specific category."""
        try:
            all_tools = await self.list_tools()
            return [tool for tool in all_tools if tool.get("category") == category]
            
        except Exception as e:
            logger.error(f"Failed to get tools by category {category}: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get tool service statistics."""
        try:
            total_tools = len(self.mock_tools)
            categories = len(set(tool["category"] for tool in self.mock_tools.values()))
            
            return {
                "total_tools": total_tools,
                "categories": categories,
                "tool_registry_available": self.tool_registry is not None,
                "mock_tools_active": self.tool_registry is None,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get tool statistics: {e}")
            return {
                "error": str(e),
                "last_updated": datetime.now().isoformat()
            }
