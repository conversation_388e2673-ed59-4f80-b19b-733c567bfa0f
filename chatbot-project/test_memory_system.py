#!/usr/bin/env python3
"""
Comprehensive Test Suite for the Memory System.
Tests episodic memory, semantic memory, working memory, and consolidation.
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import memory system components
from ai.memory.episodic_memory import (
    EpisodicMemorySystem, EpisodeType, EpisodeStatus, EpisodicContext
)
from ai.memory.semantic_memory import (
    SemanticMemorySystem, ConceptType, RelationType
)
from ai.memory.working_memory import (
    WorkingMemorySystem, WorkingMemoryType, Priority
)
from ai.memory.memory_consolidation import (
    MemoryConsolidationSystem, ConsolidationType
)
from ai.memory.enhanced_integrated_memory_system import (
    EnhancedIntegratedMemorySystem, MemoryQuery
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemorySystemTestSuite:
    """Comprehensive test suite for the Memory System."""
    
    def __init__(self):
        self.test_results = {}
        
        # Initialize memory systems
        self.episodic_memory = EpisodicMemorySystem()
        self.semantic_memory = SemanticMemorySystem()
        self.working_memory = WorkingMemorySystem()
        self.consolidation_system = MemoryConsolidationSystem(
            self.episodic_memory,
            self.semantic_memory,
            self.working_memory
        )
        self.integrated_system = EnhancedIntegratedMemorySystem()
    
    async def run_all_tests(self):
        """Run all memory system tests."""
        logger.info("🧠 Starting Memory System Tests")
        logger.info("=" * 80)
        
        tests = [
            ("Episodic Memory System", self.test_episodic_memory),
            ("Semantic Memory System", self.test_semantic_memory),
            ("Working Memory System", self.test_working_memory),
            ("Memory Consolidation System", self.test_memory_consolidation),
            ("Enhanced Integrated Memory System", self.test_integrated_memory_system)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running {test_name} test...")
            try:
                result = await test_func()
                if result:
                    logger.info(f"✅ PASSED: {test_name}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ FAILED: {test_name}")
                self.test_results[test_name] = result
            except Exception as e:
                logger.error(f"❌ FAILED: {test_name} - {e}")
                self.test_results[test_name] = False
        
        # Print summary
        logger.info("\n" + "=" * 80)
        logger.info("🎯 MEMORY SYSTEM TEST SUMMARY")
        logger.info("=" * 80)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if passed_tests == total_tests:
            logger.info("🎉 All Memory System components are working perfectly!")
            logger.info("🧠 Memory system is ready for production!")
        else:
            logger.warning("⚠️ Some tests failed. Please review the issues above.")
        
        return passed_tests == total_tests
    
    async def test_episodic_memory(self) -> bool:
        """Test Episodic Memory System functionality."""
        try:
            logger.info("📚 Testing Episodic Memory System...")
            
            # Test episode storage
            context = EpisodicContext(
                participants=["user", "agent"],
                environment={"location": "chat", "platform": "web"},
                emotional_state="positive"
            )
            
            episode_id = await self.episodic_memory.store_episode(
                episode_type=EpisodeType.CONVERSATION,
                title="Test Conversation",
                description="A test conversation about AI capabilities",
                content={
                    "messages": [
                        {"role": "user", "content": "What can you do?"},
                        {"role": "assistant", "content": "I can help with various tasks..."}
                    ],
                    "topic": "AI capabilities"
                },
                context=context,
                importance=0.8,
                tags=["test", "conversation", "AI"],
                organization="TestOrg",
                user_id="user123",
                agent_id="agent456"
            )
            
            if not episode_id:
                logger.error("Failed to store episode")
                return False
            
            logger.info(f"✅ Episode stored successfully: {episode_id}")
            
            # Test episode retrieval
            episodes = await self.episodic_memory.retrieve_episodes(
                query="AI capabilities",
                episode_type=EpisodeType.CONVERSATION,
                organization="TestOrg",
                limit=5
            )
            
            if not episodes or len(episodes) == 0:
                logger.error("Failed to retrieve episodes")
                return False
            
            logger.info(f"✅ Retrieved {len(episodes)} episodes")
            
            # Test episode by ID
            retrieved_episode = await self.episodic_memory.get_episode_by_id(episode_id)
            
            if not retrieved_episode or retrieved_episode.title != "Test Conversation":
                logger.error("Failed to retrieve episode by ID")
                return False
            
            logger.info("✅ Episode retrieval by ID successful")
            
            # Test related episodes
            related = await self.episodic_memory.find_related_episodes(episode_id, max_results=3)
            
            logger.info(f"✅ Found {len(related)} related episodes")
            
            # Test consolidation
            consolidated_count = await self.episodic_memory.consolidate_episodes()
            
            logger.info(f"✅ Consolidated {consolidated_count} episodes")
            
            # Test statistics
            stats = self.episodic_memory.get_statistics()
            
            if stats["total_episodes"] == 0:
                logger.error("Statistics show no episodes")
                return False
            
            logger.info(f"✅ Statistics: {stats['total_episodes']} total episodes")
            
            logger.info("✅ Episodic Memory System test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Episodic memory test failed: {e}")
            return False
    
    async def test_semantic_memory(self) -> bool:
        """Test Semantic Memory System functionality."""
        try:
            logger.info("🧩 Testing Semantic Memory System...")
            
            # Test concept creation
            ai_concept_id = await self.semantic_memory.add_concept(
                name="Artificial Intelligence",
                concept_type=ConceptType.CONCEPT,
                description="The simulation of human intelligence in machines",
                properties={
                    "definition": "AI systems that can perform tasks requiring human intelligence",
                    "applications": ["NLP", "Computer Vision", "Robotics"]
                },
                confidence=0.9,
                importance=0.8,
                domain="Technology",
                tags=["AI", "technology", "intelligence"]
            )
            
            if not ai_concept_id:
                logger.error("Failed to create AI concept")
                return False
            
            logger.info(f"✅ AI concept created: {ai_concept_id}")
            
            # Test related concept
            ml_concept_id = await self.semantic_memory.add_concept(
                name="Machine Learning",
                concept_type=ConceptType.CONCEPT,
                description="A subset of AI that learns from data",
                properties={
                    "definition": "Algorithms that improve through experience",
                    "types": ["Supervised", "Unsupervised", "Reinforcement"]
                },
                confidence=0.9,
                importance=0.8,
                domain="Technology",
                tags=["ML", "AI", "learning"]
            )
            
            logger.info(f"✅ ML concept created: {ml_concept_id}")
            
            # Test relationship creation
            relation_id = await self.semantic_memory.add_relation(
                source_concept_id=ml_concept_id,
                target_concept_id=ai_concept_id,
                relation_type=RelationType.PART_OF,
                strength=0.9,
                confidence=0.8
            )
            
            if not relation_id:
                logger.error("Failed to create relation")
                return False
            
            logger.info(f"✅ Relation created: {relation_id}")
            
            # Test concept search
            concepts = await self.semantic_memory.find_concepts(
                query="artificial intelligence",
                concept_type=ConceptType.CONCEPT,
                limit=5
            )
            
            if not concepts or len(concepts) == 0:
                logger.error("Failed to find concepts")
                return False
            
            logger.info(f"✅ Found {len(concepts)} concepts")
            
            # Test related concepts
            related_concepts = await self.semantic_memory.get_related_concepts(
                ai_concept_id,
                max_depth=2,
                limit=5
            )
            
            logger.info(f"✅ Found {len(related_concepts)} related concepts")
            
            # Test shortest path
            path = await self.semantic_memory.find_shortest_path(ml_concept_id, ai_concept_id)
            
            if not path:
                logger.error("Failed to find shortest path")
                return False
            
            logger.info(f"✅ Found path with {len(path)} relations")
            
            logger.info("✅ Semantic Memory System test completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Semantic memory test failed: {e}")
            return False
    
    async def test_working_memory(self) -> bool:
        """Test Working Memory System functionality."""
        try:
            logger.info("⚡ Testing Working Memory System...")
            
            # Test item addition
            goal_id = await self.working_memory.add_item(
                WorkingMemoryType.CURRENT_GOAL,
                {"goal": "Solve math problem", "status": "active"},
                Priority.CRITICAL,
                tags=["math", "problem-solving"],
                agent_id="agent123"
            )
            
            if not goal_id:
                logger.error("Failed to add goal item")
                return False
            
            logger.info(f"✅ Goal item added: {goal_id}")
            
            # Test reasoning session
            session_id = await self.working_memory.start_reasoning_session(
                goal="Calculate compound interest",
                max_steps=10,
                timeout=timedelta(minutes=5)
            )
            
            if not session_id:
                logger.error("Failed to start reasoning session")
                return False
            
            logger.info(f"✅ Reasoning session started: {session_id}")
            
            # Test reasoning steps
            step_id = await self.working_memory.add_reasoning_step(
                session_id=session_id,
                step_type="calculation",
                content={
                    "operation": "multiply",
                    "operands": [1000, 1.05],
                    "result": 1050
                },
                confidence=0.9
            )
            
            logger.info(f"✅ Reasoning step added: {step_id}")
            
            # Test item retrieval
            items = await self.working_memory.find_items(
                item_type=WorkingMemoryType.CURRENT_GOAL,
                agent_id="agent123",
                limit=5
            )
            
            if not items or len(items) == 0:
                logger.error("Failed to find items")
                return False
            
            logger.info(f"✅ Found {len(items)} items")
            
            # Test attention focus
            attention_items = await self.working_memory.get_attention_focus(session_id)
            
            logger.info(f"✅ Attention focus: {len(attention_items)} items")
            
            # Test session end
            summary = await self.working_memory.end_reasoning_session(session_id)
            
            if not summary or "session_id" not in summary:
                logger.error("Failed to end reasoning session")
                return False
            
            logger.info(f"✅ Session ended: {summary['steps_completed']} steps completed")
            
            # Test statistics
            stats = self.working_memory.get_statistics()
            
            if stats["current_items"] < 0:
                logger.error("Invalid statistics")
                return False
            
            logger.info(f"✅ Statistics: {stats['current_items']} current items")
            
            logger.info("✅ Working Memory System test completed successfully")
            return True

        except Exception as e:
            logger.error(f"Working memory test failed: {e}")
            return False

    async def test_memory_consolidation(self) -> bool:
        """Test Memory Consolidation System functionality."""
        try:
            logger.info("🔄 Testing Memory Consolidation System...")

            # Add some working memory items to consolidate
            session_id = await self.working_memory.start_reasoning_session(
                goal="Test consolidation",
                max_steps=5
            )

            # Add reasoning steps
            for i in range(3):
                await self.working_memory.add_reasoning_step(
                    session_id=session_id,
                    step_type="analysis",
                    content={
                        "step": i + 1,
                        "analysis": f"Analysis step {i + 1}",
                        "confidence": 0.8
                    }
                )

            # Test working memory to episodic consolidation
            result = await self.consolidation_system.consolidate_working_to_episodic(
                session_id=session_id,
                min_importance=0.3
            )

            if not result.success:
                logger.error(f"Working to episodic consolidation failed: {result.error}")
                return False

            logger.info(f"✅ Consolidated {len(result.source_items)} working memory items to {len(result.target_items)} episodes")

            # Add some episodic memories for pattern extraction
            for i in range(5):
                await self.episodic_memory.store_episode(
                    episode_type=EpisodeType.PROBLEM_SOLVING,
                    title=f"Problem Solving Session {i + 1}",
                    description=f"Solving problem type A with method {i % 2 + 1}",
                    content={
                        "problem_type": "A",
                        "method": i % 2 + 1,
                        "success": i % 3 == 0
                    },
                    importance=0.7,
                    tags=["problem-solving", f"method-{i % 2 + 1}"]
                )

            # Test episodic to semantic consolidation
            semantic_result = await self.consolidation_system.consolidate_episodic_to_semantic(
                min_frequency=2,
                pattern_similarity=0.6
            )

            if not semantic_result.success:
                logger.error(f"Episodic to semantic consolidation failed: {semantic_result.error}")
                return False

            logger.info(f"✅ Extracted {len(semantic_result.patterns_found)} patterns and {len(semantic_result.knowledge_extracted)} concepts")

            # Test working memory to semantic consolidation
            await self.working_memory.add_item(
                WorkingMemoryType.RESOURCE,
                {
                    "name": "Important Resource",
                    "type": "knowledge",
                    "content": "This is important knowledge that should be preserved"
                },
                Priority.HIGH,
                tags=["important", "knowledge"]
            )

            semantic_direct_result = await self.consolidation_system.consolidate_working_to_semantic(
                item_types=[WorkingMemoryType.RESOURCE],
                min_importance=0.7
            )

            if not semantic_direct_result.success:
                logger.error(f"Working to semantic consolidation failed: {semantic_direct_result.error}")
                return False

            logger.info(f"✅ Consolidated {len(semantic_direct_result.source_items)} working memory items to {len(semantic_direct_result.target_items)} semantic concepts")

            logger.info("✅ Memory Consolidation System test completed successfully")
            return True

        except Exception as e:
            logger.error(f"Memory consolidation test failed: {e}")
            return False

    async def test_integrated_memory_system(self) -> bool:
        """Test Enhanced Integrated Memory System functionality."""
        try:
            logger.info("🧠 Testing Enhanced Integrated Memory System...")

            # Start the integrated system
            await self.integrated_system.start()

            # Test comprehensive query
            query = MemoryQuery(
                query_text="artificial intelligence problem solving",
                memory_types=["episodic", "semantic", "working"],
                importance_threshold=0.3,
                limit=10,
                organization="TestOrg",
                agent_id="agent123",
                tags=["AI", "problem-solving"]
            )

            result = await self.integrated_system.comprehensive_query(query)

            if result.total_results < 0:
                logger.error("Invalid query result")
                return False

            logger.info(f"✅ Comprehensive query returned {result.total_results} results in {result.query_time:.3f}s")
            logger.info(f"    - Episodic memories: {len(result.episodic_memories)}")
            logger.info(f"    - Semantic concepts: {len(result.semantic_concepts)}")
            logger.info(f"    - Working memory items: {len(result.working_memory_items)}")
            logger.info(f"    - Related concepts: {len(result.related_concepts)}")
            logger.info(f"    - Attention focus: {len(result.attention_focus)}")

            # Test reasoning session storage
            reasoning_steps = [
                {
                    "type": "analysis",
                    "content": "Analyze the problem requirements",
                    "confidence": 0.8
                },
                {
                    "type": "planning",
                    "content": "Create solution plan",
                    "confidence": 0.9
                },
                {
                    "type": "execution",
                    "content": "Execute the solution",
                    "confidence": 0.7
                }
            ]

            outcome = {
                "success": True,
                "result": "Problem solved successfully",
                "confidence": 0.85
            }

            session_id, episode_id = await self.integrated_system.store_reasoning_session(
                goal="Solve integration problem",
                steps=reasoning_steps,
                outcome=outcome,
                agent_id="agent123",
                organization="TestOrg"
            )

            if not session_id or not episode_id:
                logger.error("Failed to store reasoning session")
                return False

            logger.info(f"✅ Reasoning session stored: session={session_id}, episode={episode_id}")

            # Test reasoning context retrieval
            context = await self.integrated_system.get_reasoning_context(
                current_goal="Solve similar integration problem",
                agent_id="agent123"
            )

            if not isinstance(context, dict):
                logger.error("Invalid reasoning context")
                return False

            logger.info(f"✅ Reasoning context retrieved:")
            logger.info(f"    - Similar episodes: {len(context.get('similar_episodes', []))}")
            logger.info(f"    - Relevant concepts: {len(context.get('relevant_concepts', []))}")
            logger.info(f"    - Current attention: {len(context.get('current_attention', []))}")

            # Stop the integrated system
            await self.integrated_system.stop()

            logger.info("✅ Enhanced Integrated Memory System test completed successfully")
            return True

        except Exception as e:
            logger.error(f"Integrated memory system test failed: {e}")
            return False
        finally:
            # Ensure system is stopped
            try:
                await self.integrated_system.stop()
            except:
                pass

async def main():
    """Main test execution function."""
    test_suite = MemorySystemTestSuite()

    try:
        success = await test_suite.run_all_tests()

        # Save test results
        results_file = "memory_system_test_results.json"
        with open(results_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "test_results": {k: v for k, v in test_suite.test_results.items()},
                "overall_success": success,
                "summary": f"{sum(test_suite.test_results.values())}/{len(test_suite.test_results)} tests passed"
            }, f, indent=2)

        logger.info(f"📄 Detailed results saved to {results_file}")

        return success

    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return False
    finally:
        # Clean up memory systems
        try:
            test_suite.episodic_memory.close()
            test_suite.semantic_memory.close()
            test_suite.working_memory.close()
        except:
            pass

if __name__ == "__main__":
    # Run the memory system test suite
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
