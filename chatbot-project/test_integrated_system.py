#!/usr/bin/env python3
"""
Integrated System Test Suite
Tests the complete CHaBot integrated system functionality
"""

import asyncio
import sys
import os
import logging
import json
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import configuration
from integrated_config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegratedSystemTestSuite:
    """Test suite for the integrated CHaBot system."""
    
    def __init__(self):
        """Initialize the test suite."""
        self.config = Config
        self.test_results = {}
        
        logger.info("Integrated System Test Suite initialized")
    
    async def test_configuration_loading(self) -> bool:
        """Test configuration loading and validation."""
        logger.info("🔧 Testing configuration loading...")
        
        try:
            # Test basic configuration access
            assert self.config.APP_NAME == "CHaBot"
            assert self.config.API_PORT > 0
            assert self.config.API_HOST is not None
            
            # Test database configuration
            assert self.config.DATABASE_PATH is not None
            assert self.config.EPISODIC_MEMORY_DB_PATH is not None
            assert self.config.SEMANTIC_MEMORY_DB_PATH is not None
            
            # Test feature flags
            assert isinstance(self.config.FEATURE_ADVANCED_REASONING, bool)
            assert isinstance(self.config.FEATURE_MULTI_AGENT_COORDINATION, bool)
            assert isinstance(self.config.FEATURE_TOOL_INTEGRATION, bool)
            
            # Test agent configuration
            assert isinstance(self.config.ENABLE_RESEARCH_AGENT, bool)
            assert isinstance(self.config.ENABLE_ANALYSIS_AGENT, bool)
            
            # Test memory configuration
            assert self.config.WORKING_MEMORY_CAPACITY > 0
            assert self.config.CONSOLIDATION_INTERVAL > 0
            
            logger.info("✅ Configuration loading test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration loading test failed: {e}")
            return False
    
    async def test_memory_system_integration(self) -> bool:
        """Test memory system integration."""
        logger.info("🧠 Testing memory system integration...")
        
        try:
            # Import memory systems
            from ai.memory.enhanced_integrated_memory_system import (
                EnhancedIntegratedMemorySystem, MemoryQuery
            )
            
            # Initialize memory system
            memory_system = EnhancedIntegratedMemorySystem(
                database_path=":memory:",
                working_memory_capacity=50,
                auto_consolidation=False
            )
            
            # Start memory system
            await memory_system.start()
            
            # Test comprehensive query
            query = MemoryQuery(
                query_text="test query",
                memory_types=["episodic", "semantic", "working"],
                limit=5
            )
            
            result = await memory_system.comprehensive_query(query)
            
            # Validate result structure
            assert hasattr(result, 'episodic_memories')
            assert hasattr(result, 'semantic_concepts')
            assert hasattr(result, 'working_memory_items')
            assert hasattr(result, 'total_results')
            assert hasattr(result, 'query_time')
            
            # Test statistics
            stats = memory_system.get_comprehensive_statistics()
            assert 'integrated_system' in stats
            assert 'episodic_memory' in stats
            assert 'semantic_memory' in stats
            assert 'working_memory' in stats
            
            # Stop memory system
            await memory_system.stop()
            
            logger.info("✅ Memory system integration test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Memory system integration test failed: {e}")
            return False
    
    async def test_agent_framework_integration(self) -> bool:
        """Test agent framework integration."""
        logger.info("🤖 Testing agent framework integration...")
        
        try:
            # Import agent components
            from agents.orchestrator.agent_orchestrator import AgentOrchestrator
            from agents.communication.agent_communication_infrastructure import AgentCommunicationInfrastructure
            
            # Test communication infrastructure
            comm_infra = AgentCommunicationInfrastructure()
            await comm_infra.start()
            
            # Test basic functionality
            assert comm_infra.is_running
            
            await comm_infra.stop()
            
            logger.info("✅ Agent framework integration test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Agent framework integration test failed: {e}")
            return False
    
    async def test_tool_integration(self) -> bool:
        """Test tool integration system."""
        logger.info("🛠️ Testing tool integration...")
        
        try:
            # Import tool components
            from agents.tools.registry.tool_registry import ToolRegistry
            from agents.tools.calculator.basic_calculator import EnhancedCalculator
            
            # Initialize tool registry
            tool_registry = ToolRegistry()
            
            # Register a test tool
            calculator = EnhancedCalculator()
            tool_registry.register_tool(
                name="test_calculate",
                func=calculator.calculate,
                description="Test calculator tool",
                category="test"
            )
            
            # Test tool execution
            result = await tool_registry.execute_tool("test_calculate", "2 + 2")
            assert result is not None
            
            # Test registry statistics
            stats = tool_registry.get_registry_statistics()
            assert 'total_tools' in stats
            assert stats['total_tools'] > 0
            
            logger.info("✅ Tool integration test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Tool integration test failed: {e}")
            return False
    
    async def test_reasoning_engine_integration(self) -> bool:
        """Test reasoning engine integration."""
        logger.info("🧩 Testing reasoning engine integration...")
        
        try:
            # Import reasoning components
            from ai.reasoning.advanced_reasoning_engine import AdvancedReasoningEngine
            
            # Test basic import and initialization structure
            # Note: Full initialization requires LLM infrastructure
            logger.info("✅ Reasoning engine integration test passed (structure check)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Reasoning engine integration test failed: {e}")
            return False
    
    async def test_knowledge_fusion_integration(self) -> bool:
        """Test knowledge fusion system integration."""
        logger.info("🔗 Testing knowledge fusion integration...")
        
        try:
            # Import knowledge fusion components
            from ai.knowledge.knowledge_fusion_system import KnowledgeFusionSystem
            
            # Test basic import and structure
            logger.info("✅ Knowledge fusion integration test passed (structure check)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Knowledge fusion integration test failed: {e}")
            return False
    
    async def test_llm_infrastructure_integration(self) -> bool:
        """Test LLM infrastructure integration."""
        logger.info("🤖 Testing LLM infrastructure integration...")
        
        try:
            # Import LLM components
            from ai.llm.local_llm_infrastructure import LocalLLMInfrastructure
            
            # Test basic structure
            llm_infra = LocalLLMInfrastructure()
            
            # Test configuration access
            config = llm_infra.get_model_config()
            assert isinstance(config, dict)
            
            logger.info("✅ LLM infrastructure integration test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ LLM infrastructure integration test failed: {e}")
            return False
    
    async def test_system_startup_components(self) -> bool:
        """Test system startup components."""
        logger.info("🚀 Testing system startup components...")
        
        try:
            # Import startup components
            from start_chabot import CHaBotSystemManager
            
            # Test system manager initialization
            system_manager = CHaBotSystemManager()
            
            # Test system info generation
            info = system_manager.get_system_info()
            assert 'name' in info
            assert 'version' in info
            assert 'config' in info
            
            logger.info("✅ System startup components test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ System startup components test failed: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all integration tests."""
        logger.info("🧪 Starting Integrated System Tests")
        logger.info("=" * 80)
        
        tests = [
            ("Configuration Loading", self.test_configuration_loading),
            ("Memory System Integration", self.test_memory_system_integration),
            ("Agent Framework Integration", self.test_agent_framework_integration),
            ("Tool Integration", self.test_tool_integration),
            ("Reasoning Engine Integration", self.test_reasoning_engine_integration),
            ("Knowledge Fusion Integration", self.test_knowledge_fusion_integration),
            ("LLM Infrastructure Integration", self.test_llm_infrastructure_integration),
            ("System Startup Components", self.test_system_startup_components),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running {test_name} test...")
            try:
                result = await test_func()
                if result:
                    logger.info(f"✅ PASSED: {test_name}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ FAILED: {test_name}")
                self.test_results[test_name] = result
            except Exception as e:
                logger.error(f"❌ FAILED: {test_name} - {e}")
                self.test_results[test_name] = False
        
        # Print summary
        logger.info("\n" + "=" * 80)
        logger.info("🎯 INTEGRATED SYSTEM TEST SUMMARY")
        logger.info("=" * 80)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if passed_tests == total_tests:
            logger.info("🎉 All integrated system tests passed!")
            logger.info("🚀 System is ready for deployment!")
        else:
            logger.warning("⚠️ Some tests failed. Please review the issues above.")
        
        return passed_tests == total_tests

async def main():
    """Main test execution function."""
    test_suite = IntegratedSystemTestSuite()
    
    try:
        success = await test_suite.run_all_tests()
        
        # Save test results
        results_file = "integrated_system_test_results.json"
        with open(results_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "test_results": {k: v for k, v in test_suite.test_results.items()},
                "overall_success": success,
                "summary": f"{sum(test_suite.test_results.values())}/{len(test_suite.test_results)} tests passed"
            }, f, indent=2)
        
        logger.info(f"📄 Detailed results saved to {results_file}")
        
        return success
        
    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return False

if __name__ == "__main__":
    # Run the integrated system test suite
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
