#!/usr/bin/env python3
"""
Comprehensive Test Script for Advanced Reasoning Engine
Tests all implemented components: Tree of Thoughts, Self-Reflection, Counterfactual Reasoning, and Multi-step Logic.
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_tree_of_thoughts():
    """Test Tree of Thoughts implementation."""
    logger.info("🌳 Testing Tree of Thoughts Implementation...")
    
    try:
        from ai.reasoning.tree_of_thoughts.tree_of_thoughts import TreeOfThoughts
        
        # Initialize Tree of Thoughts
        tot = TreeOfThoughts()
        
        # Test problem
        problem = "Should we implement a flexible work-from-home policy for all employees?"
        context = {
            "company_size": "medium",
            "current_policy": "office_only",
            "employee_feedback": "positive_towards_flexibility"
        }
        
        # Generate reasoning paths
        logger.info("Generating multiple reasoning paths...")
        paths = tot.generate_thoughts(problem, context, max_depth=3)
        
        logger.info(f"✅ Generated {len(paths)} reasoning paths")
        
        # Get best reasoning path
        best_path_result = tot.get_best_reasoning_path(problem, context)
        
        logger.info(f"✅ Best path confidence: {best_path_result['confidence']:.2f}")
        logger.info(f"✅ Reasoning steps: {len(best_path_result['reasoning'])}")
        logger.info(f"✅ Total thoughts generated: {best_path_result['total_thoughts']}")
        
        # Display reasoning steps
        for i, step in enumerate(best_path_result['reasoning'][:3]):  # Show first 3 steps
            logger.info(f"   Step {i+1}: {step['step'][:80]}... (confidence: {step['confidence']:.2f})")
        
        # Export tree structure
        tree_export = tot.export_tree()
        logger.info(f"✅ Tree structure exported with {tree_export['total_thoughts']} thoughts")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Tree of Thoughts test failed: {e}")
        return False

async def test_self_reflection_engine():
    """Test Self-Reflection Engine implementation."""
    logger.info("🪞 Testing Self-Reflection Engine...")
    
    try:
        from ai.reasoning.self_reflection.self_reflection_engine import SelfReflectionEngine
        
        # Initialize Self-Reflection Engine
        reflection_engine = SelfReflectionEngine()
        
        # Create a reasoning result to reflect on
        reasoning_result = {
            "problem": "Evaluate employee performance review process",
            "reasoning_steps": [
                {"step": "Analyze current process", "confidence": 0.8},
                {"step": "Identify improvement areas", "confidence": 0.7},
                {"step": "Propose new framework", "confidence": 0.6}
            ],
            "conclusion": "Implement quarterly reviews with peer feedback",
            "confidence": 0.7
        }
        
        context = {
            "department": "HR",
            "urgency": "medium",
            "stakeholders": ["managers", "employees", "HR_team"]
        }
        
        # Test complete self-reflection
        logger.info("Performing complete self-reflection...")
        reflection_result = await reflection_engine.complete_self_reflection(reasoning_result, context)
        
        logger.info(f"✅ Reflection completed successfully")
        logger.info(f"✅ Verification results: {reflection_result.get('verification_results', {}).get('verified_steps', 0)} steps verified")
        logger.info(f"✅ Error analysis: {reflection_result.get('error_analysis', {}).get('total_errors', 0)} errors detected")
        logger.info(f"✅ Confidence analysis: {reflection_result.get('confidence_analysis', {}).get('overall_confidence', 0.0):.2f}")
        
        # Test final assessment
        final_assessment = reflection_result.get('final_assessment', {})
        logger.info(f"✅ Overall quality: {final_assessment.get('overall_quality', 'unknown')}")
        logger.info(f"✅ Quality score: {final_assessment.get('quality_score', 0.0):.2f}")
        logger.info(f"✅ Recommendations: {len(final_assessment.get('recommendations', []))}")
        
        # Test reflection summary
        summary = reflection_result.get('reflection_summary', {})
        logger.info(f"✅ Reflection overview: {summary.get('reflection_overview', 'N/A')[:100]}...")
        
        # Test reflection statistics
        stats = reflection_engine.get_reflection_statistics()
        logger.info(f"✅ Reflection statistics: {stats.get('total_reflections', 0)} total reflections")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Self-Reflection Engine test failed: {e}")
        return False

async def test_counterfactual_reasoning():
    """Test Counterfactual Reasoning implementation."""
    logger.info("🔄 Testing Counterfactual Reasoning...")
    
    try:
        from ai.reasoning.counterfactual.counterfactual_engine import CounterfactualEngine
        
        # Initialize Counterfactual Engine
        counterfactual_engine = CounterfactualEngine()
        
        # Test scenario
        scenario = "The company implemented a strict remote work policy during the pandemic"
        context = {
            "time_period": "2020-2023",
            "company_type": "technology",
            "employee_satisfaction": "high",
            "productivity_metrics": "improved"
        }
        
        # Generate counterfactuals
        logger.info("Generating counterfactual scenarios...")
        counterfactual_result = await counterfactual_engine.generate_counterfactuals(scenario, context)
        
        logger.info(f"✅ Counterfactual analysis completed")
        logger.info(f"✅ Conditions identified: {counterfactual_result['conditions_identified']}")
        logger.info(f"✅ Alternatives generated: {counterfactual_result['alternatives_generated']}")
        logger.info(f"✅ Scenarios analyzed: {len(counterfactual_result['analyzed_scenarios'])}")
        
        # Display analyzed scenarios
        for i, scenario_analysis in enumerate(counterfactual_result['analyzed_scenarios'][:2]):  # Show first 2
            scenario_obj = scenario_analysis['scenario']
            logger.info(f"   Scenario {i+1}: {scenario_obj.alternative_condition}")
            logger.info(f"   Predicted outcome: {scenario_obj.predicted_outcome}")
            logger.info(f"   Likelihood: {scenario_obj.likelihood:.2f}")
            logger.info(f"   Impact level: {scenario_obj.impact_level}")
        
        # Display implications
        implications = counterfactual_result['implications']
        logger.info(f"✅ Key insights: {len(implications['key_insights'])}")
        logger.info(f"✅ Opportunities identified: {len(implications['opportunities'])}")
        logger.info(f"✅ Recommendations: {len(implications['recommendations'])}")
        
        # Display counterfactual insights
        insights = counterfactual_result['counterfactual_insights']
        for insight in insights:
            logger.info(f"   💡 Insight: {insight}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Counterfactual Reasoning test failed: {e}")
        return False

async def test_advanced_reasoning_engine():
    """Test the comprehensive Advanced Reasoning Engine."""
    logger.info("🧠 Testing Advanced Reasoning Engine...")
    
    try:
        from ai.reasoning.engine.advanced_reasoning_engine import (
            AdvancedReasoningEngine, ReasoningType
        )
        
        # Initialize Advanced Reasoning Engine
        reasoning_engine = AdvancedReasoningEngine()
        
        # Test problem
        problem = "How should we restructure our employee benefits package to improve retention while managing costs?"
        context = {
            "current_retention_rate": 0.85,
            "budget_constraints": "moderate",
            "employee_priorities": ["health_insurance", "flexible_time", "professional_development"],
            "industry_benchmarks": "competitive",
            "company_size": "500_employees"
        }
        
        # Test different reasoning types
        reasoning_types = [
            ReasoningType.TREE_OF_THOUGHTS,
            ReasoningType.SELF_REFLECTION,
            ReasoningType.COUNTERFACTUAL,
            ReasoningType.MULTI_STEP,
            ReasoningType.COMPREHENSIVE
        ]
        
        results = {}
        
        for reasoning_type in reasoning_types:
            logger.info(f"Testing {reasoning_type.value} reasoning...")
            
            start_time = time.time()
            result = await reasoning_engine.reason(problem, context, reasoning_type)
            execution_time = time.time() - start_time
            
            results[reasoning_type.value] = result
            
            logger.info(f"✅ {reasoning_type.value} completed in {execution_time:.2f}s")
            logger.info(f"   Confidence: {result.overall_confidence:.2f}")
            logger.info(f"   Steps: {len(result.steps)}")
            logger.info(f"   Conclusion: {result.final_conclusion[:100]}...")
            
            # Display metadata for comprehensive reasoning
            if reasoning_type == ReasoningType.COMPREHENSIVE:
                metadata = result.metadata
                logger.info(f"   Reasoning approaches: {metadata.get('reasoning_approaches', 0)}")
                component_results = metadata.get('component_results', {})
                logger.info(f"   Multi-step confidence: {component_results.get('multi_step_confidence', 0.0):.2f}")
                logger.info(f"   Tree confidence: {component_results.get('tree_confidence', 0.0):.2f}")
                logger.info(f"   Reflection quality: {component_results.get('reflection_quality', 0.0):.2f}")
                logger.info(f"   Counterfactual confidence: {component_results.get('counterfactual_confidence', 0.0):.2f}")
        
        # Test reasoning statistics
        stats = reasoning_engine.get_reasoning_statistics()
        logger.info(f"✅ Reasoning statistics:")
        logger.info(f"   Total tasks: {stats['performance_metrics']['total_reasoning_tasks']}")
        logger.info(f"   Successful tasks: {stats['performance_metrics']['successful_tasks']}")
        logger.info(f"   Average confidence: {stats['performance_metrics']['average_confidence']:.2f}")
        logger.info(f"   Average execution time: {stats['performance_metrics']['average_execution_time']:.2f}s")
        
        # Test reasoning explanation
        comprehensive_result = results['comprehensive']
        explanation = await reasoning_engine.get_reasoning_explanation(comprehensive_result.reasoning_id)
        
        logger.info(f"✅ Reasoning explanation generated:")
        logger.info(f"   Step-by-step explanation: {len(explanation['step_by_step_explanation'])} steps")
        logger.info(f"   Confidence trend: {explanation['confidence_breakdown']['confidence_trend']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Advanced Reasoning Engine test failed: {e}")
        return False

async def test_reasoning_integration():
    """Test integration between all reasoning components."""
    logger.info("🔗 Testing Reasoning Component Integration...")
    
    try:
        from ai.reasoning.engine.advanced_reasoning_engine import AdvancedReasoningEngine, ReasoningType
        
        # Initialize engine
        engine = AdvancedReasoningEngine()
        
        # Complex HR scenario
        problem = """
        An employee has filed a complaint about workplace harassment. The accused is a high-performing manager 
        who brings significant revenue to the company. How should HR handle this situation while ensuring 
        fairness, legal compliance, and maintaining team morale?
        """
        
        context = {
            "complaint_type": "harassment",
            "accused_position": "senior_manager",
            "business_impact": "high_revenue_generator",
            "legal_requirements": "investigation_mandatory",
            "team_dynamics": "tension_present",
            "company_values": ["integrity", "respect", "fairness"],
            "previous_incidents": "none_reported"
        }
        
        # Run comprehensive reasoning
        logger.info("Running comprehensive reasoning on complex HR scenario...")
        result = await engine.reason(problem, context, ReasoningType.COMPREHENSIVE)
        
        logger.info(f"✅ Integration test completed")
        logger.info(f"✅ Overall confidence: {result.overall_confidence:.2f}")
        logger.info(f"✅ Reasoning steps: {len(result.steps)}")
        logger.info(f"✅ Execution time: {result.execution_time:.2f}s")
        
        # Analyze step progression
        logger.info("📊 Step-by-step analysis:")
        for i, step in enumerate(result.steps):
            logger.info(f"   Step {i+1}: {step.step_type.value} - {step.description}")
            logger.info(f"            Confidence: {step.confidence:.2f}")
            logger.info(f"            Evidence: {len(step.evidence)} items")
        
        # Final conclusion analysis
        logger.info(f"🎯 Final conclusion: {result.final_conclusion}")
        
        # Metadata analysis
        metadata = result.metadata
        logger.info(f"📈 Component performance:")
        component_results = metadata.get('component_results', {})
        for component, confidence in component_results.items():
            logger.info(f"   {component}: {confidence:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Reasoning integration test failed: {e}")
        return False

async def main():
    """Run comprehensive Advanced Reasoning Engine tests."""
    logger.info("🚀 Starting Advanced Reasoning Engine Tests")
    logger.info("=" * 80)
    
    tests = [
        ("Tree of Thoughts Implementation", test_tree_of_thoughts),
        ("Self-Reflection Engine", test_self_reflection_engine),
        ("Counterfactual Reasoning", test_counterfactual_reasoning),
        ("Advanced Reasoning Engine", test_advanced_reasoning_engine),
        ("Reasoning Integration", test_reasoning_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name} test...")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"❌ FAILED: {test_name} - {e}")
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("🎯 ADVANCED REASONING ENGINE TEST SUMMARY")
    logger.info("=" * 80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\n📊 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All Advanced Reasoning Engine components are working perfectly!")
        logger.info("🧠 Ready for complex reasoning tasks with:")
        logger.info("   ✅ Tree of Thoughts - Multi-path reasoning exploration")
        logger.info("   ✅ Self-Reflection - Quality assessment and improvement")
        logger.info("   ✅ Counterfactual Reasoning - Alternative scenario analysis")
        logger.info("   ✅ Multi-step Logic - Complex problem decomposition")
        logger.info("   ✅ Comprehensive Integration - All components working together")
    elif passed >= total * 0.8:
        logger.info("✅ Most Advanced Reasoning Engine components are working well!")
    else:
        logger.warning("⚠️ Some Advanced Reasoning Engine components need attention!")
    
    # Save detailed results
    detailed_results = {
        "timestamp": time.time(),
        "test_results": results,
        "summary": {
            "passed": passed,
            "total": total,
            "success_rate": f"{(passed/total)*100:.1f}%"
        },
        "components_tested": [
            "Tree of Thoughts Implementation",
            "Self-Reflection Engine", 
            "Counterfactual Reasoning",
            "Multi-step Reasoning Logic",
            "Comprehensive Integration"
        ]
    }
    
    with open("advanced_reasoning_engine_test_results.json", "w") as f:
        json.dump(detailed_results, f, indent=2)
    
    logger.info("📄 Detailed results saved to advanced_reasoning_engine_test_results.json")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
