class ExperienceLearner {
    constructor() {
        this.experienceStore = new Map();
        this.patterns = new Map();
        this.strategies = new Map();
        this.learningMetrics = new Map();
        this.initializeLearner();
    }

    // Initialize experience learner
    initializeLearner() {
        this.setupExperienceStorage();
        this.initializePatternRecognition();
        this.configureStrategyAdaptation();
    }

    // Set up experience storage
    setupExperienceStorage() {
        const storageConfig = {
            maxExperiences: 10000,
            retentionPeriod: 30 * 24 * 60 * 60 * 1000, // 30 days
            categories: ['success', 'failure', 'neutral'],
            indexFields: ['timestamp', 'context', 'outcome', 'confidence']
        };

        this.experienceStore.set('config', storageConfig);
        this.experienceStore.set('experiences', []);
        this.experienceStore.set('index', new Map());
    }

    // Initialize pattern recognition
    initializePatternRecognition() {
        const patternTypes = {
            query_patterns: {
                name: 'Query Patterns',
                features: ['keywords', 'intent', 'complexity', 'domain'],
                threshold: 0.8,
                minOccurrences: 5
            },
            response_patterns: {
                name: 'Response Patterns',
                features: ['structure', 'length', 'confidence', 'success_rate'],
                threshold: 0.75,
                minOccurrences: 3
            },
            context_patterns: {
                name: 'Context Patterns',
                features: ['user_role', 'session_length', 'previous_queries', 'time_of_day'],
                threshold: 0.7,
                minOccurrences: 4
            },
            failure_patterns: {
                name: 'Failure Patterns',
                features: ['error_type', 'context', 'complexity', 'knowledge_gap'],
                threshold: 0.85,
                minOccurrences: 2
            }
        };

        Object.entries(patternTypes).forEach(([id, pattern]) => {
            this.patterns.set(id, {
                ...pattern,
                id,
                instances: [],
                lastUpdated: null
            });
        });
    }

    // Configure strategy adaptation
    configureStrategyAdaptation() {
        const strategies = {
            response_strategy: {
                name: 'Response Strategy',
                parameters: {
                    confidence_threshold: 0.8,
                    detail_level: 'medium',
                    explanation_depth: 2
                },
                adaptationRate: 0.1,
                performance: 0.75
            },
            knowledge_retrieval: {
                name: 'Knowledge Retrieval Strategy',
                parameters: {
                    search_depth: 3,
                    relevance_threshold: 0.7,
                    max_results: 5
                },
                adaptationRate: 0.15,
                performance: 0.8
            },
            reasoning_approach: {
                name: 'Reasoning Approach',
                parameters: {
                    max_steps: 5,
                    validation_level: 'high',
                    explanation_required: true
                },
                adaptationRate: 0.05,
                performance: 0.85
            }
        };

        Object.entries(strategies).forEach(([id, strategy]) => {
            this.strategies.set(id, {
                ...strategy,
                id,
                adaptations: 0,
                lastAdapted: null
            });
        });
    }

    // Store experience
    async storeExperience(experience) {
        const experienceRecord = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            agentId: experience.agentId,
            context: experience.context,
            action: experience.action,
            outcome: experience.outcome,
            success: experience.success,
            confidence: experience.confidence || 0.5,
            feedback: experience.feedback,
            metadata: experience.metadata || {}
        };

        // Add to storage
        const experiences = this.experienceStore.get('experiences');
        experiences.push(experienceRecord);

        // Maintain storage limits
        const config = this.experienceStore.get('config');
        if (experiences.length > config.maxExperiences) {
            experiences.shift(); // Remove oldest
        }

        // Update index
        this.updateExperienceIndex(experienceRecord);

        // Trigger pattern recognition
        await this.recognizePatterns(experienceRecord);

        return experienceRecord.id;
    }

    // Update experience index
    updateExperienceIndex(experience) {
        const index = this.experienceStore.get('index');
        const config = this.experienceStore.get('config');

        config.indexFields.forEach(field => {
            const value = experience[field];
            if (value) {
                if (!index.has(field)) {
                    index.set(field, new Map());
                }
                const fieldIndex = index.get(field);
                if (!fieldIndex.has(value)) {
                    fieldIndex.set(value, []);
                }
                fieldIndex.get(value).push(experience.id);
            }
        });
    }

    // Retrieve experiences
    async retrieveExperiences(query) {
        const experiences = this.experienceStore.get('experiences');
        let results = [...experiences];

        // Apply filters
        if (query.agentId) {
            results = results.filter(exp => exp.agentId === query.agentId);
        }
        if (query.success !== undefined) {
            results = results.filter(exp => exp.success === query.success);
        }
        if (query.context) {
            results = results.filter(exp => 
                JSON.stringify(exp.context).includes(query.context)
            );
        }
        if (query.timeRange) {
            const startTime = new Date(query.timeRange.start);
            const endTime = new Date(query.timeRange.end);
            results = results.filter(exp => {
                const expTime = new Date(exp.timestamp);
                return expTime >= startTime && expTime <= endTime;
            });
        }

        // Sort by relevance/timestamp
        results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        // Limit results
        const limit = query.limit || 100;
        return results.slice(0, limit);
    }

    // Recognize patterns
    async recognizePatterns(newExperience) {
        for (const [patternId, pattern] of this.patterns) {
            const similarity = await this.calculatePatternSimilarity(newExperience, pattern);
            
            if (similarity >= pattern.threshold) {
                await this.updatePattern(patternId, newExperience, similarity);
            }
        }
    }

    // Calculate pattern similarity
    async calculatePatternSimilarity(experience, pattern) {
        let totalSimilarity = 0;
        let featureCount = 0;

        for (const feature of pattern.features) {
            const similarity = this.calculateFeatureSimilarity(experience, feature, pattern);
            if (similarity > 0) {
                totalSimilarity += similarity;
                featureCount++;
            }
        }

        return featureCount > 0 ? totalSimilarity / featureCount : 0;
    }

    // Calculate feature similarity
    calculateFeatureSimilarity(experience, feature, pattern) {
        switch (feature) {
            case 'keywords':
                return this.calculateKeywordSimilarity(experience.context?.query, pattern.instances);
            case 'intent':
                return this.calculateIntentSimilarity(experience.context?.intent, pattern.instances);
            case 'complexity':
                return this.calculateComplexitySimilarity(experience.context?.complexity, pattern.instances);
            case 'confidence':
                return this.calculateConfidenceSimilarity(experience.confidence, pattern.instances);
            case 'success_rate':
                return this.calculateSuccessRateSimilarity(experience.success, pattern.instances);
            default:
                return 0.5; // Default similarity
        }
    }

    // Calculate keyword similarity
    calculateKeywordSimilarity(query, instances) {
        if (!query || instances.length === 0) return 0;
        
        const queryWords = query.toLowerCase().split(' ');
        let maxSimilarity = 0;

        instances.forEach(instance => {
            if (instance.context?.query) {
                const instanceWords = instance.context.query.toLowerCase().split(' ');
                const intersection = queryWords.filter(word => instanceWords.includes(word));
                const similarity = intersection.length / Math.max(queryWords.length, instanceWords.length);
                maxSimilarity = Math.max(maxSimilarity, similarity);
            }
        });

        return maxSimilarity;
    }

    // Calculate intent similarity
    calculateIntentSimilarity(intent, instances) {
        if (!intent || instances.length === 0) return 0;
        
        const matchingInstances = instances.filter(instance => 
            instance.context?.intent === intent
        );
        
        return matchingInstances.length / instances.length;
    }

    // Calculate complexity similarity
    calculateComplexitySimilarity(complexity, instances) {
        if (!complexity || instances.length === 0) return 0;
        
        const complexityValues = instances.map(instance => instance.context?.complexity || 0);
        const avgComplexity = complexityValues.reduce((a, b) => a + b, 0) / complexityValues.length;
        
        return 1 - Math.abs(complexity - avgComplexity) / Math.max(complexity, avgComplexity);
    }

    // Calculate confidence similarity
    calculateConfidenceSimilarity(confidence, instances) {
        if (instances.length === 0) return 0;
        
        const confidenceValues = instances.map(instance => instance.confidence || 0.5);
        const avgConfidence = confidenceValues.reduce((a, b) => a + b, 0) / confidenceValues.length;
        
        return 1 - Math.abs(confidence - avgConfidence);
    }

    // Calculate success rate similarity
    calculateSuccessRateSimilarity(success, instances) {
        if (instances.length === 0) return 0;
        
        const successRate = instances.filter(instance => instance.success).length / instances.length;
        const targetRate = success ? 1 : 0;
        
        return 1 - Math.abs(targetRate - successRate);
    }

    // Update pattern
    async updatePattern(patternId, experience, similarity) {
        const pattern = this.patterns.get(patternId);
        pattern.instances.push(experience);
        pattern.lastUpdated = new Date().toISOString();

        // Trigger strategy adaptation if pattern is significant
        if (pattern.instances.length >= pattern.minOccurrences) {
            await this.adaptStrategies(patternId, pattern);
        }
    }

    // Adapt strategies based on patterns
    async adaptStrategies(patternId, pattern) {
        for (const [strategyId, strategy] of this.strategies) {
            const adaptation = await this.calculateStrategyAdaptation(strategy, pattern);
            
            if (adaptation.shouldAdapt) {
                await this.applyStrategyAdaptation(strategyId, adaptation);
            }
        }
    }

    // Calculate strategy adaptation
    async calculateStrategyAdaptation(strategy, pattern) {
        const adaptation = {
            shouldAdapt: false,
            parameterChanges: {},
            expectedImprovement: 0
        };

        // Analyze pattern performance
        const successRate = pattern.instances.filter(exp => exp.success).length / pattern.instances.length;
        const avgConfidence = pattern.instances.reduce((sum, exp) => sum + exp.confidence, 0) / pattern.instances.length;

        // Determine if adaptation is needed
        if (successRate < 0.7 || avgConfidence < 0.6) {
            adaptation.shouldAdapt = true;
            
            // Calculate parameter adjustments
            if (strategy.id === 'response_strategy') {
                if (avgConfidence < 0.6) {
                    adaptation.parameterChanges.confidence_threshold = Math.max(0.5, strategy.parameters.confidence_threshold - 0.1);
                }
                if (successRate < 0.7) {
                    adaptation.parameterChanges.detail_level = 'high';
                }
            }
            
            adaptation.expectedImprovement = (0.8 - Math.max(successRate, avgConfidence)) * strategy.adaptationRate;
        }

        return adaptation;
    }

    // Apply strategy adaptation
    async applyStrategyAdaptation(strategyId, adaptation) {
        const strategy = this.strategies.get(strategyId);
        
        // Update parameters
        Object.assign(strategy.parameters, adaptation.parameterChanges);
        
        // Update strategy metrics
        strategy.adaptations++;
        strategy.lastAdapted = new Date().toISOString();
        strategy.performance = Math.min(1.0, strategy.performance + adaptation.expectedImprovement);

        return {
            strategyId,
            adaptations: adaptation.parameterChanges,
            newPerformance: strategy.performance
        };
    }

    // Get learning metrics
    getLearningMetrics() {
        const experiences = this.experienceStore.get('experiences');
        const totalExperiences = experiences.length;
        const successfulExperiences = experiences.filter(exp => exp.success).length;
        
        return {
            totalExperiences,
            successRate: totalExperiences > 0 ? successfulExperiences / totalExperiences : 0,
            patterns: {
                total: this.patterns.size,
                active: Array.from(this.patterns.values()).filter(p => p.instances.length >= p.minOccurrences).length
            },
            strategies: {
                total: this.strategies.size,
                adaptations: Array.from(this.strategies.values()).reduce((sum, s) => sum + s.adaptations, 0),
                avgPerformance: Array.from(this.strategies.values()).reduce((sum, s) => sum + s.performance, 0) / this.strategies.size
            },
            timestamp: new Date().toISOString()
        };
    }

    // Generate learning report
    generateLearningReport() {
        const metrics = this.getLearningMetrics();
        const experiences = this.experienceStore.get('experiences');
        
        return {
            summary: {
                totalExperiences: metrics.totalExperiences,
                successRate: metrics.successRate,
                patternsRecognized: metrics.patterns.active,
                strategiesAdapted: metrics.strategies.adaptations
            },
            patterns: Array.from(this.patterns.values()).map(p => ({
                id: p.id,
                name: p.name,
                instances: p.instances.length,
                active: p.instances.length >= p.minOccurrences
            })),
            strategies: Array.from(this.strategies.values()).map(s => ({
                id: s.id,
                name: s.name,
                performance: s.performance,
                adaptations: s.adaptations,
                parameters: s.parameters
            })),
            recentExperiences: experiences.slice(-10),
            recommendations: this.generateLearningRecommendations(metrics)
        };
    }

    // Generate learning recommendations
    generateLearningRecommendations(metrics) {
        const recommendations = [];

        if (metrics.successRate < 0.7) {
            recommendations.push({
                priority: 'high',
                category: 'performance',
                recommendation: `Success rate is low (${(metrics.successRate * 100).toFixed(1)}%) - review failure patterns`
            });
        }

        if (metrics.patterns.active < metrics.patterns.total * 0.5) {
            recommendations.push({
                priority: 'medium',
                category: 'patterns',
                recommendation: 'Many patterns are inactive - consider lowering recognition thresholds'
            });
        }

        if (metrics.strategies.adaptations === 0) {
            recommendations.push({
                priority: 'medium',
                category: 'adaptation',
                recommendation: 'No strategy adaptations detected - review adaptation criteria'
            });
        }

        return recommendations;
    }
}

export default ExperienceLearner;