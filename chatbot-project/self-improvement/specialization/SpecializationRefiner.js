class SpecializationRefiner {
    constructor() {
        this.capabilities = new Map();
        this.expertise = new Map();
        this.roles = new Map();
        this.refinementHistory = [];
        this.initializeRefiner();
    }

    // Initialize specialization refiner
    initializeRefiner() {
        this.setupCapabilityDiscovery();
        this.initializeExpertiseDevelopment();
        this.configureRoleOptimization();
    }

    // Set up capability discovery
    setupCapabilityDiscovery() {
        const capabilityTypes = {
            policy_knowledge: {
                name: 'Policy Knowledge',
                domains: ['leave_policies', 'benefits', 'compliance', 'procedures'],
                proficiency: 0.7,
                usage: 0,
                improvement_rate: 0.05
            },
            query_processing: {
                name: 'Query Processing',
                domains: ['intent_recognition', 'context_understanding', 'ambiguity_resolution'],
                proficiency: 0.8,
                usage: 0,
                improvement_rate: 0.03
            },
            response_generation: {
                name: 'Response Generation',
                domains: ['clarity', 'completeness', 'personalization', 'formatting'],
                proficiency: 0.75,
                usage: 0,
                improvement_rate: 0.04
            },
            reasoning: {
                name: 'Reasoning',
                domains: ['logical_inference', 'rule_application', 'exception_handling'],
                proficiency: 0.85,
                usage: 0,
                improvement_rate: 0.02
            },
            communication: {
                name: 'Communication',
                domains: ['tone_adaptation', 'role_awareness', 'escalation_handling'],
                proficiency: 0.6,
                usage: 0,
                improvement_rate: 0.06
            }
        };

        Object.entries(capabilityTypes).forEach(([id, capability]) => {
            this.capabilities.set(id, {
                ...capability,
                id,
                discovered: true,
                lastUsed: null,
                performance_history: []
            });
        });
    }

    // Initialize expertise development
    initializeExpertiseDevelopment() {
        const expertiseAreas = {
            hr_policies: {
                name: 'HR Policies',
                level: 'intermediate',
                experience: 150,
                specializations: ['leave_management', 'performance_reviews', 'disciplinary_actions'],
                growth_rate: 0.8
            },
            employee_services: {
                name: 'Employee Services',
                level: 'advanced',
                experience: 300,
                specializations: ['onboarding', 'benefits_enrollment', 'career_development'],
                growth_rate: 0.9
            },
            compliance: {
                name: 'Compliance',
                level: 'expert',
                experience: 500,
                specializations: ['legal_requirements', 'audit_preparation', 'risk_assessment'],
                growth_rate: 0.95
            },
            benefits_administration: {
                name: 'Benefits Administration',
                level: 'intermediate',
                experience: 200,
                specializations: ['health_insurance', 'retirement_plans', 'flexible_benefits'],
                growth_rate: 0.85
            }
        };

        Object.entries(expertiseAreas).forEach(([id, expertise]) => {
            this.expertise.set(id, {
                ...expertise,
                id,
                development_history: [],
                last_improvement: null
            });
        });
    }

    // Configure role optimization
    configureRoleOptimization() {
        const roles = {
            hr_policy_agent: {
                name: 'HR Policy Agent',
                primary_capabilities: ['policy_knowledge', 'reasoning'],
                secondary_capabilities: ['query_processing', 'response_generation'],
                expertise_areas: ['hr_policies', 'compliance'],
                efficiency: 0.8,
                specialization_score: 0.75
            },
            employee_services_agent: {
                name: 'Employee Services Agent',
                primary_capabilities: ['communication', 'response_generation'],
                secondary_capabilities: ['policy_knowledge', 'query_processing'],
                expertise_areas: ['employee_services', 'benefits_administration'],
                efficiency: 0.85,
                specialization_score: 0.8
            },
            benefits_agent: {
                name: 'Benefits Agent',
                primary_capabilities: ['policy_knowledge', 'reasoning'],
                secondary_capabilities: ['communication', 'response_generation'],
                expertise_areas: ['benefits_administration', 'compliance'],
                efficiency: 0.9,
                specialization_score: 0.85
            },
            compliance_agent: {
                name: 'Compliance Agent',
                primary_capabilities: ['reasoning', 'policy_knowledge'],
                secondary_capabilities: ['query_processing', 'communication'],
                expertise_areas: ['compliance', 'hr_policies'],
                efficiency: 0.95,
                specialization_score: 0.9
            }
        };

        Object.entries(roles).forEach(([id, role]) => {
            this.roles.set(id, {
                ...role,
                id,
                optimization_history: [],
                last_optimized: null
            });
        });
    }

    // Discover capabilities
    async discoverCapabilities(agentId, performanceData) {
        const discoveries = [];

        for (const [capabilityId, capability] of this.capabilities) {
            const discovery = await this.analyzeCapabilityPerformance(
                agentId, 
                capabilityId, 
                performanceData
            );
            
            if (discovery.discovered || discovery.improved) {
                discoveries.push(discovery);
                await this.updateCapability(capabilityId, discovery);
            }
        }

        return {
            agentId,
            discoveries,
            timestamp: new Date().toISOString()
        };
    }

    // Analyze capability performance
    async analyzeCapabilityPerformance(agentId, capabilityId, performanceData) {
        const capability = this.capabilities.get(capabilityId);
        const analysis = {
            capabilityId,
            discovered: false,
            improved: false,
            performance_change: 0,
            evidence: []
        };

        // Analyze performance data for capability indicators
        if (performanceData.success_rate > 0.8 && performanceData.domain_match) {
            const domainMatch = capability.domains.some(domain => 
                performanceData.domain_match.includes(domain)
            );
            
            if (domainMatch) {
                analysis.discovered = true;
                analysis.evidence.push(`High success rate (${performanceData.success_rate}) in relevant domain`);
            }
        }

        // Check for improvement
        const currentPerformance = performanceData.capability_scores?.[capabilityId] || capability.proficiency;
        if (currentPerformance > capability.proficiency) {
            analysis.improved = true;
            analysis.performance_change = currentPerformance - capability.proficiency;
            analysis.evidence.push(`Performance improved by ${analysis.performance_change.toFixed(3)}`);
        }

        return analysis;
    }

    // Update capability
    async updateCapability(capabilityId, discovery) {
        const capability = this.capabilities.get(capabilityId);
        
        if (discovery.improved) {
            capability.proficiency = Math.min(1.0, capability.proficiency + discovery.performance_change);
            capability.performance_history.push({
                timestamp: new Date().toISOString(),
                proficiency: capability.proficiency,
                change: discovery.performance_change
            });
        }

        capability.usage++;
        capability.lastUsed = new Date().toISOString();

        return capability;
    }

    // Develop expertise
    async developExpertise(agentId, domain, experienceData) {
        const expertise = this.expertise.get(domain);
        if (!expertise) return null;

        const development = {
            domain,
            agentId,
            before: {
                level: expertise.level,
                experience: expertise.experience
            },
            development: {
                experience_gained: 0,
                level_change: false,
                specializations_added: []
            }
        };

        // Calculate experience gain
        const experienceGain = this.calculateExperienceGain(experienceData, expertise);
        expertise.experience += experienceGain;
        development.development.experience_gained = experienceGain;

        // Check for level advancement
        const newLevel = this.calculateExpertiseLevel(expertise.experience);
        if (newLevel !== expertise.level) {
            expertise.level = newLevel;
            development.development.level_change = true;
        }

        // Check for new specializations
        const newSpecializations = this.identifyNewSpecializations(experienceData, expertise);
        if (newSpecializations.length > 0) {
            expertise.specializations.push(...newSpecializations);
            development.development.specializations_added = newSpecializations;
        }

        // Record development
        expertise.development_history.push({
            timestamp: new Date().toISOString(),
            experience_gained: experienceGain,
            level: expertise.level,
            total_experience: expertise.experience
        });
        expertise.last_improvement = new Date().toISOString();

        development.after = {
            level: expertise.level,
            experience: expertise.experience
        };

        return development;
    }

    // Calculate experience gain
    calculateExperienceGain(experienceData, expertise) {
        let baseGain = experienceData.complexity * experienceData.success_rate * 10;
        
        // Apply growth rate
        baseGain *= expertise.growth_rate;
        
        // Bonus for new domains
        if (experienceData.new_domain) {
            baseGain *= 1.5;
        }

        return Math.round(baseGain);
    }

    // Calculate expertise level
    calculateExpertiseLevel(experience) {
        if (experience >= 1000) return 'expert';
        if (experience >= 500) return 'advanced';
        if (experience >= 200) return 'intermediate';
        return 'beginner';
    }

    // Identify new specializations
    identifyNewSpecializations(experienceData, expertise) {
        const newSpecializations = [];
        
        if (experienceData.specialized_domains) {
            experienceData.specialized_domains.forEach(domain => {
                if (!expertise.specializations.includes(domain)) {
                    // Check if agent has enough experience in this domain
                    if (experienceData.domain_experience?.[domain] >= 50) {
                        newSpecializations.push(domain);
                    }
                }
            });
        }

        return newSpecializations;
    }

    // Optimize role
    async optimizeRole(roleId, performanceMetrics) {
        const role = this.roles.get(roleId);
        if (!role) return null;

        const optimization = {
            roleId,
            before: {
                efficiency: role.efficiency,
                specialization_score: role.specialization_score
            },
            optimizations: [],
            after: {}
        };

        // Analyze capability alignment
        const capabilityOptimization = await this.optimizeCapabilityAlignment(role, performanceMetrics);
        if (capabilityOptimization.improved) {
            optimization.optimizations.push(capabilityOptimization);
        }

        // Analyze expertise focus
        const expertiseOptimization = await this.optimizeExpertiseFocus(role, performanceMetrics);
        if (expertiseOptimization.improved) {
            optimization.optimizations.push(expertiseOptimization);
        }

        // Update role metrics
        if (optimization.optimizations.length > 0) {
            role.efficiency = Math.min(1.0, role.efficiency + 0.05);
            role.specialization_score = Math.min(1.0, role.specialization_score + 0.03);
            
            role.optimization_history.push({
                timestamp: new Date().toISOString(),
                optimizations: optimization.optimizations.length,
                efficiency: role.efficiency,
                specialization_score: role.specialization_score
            });
            role.last_optimized = new Date().toISOString();
        }

        optimization.after = {
            efficiency: role.efficiency,
            specialization_score: role.specialization_score
        };

        return optimization;
    }

    // Optimize capability alignment
    async optimizeCapabilityAlignment(role, metrics) {
        const optimization = {
            type: 'capability_alignment',
            improved: false,
            changes: []
        };

        // Check if primary capabilities are performing well
        for (const capabilityId of role.primary_capabilities) {
            const capability = this.capabilities.get(capabilityId);
            const performance = metrics.capability_performance?.[capabilityId] || capability.proficiency;
            
            if (performance < 0.7) {
                // Consider moving to secondary or adding support
                optimization.changes.push({
                    action: 'demote_capability',
                    capability: capabilityId,
                    reason: `Low performance: ${performance.toFixed(2)}`
                });
                optimization.improved = true;
            }
        }

        // Check if secondary capabilities could be promoted
        for (const capabilityId of role.secondary_capabilities) {
            const capability = this.capabilities.get(capabilityId);
            const performance = metrics.capability_performance?.[capabilityId] || capability.proficiency;
            
            if (performance > 0.9) {
                optimization.changes.push({
                    action: 'promote_capability',
                    capability: capabilityId,
                    reason: `High performance: ${performance.toFixed(2)}`
                });
                optimization.improved = true;
            }
        }

        return optimization;
    }

    // Optimize expertise focus
    async optimizeExpertiseFocus(role, metrics) {
        const optimization = {
            type: 'expertise_focus',
            improved: false,
            changes: []
        };

        // Analyze expertise area performance
        for (const expertiseId of role.expertise_areas) {
            const expertise = this.expertise.get(expertiseId);
            const performance = metrics.expertise_performance?.[expertiseId] || expertise.growth_rate;
            
            if (performance < 0.7) {
                optimization.changes.push({
                    action: 'reduce_focus',
                    expertise: expertiseId,
                    reason: `Low performance: ${performance.toFixed(2)}`
                });
                optimization.improved = true;
            } else if (performance > 0.95) {
                optimization.changes.push({
                    action: 'increase_focus',
                    expertise: expertiseId,
                    reason: `Excellent performance: ${performance.toFixed(2)}`
                });
                optimization.improved = true;
            }
        }

        return optimization;
    }

    // Get specialization metrics
    getSpecializationMetrics() {
        return {
            capabilities: {
                total: this.capabilities.size,
                discovered: Array.from(this.capabilities.values()).filter(c => c.discovered).length,
                avgProficiency: Array.from(this.capabilities.values())
                    .reduce((sum, c) => sum + c.proficiency, 0) / this.capabilities.size
            },
            expertise: {
                total: this.expertise.size,
                levels: this.getExpertiseLevelDistribution(),
                avgExperience: Array.from(this.expertise.values())
                    .reduce((sum, e) => sum + e.experience, 0) / this.expertise.size
            },
            roles: {
                total: this.roles.size,
                avgEfficiency: Array.from(this.roles.values())
                    .reduce((sum, r) => sum + r.efficiency, 0) / this.roles.size,
                avgSpecialization: Array.from(this.roles.values())
                    .reduce((sum, r) => sum + r.specialization_score, 0) / this.roles.size
            },
            timestamp: new Date().toISOString()
        };
    }

    // Get expertise level distribution
    getExpertiseLevelDistribution() {
        const distribution = { beginner: 0, intermediate: 0, advanced: 0, expert: 0 };
        
        Array.from(this.expertise.values()).forEach(expertise => {
            distribution[expertise.level]++;
        });

        return distribution;
    }

    // Generate specialization report
    generateSpecializationReport() {
        const metrics = this.getSpecializationMetrics();
        
        return {
            summary: {
                totalCapabilities: metrics.capabilities.total,
                avgProficiency: metrics.capabilities.avgProficiency,
                expertiseAreas: metrics.expertise.total,
                avgEfficiency: metrics.roles.avgEfficiency
            },
            capabilities: Array.from(this.capabilities.values()).map(c => ({
                id: c.id,
                name: c.name,
                proficiency: c.proficiency,
                usage: c.usage,
                domains: c.domains.length
            })),
            expertise: Array.from(this.expertise.values()).map(e => ({
                id: e.id,
                name: e.name,
                level: e.level,
                experience: e.experience,
                specializations: e.specializations.length
            })),
            roles: Array.from(this.roles.values()).map(r => ({
                id: r.id,
                name: r.name,
                efficiency: r.efficiency,
                specialization_score: r.specialization_score,
                optimizations: r.optimization_history.length
            })),
            recommendations: this.generateSpecializationRecommendations(metrics)
        };
    }

    // Generate specialization recommendations
    generateSpecializationRecommendations(metrics) {
        const recommendations = [];

        if (metrics.capabilities.avgProficiency < 0.8) {
            recommendations.push({
                priority: 'high',
                category: 'capabilities',
                recommendation: `Average capability proficiency is low (${(metrics.capabilities.avgProficiency * 100).toFixed(1)}%) - focus on skill development`
            });
        }

        if (metrics.roles.avgEfficiency < 0.8) {
            recommendations.push({
                priority: 'medium',
                category: 'roles',
                recommendation: `Role efficiency is below target (${(metrics.roles.avgEfficiency * 100).toFixed(1)}%) - consider role optimization`
            });
        }

        const expertLevelCount = metrics.expertise.levels.expert;
        if (expertLevelCount === 0) {
            recommendations.push({
                priority: 'medium',
                category: 'expertise',
                recommendation: 'No expert-level expertise areas - focus on developing deep specializations'
            });
        }

        return recommendations;
    }
}

export default SpecializationRefiner;