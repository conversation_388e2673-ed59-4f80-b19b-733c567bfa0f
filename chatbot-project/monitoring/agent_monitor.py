"""Advanced agent monitoring and analytics."""

import time
from typing import Dict, Any, List
from datetime import datetime, timedelta
import json

class AgentMonitor:
    def __init__(self):
        self.agent_metrics = {}
        self.performance_history = {}
        self.alerts = []
    
    def record_agent_activity(self, agent_id: str, activity: Dict[str, Any]):
        """Record agent activity."""
        if agent_id not in self.agent_metrics:
            self.agent_metrics[agent_id] = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "avg_response_time": 0,
                "last_activity": None,
                "status": "active"
            }
        
        metrics = self.agent_metrics[agent_id]
        metrics["total_requests"] += 1
        metrics["last_activity"] = datetime.utcnow().isoformat()
        
        if activity.get("success", False):
            metrics["successful_requests"] += 1
        else:
            metrics["failed_requests"] += 1
        
        # Update response time
        if "response_time" in activity:
            current_avg = metrics["avg_response_time"]
            new_time = activity["response_time"]
            metrics["avg_response_time"] = (current_avg + new_time) / 2
    
    def get_agent_performance(self, agent_id: str) -> Dict[str, Any]:
        """Get agent performance metrics."""
        if agent_id not in self.agent_metrics:
            return {"error": "Agent not found"}
        
        metrics = self.agent_metrics[agent_id]
        success_rate = 0
        if metrics["total_requests"] > 0:
            success_rate = metrics["successful_requests"] / metrics["total_requests"] * 100
        
        return {
            "agent_id": agent_id,
            "total_requests": metrics["total_requests"],
            "success_rate": success_rate,
            "avg_response_time": metrics["avg_response_time"],
            "status": metrics["status"],
            "last_activity": metrics["last_activity"]
        }
    
    def get_system_overview(self) -> Dict[str, Any]:
        """Get system-wide performance overview."""
        total_agents = len(self.agent_metrics)
        active_agents = sum(1 for m in self.agent_metrics.values() if m["status"] == "active")
        total_requests = sum(m["total_requests"] for m in self.agent_metrics.values())
        total_successful = sum(m["successful_requests"] for m in self.agent_metrics.values())
        
        overall_success_rate = 0
        if total_requests > 0:
            overall_success_rate = total_successful / total_requests * 100
        
        return {
            "total_agents": total_agents,
            "active_agents": active_agents,
            "total_requests": total_requests,
            "overall_success_rate": overall_success_rate,
            "alerts_count": len(self.alerts)
        }

# Global monitor instance
agent_monitor = AgentMonitor()