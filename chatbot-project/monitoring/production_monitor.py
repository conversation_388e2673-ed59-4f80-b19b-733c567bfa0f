"""Production monitoring system."""

from typing import Dict, Any, List
import time
from datetime import datetime
from dataclasses import dataclass

@dataclass
class SystemHealth:
    status: str
    response_time: float
    error_rate: float
    active_agents: int
    memory_usage: float

class ProductionMonitor:
    def __init__(self):
        self.metrics = []
        self.alerts = []
        self.thresholds = {
            "response_time": 5.0,
            "error_rate": 0.05,
            "memory_usage": 0.85
        }
    
    def check_system_health(self) -> SystemHealth:
        """Check overall system health."""
        current_time = time.time()
        
        # Mock metrics - in production, these would come from actual monitoring
        response_time = self.calculate_avg_response_time()
        error_rate = self.calculate_error_rate()
        active_agents = self.count_active_agents()
        memory_usage = self.get_memory_usage()
        
        status = self.determine_system_status(response_time, error_rate, memory_usage)
        
        health = SystemHealth(
            status=status,
            response_time=response_time,
            error_rate=error_rate,
            active_agents=active_agents,
            memory_usage=memory_usage
        )
        
        # Store metrics
        self.metrics.append({
            "timestamp": datetime.now().isoformat(),
            "health": health.__dict__
        })
        
        # Check for alerts
        self.check_alerts(health)
        
        return health
    
    def calculate_avg_response_time(self) -> float:
        """Calculate average response time."""
        # Mock implementation
        return 2.3
    
    def calculate_error_rate(self) -> float:
        """Calculate system error rate."""
        # Mock implementation
        return 0.02
    
    def count_active_agents(self) -> int:
        """Count active agents."""
        # Mock implementation
        return 8
    
    def get_memory_usage(self) -> float:
        """Get memory usage percentage."""
        # Mock implementation
        return 0.65
    
    def determine_system_status(self, response_time: float, error_rate: float, memory_usage: float) -> str:
        """Determine overall system status."""
        if (response_time > self.thresholds["response_time"] or 
            error_rate > self.thresholds["error_rate"] or 
            memory_usage > self.thresholds["memory_usage"]):
            return "degraded"
        elif error_rate > 0.01 or response_time > 3.0:
            return "warning"
        else:
            return "healthy"
    
    def check_alerts(self, health: SystemHealth):
        """Check for alert conditions."""
        alerts = []
        
        if health.response_time > self.thresholds["response_time"]:
            alerts.append({
                "type": "performance",
                "message": f"High response time: {health.response_time:.2f}s",
                "severity": "warning"
            })
        
        if health.error_rate > self.thresholds["error_rate"]:
            alerts.append({
                "type": "reliability",
                "message": f"High error rate: {health.error_rate:.2%}",
                "severity": "critical"
            })
        
        if health.memory_usage > self.thresholds["memory_usage"]:
            alerts.append({
                "type": "resource",
                "message": f"High memory usage: {health.memory_usage:.1%}",
                "severity": "warning"
            })
        
        for alert in alerts:
            alert["timestamp"] = datetime.now().isoformat()
            self.alerts.append(alert)
    
    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get monitoring dashboard data."""
        recent_metrics = self.metrics[-10:] if self.metrics else []
        recent_alerts = self.alerts[-5:] if self.alerts else []
        
        return {
            "current_health": self.check_system_health().__dict__,
            "recent_metrics": recent_metrics,
            "active_alerts": recent_alerts,
            "system_uptime": "99.8%",
            "total_requests": 15420,
            "successful_requests": 15111
        }