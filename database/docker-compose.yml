version: '3.8'

services:
  # PostgreSQL - Primary database
  postgres:
    image: postgres:15
    container_name: chabot_postgres
    restart: always
    environment:
      POSTGRES_USER: chabot_user
      POSTGRES_PASSWORD: chabot_password
      POSTGRES_DB: chabot_db
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - chabot_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chabot_user -d chabot_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Memgraph - Real-time graph database (temporarily disabled due to resource constraints)
  # memgraph:
  #   image: memgraph/memgraph:latest
  #   container_name: chabot_memgraph
  #   restart: always
  #   ports:
  #     - "7687:7687"
  #     - "7444:7444"  # Memgraph Lab
  #   volumes:
  #     - memgraph_data:/var/lib/memgraph
  #   environment:
  #     - MEMGRAPH_USER=
  #     - MEMGRAPH_PASSWORD=
  #   command: ["--memory-limit=512", "--log-level=WARNING"]
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 1G
  #       reservations:
  #         memory: 512M
  #   networks:
  #     - chabot_network
  #   healthcheck:
  #     test: ["CMD", "nc", "-z", "localhost", "7687"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Chroma - Vector memory database
  chroma:
    image: chromadb/chroma:latest
    container_name: chabot_chroma
    restart: always
    ports:
      - "8001:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    networks:
      - chabot_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  pgdata:
  memgraph_data:
  chroma_data:

networks:
  chabot_network:
    driver: bridge
